const fs = require('fs');
const path = require('path');

// Base translations for each language
const translations = {
  en: {
    title: "Create Account",
    subtitle: "Join our platform to connect with businesses worldwide",
    form: {
      accountType: {
        title: "Account Type",
        company: "Company",
        individual: "Individual",
        broker: "Broker"
      },
      sector: {
        title: "Sector Information",
        level1: "Main Category",
        level2: "Sub Category",
        level3: "Sub Category 2",
        selectPlaceholder: "Select a category"
      },
      personal: {
        title: "Personal Information",
        firstName: "First Name",
        firstNamePlaceholder: "Enter your first name",
        lastName: "Last Name",
        lastNamePlaceholder: "Enter your last name",
        email: "Email",
        emailPlaceholder: "Enter your email address",
        phone: "Phone Number",
        phonePlaceholder: "Enter your phone number",
        birthDate: "Birth Date",
        birthDatePlaceholder: "Select your birth date",
        password: "Password",
        passwordPlaceholder: "Enter your password"
      },
      location: {
        title: "Location Information",
        country: "Country",
        selectCountry: "Select your country",
        city: "City",
        selectCity: "Select your city",
        address: "Address",
        addressPlaceholder: "Enter your address",
        zipCode: "Zip Code",
        zipCodePlaceholder: "Enter your zip code"
      },
      referralCode: "Referral Code (Optional)",
      referralCodePlaceholder: "Enter referral code if you have one",
      terms: {
        accept: "I agree to the",
        readTerms: "Terms and Conditions",
        error: "You must agree to the terms and conditions"
      },
      submit: "Register",
      submitting: "Registering...",
      alreadyHaveAccount: "Already have an account?",
      login: "Login"
    },
    validation: {
      requiredFields: "Please fill in all required fields"
    },
    toasts: {
      registerSuccess: {
        title: "Success",
        description: "Registration successful! You can now log in."
      },
      registerFailed: {
        title: "Registration Failed",
        description: "Unable to complete registration. Please check your information and try again."
      },
      errorMessage: {
        description: "An error occurred. Please try again later."
      },
      fetchCountriesFailed: {
        title: "Data Loading Error",
        description: "Failed to fetch countries. Please refresh and try again."
      },
      fetchCitiesFailed: {
        title: "Data Loading Error",
        description: "Failed to fetch cities. Please refresh and try again."
      }
    }
  },
  tr: {
    title: "Kayıt Ol",
    subtitle: "Dünya çapında işletmelerle bağlantı kurmak için platformumuza katılın",
    form: {
      accountType: {
        title: "Hesap Türü",
        company: "Kurumsal Firma",
        individual: "Şahıs Firması",
        broker: "Broker"
      },
      sector: {
        title: "Sektör Bilgileri",
        level1: "Ana Kategori",
        level2: "Alt Kategori",
        level3: "İkinci Alt Kategori",
        selectPlaceholder: "Kategori seçin"
      },
      personal: {
        title: "Kişisel Bilgiler",
        firstName: "Adınız",
        firstNamePlaceholder: "Adınızı girin",
        lastName: "Soyadınız",
        lastNamePlaceholder: "Soyadınızı girin",
        email: "E-posta",
        emailPlaceholder: "E-posta adresinizi girin",
        phone: "Telefon Numarası",
        phonePlaceholder: "Telefon numaranızı girin",
        birthDate: "Doğum Tarihi",
        birthDatePlaceholder: "Doğum tarihinizi seçin",
        password: "Şifre",
        passwordPlaceholder: "Şifrenizi girin"
      },
      location: {
        title: "Konum Bilgileri",
        country: "Ülke",
        selectCountry: "Ülkenizi seçin",
        city: "Şehir",
        selectCity: "Şehrinizi seçin",
        address: "Adres",
        addressPlaceholder: "Adresinizi girin",
        zipCode: "Posta Kodu",
        zipCodePlaceholder: "Posta kodunuzu girin"
      },
      referralCode: "Referans Kodu (İsteğe Bağlı)",
      referralCodePlaceholder: "Referans kodunuz varsa girin",
      terms: {
        accept: "Kabul ediyorum",
        readTerms: "Şartlar ve Koşullar",
        error: "Şartlar ve koşulları kabul etmelisiniz"
      },
      submit: "Kayıt Ol",
      submitting: "Kayıt olunuyor...",
      alreadyHaveAccount: "Zaten hesabınız var mı?",
      login: "Giriş Yap"
    },
    validation: {
      requiredFields: "Lütfen tüm zorunlu alanları doldurun"
    },
    toasts: {
      registerSuccess: {
        title: "Başarılı",
        description: "Kayıt başarılı! Şimdi giriş yapabilirsiniz."
      },
      registerFailed: {
        title: "Kayıt Başarısız",
        description: "Kayıt tamamlanamıyor. Lütfen bilgilerinizi kontrol edin ve tekrar deneyin."
      },
      errorMessage: {
        description: "Bir hata oluştu. Lütfen daha sonra tekrar deneyin."
      },
      fetchCountriesFailed: {
        title: "Veri Yükleme Hatası",
        description: "Ülkeler getirilemedi. Lütfen sayfayı yenileyip tekrar deneyin."
      },
      fetchCitiesFailed: {
        title: "Veri Yükleme Hatası",
        description: "Şehirler getirilemedi. Lütfen sayfayı yenileyip tekrar deneyin."
      }
    }
  }
};

// Languages to update
const languages = ['ar', 'de', 'en', 'es', 'fr', 'it', 'ru', 'tr', 'zh'];

// Update each language file
languages.forEach(lang => {
  const filePath = path.join(__dirname, lang, 'register.json');
  
  // Use the specific language translation if available, otherwise use English
  const translation = translations[lang] || translations.en;
  
  // Write the updated translation to the file
  fs.writeFileSync(filePath, JSON.stringify(translation, null, 2), 'utf8');
  
  console.log(`Updated ${lang}/register.json`);
});

console.log('All locale files updated successfully!');
