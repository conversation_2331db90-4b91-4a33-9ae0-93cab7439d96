const fs = require('fs');
const path = require('path');

// Languages to update
const languages = ['ar', 'de', 'es', 'fr', 'it', 'ru', 'tr', 'zh'];

// Update each language file
languages.forEach(lang => {
  const filePath = path.join(__dirname, 'src/locales', lang, 'register.json');
  
  try {
    // Read the current file
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    
    // Update the subtitle
    if (lang === 'tr') {
      data.title = "Hesap Oluşturun";
      data.subtitle = "E-exportcity'ye hoş geldiniz";
    } else if (lang === 'ar') {
      data.subtitle = "مرحبًا بك في e-exportcity";
    } else if (lang === 'de') {
      data.subtitle = "Willkommen bei e-exportcity";
    } else if (lang === 'es') {
      data.subtitle = "Bienvenido a e-exportcity";
    } else if (lang === 'fr') {
      data.subtitle = "Bienvenue sur e-exportcity";
    } else if (lang === 'it') {
      data.subtitle = "Benvenuto su e-exportcity";
    } else if (lang === 'ru') {
      data.subtitle = "Добро пожаловать в e-exportcity";
    } else if (lang === 'zh') {
      data.subtitle = "欢迎来到 e-exportcity";
    } else {
      data.subtitle = "Welcome to e-exportcity";
    }
    
    // Write the updated file
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`Updated ${lang}/register.json`);
  } catch (error) {
    console.error(`Error updating ${lang}/register.json:`, error.message);
  }
});

console.log('All register locale files updated successfully!');
