import { readFileSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

// Get the directory name using ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Read the English version as reference
const enPath = join(__dirname, 'src/locales/en/itemListing.json');
const enData = JSON.parse(readFileSync(enPath, 'utf8'));

// List of language codes
const languages = ['ar', 'de', 'es', 'fr', 'it', 'ru', 'tr', 'zh'];

// Flatten object paths
function flattenObject(obj, prefix = '') {
  return Object.keys(obj).reduce((acc, k) => {
    const pre = prefix.length ? `${prefix}.` : '';
    if (typeof obj[k] === 'object' && obj[k] !== null && !Array.isArray(obj[k])) {
      Object.assign(acc, flattenObject(obj[k], `${pre}${k}`));
    } else {
      acc[`${pre}${k}`] = obj[k];
    }
    return acc;
  }, {});
}

// Get keys in a flat object
function getKeys(obj) {
  return Object.keys(flattenObject(obj));
}

// English keys (reference)
const enKeys = getKeys(enData);

let allMissingKeys = {};

// Check each language for missing keys
languages.forEach(lang => {
  const langPath = join(__dirname, `src/locales/${lang}/itemListing.json`);
  let langData;
  
  try {
    langData = JSON.parse(readFileSync(langPath, 'utf8'));
  } catch (error) {
    console.error(`Error reading ${lang} translation file:`, error.message);
    return;
  }
  
  const langKeys = getKeys(langData);
  const missingKeys = enKeys.filter(key => !langKeys.includes(key));
  
  if (missingKeys.length > 0) {
    console.log(`\n${lang.toUpperCase()} is missing ${missingKeys.length} keys:`);
    console.log(missingKeys);
    allMissingKeys[lang] = missingKeys;
  } else {
    console.log(`\n${lang.toUpperCase()} has all required keys.`);
  }
});

// Save missing keys to a file for reference
if (Object.keys(allMissingKeys).length > 0) {
  writeFileSync(
    join(__dirname, 'missing_itemListing_keys.json'),
    JSON.stringify(allMissingKeys, null, 2),
    'utf8'
  );
  console.log('\nMissing keys have been saved to missing_itemListing_keys.json');
}