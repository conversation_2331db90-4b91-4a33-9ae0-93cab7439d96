{"name": "e-exportcity", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@chakra-ui/icons": "2.1.1", "@chakra-ui/react": "2.8.2", "@emotion/react": "11.11.3", "@emotion/styled": "11.11.0", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@reduxjs/toolkit": "^2.5.0", "@tinymce/tinymce-react": "^5.1.1", "@types/uuid": "^10.0.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.3", "framer-motion": "10.18.0", "i18next": "^24.2.1", "i18next-browser-languagedetector": "^8.0.2", "lodash.debounce": "^4.0.8", "lucide-react": "^0.468.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-feather": "^2.0.10", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.0", "react-icons": "^5.4.0", "react-quill": "^2.0.0", "react-redux": "^9.2.0", "react-router": "^7.1.3", "react-router-dom": "^7.1.3", "recharts": "^2.15.1", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "swiper": "^11.2.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.18.0", "@shadcn/ui": "^0.0.4", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.10.7", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@types/react-redux": "^7.1.34", "@types/react-router-dom": "^5.3.3", "@types/redux-persist": "^4.3.1", "@types/webpack-env": "^1.18.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.18.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "typescript-eslint": "^8.21.0", "vite": "^6.0.11"}, "pnpm": {"ignoredBuiltDependencies": ["esbuild"]}}