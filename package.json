{"name": "api", "version": "1.0.0", "main": "index.js", "scripts": {"start": "ts-node src/index.ts", "dev": "nodemon src/index.ts", "build": "tsc -p .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:renewal": "NODE_ENV=test ts-node src/scripts/testAutoRenewal.ts --test-mode", "seed": "node --max-old-space-size=4096 -r ts-node/register scripts/seed.ts", "seed:categories": "node --max-old-space-size=4096 -r ts-node/register scripts/seedCategories.ts", "seed:items": "node --max-old-space-size=4096 -r ts-node/register scripts/seedItemsAndUsers.ts", "seed:admin": "node --max-old-space-size=4096 -r ts-node/register scripts/seedAdmin.ts", "seed:user": "node --max-old-space-size=4096 -r ts-node/register scripts/seedUsers.ts", "seed:subscriptions": "ts-node scripts/seedSubscription.ts", "seed:stores": "ts-node scripts/seedStores.ts", "test:user-flow": "ts-node scripts/testUserFlow.ts", "test:email-validation": "ts-node scripts/testEmailValidation.ts", "test:subscription-flow": "ts-node scripts/testSubscriptionFlow.ts", "seed:design-packages": "ts-node scripts/seedDesignPackages.ts", "test:complete-flow": "ts-node scripts/testCompleteFlow.ts", "generate:encryption-key": "ts-node src/scripts/generateEncryptionKey.ts", "cards:check": "ts-node src/scripts/checkCardEncryption.ts", "cards:cleanup": "ts-node src/scripts/cleanupInvalidCards.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.729.0", "@types/iyzipay": "^2.0.3", "@types/node-cron": "^3.0.11", "@types/redis": "^4.0.11", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "bull": "^4.16.4", "compression": "^1.7.5", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csv-parse": "^5.6.0", "csv-parser": "^3.0.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "express": "^4.21.2", "iyzipay": "^2.0.63", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongoose": "^8.8.4", "morgan": "^1.10.0", "multer": "1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "pg": "^8.13.1", "redis": "^4.7.0", "soap": "^1.1.7", "socket.io": "^4.8.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.3", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/body-parser": "^1.19.5", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/cors": "^2.8.17", "@types/express": "4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.13", "@types/mongoose": "^5.11.97", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.10.2", "@types/nodemailer": "^6.4.17", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.7", "@types/uuid": "^10.0.0", "jest": "^29.7.0", "nodemon": "^3.1.7", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.7.2"}, "description": "", "pnpm": {"ignoredBuiltDependencies": ["bcrypt", "msgpackr-extract"]}}