{"ar": ["editItem", "placeholders.productName", "placeholders.serviceName", "placeholders.productDescription", "placeholders.serviceDescription", "placeholders.productRequestDescription", "placeholders.serviceRequestDescription", "successMessages.itemRequestUpdated.title", "successMessages.itemRequestUpdated.description", "successMessages.itemRequestCreated.title", "successMessages.itemRequestCreated.description", "errors.loadData.title", "errors.loadData.description", "errors.loadCategoryItems.title", "errors.loadCategoryItems.description", "errors.loadSubcategories.title", "errors.loadSubcategories.description", "errors.noCreateRequests.title", "errors.noCreateRequests.description", "errors.upgradeRequired.title", "errors.upgradeRequired.description", "errors.createFailed.title", "errors.createFailed.description", "errors.updateFailed.title", "errors.updateFailed.description", "errors.deleteFailed.title", "errors.deleteFailed.description", "errors.adCreateFailed.title", "errors.adCreateFailed.description", "errors.tooManyFiles.title", "errors.tooManyFiles.description", "errors.maxFiveFiles.title", "errors.maxFiveFiles.description", "errors.validation.title", "errors.validation.description", "errors.categoryRequired.title", "errors.categoryRequired.description", "errors.subcategoryLoad.title", "errors.subcategoryLoad.description", "errors.categoryLoad.title", "errors.categoryLoad.description", "errors.tryAgain.title", "errors.tryAgain.description", "errors.imageUpload.title", "errors.imageUpload.description", "errors.missingImage.title", "errors.missingImage.description", "errors.pleaseSelectImage.title", "errors.pleaseSelectImage.description", "errors.itemLoadFailed", "errors.itemSaveFailed", "success.itemCreated.title", "success.itemCreated.description", "success.itemUpdated.title", "success.itemUpdated.description", "success.itemDeleted.title", "success.itemDeleted.description", "success.adCreated.title", "success.adCreated.description", "fields.productName", "fields.serviceName", "fields.productDescription", "fields.serviceDescription", "fields.requestDetails"], "de": ["search.placeholder", "sort.newest", "sort.oldest", "sort.mostViewed", "sort.leastViewed", "filters.button", "clearFilters", "all", "location", "mode", "store", "placeholders.productName", "placeholders.serviceName", "placeholders.productDescription", "placeholders.serviceDescription", "placeholders.productRequestDescription", "placeholders.serviceRequestDescription", "successMessages.itemRequestUpdated.title", "successMessages.itemRequestUpdated.description", "successMessages.itemRequestCreated.title", "successMessages.itemRequestCreated.description", "buttons.viewDetails", "fields.productName", "fields.serviceName", "fields.productDescription", "fields.serviceDescription", "fields.requestDetails"], "es": ["search.placeholder", "sort.newest", "sort.oldest", "sort.mostViewed", "sort.leastViewed", "filters.button", "clearFilters", "all", "location", "mode", "store", "placeholders.productName", "placeholders.serviceName", "placeholders.productDescription", "placeholders.serviceDescription", "placeholders.productRequestDescription", "placeholders.serviceRequestDescription", "successMessages.itemRequestUpdated.title", "successMessages.itemRequestUpdated.description", "successMessages.itemRequestCreated.title", "successMessages.itemRequestCreated.description", "errors.loadData.title", "errors.loadData.description", "errors.loadCategoryItems.title", "errors.loadCategoryItems.description", "errors.loadSubcategories.title", "errors.loadSubcategories.description", "errors.noCreateRequests.title", "errors.noCreateRequests.description", "errors.upgradeRequired.title", "errors.upgradeRequired.description", "errors.createFailed.title", "errors.createFailed.description", "errors.updateFailed.title", "errors.updateFailed.description", "errors.deleteFailed.title", "errors.deleteFailed.description", "errors.adCreateFailed.title", "errors.adCreateFailed.description", "errors.tooManyFiles.title", "errors.tooManyFiles.description", "errors.maxFiveFiles.title", "errors.maxFiveFiles.description", "errors.validation.title", "errors.validation.description", "errors.categoryRequired.title", "errors.categoryRequired.description", "errors.subcategoryLoad.title", "errors.subcategoryLoad.description", "errors.categoryLoad.title", "errors.categoryLoad.description", "errors.tryAgain.title", "errors.tryAgain.description", "errors.imageUpload.title", "errors.imageUpload.description", "errors.missingImage.title", "errors.missingImage.description", "success.itemCreated.title", "success.itemCreated.description", "success.itemUpdated.title", "success.itemUpdated.description", "success.itemDeleted.title", "success.itemDeleted.description", "success.adCreated.title", "success.adCreated.description", "buttons.viewDetails", "fields.productName", "fields.serviceName", "fields.productDescription", "fields.serviceDescription", "fields.requestDetails"], "fr": ["search.placeholder", "sort.newest", "sort.oldest", "sort.mostViewed", "sort.leastViewed", "filters.button", "clearFilters", "all", "location", "mode", "store", "placeholders.productName", "placeholders.serviceName", "placeholders.productDescription", "placeholders.serviceDescription", "placeholders.productRequestDescription", "placeholders.serviceRequestDescription", "successMessages.itemRequestUpdated.title", "successMessages.itemRequestUpdated.description", "successMessages.itemRequestCreated.title", "successMessages.itemRequestCreated.description", "buttons.viewDetails", "fields.productName", "fields.serviceName", "fields.productDescription", "fields.serviceDescription", "fields.requestDetails"], "it": ["search.placeholder", "sort.newest", "sort.oldest", "sort.mostViewed", "sort.leastViewed", "filters.button", "clearFilters", "all", "location", "mode", "store", "placeholders.productName", "placeholders.serviceName", "placeholders.productDescription", "placeholders.serviceDescription", "placeholders.productRequestDescription", "placeholders.serviceRequestDescription", "successMessages.itemRequestUpdated.title", "successMessages.itemRequestUpdated.description", "successMessages.itemRequestCreated.title", "successMessages.itemRequestCreated.description", "errors.tryAgain.title", "errors.tryAgain.description", "errors.itemLoadFailed", "errors.itemSaveFailed", "buttons.viewDetails", "fields.productName", "fields.serviceName", "fields.productDescription", "fields.serviceDescription", "fields.requestDetails"], "ru": ["search.placeholder", "sort.newest", "sort.oldest", "sort.mostViewed", "sort.leastViewed", "filters.button", "clearFilters", "all", "location", "mode", "store", "placeholders.productName", "placeholders.serviceName", "placeholders.productDescription", "placeholders.serviceDescription", "placeholders.productRequestDescription", "placeholders.serviceRequestDescription", "successMessages.itemRequestUpdated.title", "successMessages.itemRequestUpdated.description", "successMessages.itemRequestCreated.title", "successMessages.itemRequestCreated.description", "errors.loadData.title", "errors.loadData.description", "errors.loadCategoryItems.title", "errors.loadCategoryItems.description", "errors.loadSubcategories.title", "errors.loadSubcategories.description", "errors.noCreateRequests.title", "errors.noCreateRequests.description", "errors.upgradeRequired.title", "errors.upgradeRequired.description", "errors.createFailed.title", "errors.createFailed.description", "errors.updateFailed.title", "errors.updateFailed.description", "errors.deleteFailed.title", "errors.deleteFailed.description", "errors.adCreateFailed.title", "errors.adCreateFailed.description", "errors.tooManyFiles.title", "errors.tooManyFiles.description", "errors.maxFiveFiles.title", "errors.maxFiveFiles.description", "errors.validation.title", "errors.validation.description", "errors.categoryRequired.title", "errors.categoryRequired.description", "errors.subcategoryLoad.title", "errors.subcategoryLoad.description", "errors.categoryLoad.title", "errors.categoryLoad.description", "errors.tryAgain.title", "errors.tryAgain.description", "errors.imageUpload.title", "errors.imageUpload.description", "errors.missingImage.title", "errors.missingImage.description", "errors.pleaseSelectImage.title", "errors.pleaseSelectImage.description", "errors.itemLoadFailed", "errors.itemSaveFailed", "success.itemCreated.title", "success.itemCreated.description", "success.itemUpdated.title", "success.itemUpdated.description", "success.itemDeleted.title", "success.itemDeleted.description", "success.adCreated.title", "success.adCreated.description", "buttons.viewDetails", "fields.productName", "fields.serviceName", "fields.productDescription", "fields.serviceDescription", "fields.requestDetails"], "tr": ["errorSavingItemRequest"], "zh": ["search.placeholder", "sort.newest", "sort.oldest", "sort.mostViewed", "sort.leastViewed", "filters.button", "clearFilters", "all", "location", "mode", "store", "placeholders.productName", "placeholders.serviceName", "placeholders.productDescription", "placeholders.serviceDescription", "placeholders.productRequestDescription", "placeholders.serviceRequestDescription", "successMessages.itemRequestUpdated.title", "successMessages.itemRequestUpdated.description", "successMessages.itemRequestCreated.title", "successMessages.itemRequestCreated.description", "errors.itemLoadFailed", "errors.itemSaveFailed", "buttons.viewDetails", "fields.productName", "fields.serviceName", "fields.productDescription", "fields.serviceDescription", "fields.requestDetails"]}