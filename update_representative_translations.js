import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Define the new translation keys
const newTranslations = {
  "title": "Title/Position",
  "title_placeholder": "E.g., Export Manager",
  "company": "Company",
  "company_placeholder": "E.g., Global Trade Ltd.",
  "experience": "Experience",
  "experience_placeholder": "E.g., 10 years",
  "region": "Region",
  "region_placeholder": "E.g., Europe, Middle East",
  "languages": "Languages",
  "languages_placeholder": "E.g., English",
  "expertise": "Expertise",
  "expertise_placeholder": "E.g., Market Analysis",
  "verified": "Verified Representative",
  "remove_picture": "Remove picture",
  "add_button": "Add",
  "remove_language": "Remove {{language}}",
  "remove_expertise": "Remove {{expertise}}",
  "password": "Password",
  "password_placeholder": "Enter password",
  "new_password": "New Password (optional)"
};

// Define language-specific translations
const languageSpecificTranslations = {
  'tr': {
    "title": "Unvan/Pozisyon",
    "title_placeholder": "Örn: İhracat Müdürü",
    "company": "Şirket",
    "company_placeholder": "Örn: Global Ticaret Ltd.",
    "experience": "Deneyim",
    "experience_placeholder": "Örn: 10 yıl",
    "region": "Bölge",
    "region_placeholder": "Örn: Avrupa, Orta Doğu",
    "languages": "Diller",
    "languages_placeholder": "Örn: İngilizce",
    "expertise": "Uzmanlık",
    "expertise_placeholder": "Örn: Pazar Analizi",
    "verified": "Onaylı Temsilci",
    "remove_picture": "Fotoğrafı kaldır",
    "add_button": "Ekle",
    "remove_language": "{{language}} dilini kaldır",
    "remove_expertise": "{{expertise}} uzmanlığını kaldır",
    "password": "Şifre",
    "password_placeholder": "Şifre girin",
    "new_password": "Yeni Şifre (isteğe bağlı)"
  },
  'ar': {
    "title": "المسمى الوظيفي",
    "title_placeholder": "مثال: مدير التصدير",
    "company": "الشركة",
    "company_placeholder": "مثال: شركة التجارة العالمية",
    "experience": "الخبرة",
    "experience_placeholder": "مثال: 10 سنوات",
    "region": "المنطقة",
    "region_placeholder": "مثال: أوروبا، الشرق الأوسط",
    "languages": "اللغات",
    "languages_placeholder": "مثال: الإنجليزية",
    "expertise": "التخصص",
    "expertise_placeholder": "مثال: تحليل السوق",
    "verified": "ممثل موثق",
    "remove_picture": "إزالة الصورة",
    "add_button": "إضافة",
    "remove_language": "إزالة {{language}}",
    "remove_expertise": "إزالة {{expertise}}",
    "password": "كلمة المرور",
    "password_placeholder": "أدخل كلمة المرور",
    "new_password": "كلمة المرور الجديدة (اختياري)"
  },
  'de': {
    "title": "Titel/Position",
    "title_placeholder": "Z.B. Exportmanager",
    "company": "Unternehmen",
    "company_placeholder": "Z.B. Global Trade GmbH",
    "experience": "Erfahrung",
    "experience_placeholder": "Z.B. 10 Jahre",
    "region": "Region",
    "region_placeholder": "Z.B. Europa, Naher Osten",
    "languages": "Sprachen",
    "languages_placeholder": "Z.B. Englisch",
    "expertise": "Fachkenntnisse",
    "expertise_placeholder": "Z.B. Marktanalyse",
    "verified": "Verifizierter Vertreter",
    "remove_picture": "Bild entfernen",
    "add_button": "Hinzufügen",
    "remove_language": "{{language}} entfernen",
    "remove_expertise": "{{expertise}} entfernen",
    "password": "Passwort",
    "password_placeholder": "Passwort eingeben",
    "new_password": "Neues Passwort (optional)"
  },
  'es': {
    "title": "Título/Posición",
    "title_placeholder": "Ej: Gerente de Exportación",
    "company": "Empresa",
    "company_placeholder": "Ej: Comercio Global S.L.",
    "experience": "Experiencia",
    "experience_placeholder": "Ej: 10 años",
    "region": "Región",
    "region_placeholder": "Ej: Europa, Oriente Medio",
    "languages": "Idiomas",
    "languages_placeholder": "Ej: Inglés",
    "expertise": "Especialización",
    "expertise_placeholder": "Ej: Análisis de mercado",
    "verified": "Representante Verificado",
    "remove_picture": "Eliminar imagen",
    "add_button": "Añadir",
    "remove_language": "Eliminar {{language}}",
    "remove_expertise": "Eliminar {{expertise}}",
    "password": "Contraseña",
    "password_placeholder": "Ingrese contraseña",
    "new_password": "Nueva Contraseña (opcional)"
  },
  'fr': {
    "title": "Titre/Poste",
    "title_placeholder": "Ex: Responsable Export",
    "company": "Entreprise",
    "company_placeholder": "Ex: Commerce Global SARL",
    "experience": "Expérience",
    "experience_placeholder": "Ex: 10 ans",
    "region": "Région",
    "region_placeholder": "Ex: Europe, Moyen-Orient",
    "languages": "Langues",
    "languages_placeholder": "Ex: Anglais",
    "expertise": "Expertise",
    "expertise_placeholder": "Ex: Analyse de marché",
    "verified": "Représentant Vérifié",
    "remove_picture": "Supprimer l'image",
    "add_button": "Ajouter",
    "remove_language": "Supprimer {{language}}",
    "remove_expertise": "Supprimer {{expertise}}",
    "password": "Mot de passe",
    "password_placeholder": "Entrez le mot de passe",
    "new_password": "Nouveau mot de passe (optionnel)"
  },
  'it': {
    "title": "Titolo/Posizione",
    "title_placeholder": "Es: Responsabile Export",
    "company": "Azienda",
    "company_placeholder": "Es: Commercio Globale S.r.l.",
    "experience": "Esperienza",
    "experience_placeholder": "Es: 10 anni",
    "region": "Regione",
    "region_placeholder": "Es: Europa, Medio Oriente",
    "languages": "Lingue",
    "languages_placeholder": "Es: Inglese",
    "expertise": "Competenze",
    "expertise_placeholder": "Es: Analisi di mercato",
    "verified": "Rappresentante Verificato",
    "remove_picture": "Rimuovi immagine",
    "add_button": "Aggiungi",
    "remove_language": "Rimuovi {{language}}",
    "remove_expertise": "Rimuovi {{expertise}}",
    "password": "Password",
    "password_placeholder": "Inserisci password",
    "new_password": "Nuova Password (opzionale)"
  },
  'ru': {
    "title": "Должность/Позиция",
    "title_placeholder": "Напр.: Менеджер по экспорту",
    "company": "Компания",
    "company_placeholder": "Напр.: Глобал Трейд ООО",
    "experience": "Опыт",
    "experience_placeholder": "Напр.: 10 лет",
    "region": "Регион",
    "region_placeholder": "Напр.: Европа, Ближний Восток",
    "languages": "Языки",
    "languages_placeholder": "Напр.: Английский",
    "expertise": "Специализация",
    "expertise_placeholder": "Напр.: Анализ рынка",
    "verified": "Проверенный представитель",
    "remove_picture": "Удалить изображение",
    "add_button": "Добавить",
    "remove_language": "Удалить {{language}}",
    "remove_expertise": "Удалить {{expertise}}",
    "password": "Пароль",
    "password_placeholder": "Введите пароль",
    "new_password": "Новый пароль (опционально)"
  },
  'zh': {
    "title": "职称/职位",
    "title_placeholder": "例如：出口经理",
    "company": "公司",
    "company_placeholder": "例如：全球贸易有限公司",
    "experience": "经验",
    "experience_placeholder": "例如：10年",
    "region": "地区",
    "region_placeholder": "例如：欧洲，中东",
    "languages": "语言",
    "languages_placeholder": "例如：英语",
    "expertise": "专长",
    "expertise_placeholder": "例如：市场分析",
    "verified": "已验证代表",
    "remove_picture": "删除图片",
    "add_button": "添加",
    "remove_language": "删除{{language}}",
    "remove_expertise": "删除{{expertise}}",
    "password": "密码",
    "password_placeholder": "输入密码",
    "new_password": "新密码（可选）"
  },
  'en': {
    "title": "Title/Position",
    "title_placeholder": "E.g., Export Manager",
    "company": "Company",
    "company_placeholder": "E.g., Global Trade Ltd.",
    "experience": "Experience",
    "experience_placeholder": "E.g., 10 years",
    "region": "Region",
    "region_placeholder": "E.g., Europe, Middle East",
    "languages": "Languages",
    "languages_placeholder": "E.g., English",
    "expertise": "Expertise",
    "expertise_placeholder": "E.g., Market Analysis",
    "verified": "Verified Representative",
    "remove_picture": "Remove picture",
    "add_button": "Add",
    "remove_language": "Remove {{language}}",
    "remove_expertise": "Remove {{expertise}}",
    "password": "Password",
    "password_placeholder": "Enter password",
    "new_password": "New Password (optional)"
  }
};

// Get all language folders
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const localesPath = path.join(__dirname, 'src', 'locales');
const languages = fs.readdirSync(localesPath).filter(lang => {
  // Filter only directory names and known language codes
  return fs.statSync(path.join(localesPath, lang)).isDirectory() &&
         Object.keys(languageSpecificTranslations).includes(lang);
});

console.log(`Found ${languages.length} language folders: ${languages.join(', ')}`);

// Process each language file
languages.forEach(lang => {
  const adminJsonPath = path.join(localesPath, lang, 'admin.json');
  
  if (!fs.existsSync(adminJsonPath)) {
    console.log(`No admin.json file found for language: ${lang}`);
    return;
  }
  
  try {
    // Read the current file content
    const adminJson = JSON.parse(fs.readFileSync(adminJsonPath, 'utf8'));
    
    // Check if representatives.form exists
    if (!adminJson.representatives || !adminJson.representatives.form) {
      console.log(`No representatives.form entry found in admin.json for language: ${lang}`);
      return;
    }
    
    // Add new translation keys
    const translationsToAdd = languageSpecificTranslations[lang] || newTranslations;
    
    // Merge the existing translations with the new ones
    adminJson.representatives.form = {
      ...adminJson.representatives.form,
      ...translationsToAdd
    };
    
    // Write the updated JSON back to the file
    fs.writeFileSync(
      adminJsonPath,
      JSON.stringify(adminJson, null, 2),
      'utf8'
    );
    
    console.log(`Updated admin.json for language: ${lang}`);
  } catch (error) {
    console.error(`Error updating admin.json for language ${lang}:`, error.message);
  }
});

console.log('All language files have been updated successfully.');