{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["react-dom"]}, "include": ["src", "types"]}