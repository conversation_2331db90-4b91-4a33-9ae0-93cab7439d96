// This file will run before all tests
// Silence console logs during tests
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Comment these lines if you want to see console output during tests
console.log = jest.fn();
console.error = jest.fn();
console.warn = jest.fn();

// Restore the original console methods after tests
afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});
