import axios from 'axios';
import readline from 'readline';
import { promisify } from 'util';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = promisify(rl.question).bind(rl);

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000';

interface UserData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phoneNumber: string;
  birthDate: string;
  address: string;
  city: string;
  country: string;
  zipCode: string;
  categoryLevel1Id?: string;
  categoryLevel2Id?: string;
  categoryLevel3Id?: string;
}

interface CardInfo {
  cardHolderName: string;
  cardNumber: string;
  expireMonth: string;
  expireYear: string;
  cvc: string;
}

let authToken: string = '';
let userEmail: string = '';

async function registerUser() {
  console.log('\n=== USER REGISTRATION ===\n');
  
  const userData: UserData = {
    firstName: await question('First Name: ') as string,
    lastName: await question('Last Name: ') as string,
    email: await question('Email: ') as string,
    password: await question('Password: ') as string,
    phoneNumber: await question('Phone Number: ') as string,
    birthDate: await question('Birth Date (YYYY-MM-DD): ') as string,
    address: await question('Address: ') as string,
    city: await question('City: ') as string,
    country: await question('Country: ') as string,
    zipCode: await question('Zip Code: ') as string,
  };

  userEmail = userData.email;

  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/register`, userData, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'tr'
      }
    });

    if (response.data.success) {
      console.log('\n✅ User registered successfully!');
      console.log('Token received:', response.data.token);
      authToken = response.data.token;
      return true;
    } else {
      console.log('\n❌ Registration failed:', response.data.message);
      return false;
    }
  } catch (error: any) {
    console.error('\n❌ Registration error:', error.response?.data?.message || error.message);
    return false;
  }
}

async function loginUser() {
  console.log('\n=== USER LOGIN ===\n');
  
  const email = await question('Email: ') as string;
  const password = await question('Password: ') as string;

  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email,
      password
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'tr'
      }
    });

    if (response.data.success) {
      console.log('\n✅ Login successful!');
      console.log('User:', response.data.user);
      authToken = response.data.token;
      userEmail = email;
      return true;
    } else {
      console.log('\n❌ Login failed:', response.data.message);
      return false;
    }
  } catch (error: any) {
    console.error('\n❌ Login error:', error.response?.data?.message || error.message);
    return false;
  }
}

async function getUserProfile() {
  console.log('\n=== FETCHING USER PROFILE ===\n');

  try {
    const response = await axios.get(`${API_BASE_URL}/api/users/profile`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Accept-Language': 'tr'
      }
    });

    if (response.data.success) {
      console.log('\n✅ User Profile:');
      console.log(JSON.stringify(response.data.data, null, 2));
      return response.data.data;
    }
  } catch (error: any) {
    console.error('\n❌ Error fetching profile:', error.response?.data?.message || error.message);
    return null;
  }
}

async function getPackages() {
  console.log('\n=== AVAILABLE PACKAGES ===\n');

  try {
    const response = await axios.get(`${API_BASE_URL}/api/packages`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Accept-Language': 'tr'
      }
    });

    const packages = response.data.packages;
    console.log('\nAvailable Packages:');
    packages.forEach((pkg: any, index: number) => {
      console.log(`\n${index + 1}. ${pkg.name} (${pkg.nameEn})`);
      console.log(`   Type: ${pkg.type}`);
      console.log(`   Price: ${pkg.price} ${pkg.currency || 'TRY'}`);
      console.log(`   View Requests: ${pkg.viewRequestLimit}`);
      console.log(`   Create Requests: ${pkg.createRequestLimit}`);
      console.log(`   Features: ${pkg.features.join(', ')}`);
    });

    return packages;
  } catch (error: any) {
    console.error('\n❌ Error fetching packages:', error.response?.data?.message || error.message);
    return [];
  }
}

async function purchasePackage() {
  console.log('\n=== PACKAGE PURCHASE ===\n');

  const packages = await getPackages();
  if (packages.length === 0) {
    console.log('No packages available');
    return false;
  }

  const packageIndex = parseInt(await question('\nSelect package number: ') as string) - 1;
  if (packageIndex < 0 || packageIndex >= packages.length) {
    console.log('Invalid package selection');
    return false;
  }

  const selectedPackage = packages[packageIndex];
  console.log(`\nSelected package: ${selectedPackage.name} - ${selectedPackage.price} ${selectedPackage.currency || 'TRY'}`);

  console.log('\n=== ENTER CARD INFORMATION ===\n');
  const cardInfo: CardInfo = {
    cardHolderName: await question('Card Holder Name: ') as string,
    cardNumber: await question('Card Number: ') as string,
    expireMonth: await question('Expire Month (MM): ') as string,
    expireYear: await question('Expire Year (YY): ') as string,
    cvc: await question('CVC: ') as string
  };

  const saveCard = (await question('Save card for future use? (y/n): ') as string).toLowerCase() === 'y';

  try {
    console.log('\n🔄 Processing payment...');
    
    const response = await axios.post(
      `${API_BASE_URL}/api/packages/${selectedPackage._id}/purchase`,
      {
        cardInfo,
        saveCard
      },
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
          'Accept-Language': 'tr'
        }
      }
    );

    if (response.data.paymentPageUrl) {
      console.log('\n✅ Payment initiated successfully!');
      console.log('3D Secure verification required.');
      console.log('Payment Page URL:', response.data.paymentPageUrl);
      console.log('\nPlease complete the payment in your browser and then press Enter to continue...');
      await question('');
      return true;
    } else if (response.data.success) {
      console.log('\n✅ Package purchased successfully!');
      console.log('Subscription ID:', response.data.subscriptionId);
      return true;
    } else {
      console.log('\n❌ Purchase failed:', response.data.message);
      return false;
    }
  } catch (error: any) {
    console.error('\n❌ Purchase error:', error.response?.data?.message || error.message);
    return false;
  }
}

async function checkEmails() {
  console.log('\n=== EMAIL NOTIFICATIONS ===\n');
  console.log(`Check your email (${userEmail}) for:`);
  console.log('1. Welcome email');
  console.log('2. Package purchase confirmation');
  console.log('3. Other notification emails');
  console.log('\nNote: Emails are sent asynchronously, they might take a moment to arrive.');
}

async function main() {
  console.log('=== E-EXPORT CITY USER FLOW TEST ===');
  console.log(`API Base URL: ${API_BASE_URL}`);

  try {
    const choice = await question('\nChoose an option:\n1. Register new user\n2. Login existing user\n\nYour choice (1 or 2): ');

    let success = false;
    if (choice === '1') {
      success = await registerUser();
    } else if (choice === '2') {
      success = await loginUser();
    } else {
      console.log('Invalid choice');
      process.exit(1);
    }

    if (!success) {
      console.log('\n❌ Authentication failed. Exiting...');
      process.exit(1);
    }

    // Get and display user profile
    await getUserProfile();

    // Ask if user wants to purchase a package
    const purchaseChoice = await question('\nDo you want to purchase a package? (y/n): ');
    if (purchaseChoice.toLowerCase() === 'y') {
      await purchasePackage();
      
      // Fetch updated profile to see subscription
      console.log('\n=== UPDATED PROFILE WITH SUBSCRIPTION ===');
      await getUserProfile();
    }

    // Check emails
    await checkEmails();

    console.log('\n=== TEST COMPLETED ===');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    rl.close();
  }
}

// Run the test
main();