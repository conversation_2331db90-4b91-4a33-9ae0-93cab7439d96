import mongoose from 'mongoose';
import { User } from '../src/models/User';
import { Store } from '../src/models/Store';
import connectDB from '../src/config/database';
import fs from 'fs';
import path from 'path';

connectDB();

async function seedUsersAndStores() {
  try {
    // Drop existing collections
    await User.deleteMany({ email: { $ne: '<EMAIL>' } });
    await Store.deleteMany({});
    
    // Ensure uploads directories exist
    const userUploadsDir = path.join(__dirname, '../uploads/users');
    if (!fs.existsSync(userUploadsDir)) {
      fs.mkdirSync(userUploadsDir, { recursive: true });
    }

    // Drop the existing index if it exists
    try {
      await mongoose.connection.collection('users').dropIndex('email_1');
    } catch (error) {
      // Ignore error if index doesn't exist
      console.log('No existing index to drop or already dropped');
    }

    const users = [
      {
        email: '<EMAIL>',
        password: 'Umut13579!',
        firstName: 'John',
        lastName: 'Doe',
        isEmailVerified: true,
        isActive: true,
        role: 'user',
        ipAddress: '***********',
        zipCode: '34000',
        country: 'Turkey',
        city: 'Istanbul',
        address: '123 Main St',
        birthDate: new Date('1990-01-01'),
        phoneNumber: '+905551234567'
      },
      {
        email: '<EMAIL>',
        password: 'Umut13579!',
        firstName: 'Jane',
        lastName: 'Smith',
        isEmailVerified: true,
        isActive: true,
        role: 'user',
        ipAddress: '***********',
        zipCode: '34001',
        country: 'Turkey',
        city: 'Ankara',
        address: '456 Elm St',
        birthDate: new Date('1991-02-02'),
        phoneNumber: '+905552345678'
      },
      {
        email: '<EMAIL>',
        password: 'Umut13579!',
        firstName: 'Bob',
        lastName: 'Johnson',
        isEmailVerified: true,
        isActive: true,
        role: 'user',
        ipAddress: '***********',
        zipCode: '34002',
        country: 'Turkey',
        city: 'Izmir',
        address: '789 Oak St',
        birthDate: new Date('1992-03-03'),
        phoneNumber: '+905553456789'
      }
    ];

    for (const userData of users) {
      // Create user profile image
      const userId = new mongoose.Types.ObjectId();
      const profileImagePath = `/uploads/users/${userId.toString()}/profile.jpg`;
      
      // Create directory for user
      const userDir = path.join(__dirname, '..', 'uploads/users', userId.toString());
      if (!fs.existsSync(userDir)) {
        fs.mkdirSync(userDir, { recursive: true });
      }
      
      // Create empty profile image file
      fs.writeFileSync(path.join(__dirname, '..', profileImagePath), '');
      
      const user = new User({
        _id: userId,
        ...userData,
        profileImage: profileImagePath,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await user.save();

      for (let j = 0; j < 1; j++) {
        const store = new Store({
          name: `Store ${j + 1} of ${user.firstName}`,
          description: `This is store ${j + 1} owned by ${user.firstName} ${user.lastName}`,
          owner: user._id,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        await store.save();
      }

      console.log(`Created user ${user.email} with 1 store`);
    }

    console.log('Seeding completed successfully');
  } catch (error) {
    console.error('Error seeding data:', error);
  } finally {
    mongoose.connection.close();
  }
}

seedUsersAndStores();
