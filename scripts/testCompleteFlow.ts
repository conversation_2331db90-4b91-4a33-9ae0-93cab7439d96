import axios from 'axios';

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000';

interface TestUser {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phoneNumber: string;
  birthDate: string;
  address: string;
  city: string;
  country: string;
  zipCode: string;
}

interface TestResults {
  registration: boolean;
  login: boolean;
  profileFetch: boolean;
  packageList: boolean;
  packagePurchase: boolean;
  emailsSent: string[];
}

class E2ETestRunner {
  private authToken: string = '';
  private testUser: TestUser;
  private results: TestResults = {
    registration: false,
    login: false,
    profileFetch: false,
    packageList: false,
    packagePurchase: false,
    emailsSent: []
  };

  constructor() {
    // Generate random test user data
    const timestamp = Date.now();
    const randomNum = Math.floor(Math.random() * 1000);
    
    this.testUser = {
      firstName: `Test${randomNum}`,
      lastName: `User${timestamp}`,
      email: `test_${timestamp}_${randomNum}@example.com`,
      password: 'Test123!@#',
      phoneNumber: `05${Math.floor(Math.random() * 900000000 + 100000000)}`,
      birthDate: '1990-01-01',
      address: `Test Street ${randomNum}`,
      city: 'Istanbul',
      country: 'Turkey',
      zipCode: '34000'
    };
  }

  async run() {
    console.log('🚀 E-Export City Complete Flow Test Starting...\n');
    console.log('Test User:', {
      name: `${this.testUser.firstName} ${this.testUser.lastName}`,
      email: this.testUser.email
    });

    await this.testRegistration();
    await this.delay(2000);

    await this.testLogin();
    await this.delay(1000);

    await this.testGetProfile();
    await this.delay(1000);

    await this.testGetPackages();
    await this.delay(1000);

    await this.testPurchasePackage();
    await this.delay(2000);

    await this.testGetUpdatedProfile();

    this.printResults();
  }

  private async testRegistration() {
    console.log('\n📝 Testing User Registration...');
    try {
      const response = await axios.post(`${API_BASE_URL}/api/auth/register`, this.testUser, {
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': 'tr'
        }
      });

      if (response.data.success) {
        this.results.registration = true;
        this.authToken = response.data.token;
        console.log('✅ Registration successful!');
        console.log('   Token received:', this.authToken.substring(0, 20) + '...');
        this.results.emailsSent.push('Welcome Email');
      } else {
        console.log('❌ Registration failed:', response.data.message);
      }
    } catch (error: any) {
      console.error('❌ Registration error:', error.response?.data?.message || error.message);
    }
  }

  private async testLogin() {
    console.log('\n🔐 Testing User Login...');
    try {
      const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
        email: this.testUser.email,
        password: this.testUser.password
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': 'tr'
        }
      });

      if (response.data.success) {
        this.results.login = true;
        this.authToken = response.data.token;
        console.log('✅ Login successful!');
        console.log('   User ID:', response.data.user._id);
      } else {
        console.log('❌ Login failed:', response.data.message);
      }
    } catch (error: any) {
      console.error('❌ Login error:', error.response?.data?.message || error.message);
    }
  }

  private async testGetProfile() {
    console.log('\n👤 Testing Get User Profile...');
    try {
      const response = await axios.get(`${API_BASE_URL}/api/users/profile`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Accept-Language': 'tr'
        }
      });

      if (response.data.success) {
        this.results.profileFetch = true;
        console.log('✅ Profile fetched successfully!');
        console.log('   Has Package:', response.data.data.hasPackage);
        console.log('   Subscription:', response.data.data.subscriptionDetails ? 'Active' : 'None');
      }
    } catch (error: any) {
      console.error('❌ Profile fetch error:', error.response?.data?.message || error.message);
    }
  }

  private async testGetPackages() {
    console.log('\n📦 Testing Get Packages...');
    try {
      const response = await axios.get(`${API_BASE_URL}/api/packages`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Accept-Language': 'tr'
        }
      });

      const packages = response.data.packages;
      this.results.packageList = true;
      console.log('✅ Packages fetched successfully!');
      console.log(`   Total packages: ${packages.length}`);
      console.log('   Package types:', [...new Set(packages.map((p: any) => p.type))].join(', '));
    } catch (error: any) {
      console.error('❌ Package fetch error:', error.response?.data?.message || error.message);
    }
  }

  private async testPurchasePackage() {
    console.log('\n💳 Testing Package Purchase...');
    console.log('   Note: Using test card for 3D Secure simulation');
    
    try {
      // First get packages to find a starter package
      const packagesResponse = await axios.get(`${API_BASE_URL}/api/packages`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Accept-Language': 'tr'
        }
      });

      const starterPackage = packagesResponse.data.packages.find((p: any) => 
        p.type === 'standard' && p.name.toLowerCase().includes('starter')
      );

      if (!starterPackage) {
        console.log('❌ No starter package found');
        return;
      }

      console.log(`   Selected package: ${starterPackage.name} - ${starterPackage.price} ${starterPackage.currency}`);

      // Test card info for Iyzico sandbox
      const testCardInfo = {
        cardHolderName: 'Test User',
        cardNumber: '****************',  // Iyzico test card
        expireMonth: '12',
        expireYear: '30',
        cvc: '123'
      };

      const response = await axios.post(
        `${API_BASE_URL}/api/packages/${starterPackage._id}/purchase`,
        {
          cardInfo: testCardInfo,
          saveCard: false
        },
        {
          headers: {
            'Authorization': `Bearer ${this.authToken}`,
            'Content-Type': 'application/json',
            'Accept-Language': 'tr'
          }
        }
      );

      if (response.data.paymentPageUrl) {
        this.results.packagePurchase = true;
        console.log('✅ Payment initiated successfully!');
        console.log('   3D Secure URL:', response.data.paymentPageUrl);
        console.log('   Status: Waiting for 3D Secure completion');
        this.results.emailsSent.push('Package Purchase Confirmation (pending)');
      } else if (response.data.success) {
        this.results.packagePurchase = true;
        console.log('✅ Package purchased successfully!');
        this.results.emailsSent.push('Package Purchase Confirmation');
      }
    } catch (error: any) {
      console.error('❌ Purchase error:', error.response?.data?.message || error.message);
    }
  }

  private async testGetUpdatedProfile() {
    console.log('\n👤 Testing Get Updated Profile...');
    try {
      const response = await axios.get(`${API_BASE_URL}/api/users/profile`, {
        headers: {
          'Authorization': `Bearer ${this.authToken}`,
          'Accept-Language': 'tr'
        }
      });

      if (response.data.success) {
        console.log('✅ Updated profile fetched!');
        const subscription = response.data.data.subscriptionDetails;
        if (subscription?.standard) {
          console.log('   Subscription Status:', subscription.standard.status);
          console.log('   Package:', subscription.standard.packageId.name);
          console.log('   View Requests:', subscription.standard.remainingViewRequests);
          console.log('   Create Requests:', subscription.standard.remainingCreateRequests);
        }
      }
    } catch (error: any) {
      console.error('❌ Profile fetch error:', error.response?.data?.message || error.message);
    }
  }

  private printResults() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(50));
    console.log(`✅ Registration: ${this.results.registration ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Login: ${this.results.login ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Profile Fetch: ${this.results.profileFetch ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Package List: ${this.results.packageList ? 'PASSED' : 'FAILED'}`);
    console.log(`✅ Package Purchase: ${this.results.packagePurchase ? 'PASSED' : 'FAILED'}`);
    
    console.log('\n📧 Expected Emails:');
    this.results.emailsSent.forEach(email => {
      console.log(`   - ${email}`);
    });

    console.log('\n📌 Test User Credentials:');
    console.log(`   Email: ${this.testUser.email}`);
    console.log(`   Password: ${this.testUser.password}`);

    const passedTests = Object.values(this.results).filter(r => r === true).length;
    const totalTests = 5;
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run the test
const runner = new E2ETestRunner();
runner.run().catch(console.error);