import axios from 'axios';
import readline from 'readline';
import { promisify } from 'util';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = promisify(rl.question).bind(rl);

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000';

interface SubscriptionInfo {
  standard?: {
    _id: string;
    packageId: {
      _id: string;
      name: string;
      type: string;
      viewRequestLimit: number;
      createRequestLimit: number;
      price: number;
      currency: string;
    };
    startDate: string;
    endDate: string;
    status: string;
    remainingViewRequests: number;
    remainingCreateRequests: number;
  };
  addons?: Array<{
    _id: string;
    packageId: {
      _id: string;
      name: string;
      type: string;
      viewRequestLimit: number;
      createRequestLimit: number;
      price: number;
      currency: string;
    };
    startDate: string;
    endDate: string;
    status: string;
  }>;
}

let authToken: string = '';

async function login() {
  console.log('\n=== LOGIN ===\n');
  
  const email = await question('Email: ') as string;
  const password = await question('Password: ') as string;

  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/login`, {
      email,
      password
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'tr'
      }
    });

    if (response.data.success) {
      console.log('\n✅ Login successful!');
      authToken = response.data.token;
      return true;
    } else {
      console.log('\n❌ Login failed:', response.data.message);
      return false;
    }
  } catch (error: any) {
    console.error('\n❌ Login error:', error.response?.data?.message || error.message);
    return false;
  }
}

async function getSubscriptionInfo(): Promise<SubscriptionInfo | null> {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/users/profile`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Accept-Language': 'tr'
      }
    });

    if (response.data.success && response.data.data.subscriptionDetails) {
      return response.data.data.subscriptionDetails;
    }
    return null;
  } catch (error: any) {
    console.error('Error fetching subscription:', error.response?.data?.message || error.message);
    return null;
  }
}

async function displaySubscriptionInfo() {
  console.log('\n=== CURRENT SUBSCRIPTION ===\n');
  
  const subscription = await getSubscriptionInfo();
  
  if (!subscription || !subscription.standard) {
    console.log('No active subscription found.');
    return;
  }

  const standard = subscription.standard;
  console.log('Standard Package:');
  console.log(`  Name: ${standard.packageId.name}`);
  console.log(`  Status: ${standard.status}`);
  console.log(`  Start Date: ${new Date(standard.startDate).toLocaleDateString()}`);
  console.log(`  End Date: ${new Date(standard.endDate).toLocaleDateString()}`);
  console.log(`  Remaining View Requests: ${standard.remainingViewRequests}`);
  console.log(`  Remaining Create Requests: ${standard.remainingCreateRequests}`);

  if (subscription.addons && subscription.addons.length > 0) {
    console.log('\nAddon Packages:');
    subscription.addons.forEach((addon, index) => {
      console.log(`\n  Addon ${index + 1}:`);
      console.log(`    Name: ${addon.packageId.name}`);
      console.log(`    Status: ${addon.status}`);
      console.log(`    View Requests: +${addon.packageId.viewRequestLimit}`);
      console.log(`    Create Requests: +${addon.packageId.createRequestLimit}`);
    });
  }
}

async function getAvailablePackages() {
  try {
    const response = await axios.get(`${API_BASE_URL}/api/packages`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Accept-Language': 'tr'
      }
    });

    return response.data;
  } catch (error: any) {
    console.error('Error fetching packages:', error.response?.data?.message || error.message);
    return null;
  }
}

async function purchasePackage(packageId: string, saveCard: boolean = false) {
  console.log('\n=== PURCHASE PACKAGE ===\n');

  const cardInfo = {
    cardHolderName: await question('Card Holder Name: ') as string,
    cardNumber: await question('Card Number: ') as string,
    expireMonth: await question('Expire Month (MM): ') as string,
    expireYear: await question('Expire Year (YY): ') as string,
    cvc: await question('CVC: ') as string
  };

  try {
    console.log('\n🔄 Processing payment...');
    
    const response = await axios.post(
      `${API_BASE_URL}/api/packages/${packageId}/purchase`,
      {
        cardInfo,
        saveCard
      },
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
          'Accept-Language': 'tr'
        }
      }
    );

    if (response.data.paymentPageUrl) {
      console.log('\n✅ Payment initiated!');
      console.log('3D Secure URL:', response.data.paymentPageUrl);
      console.log('\nComplete payment in browser, then press Enter...');
      await question('');
      return true;
    } else if (response.data.success) {
      console.log('\n✅ Package purchased successfully!');
      return true;
    }
  } catch (error: any) {
    console.error('\n❌ Purchase error:', error.response?.data?.message || error.message);
    return false;
  }
}

async function cancelSubscription() {
  console.log('\n=== CANCEL SUBSCRIPTION ===\n');
  
  const confirm = await question('Are you sure you want to cancel your subscription? (yes/no): ');
  if (confirm.toLowerCase() !== 'yes') {
    console.log('Cancellation aborted.');
    return;
  }

  try {
    const response = await axios.post(`${API_BASE_URL}/api/subscriptions/cancel`, {}, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Accept-Language': 'tr'
      }
    });

    console.log('\n✅ Subscription cancelled successfully!');
    console.log('You should receive a cancellation confirmation email.');
  } catch (error: any) {
    console.error('\n❌ Cancellation error:', error.response?.data?.message || error.message);
  }
}

async function renewSubscription() {
  console.log('\n=== RENEW SUBSCRIPTION ===\n');
  
  const data = await getAvailablePackages();
  if (!data) return;

  const standardPackages = data.packages.filter((p: any) => p.type === 'standard');
  
  console.log('Available packages for renewal:');
  standardPackages.forEach((pkg: any, index: number) => {
    console.log(`${index + 1}. ${pkg.name} - ${pkg.price} ${pkg.currency}`);
  });

  const choice = parseInt(await question('\nSelect package number: ') as string) - 1;
  if (choice < 0 || choice >= standardPackages.length) {
    console.log('Invalid selection');
    return;
  }

  try {
    const response = await axios.post(`${API_BASE_URL}/api/subscriptions/renew`, {
      packageId: standardPackages[choice]._id
    }, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
        'Accept-Language': 'tr'
      }
    });

    if (response.data.paymentPageUrl) {
      console.log('\n✅ Renewal initiated!');
      console.log('3D Secure URL:', response.data.paymentPageUrl);
      console.log('\nComplete payment in browser to renew subscription.');
    }
  } catch (error: any) {
    console.error('\n❌ Renewal error:', error.response?.data?.message || error.message);
  }
}

async function testRequestLimits() {
  console.log('\n=== TEST REQUEST LIMITS ===\n');

  try {
    // Check view request
    const viewCheck = await axios.get(`${API_BASE_URL}/api/subscriptions/check-view-request`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Accept-Language': 'tr'
      }
    });
    console.log('View request available:', viewCheck.data.canView);
    console.log('Remaining view requests:', viewCheck.data.remainingRequests);

    // Check create request
    const createCheck = await axios.get(`${API_BASE_URL}/api/subscriptions/check-create-request`, {
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Accept-Language': 'tr'
      }
    });
    console.log('\nCreate request available:', createCheck.data.canCreate);
    console.log('Remaining create requests:', createCheck.data.remainingRequests);

    // Use a view request
    const useView = await question('\nUse a view request? (y/n): ');
    if (useView.toLowerCase() === 'y') {
      const useResponse = await axios.post(`${API_BASE_URL}/api/subscriptions/use-view-request`, {}, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Accept-Language': 'tr'
        }
      });
      console.log('View request used. Remaining:', useResponse.data.remainingRequests);
    }

  } catch (error: any) {
    console.error('Error testing limits:', error.response?.data?.message || error.message);
  }
}

async function main() {
  console.log('=== E-EXPORT CITY SUBSCRIPTION FLOW TEST ===');
  console.log(`API Base URL: ${API_BASE_URL}`);

  if (!await login()) {
    console.log('Login failed. Exiting...');
    process.exit(1);
  }

  let running = true;
  while (running) {
    console.log('\n=== MENU ===');
    console.log('1. View current subscription');
    console.log('2. Purchase new package');
    console.log('3. Purchase addon package');
    console.log('4. Cancel subscription');
    console.log('5. Renew subscription');
    console.log('6. Test request limits');
    console.log('7. Exit');

    const choice = await question('\nYour choice: ');

    switch (choice) {
      case '1':
        await displaySubscriptionInfo();
        break;

      case '2':
        const data = await getAvailablePackages();
        if (data) {
          const standardPackages = data.packages.filter((p: any) => p.type === 'standard');
          console.log('\nStandard Packages:');
          standardPackages.forEach((pkg: any, index: number) => {
            console.log(`${index + 1}. ${pkg.name} - ${pkg.price} ${pkg.currency}`);
          });
          const pkgChoice = parseInt(await question('\nSelect package: ') as string) - 1;
          if (pkgChoice >= 0 && pkgChoice < standardPackages.length) {
            await purchasePackage(standardPackages[pkgChoice]._id);
          }
        }
        break;

      case '3':
        const addonData = await getAvailablePackages();
        if (addonData) {
          const addonPackages = addonData.packages.filter((p: any) => p.type === 'addon');
          console.log('\nAddon Packages:');
          addonPackages.forEach((pkg: any, index: number) => {
            console.log(`${index + 1}. ${pkg.name} - ${pkg.price} ${pkg.currency}`);
            console.log(`   +${pkg.viewRequestLimit} view requests, +${pkg.createRequestLimit} create requests`);
          });
          const addonChoice = parseInt(await question('\nSelect addon: ') as string) - 1;
          if (addonChoice >= 0 && addonChoice < addonPackages.length) {
            await purchasePackage(addonPackages[addonChoice]._id);
          }
        }
        break;

      case '4':
        await cancelSubscription();
        break;

      case '5':
        await renewSubscription();
        break;

      case '6':
        await testRequestLimits();
        break;

      case '7':
        running = false;
        break;

      default:
        console.log('Invalid choice');
    }
  }

  console.log('\n=== TEST COMPLETED ===');
  rl.close();
}

// Run the test
main();