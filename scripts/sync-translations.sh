#!/bin/bash

# Colors for output
RED="\033[0;31m"
GREEN="\033[0;32m"
YELLOW="\033[1;33m"
NC="\033[0m"

# Source and target languages
SOURCE_LANG="en"
TARGET_LANGS=("tr" "ar" "ru" "fr" "es" "de" "it" "zh")

# Directory containing locale files
LOCALE_DIR="src/locales"

# Function to copy missing files
copy_missing_files() {
    local lang=$1
    echo -e "${YELLOW}Checking $lang translations...${NC}"

    for file in "$LOCALE_DIR/$SOURCE_LANG"/*.json; do
        base=$(basename "$file")
        target="$LOCALE_DIR/$lang/$base"

        if [ ! -f "$target" ]; then
            echo -e "${RED}Missing: $base in $lang${NC}"
            cp "$file" "$target"
            echo -e "${GREEN}Created: $target${NC}"
        fi
    done
}

# Process each target language
for lang in "${TARGET_LANGS[@]}"; do
    copy_missing_files "$lang"
done

echo -e "${GREEN}Done!${NC}"