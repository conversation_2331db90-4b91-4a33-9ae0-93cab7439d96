import fs from 'fs';
import csv from 'csv-parser';
import mongoose from 'mongoose';
import { Category, ICategory } from '../src/models/Category';
import connectDB from '../src/config/database';

// Connect to the database
connectDB();

async function populateCategories() {
  try {
    // Clear existing categories
    await Category.deleteMany({});

    // Read categories from CSV
    const rawCategoriesData = await readCSV('./data/sectors_categories.csv');

    const processedCategories = rawCategoriesData.map(row => {
      const level = parseInt(row.level, 10);
      if (!row.id || !row.name || !row.nameEn || !row.type || isNaN(level)) {
        console.warn(`Skipping invalid category row: id=${row.id}, name=${row.name}, type=${row.type}, level=${row.level}`);
        return null;
      }
      if (row.type !== 'Service' && row.type !== 'Product') {
        console.warn(`Skipping category row with invalid type: id=${row.id}, name=${row.name}, type=${row.type}`);
        return null;
      }
      return {
        id: row.id,
        name: row.name, // Assuming CSV 'name' maps to Turkish 'name' in model
        nameEn: row.nameEn,
        parent_id: row.parent_id || null, // Ensure null if empty
        level: level,
        type: row.type as 'Service' | 'Product',
        // Other language fields like nameTr, nameAr etc. from CSV will be ignored by Mongoose
        // as they are not in the CategorySchema unless added.
      };
    }).filter(category => category !== null) as ICategory[];


    // Insert categories into the database
    if (processedCategories.length > 0) {
      await Category.insertMany(processedCategories);
      console.log(`Inserted ${processedCategories.length} categories.`);
    } else {
      console.log('No valid categories found to insert.');
    }

  } catch (error:any) {
    console.error('Error populating categories:', error);
  } finally {
    mongoose.connection.close();
  }
}

function readCSV(filePath: string): Promise<any[]> {
  return new Promise((resolve, reject) => {
    const results: any[] = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error:any) => reject(error));
  });
}

populateCategories();
