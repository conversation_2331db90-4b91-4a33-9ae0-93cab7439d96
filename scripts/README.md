# E-Export City API Test Scripts

This directory contains test scripts to validate the complete user flow of the E-Export City platform, including user registration, email validation, and package purchases.

## Available Test Scripts

### 1. Complete Flow Test (`testCompleteFlow.ts`)
Automatically runs through the entire user journey:
- User registration
- Login
- Profile fetching  
- Package listing
- Package purchase with test credit card
- Email notification verification

**Run:**
```bash
npm run test:complete-flow
```

### 2. Interactive User Flow Test (`testUserFlow.ts`)
Interactive terminal-based test that prompts for user input:
- Register new user or login existing user
- View user profile
- Browse available packages
- Purchase a package with manual card entry
- Check email notifications

**Run:**
```bash
npm run test:user-flow
```

### 3. Email Validation Test (`testEmailValidation.ts`)
Tests the password reset/email validation flow:
- Request password reset (sends validation email)
- Reset password with token from email

**Run:**
```bash
npm run test:email-validation
```

### 4. Subscription Management Test (`testSubscriptionFlow.ts`)
Comprehensive subscription testing with menu-driven interface:
- View current subscription details
- Purchase standard packages
- Add addon packages
- Cancel subscription
- Renew subscription
- Test request limits (view/create)

**Run:**
```bash
npm run test:subscription-flow
```

## Environment Configuration

Set the API base URL before running tests:

```bash
# For local testing
export API_BASE_URL=http://localhost:5000

# For production testing
export API_BASE_URL=https://api.e-exportcity.com
```

## Test Credit Cards (Iyzico Sandbox)

For testing payments, use these Iyzico test cards:

- **Successful 3D Payment:** ****************
- **Non-3D Payment:** 5890040000000016
- **Debit Card:** ****************

All test cards use:
- Expiry: 12/30
- CVC: 123

## Email Notifications

The following emails are sent during the user flow:

1. **Welcome Email** - Sent after user registration
2. **Email Validation** - Sent for password reset
3. **Package Purchase Confirmation** - Sent after successful payment
4. **Package Cancellation Confirmation** - Sent when subscription is cancelled
5. **Password Change Success** - Sent after password reset

## Notes

- All scripts support Turkish (`tr`) and English (`en`) languages via the `Accept-Language` header
- Email notifications are sent asynchronously and may take a few seconds to arrive
- The complete flow test uses randomly generated user data to avoid conflicts
- For 3D Secure payments, you'll need to complete the verification in a browser

## Troubleshooting

If you encounter issues:

1. Ensure the API server is running
2. Check that the API_BASE_URL is correct
3. Verify email service is configured properly
4. For payment issues, ensure Iyzico is in sandbox mode for testing