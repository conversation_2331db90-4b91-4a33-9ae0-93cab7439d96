import mongoose from 'mongoose';
import { Package } from '../src/models/Package';
import { Subscription } from '../src/models/Subscription';
import { Payment } from '../src/models/Payment';
import dotenv from 'dotenv';
import { User } from '../src/models/User';

dotenv.config();

const packages = [
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Başlangıç Paketi",
    nameEn: "Starter Package",
    description: "Yeni kullanıcılar icin baslangıc paketi",
    descriptionEn: "Starter package for new users",
    price: 99.00,
    type: "standard",
    viewRequestLimit: 50,
    createRequestLimit: 5,
    emailNotification: true,
    smsNotification: false,
    languageIntroRights: 5,
    messagingAllowed: false,
    homepageAd: false,
    yearEndSectorReport: false,
    isActive: true,
    features: ["email_notifications", "language_intro"],
    maxMessages: 100,
    duration: 30,
    order: 1
  },
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Profesyonel Paket",
    nameEn: "Professional Package",
    description: "Profesyonel paket gelişmis kullanıcılar icin",
    descriptionEn: "Professional package for advanced users",
    price: 199.00,
    type: "standard",
    viewRequestLimit: 200,
    createRequestLimit: 20,
    emailNotification: true,
    smsNotification: false,
    languageIntroRights: 5,
    messagingAllowed: true,
    homepageAd: false,
    yearEndSectorReport: false,
    isActive: true,
    features: ["basic_messaging", "email_notifications", "language_intro", "homepage_ad"],
    maxMessages: 500,
    duration: 30,
    order: 2
  },
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Premium Paket",
    nameEn: "Premium Package",
    description: "Premium paket, tüm ozellikler dahildir",
    descriptionEn: "Premium package with all features",
    price: 399.00,
    type: "standard",
    viewRequestLimit: 500,
    createRequestLimit: 50,
    emailNotification: true,
    smsNotification: true,
    languageIntroRights: 5,
    messagingAllowed: true,
    homepageAd: true,
    yearEndSectorReport: true,
    isActive: true,
    features: ["unlimited_messaging", "sms_notifications", "email_notifications", "homepage_ad", "sector_report", "language_intro"],
    maxMessages: 1000,
    duration: 30,
    order: 3
  },
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Ek Paket 1",
    nameEn: "Extra Package 1",
    description: "Ek paket 1, ek görüntüleme isteği",
    descriptionEn: "Extra package 1, additional view request",
    price: 10.00,
    type: "addon",
    viewRequestLimit: 10,
    createRequestLimit: 0,
    emailNotification: false,
    smsNotification: false,
    languageIntroRights: 0,
    messagingAllowed: false,
    homepageAd: false,
    yearEndSectorReport: false,
    isActive: true,
    features: ["additional_views"],
    maxMessages: 0,
    duration: 30,
    order: 4
  },
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Ek Paket 2",
    nameEn: "Extra Package 2",
    description: "Ek paket 2, ek oluşturma isteği",
    descriptionEn: "Extra package 2, additional create request",
    price: 10.00,
    type: "addon",
    viewRequestLimit: 0,
    createRequestLimit: 10,
    emailNotification: false,
    smsNotification: false,
    languageIntroRights: 0,
    messagingAllowed: false,
    homepageAd: false,
    yearEndSectorReport: false,
    isActive: true,
    features: ["additional_creates"],
    maxMessages: 0,
    duration: 30,
    order: 5
  },
  {
    _id: new mongoose.Types.ObjectId(),
    name: "Ek Paket 3",
    nameEn: "Extra Package 3",
    description: "Ek paket 3, ek anasayfa reklam paketi",
    descriptionEn: "Extra package 3, additional homepage ad",
    price: 250.00,
    type: "addon",
    viewRequestLimit: 0,
    createRequestLimit: 0,
    emailNotification: false,
    smsNotification: false,
    languageIntroRights: 0,
    messagingAllowed: false,
    homepageAd: true,
    yearEndSectorReport: false,
    isActive: true,
    features: ["homepage_ad"],
    maxMessages: 0,
    duration: 30,
    order: 6
  }
];

const getFutureDate = (days: number): Date => {
  const date = new Date();
  date.setDate(date.getDate() + days);
  return date;
};

const getRecentPastDate = (): Date => {
  const date = new Date();
  const daysAgo = Math.floor(Math.random() * 30);
  date.setDate(date.getDate() - daysAgo);
  return date;
};

const seedDatabase = async () => {
  try {
    const mongoUri = process.env.MONGO_URI;
    if (!mongoUri) {
      throw new Error('MONGO_URI is not defined in environment variables');
    }

    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    await Promise.all([
      Package.deleteMany({}),
      Subscription.deleteMany({}),
      Payment.deleteMany({})
    ]);
    console.log('Cleared existing data');

    const createdPackages = await Package.create(packages);
    console.log('Seeded packages:', createdPackages.length);

    const users = await User.find().limit(3);
    const sampleUserIds = users.map(user => user._id);

    const subscriptions = [
      {
        userId: sampleUserIds[0],
        packageId: createdPackages[0]._id,
        startDate: getRecentPastDate(),
        endDate: getFutureDate(30),
        remainingViewRequests: 48,
        remainingCreateRequests: 3,
        isActive: true,
        status: 'ACTIVE',
        paymentStatus: 'paid',
        renewalDate: getFutureDate(30),
        addons: [],
        usageHistory: {
          viewRequestsUsed: 2,
          createRequestsUsed: 2
        },
        features: {
          emailNotification: true,
          smsNotification: false,
          messagingAllowed: false,
          homepageAd: false
        }
      },
      {
        userId: sampleUserIds[1],
        packageId: createdPackages[2]._id,
        startDate: getRecentPastDate(),
        endDate: getFutureDate(30),
        remainingViewRequests: 480,
        remainingCreateRequests: 35,
        isActive: true,
        status: 'ACTIVE',
        paymentStatus: 'paid',
        renewalDate: getFutureDate(30),
        addons: [],
        usageHistory: {
          viewRequestsUsed: 20,
          createRequestsUsed: 15
        },
        features: {
          emailNotification: true,
          smsNotification: true,
          messagingAllowed: true,
          homepageAd: true
        }
      },
      {
        userId: sampleUserIds[2],
        packageId: createdPackages[1]._id,
        startDate: getRecentPastDate(),
        endDate: new Date(),
        remainingViewRequests: 180,
        remainingCreateRequests: 15,
        isActive: false,
        status: 'EXPIRED',
        paymentStatus: 'expired',
        renewalDate: new Date(),
        addons: [],
        usageHistory: {
          viewRequestsUsed: 20,
          createRequestsUsed: 5
        },
        features: {
          emailNotification: true,
          smsNotification: false,
          messagingAllowed: true,
          homepageAd: true
        }
      }
    ];

    const createdSubscriptions = await Subscription.create(subscriptions);
    console.log('Seeded subscriptions:', createdSubscriptions.length);

    const payments = [
      {
        subscriptionId: createdSubscriptions[0]._id,
        userId: sampleUserIds[0],
        packageId: createdPackages[0]._id,
        amount: packages[0].price,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'credit_card',
        paymentDate: getRecentPastDate(),
        nextBillingDate: getFutureDate(30)
      },
      {
        subscriptionId: createdSubscriptions[1]._id,
        userId: sampleUserIds[1],
        packageId: createdPackages[2]._id,
        amount: packages[2].price,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'credit_card',
        paymentDate: getRecentPastDate(),
        nextBillingDate: getFutureDate(30)
      },
      {
        subscriptionId: createdSubscriptions[2]._id,
        userId: sampleUserIds[2],
        packageId: createdPackages[1]._id,
        amount: packages[1].price,
        currency: 'USD',
        status: 'completed',
        paymentMethod: 'credit_card',
        paymentDate: getRecentPastDate(),
        nextBillingDate: new Date()
      }
    ];

    const createdPayments = await Payment.create(payments);
    console.log('Seeded payments:', createdPayments.length);

    const additionalSubscription = await Subscription.create({
      userId: users[2]._id,
      packageId: createdPackages[2]._id,
      startDate: getRecentPastDate(),
      endDate: getFutureDate(30),
      remainingViewRequests: 500,
      remainingCreateRequests: 50,
      isActive: true,
      status: 'ACTIVE',
      paymentStatus: 'paid',
      renewalDate: getFutureDate(30),
      addons: [],
      usageHistory: {
        viewRequestsUsed: 0,
        createRequestsUsed: 0
      },
      features: {
        emailNotification: true,
        smsNotification: true,
        messagingAllowed: true,
        homepageAd: true
      }
    });

    await Payment.create({
      subscriptionId: additionalSubscription._id,
      userId: users[2]._id,
      packageId: createdPackages[2]._id,
      amount: packages[2].price,
      currency: 'USD',
      status: 'completed',
      paymentMethod: 'credit_card',
      paymentDate: getRecentPastDate(),
      nextBillingDate: getFutureDate(30)
    });

    const package2Subscription = await Subscription.create({
      userId: users[1]._id,
      packageId: createdPackages[1]._id,
      startDate: getRecentPastDate(),
      endDate: getFutureDate(30),
      remainingViewRequests: 200,
      remainingCreateRequests: 20,
      isActive: true,
      status: 'ACTIVE',
      paymentStatus: 'paid',
      renewalDate: getFutureDate(30),
      addons: [],
      usageHistory: {
        viewRequestsUsed: 0,
        createRequestsUsed: 0
      },
      features: {
        emailNotification: true,
        smsNotification: false,
        messagingAllowed: true,
        homepageAd: true
      }
    });

    await Payment.create({
      subscriptionId: package2Subscription._id,
      userId: users[1]._id,
      packageId: createdPackages[1]._id,
      amount: packages[1].price,
      currency: 'USD',
      status: 'completed',
      paymentMethod: 'credit_card',
      paymentDate: getRecentPastDate(),
      nextBillingDate: getFutureDate(30)
    });

    console.log('Added subscriptions and payments for specific users');
    console.log('Database seeding completed successfully');
  } catch (error:any) {
    console.error('Error seeding database:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
};

seedDatabase().catch(console.error);