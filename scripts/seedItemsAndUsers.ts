import mongoose from 'mongoose';
import { Item } from '../src/models/Item';
import { User } from '../src/models/User';
import { Store } from '../src/models/Store';
import connectDB from '../src/config/database';
import { Category } from '../src/models/Category';
import fs from 'fs';
import path from 'path';

// Connect to the database
connectDB();

async function seedItems() {
  try {
    // Clear existing items
    await Item.deleteMany({});
    console.log('Cleared existing items');

    // Get existing users and their stores
    const users = await User.find().limit(3);
    const stores = await Store.find({ owner: { $in: users.map(user => user._id) } });

    const categories = await Category.find({
      id: {
        $in: [
          "1.1.1", "1.1.2", "1.2.1", "1.3.1", "1.3.2",
          "2.1.3", "2.2.1", "2.3.1", "2.4.1", "2.5.4",
          "5.1.1", "5.2.1", "5.2.2", "5.4.1", "5.4.2",
          "6.1.1", "6.1.2", "6.1.3", "6.2.1", "6.2.2",
          "8.1.1", "8.1.3", "8.2.1", "8.2.2", "8.2.3"
        ]
      }
    }).lean();

    const productAdjectives = ['Premium', 'Deluxe', 'Elite', 'Advanced', 'Ultra', 'Pro', 'Eco-friendly', 'Smart', 'Innovative', 'Ergonomic'];
    const serviceAdjectives = ['Expert', 'Professional', 'Customized', 'Comprehensive', 'Rapid', 'Reliable', 'Personalized', 'On-demand', 'Premium', 'Specialized'];

    // Ensure uploads directories exist
    const uploadsDir = path.join(__dirname, '../uploads/items');
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }

    for (const store of stores) {
      const user = users.find(u => u._id.toString() === store.owner.toString());
      if (!user) continue;

      // Select 5 random categories for each store
      const storeCategories = categories
        .sort(() => Math.random() - 0.5)
        .slice(0, 5);

      for (const category of storeCategories) {
        const adjective = category.type === 'Product' ?
          productAdjectives[Math.floor(Math.random() * productAdjectives.length)] :
          serviceAdjectives[Math.floor(Math.random() * serviceAdjectives.length)];

        const itemName = `${adjective} ${category.name}`;
        const desc = `${user.firstName} ${user.lastName} tarafından sunulan yenilikçi ${itemName.toLowerCase()} ${category.type === 'Product' ? 'ürünü' : 'hizmeti'}`;

        const description = `
          <div>
            <h2>${store.name} - ${itemName}</h2>
            <p>${desc}</p>
            <h3>Özellikler:</h3>
            <ul>
              <li>Yüksek kaliteli ${category.type === 'Product' ? 'malzemeler' : 'hizmet'}</li>
              <li>Profesyonel destek</li>
              <li>Rekabetçi fiyatlandırma</li>
              <li>Hızlı teslimat</li>
            </ul>
            <h3>Ek Bilgiler:</h3>
            <p>${category.type === 'Product' ? 'Ürünümüz' : 'Hizmetimiz'} hakkında daha fazla bilgi için lütfen bizimle iletişime geçin.</p>
          </div>
        `;

        // Create store-specific folder for items
        const storeDir = path.join(uploadsDir, store._id.toString());
        if (!fs.existsSync(storeDir)) {
          fs.mkdirSync(storeDir, { recursive: true });
        }

        // Generate a unique image name with MongoDB ID
        const imageId = new mongoose.Types.ObjectId().toString();
        const imageName = `${imageId}.jpg`;
        const imagePath = path.join(storeDir, imageName);

        // Here you would generate or copy an actual image file
        // For this example, we'll just create an empty file
        fs.writeFileSync(imagePath, '');

        const itemData = {
          _id: new mongoose.Types.ObjectId(),
          name: `${store.name} - ${itemName}`,
          description,
          category: category._id,
          store: store._id,
          type: category.type.toLowerCase() as 'product'  | 'service',
          listingType: Math.random() > 0.5 ? 'demand' : 'sale',
          isApproved: true,
          date: new Date(),
          status: 'ACTIVE' as const,
          images: [`/uploads/items/${store._id.toString()}/${imageName}`],
          isDisabled: false
        };

        const item = await Item.create(itemData);
        console.log(`Created item: ${item.name}`);
      }
    }

    console.log('Seeding completed successfully');
  } catch (error) {
    console.error('Error seeding data:', error);
  } finally {
    mongoose.connection.close();
  }
}

seedItems();