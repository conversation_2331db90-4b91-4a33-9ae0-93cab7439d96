import mongoose from 'mongoose';
import { DesignPackage } from '../src/models/DesignPackage';
import dotenv from 'dotenv';

dotenv.config();

const designPackages = [
  {
    name: "Web Sitesi Tasarımı",
    nameEn: "Website Design",
    description: "Modern ve profesyonel web sitesi tasarımı ile markanızı dijitalde güçlendirin.",
    descriptionEn: "Strengthen your brand digitally with modern and professional website design.",
    price: 150,
    currency: "USD",
    icon: "Globe",
    features: [
      "Responsive tasarım",
      "5 sayfa tasarımı",
      "UI/UX odaklı tasarım",
      "SEO uyumlu yapı",
      "Sosyal medya entegrasyonu",
      "İletişim formu",
      "3 revizyon hakkı",
      "Kaynak dosyalar (Figma, XD)"
    ],
    featuresEn: [
      "Responsive design",
      "5 page design",
      "UI/UX focused design",
      "SEO compatible structure",
      "Social media integration",
      "Contact form",
      "3 revision rights",
      "Source files (Figma, XD)"
    ],
    deliveryTime: 10,
    revisionCount: 3,
    isPopular: false,
    order: 1
  },
  {
    name: "Kurumsal Kimlik Tasarımı",
    nameEn: "Corporate Identity Design",
    description: "Markanızı yansıtan profesyonel kurumsal kimlik tasarımı ile fark yaratın.",
    descriptionEn: "Make a difference with professional corporate identity design that reflects your brand.",
    price: 200,
    currency: "USD",
    icon: "Layers",
    features: [
      "Logo tasarımı",
      "Kartvizit tasarımı",
      "Antetli kağıt tasarımı",
      "Zarf tasarımı",
      "Dosya tasarımı",
      "E-posta imzası",
      "5 revizyon hakkı",
      "Tüm kaynak dosyalar"
    ],
    featuresEn: [
      "Logo design",
      "Business card design",
      "Letterhead design",
      "Envelope design",
      "Folder design",
      "Email signature",
      "5 revision rights",
      "All source files"
    ],
    deliveryTime: 14,
    revisionCount: 5,
    isPopular: true,
    order: 2
  },
  {
    name: "Logo Tasarımı",
    nameEn: "Logo Design",
    description: "Yeni başlayan ihracatçılar için temel ihtiyaçlara odaklı ekonomik çözüm.",
    descriptionEn: "Economical solution focused on basic needs for new exporters.",
    price: 100,
    currency: "USD",
    icon: "PenTool",
    features: [
      "Özgün logo tasarımı",
      "3 farklı konsept çalışması",
      "Kaynak dosyalar (AI, PNG, SVG)",
      "3 revizyon hakkı",
      "Vektörel çalışma",
      "Farklı versiyonlar",
      "Renk alternatifleri",
      "Kullanım kılavuzu"
    ],
    featuresEn: [
      "Original logo design",
      "3 different concept works",
      "Source files (AI, PNG, SVG)",
      "3 revision rights",
      "Vector work",
      "Different versions",
      "Color alternatives",
      "Usage guide"
    ],
    deliveryTime: 7,
    revisionCount: 3,
    isPopular: false,
    order: 3
  }
];

async function seedDesignPackages() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || '*********************************************************************');
    console.log('Connected to MongoDB');

    // Clear existing design packages
    await DesignPackage.deleteMany({});
    console.log('Cleared existing design packages');

    // Insert new design packages
    const insertedPackages = await DesignPackage.insertMany(designPackages);
    console.log(`Inserted ${insertedPackages.length} design packages`);

    // Log the inserted packages
    console.log('\nInserted Design Packages:');
    insertedPackages.forEach(pkg => {
      console.log(`- ${pkg.name} (${pkg.nameEn}) - $${pkg.price}`);
    });

    console.log('\nDesign packages seeding completed successfully!');
  } catch (error) {
    console.error('Error seeding design packages:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the seed function
seedDesignPackages();