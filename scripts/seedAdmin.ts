import mongoose from 'mongoose';
import { User } from '../src/models/User';
import connectDB from '../src/config/database';

connectDB();

async function seedAdmin() {
  try {
    // Delete existing admin user
    await User.deleteOne({ email: '<EMAIL>' });

    // Create new admin user
    const adminUser = new User({
      email: '<EMAIL>',
      password: 'Admin13579!',
      role: 'admin',
      firstName: 'Admin',
      lastName: 'User',
      isEmailVerified: true,
      isActive: true,
      createdAt: new Date(),
      referralCode: 'ADMIN',
      referredBy: null,
      updatedAt: new Date(),
      ipAddress: '127.0.0.1',
      zipCode: '34000',
      country: 'Turkey',
      city: 'Istanbul',
      birthDate: new Date('1990-01-01'),
      phoneNumber: '+905555555555',
      address: 'Istanbul, Turkey',
    });

    await adminUser.save();
    console.log('Admin user created successfully');

  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error seeding admin user:', error.message);
    } else {
      console.error('Error seeding admin user:', error);
    }
  } finally {
    mongoose.connection.close();
  }
}

seedAdmin();
