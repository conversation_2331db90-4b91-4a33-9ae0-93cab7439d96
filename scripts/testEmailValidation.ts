import axios from 'axios';
import readline from 'readline';
import { promisify } from 'util';

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

const question = promisify(rl.question).bind(rl);

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000';

async function forgotPassword() {
  console.log('\n=== FORGOT PASSWORD / EMAIL VALIDATION ===\n');
  
  const email = await question('Enter your email address: ') as string;

  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/forgot-password`, {
      email
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'tr'
      }
    });

    console.log('\n✅ Password reset email sent successfully!');
    console.log('Response:', response.data.message);
    console.log('\nCheck your email for the reset link.');
    console.log('The link will contain a token that expires in 10 minutes.');
    
    return true;
  } catch (error: any) {
    console.error('\n❌ Error:', error.response?.data?.message || error.message);
    return false;
  }
}

async function resetPassword() {
  console.log('\n=== RESET PASSWORD ===\n');
  
  const token = await question('Enter the reset token from your email: ') as string;
  const newPassword = await question('Enter your new password: ') as string;

  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/reset-password`, {
      token,
      password: newPassword
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'tr'
      }
    });

    console.log('\n✅ Password reset successful!');
    console.log('Response:', response.data.message);
    console.log('\nYou should receive a password change confirmation email.');
    console.log('You can now login with your new password.');
    
    return true;
  } catch (error: any) {
    console.error('\n❌ Error:', error.response?.data?.message || error.message);
    return false;
  }
}

async function main() {
  console.log('=== E-EXPORT CITY EMAIL VALIDATION TEST ===');
  console.log(`API Base URL: ${API_BASE_URL}`);

  try {
    const choice = await question('\nChoose an option:\n1. Request password reset (send validation email)\n2. Reset password with token\n\nYour choice (1 or 2): ');

    if (choice === '1') {
      await forgotPassword();
    } else if (choice === '2') {
      await resetPassword();
    } else {
      console.log('Invalid choice');
    }

    console.log('\n=== TEST COMPLETED ===');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    rl.close();
  }
}

// Run the test
main();