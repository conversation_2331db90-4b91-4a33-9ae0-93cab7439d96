import fs from 'fs';
import csv from 'csv-parser';
import mongoose from 'mongoose';
import { Country } from '../src/models/Country';
import { City } from '../src/models/City';
import connectDB from '../src/config/database';

connectDB();

async function populateDatabase() {
  try {
    // Clear existing data
    await Country.deleteMany({});
    await City.deleteMany({});

    // Read and insert all countries
    const countries = await readCSV('./data/countries.csv');
    const countryDocuments = countries.map(country => ({
      name: country.name,
      nameTr: country.nameTr,
      code: country.phone_code,
      country_id: country.id
    }));
    const insertedCountries = await Country.insertMany(countryDocuments);
    console.log(`Inserted ${insertedCountries.length} countries`);

    // Read and insert all cities
    const cities = await readCSV('./data/states.csv');
    const cityDocuments = cities.map(city => {
      // Find the corresponding country document
      const countryDoc = insertedCountries.find(c => c.country_id === city.country_id);
      if (!countryDoc) {
        console.warn(`No matching country found for city ${city.name} (ID: ${city.id})`);
        return null;
      }
      return {
        city_id: city.id,
        name: city.name,
        country: countryDoc._id
      };
    }).filter(doc => doc !== null); // Remove any null entries

    try {
      const insertedCities = await City.insertMany(cityDocuments, { ordered: false });
      console.log(`Inserted ${insertedCities.length} cities`);
    } catch (bulkError: any) {
      if (bulkError.writeErrors) {
        console.log(`Inserted ${bulkError.insertedDocs.length} cities with ${bulkError.writeErrors.length} duplicates skipped`);
      } else {
        throw bulkError;
      }
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error('Error populating database:', error.message);
    } else {
      console.error('Error populating database:', error);
    }
  } finally {
    mongoose.connection.close();
  }
}

function readCSV(filePath: string): Promise<any[]> {
  return new Promise((resolve, reject) => {
    const results: any[] = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error:any) => reject(error));
  });
}

populateDatabase();