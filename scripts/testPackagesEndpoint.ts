import axios from 'axios';

const testPackagesEndpoint = async () => {
  try {
    // Generate a test token with the user's ID
    const jwt = require('jsonwebtoken');
    const token = jwt.sign(
      {
        id: '67c2094a86a419922e2dad83',
        userId: '67c2094a86a419922e2dad83',
        role: 'user'
      },
      process.env.JWT_SECRET_USER || 'default-secret'
    );

    console.log('Generated token:', token);
    console.log('Decoded token:', jwt.decode(token));

    const response = await axios.get('http://localhost:5050/api/packages', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Accept-Language': 'en'
      }
    });

    console.log('\n=== RESPONSE DATA ===');
    console.log('Current Subscription:', response.data.currentSubscription);
    console.log('Packages with flags:', response.data.packages.map((p: any) => ({
      name: p.name,
      type: p.type,
      isCurrentPackage: p.isCurrentPackage,
      price: p.price
    })));
  } catch (error: any) {
    console.error('Error:', error.response?.data || error.message);
  }
};

testPackagesEndpoint();