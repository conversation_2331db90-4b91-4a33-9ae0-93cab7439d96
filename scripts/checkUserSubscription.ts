import mongoose from 'mongoose';
import { Subscription } from '../src/models/Subscription';
import { Package } from '../src/models/Package';
import { User } from '../src/models/User';

const checkUserSubscription = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGO_URI || '*********************************************************************');
    console.log('Connected to MongoDB');

    const userId = '67c2094a86a419922e2dad83';
    
    // Check user exists
    const user = await User.findById(userId);
    console.log('\n=== USER INFO ===');
    console.log('User found:', user ? 'Yes' : 'No');
    console.log('User subscription field:', user?.subscription);
    console.log('User hasPackage:', user?.hasPackage);

    // Check all subscriptions for this user
    console.log('\n=== ALL USER SUBSCRIPTIONS ===');
    const allSubscriptions = await Subscription.find({ userId });
    console.log('Total subscriptions found:', allSubscriptions.length);
    
    allSubscriptions.forEach((sub: any) => {
      console.log('\nSubscription:', {
        _id: sub._id.toString(),
        userId: sub.userId,
        packageId: sub.packageId,
        status: sub.status,
        paymentStatus: sub.paymentStatus,
        startDate: sub.startDate,
        endDate: sub.endDate,
        isExpired: sub.endDate < new Date()
      });
    });

    // Check active subscriptions
    console.log('\n=== ACTIVE SUBSCRIPTIONS ===');
    const activeSubscriptions = await Subscription.find({
      userId,
      status: 'ACTIVE',
      endDate: { $gte: new Date() }
    });
    
    console.log('Active subscriptions found:', activeSubscriptions.length);
    
    activeSubscriptions.forEach((sub: any) => {
      console.log('\nActive Subscription:', {
        _id: sub._id.toString(),
        userId: sub.userId,
        packageId: sub.packageId,
        endDate: sub.endDate,
        remainingDays: Math.ceil((sub.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
      });
    });

    // Test different userId formats
    console.log('\n=== TESTING USERID FORMATS ===');
    console.log('String userId:', userId);
    console.log('ObjectId userId:', new mongoose.Types.ObjectId(userId));
    
    // Try query with ObjectId
    const subsWithObjectId = await Subscription.find({
      userId: new mongoose.Types.ObjectId(userId),
      status: 'ACTIVE',
      endDate: { $gte: new Date() }
    });
    console.log('Subscriptions found with ObjectId:', subsWithObjectId.length);

    await mongoose.disconnect();
  } catch (error) {
    console.error('Error:', error);
    await mongoose.disconnect();
  }
};

checkUserSubscription();