import mongoose from 'mongoose';
import { User } from '../src/models/User';
import { Store } from '../src/models/Store';
import { Item } from '../src/models/Item';
import { Category } from '../src/models/Category';
import connectDB from '../src/config/database';
import fs from 'fs';
import path from 'path';

const storeNames = [
  'Fashion Boutique',
  'Electronics Hub',
  'Home Decor Center',
  'Sports Equipment Store',
  'Beauty & Wellness Shop',
  'Jewelry & Accessories',
  'Digital Gadgets Store',
  'Furniture Gallery',
  'Outdoor Living Store',
  'Kitchen Essentials'
];

const storeDescriptions = [
  'Your one-stop shop for trendy fashion items',
  'Latest electronics and tech gadgets',
  'Beautiful home decoration items',
  'Quality sports and fitness equipment',
  'Premium beauty and wellness products',
  'Elegant jewelry and fashion accessories',
  'Cutting-edge digital devices and accessories',
  'Contemporary and classic furniture pieces',
  'Everything for your outdoor activities',
  'Essential kitchen tools and appliances'
];

async function seedStores() {
  try {
    await connectDB();
    console.log('Connected to database');

    // Get all users from the database
    const users = await User.find({});
    console.log(`Found ${users.length} users`);

    // Delete existing stores and items
    // await Store.deleteMany({});
    // await Item.deleteMany({});
    console.log('Skipping deletion of existing stores and items. New records will be added.');

    const createdStores: any[] = [];

    // Get categories for items
    const categories = await Category.find({
      id: {
        $in: [
          "1.1.1", "1.1.2", "1.2.1", "1.3.1", "1.3.2",
          "2.1.3", "2.2.1", "2.3.1", "2.4.1", "2.5.4",
          "5.1.1", "5.2.1", "5.2.2", "5.4.1", "5.4.2",
          "6.1.1", "6.1.2", "6.1.3", "6.2.1", "6.2.2",
          "8.1.1", "8.1.3", "8.2.1", "8.2.2", "8.2.3"
        ]
      }
    }).lean();

    for (const user of users) {
      // Create 2-4 random stores for each user
      const numberOfStores = Math.floor(Math.random() * 3) + 2; // Random number between 2 and 4

      for (let i = 0; i < numberOfStores; i++) {
        const randomNameIndex = Math.floor(Math.random() * storeNames.length);
        const randomDescIndex = Math.floor(Math.random() * storeDescriptions.length);

        const store = new Store({
          name: `${user.firstName}'s ${storeNames[randomNameIndex]}`,
          description: storeDescriptions[randomDescIndex],
          owner: user._id,
          isActive: true,
          isApproved: true,
          date: new Date(),
          address: user.address,
          phone: user.phoneNumber,
          email: user.email,
          location: {
            city: user.city,
            country: user.country
          },
          city: user.city,
          country: user.country,
          socialMedia: {
            facebook: `https://facebook.com/${user.firstName.toLowerCase()}-store-${i + 1}`,
            twitter: `https://twitter.com/${user.firstName.toLowerCase()}-store-${i + 1}`,
            instagram: `https://instagram.com/${user.firstName.toLowerCase()}-store-${i + 1}`
          }
        });

        const savedStore = await store.save();
        createdStores.push(savedStore);
        console.log(`Created store: ${store.name} for user: ${user.email}`);

        // Create items for this store
        // Select 5 random categories for each store
        const storeCategories = categories
          .sort(() => Math.random() - 0.5)
          .slice(0, 5);

        for (const category of storeCategories) {
          const desc = `${user.firstName} ${user.lastName} tarafından sunulan yenilikçi ${category.name.toLowerCase()} ${category.type === 'Product' ? 'ürünü' : 'hizmeti'}`;

          const description = `
            <div>
              <h2>${store.name} - ${category.name}</h2>
              <p>${desc}</p>
              <h3>Özellikler:</h3>
              <ul>
                <li>Yüksek kaliteli ${category.type === 'Product' ? 'malzemeler' : 'hizmet'}</li>
                <li>Profesyonel destek</li>
                <li>Rekabetçi fiyatlandırma</li>
                <li>Hızlı teslimat</li>
              </ul>
              <h3>Ek Bilgiler:</h3>
              <p>${category.type === 'Product' ? 'Ürünümüz' : 'Hizmetimiz'} hakkında daha fazla bilgi için lütfen bizimle iletişime geçin.</p>
            </div>
          `;

          const itemData = {
            name: `${store.name} - ${category.name}`,
            description,
            price: Math.floor(Math.random() * 1000) + 500,
            category: category._id,
            store: store._id,
            type: category.type.toLowerCase() as 'product' | 'service',
            isApproved: true,
            date: new Date(),
            status: 'ACTIVE' as const,
            images: [`/uploads/items/${store._id.toString()}/${new mongoose.Types.ObjectId().toString()}.jpg`]
          };

          const item = await Item.create(itemData);
          console.log(`Created item: ${itemData.name} for store: ${store.name}`);
        }
      }
    }

    console.log(`Successfully created ${createdStores.length} stores with items`);
  } catch (error: any) {
    console.error('Error seeding stores and items:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from database');
  }
}

seedStores().catch(console.error);
