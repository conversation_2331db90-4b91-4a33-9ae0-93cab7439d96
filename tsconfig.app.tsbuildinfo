{"root": ["./src/app.tsx", "./src/adminapi.ts", "./src/api.ts", "./src/global.d.ts", "./src/i18n.ts", "./src/main.tsx", "./src/theme.ts", "./src/vite-env.d.ts", "./src/api/adminapi.ts", "./src/api/cardapi.ts", "./src/api/categoryapi.ts", "./src/api/designpackageapi.ts", "./src/api/index.ts", "./src/api/itemapi.ts", "./src/api/packageapi.ts", "./src/api/representativeapi.ts", "./src/api/sliderapi.ts", "./src/api/storeapi.ts", "./src/api/subscriptionapi.ts", "./src/api/ticketapi.ts", "./src/api/userapi.ts", "./src/components/aboutsection.tsx", "./src/components/adminheader.tsx", "./src/components/carousel.tsx", "./src/components/categories.tsx", "./src/components/designpackagepaymentmodal.tsx", "./src/components/discoversection.tsx", "./src/components/featureshowcase.tsx", "./src/components/featuredcompany.tsx", "./src/components/featuredproducts.tsx", "./src/components/featuredstores.tsx", "./src/components/header.tsx", "./src/components/heroslider.tsx", "./src/components/homeads.tsx", "./src/components/homerepresentatives.tsx", "./src/components/homepageads.tsx", "./src/components/imageplaceholder.tsx", "./src/components/introsections.tsx", "./src/components/languageselector.tsx", "./src/components/livechat.tsx", "./src/components/maskedimage.tsx", "./src/components/offlineheader.tsx", "./src/components/packagepurchase.tsx", "./src/components/productcard.tsx", "./src/components/productmessage.tsx", "./src/components/representatives.tsx", "./src/components/searchandfilter.tsx", "./src/components/userdashboard.tsx", "./src/components/userheader.tsx", "./src/components/notifications/notificationbell.tsx", "./src/components/notifications/notificationprovider.tsx", "./src/components/admin/adminheader.tsx", "./src/components/admin/adminnav.tsx", "./src/components/admin/adminsidebar.tsx", "./src/components/admin/cancellationrequests.tsx", "./src/components/admin/imagepreview.tsx", "./src/components/admin/imagevalidator.tsx", "./src/components/admin/userstatistics.tsx", "./src/components/admin/homepage-ads/homepageadlist.tsx", "./src/components/admin/representatives/representativeform.tsx", "./src/components/admin/representatives/representativelist.tsx", "./src/components/common/footer.tsx", "./src/components/common/productcard.tsx", "./src/components/common/richtexteditor.tsx", "./src/components/common/storecard.tsx", "./src/components/common/termsmodal.tsx", "./src/components/common/whatsappbutton.tsx", "./src/components/layout/adminlayout.tsx", "./src/components/layout/layout.tsx", "./src/components/profile/cardtab.tsx", "./src/components/profile/homepageadtab.tsx", "./src/components/profile/itemcard.tsx", "./src/components/profile/itemrequestmodal.tsx", "./src/components/profile/itemstab.tsx", "./src/components/profile/profiletab.tsx", "./src/components/profile/referralstats.tsx", "./src/components/profile/storetab.tsx", "./src/components/profile/subscriptiontab.tsx", "./src/components/profile/ticketstab.tsx", "./src/components/register/newtermsmodal.tsx", "./src/components/register/privacypolicymodal.tsx", "./src/components/register/termsmodal.tsx", "./src/components/tickets/ticketlist.tsx", "./src/components/ui/button.tsx", "./src/components/ui/input.tsx", "./src/components/ui/loadingspinner.tsx", "./src/components/ui/modal.tsx", "./src/components/ui/notification.tsx", "./src/components/ui/select.tsx", "./src/context/authcontext.tsx", "./src/context/notificationcontext.tsx", "./src/context/socketcontext.tsx", "./src/hooks/useapi.ts", "./src/hooks/useauthcheck.ts", "./src/hooks/usecountries.ts", "./src/lib/utils.ts", "./src/locales/terms/ar.ts", "./src/locales/terms/de.ts", "./src/locales/terms/en.ts", "./src/locales/terms/es.ts", "./src/locales/terms/fr.ts", "./src/locales/terms/index.ts", "./src/locales/terms/it.ts", "./src/locales/terms/ru.ts", "./src/locales/terms/tr.ts", "./src/locales/terms/zh.ts", "./src/pages/about.tsx", "./src/pages/chat.tsx", "./src/pages/contact.tsx", "./src/pages/debugstore.tsx", "./src/pages/designpackages.tsx", "./src/pages/editpackage.tsx", "./src/pages/faq.tsx", "./src/pages/forgotpassword.tsx", "./src/pages/home.tsx", "./src/pages/itemlisting.tsx", "./src/pages/itemview.tsx", "./src/pages/iyzicopaymentcallback.tsx", "./src/pages/login.tsx", "./src/pages/messagecenter.tsx", "./src/pages/packagelisting.tsx", "./src/pages/packageselection.tsx", "./src/pages/partnership.tsx", "./src/pages/privacypolicy.tsx", "./src/pages/productadd.tsx", "./src/pages/productdetail.tsx", "./src/pages/register.tsx", "./src/pages/representatives.tsx", "./src/pages/resetpassword.tsx", "./src/pages/storedetail.tsx", "./src/pages/stores.tsx", "./src/pages/userdashboard.tsx", "./src/pages/useritems.tsx", "./src/pages/userprofile.tsx", "./src/pages/admin/admindashboard.tsx", "./src/pages/admin/adminlogin.tsx", "./src/pages/admin/adminpanel.tsx", "./src/pages/admin/contenthistory.tsx", "./src/pages/admin/editdesignpackage.tsx", "./src/pages/admin/editpackage.tsx", "./src/pages/admin/managedesignpackages.tsx", "./src/pages/admin/managehomeads.tsx", "./src/pages/admin/managehomepageads.tsx", "./src/pages/admin/managelivechat.tsx", "./src/pages/admin/managepackages.tsx", "./src/pages/admin/manageproductsservices.tsx", "./src/pages/admin/managerepresentatives.tsx", "./src/pages/admin/managestores.tsx", "./src/pages/admin/managetickets.tsx", "./src/pages/admin/manageusers.tsx", "./src/pages/reasons/fastgrowth.tsx", "./src/pages/reasons/globalaccess.tsx", "./src/pages/reasons/partnerships.tsx", "./src/pages/reasons/secureplatform.tsx", "./src/pages/user/createticket.tsx", "./src/pages/user/ticketlist.tsx", "./src/redux/hooks.ts", "./src/redux/rootreducer.ts", "./src/redux/store.ts", "./src/redux/slices/authslice.ts", "./src/redux/slices/productslice.ts", "./src/redux/slices/userslice.ts", "./src/routes/admin.tsx", "./src/routes/index.tsx", "./src/types/category.ts", "./src/types/item.ts", "./src/types/itemrequest.ts", "./src/types/message.ts", "./src/types/package.ts", "./src/types/representative.ts", "./src/types/review.ts", "./src/types/store.ts", "./src/types/ticket.ts", "./src/types/user.ts", "./src/utils/helpers.ts", "./src/utils/locationutils.ts", "./src/utils/textmask.ts"], "version": "5.7.3"}