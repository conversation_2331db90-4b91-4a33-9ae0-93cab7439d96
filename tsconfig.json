{"compilerOptions": {"target": "ES6", "module": "NodeNext", "moduleResolution": "NodeNext", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "resolveJsonModule": true, "allowSyntheticDefaultImports": true}, "include": ["src/**/*.ts", "../react/api.ts", "env.d.ts"], "exclude": ["node_modules"]}