import { Request, Response } from 'express';
import { AuthRequest } from '../types/express';
import { IItemRequest, ItemRequest } from '../models/ItemRequest';
import { Store } from '../models/Store';
import { Item } from '../models/Item';
import mongoose from 'mongoose';

// Create item request
export const createItemRequest = async (req: AuthRequest, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const userId = req.user?.id;
    // Log raw request body first
    console.log('Raw request body:', JSON.stringify(req.body));

    // Handle both formats and ensure we're getting the data correctly
    const itemData = req.body.itemData || req.body;

    // Log parsed item data
    console.log('Parsed item data:', JSON.stringify(itemData));

    // Check if the data exists at all
    if (!itemData || Object.keys(itemData).length === 0) {
      return res.status(400).json({
        message: language === 'en'
          ? 'No data provided in request body'
          : 'İstek gövdesinde veri sağlanmadı',
        error: 'NO_DATA'
      });
    }

    // Validate required fields
    const requiredFields = ['name', 'description', 'category', 'type'];
    const missingFields = requiredFields.filter(field => !itemData[field]);

    if (missingFields.length > 0) {
      return res.status(400).json({
        message: language === 'en'
          ? `Missing required fields: ${missingFields.join(', ')}`
          : `Eksik alanlar: ${missingFields.join(', ')}`,
        fields: missingFields
      });
    }

    // Validate type field
    if (!['product', 'service'].includes(itemData.type)) {
      return res.status(400).json({
        message: language === 'en'
          ? 'Type must be either "product" or "service"'
          : 'Tür "ürün" veya "hizmet" olmalıdır',
        field: 'type'
      });
    }

    // Handle file uploads
    const files = req.files as any;
    const images = files ? files.map((file: any) => file.path) : [];

    // Check if user has a store
    const userStore = await Store.findOne({ owner: userId, isApproved: true });
    if (!userStore) {
      return res.status(403).json({
        message: language === 'en' ? 'You need to create a store first' : 'Önce bir mağaza oluşturmanız gerekiyor',
        code: 'NO_STORE',
        redirectTo: '/profile/store'
      });
    }

    // Create new item request with validated data
    const newItemRequest = new ItemRequest({
      name: itemData.name,
      description: itemData.description,
      category: itemData.category,
      type: itemData.type,
      listingType: itemData.listingType,
      store: userStore._id,
      images,
      status: 'PENDING'
    });

    await newItemRequest.save();

    res.status(201).json({
      message: language === 'en' ? 'Item request created successfully' : 'Ürün talebi başarıyla oluşturuldu',
      itemRequest: newItemRequest
    });
  } catch (err) {
    console.error('Error creating item request:', err);
    res.status(500).json({
      message: language === 'en'
        ? 'Error creating item request'
        : 'Ürün talebi oluşturulurken hata oluştu',
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }
};

// Get user's item requests
export const getOwnedItemRequests = async (req: AuthRequest, res: Response) => {
  const userId = req.user?.id;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const userStore = await Store.findOne({ owner: userId });
    if (!userStore) {
      return res.json([]);
    }

    const itemRequests = await ItemRequest.find({
      store: userStore._id,
      status: 'ACTIVE'
    })
    .populate('category')
    .populate('user', 'name email location')
    .sort({ createdAt: -1 });

    res.json(itemRequests);
  } catch (err) {
    console.error('Error getting owned item requests:', err);
    res.status(500).json({
      message: language === 'en' ? 'Error getting item requests' : 'Ürün talepleri getirilirken hata oluştu'
    });
  }
};

// Update item request
export const updateItemRequest = async (req: AuthRequest, res: Response) => {
  const userId = req.user?._id;
  const { requestId } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    // Log raw request body for debugging
    console.log('Update request body:', JSON.stringify(req.body));

    // Get item data from request body
    const itemData = req.body.itemData || req.body;

    // Log parsed item data
    console.log('Parsed update item data:', JSON.stringify(itemData));

    const userStore = await Store.findOne({ owner: userId });
    console.log('User store:', userStore);
    console.log('Request ID:', requestId);
    console.log('Item data:', itemData);
    if (!userStore) {
      return res.status(403).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    const itemRequest = await ItemRequest.findOne({
      _id: requestId,
      store: userStore._id
    });

    // If no item request exists, return error
    if (!itemRequest) {
      return res.status(404).json({
        message: language === 'en' ? 'Item request not found' : 'Ürün talebi bulunamadı'
      });
    }

    // Validate required fields only if they are being updated
    const requiredFields = ['name', 'description', 'category', 'type'];
    const providedFields = Object.keys(itemData);
    const missingFields = requiredFields.filter(field =>
      providedFields.includes(field) && !itemData[field]
    );

    if (missingFields.length > 0) {
      return res.status(400).json({
        message: language === 'en'
          ? `Missing required fields: ${missingFields.join(', ')}`
          : `Eksik alanlar: ${missingFields.join(', ')}`,
        fields: missingFields
      });
    }

    // If type is being updated, validate it
    if (itemData.type && !['product', 'service'].includes(itemData.type)) {
      return res.status(400).json({
        message: language === 'en'
          ? 'Type must be either "product" or "service"'
          : 'Tür "ürün" veya "hizmet" olmalıdır',
        field: 'type'
      });
    }

    // If item request is approved, create a new one
    if (itemRequest.status === 'APPROVED') {
      const files = req.files as any;
      const newImages = files ? files.map((file: any) => file.path) : [];
      const images = itemData.existingImages ? [itemData.existingImages] : newImages;

      const newItemRequest = new ItemRequest({
        name: itemData.name || itemRequest.name,
        description: itemData.description || itemRequest.description,
        category: itemData.category || itemRequest.category,
        type: itemData.type || itemRequest.type,
        listingType: itemData.listingType || itemRequest.listingType,
        store: userStore._id,
        images,
        status: 'PENDING',
        previousRequestId: itemRequest._id
      });

      await newItemRequest.save();

      if (itemData.itemId) {
        await Item.findByIdAndUpdate(itemData.itemId, { status: 'PENDING' });
      }

      return res.status(201).json({
        message: language === 'en' ? 'New item request created successfully' : 'Yeni ürün talebi başarıyla oluşturuldu',
        itemRequest: newItemRequest
      });
    }

    // Update existing pending item request
    const files = req.files as any;
    const newImages = files ? files.map((file: any) => file.path) : [];
    const images = newImages.length > 0 ? newImages :
                  itemData.existingImages ? [itemData.existingImages] :
                  itemRequest.images;

    // Only update fields that are provided
    const updatedData = {
      ...itemRequest.toObject(),
      ...Object.fromEntries(
        Object.entries(itemData)
          .filter(([key, value]) => value !== undefined)
      ),
      images,
      status: 'PENDING'
    };

    // Update the item request
    Object.assign(itemRequest, updatedData);
    await itemRequest.save();

    // Update associated item if exists
    if (itemData.itemId) {
      await Item.findByIdAndUpdate(itemData.itemId, { status: 'PENDING' });
    }

    res.json({
      message: language === 'en' ? 'Item request updated successfully' : 'Ürün talebi başarıyla güncellendi',
      itemRequest
    });
  } catch (err) {
    console.error('Error updating item request:', err);
    res.status(500).json({
      message: language === 'en' ? 'Error updating item request' : 'Ürün talebi güncellenirken hata oluştu',
      error: err instanceof Error ? err.message : 'Unknown error'
    });
  }
};

// Delete item request
export const deleteItemRequest = async (req: AuthRequest, res: Response) => {
  const userId = req.user?.id;
  const { requestId } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const userStore = await Store.findOne({ owner: userId });
    if (!userStore) {
      return res.status(403).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    const itemRequest = await ItemRequest.findOne({
      _id: requestId,
      store: userStore._id
    });

    if (!itemRequest) {
      return res.status(404).json({
        message: language === 'en' ? 'Item request not found' : 'Ürün talebi bulunamadı'
      });
    }

    if (itemRequest.status === 'APPROVED') {
      return res.status(403).json({
        message: language === 'en' ? 'Cannot delete approved item request' : 'Onaylanmış ürün talebi silinemez',
        code: 'CANNOT_DELETE_APPROVED_ITEM'
      });
    }

    itemRequest.status = 'DELETED';
    await itemRequest.save();

    res.json({
      message: language === 'en' ? 'Item request deleted successfully' : 'Ürün talebi başarıyla silindi'
    });
  } catch (err) {
    console.error('Error deleting item request:', err);
    res.status(500).json({
      message: language === 'en' ? 'Error deleting item request' : 'Ürün talebi silinirken hata oluştu'
    });
  }
};
