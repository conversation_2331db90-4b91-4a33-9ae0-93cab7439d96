import { Request, Response } from 'express';
import { StoredCard } from '../models/StoredCard';
import { storeCard } from '../services/iyzicoPaymentService';
import { Types } from 'mongoose';
import { IUser } from '../models/User';
import { CardAuditLog } from '../models/CardAuditLog';

// Helper function to get client IP
const getClientIp = (req: Request): string => {
  return (req.headers['x-forwarded-for'] as string)?.split(',')[0] || 
         req.socket.remoteAddress || 
         'unknown';
};

// Helper function to create audit log
const createAuditLog = async (
  userId: Types.ObjectId | string,
  cardId: Types.ObjectId | string | undefined,
  action: string,
  ipAddress: string,
  details?: any,
  success: boolean = true
) => {
  try {
    await CardAuditLog.create({
      userId,
      cardId,
      action,
      ipAddress,
      details,
      success,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Failed to create audit log:', error);
  }
};

// Use the existing Express namespace augmentation
export const getStoredCards = async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const userId = user._id;
  const language = req.headers['accept-language'] as string || 'tr';
  const ipAddress = getClientIp(req);

  try {
    const cards = await StoredCard.find({ userId, isActive: true });
    
    // Filter out cards that can't be decrypted and collect valid cards
    const validCards = [];
    const problematicCardIds = [];
    
    for (const card of cards) {
      try {
        // Try to access the decrypted values to trigger decryption
        const cardData = card.toObject();
        if (cardData.cardToken && cardData.cardUserKey) {
          validCards.push(card);
          // Log viewed action for valid cards
          await createAuditLog(userId, card._id.toString(), 'viewed', ipAddress);
        }
      } catch (decryptError: any) {
        console.error(`Failed to decrypt card ${card._id}:`, decryptError.message);
        problematicCardIds.push(card._id);
        // Log the failed decryption attempt
        await createAuditLog(userId, card._id.toString(), 'decrypt_failed', ipAddress, {
          error: decryptError.message
        }, false);
      }
    }
    
    // If there are problematic cards, deactivate them
    if (problematicCardIds.length > 0) {
      await StoredCard.updateMany(
        { _id: { $in: problematicCardIds } },
        { isActive: false }
      );
      console.log(`Deactivated ${problematicCardIds.length} cards due to decryption errors`);
    }
    
    res.status(200).json(validCards);
  } catch (error: any) {
    // Check if this is a decryption error
    if (error.message && error.message.includes('decrypt')) {
      res.status(500).json({
        message: language === 'en' ? 'Error retrieving stored cards' : 'Kayıtlı kartlar alınırken hata oluştu',
        error: 'Failed to decrypt data. This usually happens when:\n1. CARD_ENCRYPTION_KEY is not set in .env\n2. The encryption key has changed\n3. The data was encrypted with a different key\nRun: npm run generate:encryption-key to create a new key'
      });
    } else {
      res.status(500).json({
        message: language === 'en' ? 'Error retrieving stored cards' : 'Kayıtlı kartlar alınırken hata oluştu',
        error: error.message
      });
    }
  }
};

export const addStoredCard = async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const userId = user._id;
  const { cardHolderName, cardNumber, expireMonth, expireYear, cvc, cardAlias } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';
  const ipAddress = getClientIp(req);
  const userAgent = req.headers['user-agent'] || 'unknown';

  try {
    // First, check if this will be the first card
    const existingCards = await StoredCard.find({ userId, isActive: true });
    const isFirstCard = existingCards.length === 0;

    // Detect card association from the card number before sending to iyzico
    // Clean the card number to remove spaces
    const cleanedCardNumber = cardNumber.replace(/\s+/g, '');
    const firstDigit = cleanedCardNumber.charAt(0);
    const firstTwoDigits = cleanedCardNumber.substring(0, 2);
    
    // Determine card association based on card number patterns
    let detectedCardAssociation = 'UNKNOWN';
    let detectedCardType = 'CREDIT_CARD';
    
    if (firstDigit === '4') {
      detectedCardAssociation = 'VISA';
    } else if (['51', '52', '53', '54', '55'].includes(firstTwoDigits) || 
              (parseInt(firstTwoDigits) >= 22 && parseInt(firstTwoDigits) <= 27)) {
      detectedCardAssociation = 'MASTER_CARD';
    } else if (['34', '37'].includes(firstTwoDigits)) {
      detectedCardAssociation = 'AMEX';
    } else if (['62', '81'].includes(firstTwoDigits)) {
      detectedCardAssociation = 'TROY';
    }

    console.log(`Card detection: First digits=${firstTwoDigits}, Detected association=${detectedCardAssociation}`);

    const result = await storeCard(userId.toString(), {
      cardHolderName,
      cardNumber,
      expireMonth,
      expireYear,
      cvc
    });

    if (!result.success) {
      // Log failed attempt
      await createAuditLog(
        userId,
        undefined,
        'created',
        ipAddress,
        { error: result.error, cardAlias },
        false
      );
      
      return res.status(400).json({
        message: language === 'en' ? 'Error storing card' : 'Kart kaydedilirken hata oluştu',
        error: result.error
      });
    }

    // Use API result first, fall back to our detection if needed
    let cardType = result.cardType || detectedCardType;
    let cardAssociation = result.cardAssociation || detectedCardAssociation;
    
    console.log(`Card info - API: type=${result.cardType}, association=${result.cardAssociation}`);
    console.log(`Final card info: type=${cardType}, association=${cardAssociation}`);
    
    // If we still don't have a card association, use a fallback
    if (!cardAssociation || cardAssociation === 'UNKNOWN') {
      cardAssociation = 'MASTER_CARD'; // Default to the most common type as fallback
    }

    // Create the stored card with iyzico response data and determined card info
    const storedCard = new StoredCard({
      userId,
      cardToken: result.cardToken,
      cardUserKey: result.cardUserKey,
      cardAlias: cardAlias || 'My Card',
      binNumber: result.binNumber,
      lastFourDigits: result.lastFourDigits,
      cardType: cardType,
      cardAssociation: cardAssociation,
      cardFamily: result.cardFamily,
      isDefault: isFirstCard, // Set as default if it's the first card
      isActive: true,
      createdIP: ipAddress,
      lastUsedIP: ipAddress,
      userAgent: userAgent
    });

    await storedCard.save();
    
    // Log successful card creation
    await createAuditLog(
      userId,
      storedCard._id.toString(),
      'created',
      ipAddress,
      { 
        cardAlias: storedCard.cardAlias,
        binNumber: storedCard.binNumber,
        lastFourDigits: storedCard.lastFourDigits,
        cardAssociation: storedCard.cardAssociation
      },
      true
    );

    res.status(201).json({
      message: language === 'en' ? 'Card stored successfully' : 'Kart başarıyla kaydedildi',
      card: {...storedCard, cardId: storedCard._id}
    });
  } catch (error: any) {
    console.log(error, "error")
    res.status(500).json({
      message: language === 'en' ? 'Error storing card' : 'Kart kaydedilirken hata oluştu',
      error: error.message
    });
  }
};

export const removeStoredCard = async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const userId = user._id;
  const { cardId } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';
  const ipAddress = getClientIp(req);

  try {
    const card = await StoredCard.findOne({ _id: cardId, userId });
    if (!card) {
      // Log failed attempt
      await createAuditLog(
        userId,
        cardId,
        'deleted',
        ipAddress,
        { reason: 'Card not found' },
        false
      );
      
      return res.status(404).json({
        message: language === 'en' ? 'Card not found' : 'Kart bulunamadı'
      });
    }

    card.isActive = false;
    await card.save();
    
    // Log successful deletion
    await createAuditLog(
      userId,
      card._id.toString(),
      'deleted',
      ipAddress,
      { 
        cardAlias: card.cardAlias,
        lastFourDigits: card.lastFourDigits
      },
      true
    );

    res.status(200).json({
      message: language === 'en' ? 'Card removed successfully' : 'Kart başarıyla silindi'
    });
  } catch (error: any) {
    // Log error
    await createAuditLog(
      userId,
      cardId,
      'deleted',
      ipAddress,
      { error: error.message },
      false
    );
    
    res.status(500).json({
      message: language === 'en' ? 'Error removing card' : 'Kart silinirken hata oluştu',
      error: error.message
    });
  }
};

export const setDefaultCard = async (req: Request, res: Response) => {
  const user = req.user as IUser;
  const userId = user._id;
  const { cardId } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';
  const ipAddress = getClientIp(req);

  try {
    // Remove default status from all cards
    await StoredCard.updateMany(
      { userId, isActive: true },
      { $set: { isDefault: false } }
    );

    // Set the selected card as default
    const card = await StoredCard.findOneAndUpdate(
      { _id: cardId, userId, isActive: true },
      { $set: { isDefault: true } },
      { new: true }
    );

    if (!card) {
      // Log failed attempt
      await createAuditLog(
        userId,
        cardId,
        'set_default',
        ipAddress,
        { reason: 'Card not found' },
        false
      );
      
      return res.status(404).json({
        message: language === 'en' ? 'Card not found' : 'Kart bulunamadı'
      });
    }
    
    // Log successful default change
    await createAuditLog(
      userId,
      card._id.toString(),
      'set_default',
      ipAddress,
      { 
        cardAlias: card.cardAlias,
        lastFourDigits: card.lastFourDigits
      },
      true
    );

    res.status(200).json({
      message: language === 'en' ? 'Default card set successfully' : 'Varsayılan kart başarıyla ayarlandı',
      card
    });
  } catch (error: any) {
    // Log error
    await createAuditLog(
      userId,
      cardId,
      'set_default',
      ipAddress,
      { error: error.message },
      false
    );
    
    res.status(500).json({
      message: language === 'en' ? 'Error setting default card' : 'Varsayılan kart ayarlanırken hata oluştu',
      error: error.message
    });
  }
};
