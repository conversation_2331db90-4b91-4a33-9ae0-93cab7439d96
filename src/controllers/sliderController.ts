import { Request, Response } from 'express';
import { Slider } from '../models/Slider';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

// Configure multer for file upload
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/sliders';
    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Not an image! Please upload an image.'));
  }
};

export const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

/**
 * Get all sliders ordered by their order field
 */
export const getSliders = async (req: Request, res: Response) => {
  try {
    const sliders = await Slider.find().sort({ order: 1 });
    res.json(sliders);
  } catch (error) {
    res.status(500).json({ message: 'Error fetching sliders' });
  }
};

/**
 * Create a new slider with image uploads
 */
export const createSlider = async (req: Request, res: Response) => {
  try {
    const { link, order, header, description, linkText } = req.body;
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };

    if (!files.webImage || !files.mobileImage) {
      return res.status(400).json({ message: 'Both web and mobile images are required' });
    }

    const webImagePath = '/' + files.webImage[0].path.replace(/\\/g, '/');
    const mobileImagePath = '/' + files.mobileImage[0].path.replace(/\\/g, '/');

    const newSlider = new Slider({
      web: webImagePath,
      mobile: mobileImagePath,
      link,
      header,
      description,
      linkText,
      order: order || 0
    });

    await newSlider.save();
    res.status(201).json(newSlider);
  } catch (error) {
    res.status(500).json({ message: 'Error creating slider' });
  }
};

/**
 * Update an existing slider with optional image updates
 */
export const updateSlider = async (req: Request, res: Response) => {
  try {
    const { link, order, header, description, linkText } = req.body;
    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    const updateData: any = { link, order, header, description, linkText };

    // Get the existing slider to handle image updates
    const existingSlider = await Slider.findById(req.params.id);
    if (!existingSlider) {
      return res.status(404).json({ message: 'Slider not found' });
    }

    // Update images if new ones are uploaded
    if (files.webImage) {
      // Delete old web image if it exists
      if (existingSlider.web && existingSlider.web.startsWith('/uploads/')) {
        const oldPath = path.join(__dirname, '..', '..', existingSlider.web);
        if (fs.existsSync(oldPath)) {
          fs.unlinkSync(oldPath);
        }
      }
      updateData.web = '/' + files.webImage[0].path.replace(/\\/g, '/');
    }

    if (files.mobileImage) {
      // Delete old mobile image if it exists
      if (existingSlider.mobile && existingSlider.mobile.startsWith('/uploads/')) {
        const oldPath = path.join(__dirname, '..', '..', existingSlider.mobile);
        if (fs.existsSync(oldPath)) {
          fs.unlinkSync(oldPath);
        }
      }
      updateData.mobile = '/' + files.mobileImage[0].path.replace(/\\/g, '/');
    }

    const slider = await Slider.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true }
    );

    res.json(slider);
  } catch (error) {
    res.status(500).json({ message: 'Error updating slider' });
  }
};

/**
 * Delete a slider and its associated images
 */
export const deleteSlider = async (req: Request, res: Response) => {
  try {
    const slider = await Slider.findById(req.params.id);
    if (!slider) {
      return res.status(404).json({ message: 'Slider not found' });
    }

    // Delete associated images
    if (slider.web && slider.web.startsWith('/uploads/')) {
      const webPath = path.join(__dirname, '..', '..', slider.web);
      if (fs.existsSync(webPath)) {
        fs.unlinkSync(webPath);
      }
    }

    if (slider.mobile && slider.mobile.startsWith('/uploads/')) {
      const mobilePath = path.join(__dirname, '..', '..', slider.mobile);
      if (fs.existsSync(mobilePath)) {
        fs.unlinkSync(mobilePath);
      }
    }

    await slider.deleteOne();
    res.json({ message: 'Slider deleted successfully' });
  } catch (error) {
    res.status(500).json({ message: 'Error deleting slider' });
  }
};
