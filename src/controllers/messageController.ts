import { Request, Response } from 'express';
import { Message } from '../models/Message';
import { User } from '../models/User';
import mongoose from 'mongoose';
import { Subscription } from '../models/Subscription';
import { Item } from '../models/Item';
import { IMessageResponse } from '../interfaces/message.interface';

interface PopulatedMessage {
    _id: mongoose.Types.ObjectId;
    content: string;
    senderId: {
        _id: mongoose.Types.ObjectId;
        firstName: string;
        lastName: string;
    };
    recipientId?: {
        _id: mongoose.Types.ObjectId;
        firstName: string;
        lastName: string;
    };
    productId: {
        _id: mongoose.Types.ObjectId;
        name: string;
        images: string[];
    };
    roomId: string;
    createdAt: Date;
    read: boolean;
}

// Send a message
export const sendMessage = async (req: Request, res: Response) => {
    const { recipientId, content, roomId, productId } = req.body;
    const language = req.headers['accept-language'] as string || 'tr';

    try {
        const senderId = (req as any).user?._id;

        // Validate required fields
        if (!content || !productId) {
            return res.status(400).json({
                message: language === 'en' ? 'Content and productId are required' : 'İçerik ve ürün ID\'si gereklidir'
            });
        }

        // Check if the sender exists
        const sender = await User.findById(senderId);
        if (!sender) {
            return res.status(404).json({
                message: language === 'en' ? 'Sender not found' : 'Gönderen bulunamadı'
            });
        }

        // Check if product exists
        const product = await Item.findById(productId);
        if (!product) {
            return res.status(404).json({
                message: language === 'en' ? 'Product not found' : 'Ürün bulunamadı'
            });
        }

        // If recipientId is provided, check if recipient exists
        let recipient = null;
        if (recipientId) {
            recipient = await User.findById(recipientId);
            if (!recipient) {
                return res.status(404).json({
                    message: language === 'en' ? 'Recipient not found' : 'Alıcı bulunamadı'
                });
            }
        }

        // Check if sender has an active subscription with messaging allowed
        const activeSubscription = await Subscription.findOne({
            userId: senderId,
            endDate: { $gte: new Date() },
            status: 'ACTIVE'
        }).populate('packageId');

        if (!activeSubscription || !activeSubscription.packageId) {
            return res.status(403).json({
                message: language === 'en' ? 'You need an active subscription to send messages' : 'Mesaj göndermek için aktif bir aboneliğe ihtiyacınız var',
                code: 'NO_SUBSCRIPTION'
            });
        }

        // Check if messaging is allowed in the package
        const pkg = activeSubscription.packageId as any;
        const isMessagingAllowed = pkg.messagingAllowed ||
                                 (pkg.features && pkg.features.includes('messaging')) ||
                                 (activeSubscription.features && activeSubscription.features.messagingAllowed);

        if (!isMessagingAllowed) {
            return res.status(403).json({
                message: language === 'en' ? 'Your current package does not include messaging. Please upgrade your package.' : 'Mevcut paketiniz mesajlaşma içermiyor. Lütfen paketinizi yükseltin.',
                code: 'MESSAGING_NOT_ALLOWED'
            });
        }

        // Create and save the message
        const message = new Message({
            content,
            senderId: new mongoose.Types.ObjectId(senderId),
            recipientId: recipientId ? new mongoose.Types.ObjectId(recipientId) : null,
            roomId: roomId || `${senderId}-${productId}`,
            productId: new mongoose.Types.ObjectId(productId),
            createdAt: new Date(),
            timestamp: new Date(),
            read: false
        });

        await message.save();

        // Populate sender and recipient details
        const populatedMessage = await Message.findById(message._id)
            .populate('senderId', 'firstName lastName email phoneNumber _id')
            .populate('recipientId', 'firstName lastName email phoneNumber _id')
            .populate('productId', 'name images _id')
            .lean();

        if (!populatedMessage) {
            throw new Error('Failed to retrieve saved message');
        }

        const response = {
            _id: populatedMessage._id.toString(),
            content: populatedMessage.content,
            senderId: populatedMessage.senderId,
            recipientId: populatedMessage.recipientId,
            productId: populatedMessage.productId,
            roomId: populatedMessage.roomId,
            createdAt: populatedMessage.createdAt,
            timestamp: populatedMessage.timestamp,
            read: populatedMessage.read
        };

        return res.status(201).json(response);

    } catch (error:any) {
        console.error('Error sending message:', error);
        return res.status(500).json({
            message: language === 'en' ? 'Error sending message' : 'Mesaj gönderilirken hata oluştu'
        });
    }
};

// Get all messages for the authenticated user grouped by product and conversation
export const getMessages = async (req: Request, res: Response) => {
    const userId = (req as any).user?._id;
    const language = req.headers['accept-language'] as string || 'tr';

    try {
        console.log('1. User ID:', userId);

        const messages = await Message.find({
            $or: [
                { senderId: userId },
                { recipientId: userId }
            ]
        })
            .populate({
                path: 'senderId',
                select: 'firstName lastName email phoneNumber _id'
            })
            .populate({
                path: 'recipientId',
                select: 'firstName lastName email phoneNumber _id'
            })
            .populate({
                path: 'productId',
                select: 'name images _id'
            })
            .sort({ timestamp: -1 });

        console.log('2. Found messages:', messages.length);

        // Group messages by product
        const productConversations = messages.reduce((acc: any, message: any) => {
            const productId = message.productId?._id?.toString();
            if (!productId || !message.productId) {
                console.log('Skipping message - missing productId:', message._id);
                return acc;
            }

            // Initialize product in accumulator if it doesn't exist
            if (!acc[productId]) {
                acc[productId] = {
                    productId: message.productId._id,
                    productName: message.productId.name,
                    productImage: message.productId.images && message.productId.images.length > 0 ? message.productId.images[0] : '',
                    conversations: {}
                };
            }

            // Get the other user (either sender or recipient)
            const currentUserIsSender = message.senderId?._id?.toString() === userId.toString();
            const otherUser = currentUserIsSender ? message.recipientId : message.senderId;

            // Skip if otherUser is null or missing _id
            if (!otherUser || !otherUser._id) {
                console.log('Skipping message - missing other user:', message._id);
                return acc;
            }

            const otherUserId = otherUser._id.toString();

            // Create a unique conversation key that's the same regardless of sender/recipient order
            const conversationKey = [userId.toString(), otherUserId].sort().join('-');

            // Initialize conversation if it doesn't exist
            if (!acc[productId].conversations[conversationKey]) {
                acc[productId].conversations[conversationKey] = {
                    user: {
                        _id: otherUser._id,
                        firstName: otherUser.firstName || '',
                        lastName: otherUser.lastName || '',
                        email: otherUser.email || '',
                        phoneNumber: otherUser.phoneNumber || ''
                    },
                    messages: []
                };
            }

            // Add message to conversation
            acc[productId].conversations[conversationKey].messages.push({
                _id: message._id,
                content: message.content,
                senderId: message.senderId?._id,
                recipientId: message.recipientId?._id,
                createdAt: message.createdAt,
                read: message.read
            });

            return acc;
        }, {});

        // Convert the conversations object to an array
        const formattedResponse = Object.values(productConversations).map((product: any) => ({
            ...product,
            conversations: Object.values(product.conversations)
        }));

        console.log('3. Formatted response products:', formattedResponse.length);

        res.json(formattedResponse);
    } catch (error) {
        console.error('Error in getMessages:', error);
        res.status(500).json({
            message: language === 'en' ? 'Error getting messages' : 'Mesajlar alınırken hata oluştu',
            error: (error as Error).message
        });
    }
};

// Get conversation between two users for a specific product
export const getConversation = async (req: Request, res: Response) => {
    const language = req.headers['accept-language'] as string || 'tr';
    try {
        const userId = (req as any).user?._id;
        const { otherUserId, productId } = req.params;

        const messages = await Message.find({
            $and: [
                {
                    $or: [
                        { senderId: userId, recipientId: otherUserId },
                        { senderId: otherUserId, recipientId: userId }
                    ]
                },
                { productId: productId }
            ]
        })
            .populate('senderId', 'firstName lastName email')
            .populate('recipientId', 'firstName lastName email')
            .populate('productId', 'name images')
            .sort({ timestamp: 1 });

        res.status(200).json(messages);
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
    }
};

// Delete a message
export const deleteMessage = async (req: Request, res: Response) => {
    const language = req.headers['accept-language'] as string || 'tr';
    try {
        const userId = (req as any).user?._id;
        const { messageId } = req.params;

        const message = await Message.findById(messageId);

        if (!message) {
            return res.status(404).json({ message: language === 'en' ? 'Message not found' : 'Mesaj bulunamadı' });
        }

        if (typeof message.senderId === 'string' ? message.senderId !== userId : message?.senderId?.toString() !== userId.toString()) {
            return res.status(403).json({ message: language === 'en' ? 'Unauthorized to delete this message' : 'Bu mesajı silme yetkiniz yok' });
        }

        await message.deleteOne();
        res.status(200).json({ message: language === 'en' ? 'Message deleted successfully' : 'Mesaj başarıyla silindi' });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
    }
};

// Get messages sent by the authenticated user for a specific product
export const getSentMessages = async (req: Request, res: Response) => {
    const language = req.headers['accept-language'] as string || 'tr';
    try {
        const userId = (req as any).user?._id;
        const { productId } = req.params;

        const messages = await Message.find({
            senderId: userId,
            productId: productId
        })
            .populate('recipientId', 'firstName lastName email')
            .populate('productId', 'name images');

        res.status(200).json(messages);
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
    }
};

// Get messages received by the authenticated user for a specific product
export const getReceivedMessages = async (req: Request, res: Response) => {
    const language = req.headers['accept-language'] as string || 'tr';
    try {
        const userId = (req as any).user?._id;
        const { productId } = req.params;

        const messages = await Message.find({
            recipientId: userId,
            productId: productId
        })
            .populate('senderId', 'firstName lastName email')
            .populate('productId', 'name images');

        res.status(200).json(messages);
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
    }
};
