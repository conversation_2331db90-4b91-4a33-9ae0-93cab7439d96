import { Request, Response } from 'express';
import { Item } from '../models/Item';
import { User } from '../models/User';
import { Store } from '../models/Store';
import jwt from 'jsonwebtoken';
import { IPackage, Package } from '../models/Package';
import { Subscription } from '../models/Subscription';
import { sendPackagePurchaseEmail, sendItemApprovedEmail, sendItemRejectedEmail, sendItemCancelledEmail } from '../services/emailService';
import mongoose from 'mongoose';
import { HomeAd } from '../models/HomeAd';
import logger from '../utils/logger';

// Get Admin Profile
export const getAdminProfile = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    // The user is already attached to the request by the adminMiddleware
    const admin = await User.findById(req.user?._id).select('-password');
    if (!admin) {
      return res.status(404).json({
        message: language === 'en' ? 'Admin profile not found' : 'Yönetici profili bulunamadı'
      });
    }
    res.json(admin);
  } catch (error) {
    res.status(500).json({
      message: language === 'en' ? 'Error retrieving admin profile' : 'Yönetici profili alınırken hata oluştu',
      error
    });
  }
};

// Approve Item (Product or Service)
export const approveItem = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const item = await Item.findByIdAndUpdate(req.params.itemId, { isApproved: true, status: 'ACTIVE' }, { new: true });
    if (!item) {
      return res.status(404).json({ message: language === 'en' ? 'Item not found' : 'Öğe bulunamadı' });
    }

    // Get store and user details for sending email
    try {
      const store = await Store.findById(item.store).populate('owner');
      if (store && store.owner && 'email' in store.owner) {
        const user = store.owner as any;
        logger.info(`Sending item approval email for item: ${item.name}`);
        
        await sendItemApprovedEmail(
          user.email,
          user.firstName || 'Kullanıcı',
          user.lastName || '',
          item.name || 'Ürün',
          item.type || 'Product',
          item._id.toString(),
          language
        );
        
        logger.info(`Item approval email sent successfully to: ${user.email}`);
      }
    } catch (emailError) {
      // Log the error but don't fail the approval
      logger.error('Error sending item approval email:', emailError);
    }

    res.json(item);
  } catch (err) {
    console.log("error",err)
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};

// Reject Item (Product or Service)
export const rejectItem = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { reason } = req.body; // Get rejection reason from request body
    const item = await Item.findByIdAndUpdate(req.params.itemId, { isApproved: false, status: 'INACTIVE' }, { new: true });
    if (!item) {
      return res.status(404).json({ message: language === 'en' ? 'Item not found' : 'Öğe bulunamadı' });
    }

    // Get store and user details for sending email
    try {
      const store = await Store.findById(item.store).populate('owner');
      if (store && store.owner && 'email' in store.owner) {
        const user = store.owner as any;
        logger.info(`Sending item rejection email for item: ${item.name}`);
        
        await sendItemRejectedEmail(
          user.email,
          user.firstName || 'Kullanıcı',
          user.lastName || '',
          item.name || 'Ürün',
          item.type || 'Product',
          reason,
          language
        );
        
        logger.info(`Item rejection email sent successfully to: ${user.email}`);
      }
    } catch (emailError) {
      // Log the error but don't fail the rejection
      logger.error('Error sending item rejection email:', emailError);
    }

    res.json(item);
  } catch (err) {
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};

// Get Dashboard Stats
export const getDashboardStats = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    // Get basic counts
    const users = await User.countDocuments();
    const itemCount = await Item.countDocuments();
    const productCount = await Item.countDocuments({ type: 'Product' });
    const serviceCount = await Item.countDocuments({ type: 'Service' });

    // Get active users (users who have logged in within the last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const activeUsers = users;

    // Get new users (registered in the last 30 days)
    const newUsers = users;

    // Calculate user growth (comparing current month with previous month)
    const sixtyDaysAgo = new Date();
    sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60);
    const previousMonthUsers = await User.countDocuments({
      createdAt: { $gte: sixtyDaysAgo, $lt: thirtyDaysAgo }
    });
    const userGrowth = previousMonthUsers > 0
      ? ((newUsers - previousMonthUsers) / previousMonthUsers) * 100
      : 100;

    // Get user activity data for the last 7 days
    const userActivityData = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const startOfDay = new Date(date.setHours(0, 0, 0, 0));
      const endOfDay = new Date(date.setHours(23, 59, 59, 999));

      const activeCount = await User.countDocuments({
        lastLoginAt: { $gte: startOfDay, $lte: endOfDay }
      });

      const inactiveCount = users - activeCount;

      userActivityData.push({
        date: startOfDay.toISOString().split('T')[0],
        active: activeCount,
        inactive: inactiveCount
      });
    }

    // Get user recruitment data (last 6 months)
    const userRecruitmentData = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const monthlyUsers = await User.countDocuments({
        createdAt: { $gte: startOfMonth, $lte: endOfMonth }
      });

      userRecruitmentData.push({
        month: startOfMonth.toLocaleString('default', { month: 'short' }),
        users: monthlyUsers
      });
    }

    // Get user type distribution
    const userTypeDistribution = [
      {
        type: 'Basic',
        value: await User.countDocuments({ subscriptionType: 'basic' })
      },
      {
        type: 'Premium',
        value: await User.countDocuments({ subscriptionType: 'premium' })
      },
      {
        type: 'Enterprise',
        value: await User.countDocuments({ subscriptionType: 'enterprise' })
      }
    ];

    return res.status(200).json({
      users,
      itemCount,
      productCount,
      serviceCount,
      activeUsers,
      newUsers,
      userGrowth,
      userActivityData,
      userRecruitmentData,
      userTypeDistribution
    });
  } catch (error:any) {
    return res.status(500).json({ message: language === 'en' ? 'Error fetching dashboard stats' : 'Gösterge paneli istatistiklerini alırken hata oluştu', error });
  }
};

// Manage User
export const manageUser = async (req: Request, res: Response) => {
  const { userId, action } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı' });
    }

    switch (action) {
      case 'activate':
        user.isActive = true;
        break;
      case 'deactivate':
        user.isActive = false;
        break;
      case 'delete':
        await user.deleteOne();
        return res.status(200).json({ message: language === 'en' ? 'User deleted successfully' : 'Kullanıcı başarıyla silindi' });
      default:
        return res.status(400).json({ message: language === 'en' ? 'Invalid action' : 'Geçersiz işlem' });
    }

    await user.save();
    return res.status(200).json({ message: language === 'en' ? `User ${action}d successfully` : `Kullanıcı başarıyla ${action === 'activate' ? 'etkinleştirildi' : 'devre dışı bırakıldı'}`, user });
  } catch (error:any) {
    console.log("error",error)
    return res.status(500).json({ message: language === 'en' ? 'Error managing user' : 'Kullanıcı yönetiminde hata', error });
  }
};

// Apply package to user by admin
export const applyPackageToUser = async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { packageId, duration } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  console.log("====== APPLY PACKAGE TO USER FUNCTION CALLED ======");
  console.log("Request URL:", req.originalUrl);
  console.log("Request method:", req.method);
  console.log("Request params:", req.params);
  console.log("Request body:", req.body);
  console.log("Request path:", req.path);
  console.log("Request query:", req.query);
  console.log("===================================================");

  try {
    // Validate input
    if (!userId || !packageId || duration === undefined || duration === null) {
      console.log('Missing required fields:', { userId: !!userId, packageId: !!packageId, duration });
      return res.status(400).json({
        message: language === 'en'
          ? 'User ID, package ID, and duration are required'
          : 'Kullanıcı kimliği, paket kimliği ve süre gereklidir'
      });
    }

    // Ensure duration is a number
    const durationNum = Number(duration);
    if (isNaN(durationNum) || durationNum <= 0) {
      return res.status(400).json({
        message: language === 'en'
          ? 'Duration must be a positive number'
          : 'Süre pozitif bir sayı olmalıdır'
      });
    }

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
      });
    }

    // Check if package exists
    const selectedPackage = await Package.findById(packageId);
    if (!selectedPackage) {
      return res.status(404).json({
        message: language === 'en' ? 'Package not found' : 'Paket bulunamadı'
      });
    }

    // Calculate end date based on duration (in months)
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + durationNum);

    // Check for existing subscription and cancel it if exists
    const existingSubscription = await Subscription.findOne({
      userId: userId,
      status: { $in: ['ACTIVE', 'active'] }
    });

    if (existingSubscription) {
      existingSubscription.status = 'CANCELED';
      await existingSubscription.save();
    }

    // Create new subscription
    const newSubscription = new Subscription({
      userId,
      packageId: selectedPackage._id,
      startDate,
      endDate,
      renewalDate: endDate, // Set renewal date same as end date
      remainingViewRequests: selectedPackage.viewRequestLimit,
      remainingCreateRequests: selectedPackage.createRequestLimit,
      status: 'ACTIVE',
      paymentStatus: 'paid',
      addons: [],
      usageHistory: {
        viewRequestsUsed: 0,
        createRequestsUsed: 0
      },
      features: {
        emailNotification: selectedPackage.emailNotification,
        smsNotification: selectedPackage.smsNotification,
        messagingAllowed: selectedPackage.messagingAllowed,
        homepageAd: selectedPackage.homepageAd,
        languageIntroRights: selectedPackage.languageIntroRights
      }
    });

    await newSubscription.save();

    // Update user with subscription and hasPackage flag
    await User.findByIdAndUpdate(userId, {
      subscription: newSubscription._id,
      hasPackage: true
    });

    // Send package purchase email
    try {
      // Send the email asynchronously but log the result
      console.log(`Sending package assignment email to user: ${user.email}`);
      sendPackagePurchaseEmail(
        user.email,
        user.firstName,
        user.lastName,
        selectedPackage.name,
        0, // Price set to 0 since it's admin assigned
        selectedPackage.currency || 'TRY',
        newSubscription.startDate,
        newSubscription.endDate,
        language
      )
      .then(() => {
        console.log(`Package assignment email sent successfully to ${user.email}`);
      })
      .catch(err => {
        console.error(`Error sending package assignment email to ${user.email}:`, err);
      });
    } catch (emailError) {
      console.error('Error preparing package assignment email:', emailError);
      // Continue with subscription process even if email fails
    }

    return res.status(201).json({
      message: language === 'en'
        ? `Package applied to user successfully for ${duration} month(s)`
        : `Paket kullanıcıya ${duration} ay için başarıyla uygulandı`,
      subscription: newSubscription
    });
  } catch (error: any) {
    console.error("Error applying package to user:", error);
    return res.status(500).json({
      message: language === 'en' ? 'Error applying package to user' : 'Paketi kullanıcıya uygularken hata',
      error: error.message
    });
  }
};

// Get Users
export const getUsers = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const users = await User.find().select('-password'); // Exclude the password field
    res.status(200).json(users.filter((user) => user.role !== 'admin'));
  } catch (error:any) {
    res.status(500).json({ message: language === 'en' ? 'Error retrieving users' : 'Kullanıcıları alırken hata oluştu', error });
  }
};

// Manage Content
export const manageContent = async (req: Request, res: Response) => {
  // Get contentId and action from req.params if they exist, otherwise from req.body
  const contentId = req.params.contentId || req.body.contentId;
  const action = req.params.action || req.body.action;
  const { reason } = req.body; // Get reason for rejection/deletion
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    if (!contentId) {
      return res.status(400).json({ message: language === 'en' ? 'Content ID is required' : 'İçerik kimliği gerekli' });
    }

    if (!action) {
      return res.status(400).json({ message: language === 'en' ? 'Action is required' : 'Eylem gerekli' });
    }

    console.log(`Managing content ID ${contentId} with action ${action}`);

    const item = await Item.findById(contentId);

    if (!item) {
      return res.status(404).json({ message: language === 'en' ? 'Item not found' : 'Öğe bulunamadı' });
    }

    // Get store and user details for sending email
    const sendEmailNotification = async (emailType: 'approve' | 'reject' | 'cancel') => {
      try {
        const store = await Store.findById(item.store).populate('owner');
        if (store && store.owner && 'email' in store.owner) {
          const user = store.owner as any;
          
          switch (emailType) {
            case 'approve':
              logger.info(`Sending item approval email for item: ${item.name}`);
              await sendItemApprovedEmail(
                user.email,
                user.firstName || 'Kullanıcı',
                user.lastName || '',
                item.name || 'Ürün',
                item.type || 'Product',
                item._id.toString(),
                language
              );
              break;
            case 'reject':
              logger.info(`Sending item rejection email for item: ${item.name}`);
              await sendItemRejectedEmail(
                user.email,
                user.firstName || 'Kullanıcı',
                user.lastName || '',
                item.name || 'Ürün',
                item.type || 'Product',
                reason,
                language
              );
              break;
            case 'cancel':
              logger.info(`Sending item cancellation email for item: ${item.name}`);
              await sendItemCancelledEmail(
                user.email,
                user.firstName || 'Kullanıcı',
                user.lastName || '',
                item.name || 'Ürün',
                item.type || 'Product',
                reason,
                language
              );
              break;
          }
          
          logger.info(`Item ${emailType} email sent successfully to: ${user.email}`);
        }
      } catch (emailError) {
        // Log the error but don't fail the action
        logger.error(`Error sending item ${emailType} email:`, emailError);
      }
    };

    switch (action) {
      case 'approve':
        item.isApproved = true;
        item.status = 'ACTIVE';
        await item.save();
        await sendEmailNotification('approve');
        break;
      case 'reject':
        item.isApproved = false;
        item.status = 'INACTIVE';
        await item.save();
        await sendEmailNotification('reject');
        break;
      case 'delete':
        await sendEmailNotification('cancel');
        await item.deleteOne();
        return res.status(200).json({ message: language === 'en' ? 'Item deleted successfully' : 'Öğe başarıyla silindi' });
      default:
        return res.status(400).json({ message: language === 'en' ? 'Invalid action' : 'Geçersiz işlem' });
    }

    return res.status(200).json({ message: language === 'en' ? `Item ${action}d successfully` : `Öğe başarıyla ${action === 'approve' ? 'onaylandı' : 'reddedildi'}`, item });
  } catch (error:any) {
    console.log("error",error)
    return res.status(500).json({ message: language === 'en' ? 'Error managing item' : 'Öğe yönetiminde hata', error: error.message });
  }
};

// Disable Item (Product or Service) - New function for disabling items
export const disableItem = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { reason } = req.body; // Get disable reason from request body
    const item = await Item.findByIdAndUpdate(
      req.params.itemId, 
      { isDisabled: true, status: 'INACTIVE' }, 
      { new: true }
    );
    
    if (!item) {
      return res.status(404).json({ message: language === 'en' ? 'Item not found' : 'Öğe bulunamadı' });
    }

    // Get store and user details for sending email
    try {
      const store = await Store.findById(item.store).populate('owner');
      if (store && store.owner && 'email' in store.owner) {
        const user = store.owner as any;
        logger.info(`Sending item disable email for item: ${item.name}`);
        
        await sendItemCancelledEmail(
          user.email,
          user.firstName || 'Kullanıcı',
          user.lastName || '',
          item.name || 'Ürün',
          item.type || 'Product',
          reason || 'Admin tarafından devre dışı bırakıldı',
          language
        );
        
        logger.info(`Item disable email sent successfully to: ${user.email}`);
      }
    } catch (emailError) {
      // Log the error but don't fail the disable action
      logger.error('Error sending item disable email:', emailError);
    }

    res.json({ 
      message: language === 'en' ? 'Item disabled successfully' : 'Öğe başarıyla devre dışı bırakıldı',
      item 
    });
  } catch (err) {
    console.log("error", err);
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};

// Enable Item (Product or Service) - New function for enabling items
export const enableItem = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const item = await Item.findByIdAndUpdate(
      req.params.itemId, 
      { isDisabled: false, status: 'ACTIVE', isApproved: true }, 
      { new: true }
    );
    
    if (!item) {
      return res.status(404).json({ message: language === 'en' ? 'Item not found' : 'Öğe bulunamadı' });
    }

    // Get store and user details for sending email (reuse approval email)
    try {
      const store = await Store.findById(item.store).populate('owner');
      if (store && store.owner && 'email' in store.owner) {
        const user = store.owner as any;
        logger.info(`Sending item enable email for item: ${item.name}`);
        
        await sendItemApprovedEmail(
          user.email,
          user.firstName || 'Kullanıcı',
          user.lastName || '',
          item.name || 'Ürün',
          item.type || 'Product',
          item._id.toString(),
          language
        );
        
        logger.info(`Item enable email sent successfully to: ${user.email}`);
      }
    } catch (emailError) {
      // Log the error but don't fail the enable action
      logger.error('Error sending item enable email:', emailError);
    }

    res.json({ 
      message: language === 'en' ? 'Item enabled successfully' : 'Öğe başarıyla etkinleştirildi',
      item 
    });
  } catch (err) {
    console.log("error", err);
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};

// Admin Registration
export const registerAdmin = async (req: Request, res: Response) => {
  const { name, email, password } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    let user = await User.findOne({ email });
    if (user) {
      return res.status(400).json({ message: language === 'en' ? 'Admin already exists' : 'Yönetici zaten mevcut' });
    }

    const trimmedPassword = password.trim();

    user = new User({
      name,
      email,
      password: trimmedPassword,
      role: 'admin',
      isActive: true,
    });

    await user.save();

    const payload = { id: user.id, role: user.role };
    const token = jwt.sign(payload, process.env.JWT_SECRET_ADMIN as string, { expiresIn: '1h' });

    res.json({ token });
  } catch (err) {
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası', err });
  }
};

// Admin Login
export const loginAdmin = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { email, password } = req.body;

    // Find admin user with password field explicitly selected
    const user = await User.findOne({ email, role: 'admin' }).select('+password');
    if (!user) {
      return res.status(401).json({
        message: language === 'en' ? 'Invalid email or password' : language === 'de' ? 'Ungültige E-Mail oder Passwort' : 'Geçersiz e-posta veya şifre'
      });
    }

    // Check password
    try {
      const isMatch = await user.comparePassword(password);
      if (!isMatch) {
        return res.status(401).json({
          message: language === 'en' ? 'Invalid email or password' : language === 'de' ? 'Ungültige E-Mail oder Passwort' : 'Geçersiz e-posta veya şifre'
        });
      }
    } catch (passwordError) {
      return res.status(500).json({
        message: language === 'en' ? 'Error verifying credentials' : 'Kimlik bilgileri doğrulanırken hata oluştu'
      });
    }

    // Create token
    const token = jwt.sign(
      { id: user._id, role: 'admin' },
      process.env.JWT_SECRET_ADMIN ?? process.env.JWT_SECRET as jwt.Secret,
      { expiresIn: '1d' }
    );

    // Return success with user data (excluding password)
    const userData = user.toObject();
    delete (userData as any).password;

    res.json({
      success: true,
      message: language === 'en' ? 'Login successful' : 'Giriş başarılı',
      token,
      user: userData
    });
  } catch (error:any) {
    res.status(500).json({
      message: language === 'en' ? 'Server error' : 'Sunucu hatası',
      error: error.message
    });
  }
};

export const getPackages = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const packages = await Package.find().sort('order');
    res.status(200).json(packages);
  } catch (error:any) {
    res.status(500).json({ message: language === 'en' ? 'Error retrieving packages' : 'Paketleri alırken hata oluştu', error });
  }
};

export const updatePackage = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const updatedPackage = await Package.findByIdAndUpdate(req.params.packageId, req.body, { new: true });
    if (!updatedPackage) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }
    res.status(200).json(updatedPackage);
  } catch (error:any) {
    res.status(500).json({ message: language === 'en' ? 'Error updating package' : 'Paket güncellenirken hata oluştu', error });
  }
};

export const deletePackage = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const deletedPackage = await Package.findByIdAndDelete(req.params.packageId);
    if (!deletedPackage) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }
    res.status(200).json({ message: language === 'en' ? 'Package deleted successfully' : 'Paket başarıyla silindi' });
  } catch (error:any) {
    res.status(500).json({ message: language === 'en' ? 'Error deleting package' : 'Paket silinirken hata oluştu', error });
  }
};

export const createPackage = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const packageData: IPackage = req.body;
    const newPackage = new Package(packageData);
    await newPackage.validate(); // This will throw a validation error if the data is incorrect
    await newPackage.save();
    res.status(201).json(newPackage);
  } catch (error: unknown) {
    if (error instanceof Error && error.name === 'ValidationError' && 'errors' in error) {
      const validationErrors = Object.values((error as any).errors).map((err: any) => err.message);
      res.status(400).json({ message: language === 'en' ? 'Validation failed' : 'Doğrulama başarısız oldu', errors: validationErrors });
    } else {
      console.error('Error creating package:', error);
      res.status(500).json({ message: language === 'en' ? 'Error creating package' : 'Paket oluşturulurken hata oluştu', error: error instanceof Error ? error.message : 'Unknown error' });
    }
  }
};

// Get Package By ID
export const getPackageById = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    // Find the package by ID
    const pkg = await Package.findById(req.params.packageId);
    if (!pkg) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }

    // Get subscription statistics for this package
    const subscriptionCount = await User.countDocuments({
      'subscriptions.packageId': pkg._id,
      'subscriptions.status': 'active'
    });

    // Create a response object with package data and statistics
    const response = {
      ...pkg.toObject(),
      statistics: {
        activeSubscriptions: subscriptionCount
      }
    };

    res.status(200).json(response);
  } catch (error: any) {
    console.error('Error retrieving package:', error);
    res.status(500).json({ message: language === 'en' ? 'Error retrieving package' : 'Paket alınırken hata oluştu', error: error.message });
  }
};

export const getHomeAds = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const ads = await HomeAd.find()
      .populate('userId', 'firstName lastName email') // Keep email in populate for potential future use or debugging
      .sort({ createdAt: -1 });

    //return user field as firstName + lastName, others remaning as the same
    const formattedAds = ads.map(ad => {
      const adObject = ad.toObject(); // Convert to plain JS object

      let userName = 'Unknown User'; // Default value
      if (adObject.userId && typeof adObject.userId === 'object') {
        // Check if userId is populated and is an object
        const user = adObject.userId as { firstName?: string, lastName?: string }; // Type assertion
        const firstName = user.firstName || '';
        const lastName = user.lastName || '';
        userName = `${firstName} ${lastName}`.trim() || 'Unknown User'; // Combine names, trim space, provide default
      }

      // Replace the userId object with the formatted userName string
      return {
        ...adObject,
        userId: adObject.userId && typeof adObject.userId === 'object' ? adObject.userId._id : adObject.userId, // Keep original ID if needed elsewhere, or adjust as needed
        userName: userName // Add the formatted user name
      };
    });


    res.status(200).json(formattedAds);
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error retrieving home advertisements' : 'Anasayfa reklamları alınırken hata oluştu',
      error: error.message
    });
  }
};

// Get User Subscriptions
export const getUserSubscriptions = async (req: Request, res: Response) => {
  const { userId } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
      });
    }

    // Get all subscriptions for the user, including expired ones
    const subscriptions = await Subscription.find({ userId })
      .populate('packageId')
      .sort({ startDate: -1 }); // Most recent first

    return res.status(200).json({
      subscriptions
    });
  } catch (error: any) {
    console.error("Error getting user subscriptions:", error);
    return res.status(500).json({
      message: language === 'en' ? 'Error retrieving user subscriptions' : 'Kullanıcı aboneliklerini alırken hata',
      error: error.message
    });
  }
};

/**
 * Get auto-renewal logs
 * @route GET /api/admin/renewals/logs
 */
export const getRenewalLogs = async (req: Request, res: Response) => {
  try {
    const renewalLogSchema = new mongoose.Schema({
      subscriptionId: { type: mongoose.Schema.Types.ObjectId, ref: 'Subscription' },
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      packageId: { type: mongoose.Schema.Types.ObjectId, ref: 'Package' },
      amount: Number,
      status: String,
      newEndDate: Date,
      errorMessage: String,
      timestamp: { type: Date, default: Date.now }
    });

    const RenewalLog = mongoose.models.RenewalLog ||
      mongoose.model('RenewalLog', renewalLogSchema);

    const { page = 1, limit = 50, status } = req.query;
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const query: any = {};
    if (status) {
      query.status = status;
    }

    const logs = await RenewalLog.find(query)
      .sort({ timestamp: -1 })
      .skip(skip)
      .limit(limitNum)
      .populate('userId', 'firstName lastName email')
      .populate('packageId', 'name price')
      .populate('subscriptionId');

    const total = await RenewalLog.countDocuments(query);

    res.json({
      logs,
      totalPages: Math.ceil(total / limitNum),
      currentPage: pageNum,
      total
    });
  } catch (error: any) {
    res.status(500).json({
      message: 'Error fetching renewal logs',
      error: error.message
    });
  }
};

/**
 * Toggle auto-renewal for a subscription
 * @route PUT /api/admin/subscriptions/:subscriptionId/auto-renewal
 */
export const toggleAutoRenewal = async (req: Request, res: Response) => {
  try {
    const { subscriptionId } = req.params;
    const { enableAutoRenewal } = req.body;

    if (typeof enableAutoRenewal !== 'boolean') {
      return res.status(400).json({
        message: 'enableAutoRenewal parameter must be a boolean'
      });
    }

    const subscription = await Subscription.findById(subscriptionId);

    if (!subscription) {
      return res.status(404).json({ message: 'Subscription not found' });
    }

    const updatedSubscription = await Subscription.findByIdAndUpdate(
      subscriptionId,
      { autoRenewal: enableAutoRenewal },
      { new: true }
    );

    res.json({
      message: `Auto-renewal ${enableAutoRenewal ? 'enabled' : 'disabled'} for subscription`,
      subscription: updatedSubscription
    });
  } catch (error: any) {
    res.status(500).json({
      message: 'Error updating auto-renewal setting',
      error: error.message
    });
  }
};
