import { Request, Response } from 'express';
import { HomeAd } from '../models/HomeAd';
import { User } from '../models/User';
import { addDays } from 'date-fns';
import { Subscription } from '../models/Subscription';
import mongoose from 'mongoose';
import { PopulatedSubscription } from '../models/Subscription';
import { Store } from '../models/Store';

// Create a new home ad
export const createHomeAd = async (req: Request, res: Response) => {
  try {
    // Log the request for debugging
    console.log('Creating home ad:', {
      user: req.user?._id,
      title: req.body.title,
      file: req.file ? {
        originalname: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype,
        filename: req.file.filename
      } : null,
      body: req.body
    });

    const userId = req.user?._id;
    const { title } = req.body;

    if (!req.file || !req.file.filename) {
      return res.status(400).json({ message: 'Image is required' });
    }

    if (!title) {
      return res.status(400).json({ message: 'Title is required' });
    }

    const image = "/home-ads/" + req.file.filename;

    // Check if user has the homepageAd addon
    const user: any = await User.findById(userId).select('-password').lean();

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const activeSubscriptionWithAddons = await Subscription.find({
      userId: new mongoose.Types.ObjectId(userId),
      endDate: { $gte: new Date() },
      status: 'ACTIVE',
    })
    .populate<PopulatedSubscription>('packageId')
    .lean();

    // Debug subscription features
    console.log('User subscription data:', {
      userId,
      subscriptions: activeSubscriptionWithAddons.map(sub => ({
        id: sub._id,
        packageId: sub.packageId,
        features: sub.features
      }))
    });

    const hasHomepageAdFeature = activeSubscriptionWithAddons?.some((subscription) => {
      // Check if homepageAd feature exists and is true
      return subscription.features?.homepageAd === true;
    });

    console.log('Has homepage ad feature:', hasHomepageAdFeature);

    if (!hasHomepageAdFeature) {
      return res.status(403).json({ message: 'Homepage ad addon is required' });
    }

    const existingAd = await HomeAd.findOne({ userId });
    if (existingAd) {
      return res.status(400).json({ message: 'You already have an active homepage ad' });
    }

    // Create new ad with 30-day expiration
    const ad = new HomeAd({
      userId,
      title,
      image,
      status: 'pending',
      clicks: 0,
      expiresAt: addDays(new Date(), 30),
      createdAt: new Date()
    });

    await ad.save();

    res.status(201).json(ad);
  } catch (error) {
    console.error('Error creating home ad:', error);
    res.status(500).json({ message: 'Failed to create home ad' });
  }
};

// Get user's home ads
export const getUserHomeAds = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const ads = await HomeAd.find({ userId }).sort({ createdAt: -1 });
    res.json(ads);
  } catch (error) {
    console.error('Error fetching user home ads:', error);
    res.status(500).json({ message: 'Failed to fetch home ads' });
  }
};

// Get active home ads
export const getActiveHomeAds = async (req: Request, res: Response) => {
  try {
    const ads = await HomeAd.find({
      status: 'approved',
      expiresAt: { $gt: new Date() },
    })
    .populate('userId')
    .sort({ createdAt: -1 });

    // Find stores for each ad's user
    const adsWithStores = await Promise.all(ads.map(async (ad) => {
      const store = await Store.findOne({ owner: ad.userId });
      console.log(store?._id);
      return {
        ...ad.toObject(),
        storeId: store?._id
      };
    }));

    res.json(adsWithStores);
  } catch (error) {
    console.error('Error fetching active home ads:', error);
    res.status(500).json({ message: 'Failed to fetch active home ads' });
  }
};

export const getPendingHomeAds = async (req: Request, res: Response) => {
  try {
    const userId = req.user?._id;
    const ads = await HomeAd.find({ userId, status: 'pending' }).sort({ createdAt: -1 });
    res.json(ads);
  } catch (error) {
    console.error('Error fetching pending home ads:', error);
    res.status(500).json({ message: 'Failed to fetch pending home ads' });
  }
};

// Get all home ads (admin)
export const getAdminHomeAds = async (req: Request, res: Response) => {
  try {
    console.log('Getting admin home ads, admin user:', req.user?._id);

    // Use explicit filtering for HomeAd collection only
    const adsFromHomeAdCollection = await HomeAd.find()
      .populate('userId', 'firstName lastName email')
      .sort({ createdAt: -1 });

    console.log(`Found ${adsFromHomeAdCollection.length} home ads for admin view`);

    if (adsFromHomeAdCollection.length === 0) {
      // Add a check to make sure there are actually ads in the database
      const totalAdsCount = await HomeAd.countDocuments();
      console.log(`Total ads in database: ${totalAdsCount}`);
    }

    // Format each ad with user information
    const formattedAds = adsFromHomeAdCollection.map((ad) => {
      // Convert Mongoose document to plain object and type safely
      // We use 'as any' for some parts to work around TypeScript constraints
      // with Mongoose's dynamic typing
      const adObject: any = ad.toObject();

      // Handle populated user info with type safety
      // If userId is populated it will be an object with user details, otherwise just an ID
      const populatedUser = typeof adObject.userId === 'object' && adObject.userId !== null;

      // Get user information if userId is populated
      const firstName = populatedUser ? adObject.userId.firstName || 'Unknown' : 'Unknown';
      const lastName = populatedUser ? adObject.userId.lastName || 'User' : 'User';
      const email = populatedUser ? adObject.userId.email || '' : '';

      // Create a formatted userName that includes email if available
      const userNameWithEmail = email
        ? `${firstName} ${lastName} (${email})`
        : `${firstName} ${lastName}`;

      // Construct a clean response with consistent structure matching frontend expectations
      const formattedAd: Record<string, any> = {
        _id: adObject._id.toString(),
        userId: populatedUser
          ? adObject.userId._id.toString()
          : adObject.userId.toString(),
        title: adObject.title,
        image: adObject.image,
        clicks: adObject.clicks,
        status: adObject.status,
        expiresAt: adObject.expiresAt,
        createdAt: adObject.createdAt,
        userName: userNameWithEmail,
      };

      // Add optional fields only if they exist
      if (adObject.updatedAt) {
        formattedAd.updatedAt = adObject.updatedAt;
      }
      if (adObject.rejectionReason) {
        formattedAd.rejectionReason = adObject.rejectionReason;
      }

      return formattedAd;
    });

    console.log('Sending formatted ads to admin client');
    res.json(formattedAds);
  } catch (error) {
    console.error('Error fetching admin home ads:', error);
    res.status(500).json({ message: 'Failed to fetch home ads' });
  }
};

// Admin update home ad status (approve/reject)
export const updateHomeAdStatusByAdmin = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { id, status } = req.params;
    const { rejectionReason, reason } = req.body;
    console.log("Params:", req.params, "Body:", req.body);

    // Determine the actual status from the path parameter
    // Handle both /home-ads/:id/:status and /home-ads/:id/approve or /home-ads/:id/reject formats
    let actualStatus: string;
    
    if (status === 'approve') {
      actualStatus = 'approved';
    } else if (status === 'reject') {
      actualStatus = 'rejected';
    } else {
      actualStatus = status;
    }

    if (!['approved', 'rejected'].includes(actualStatus)) {
      return res.status(400).json({ message: language === 'en' ? 'Invalid status value.' : 'Geçersiz durum değeri.' });
    }

    const updateData: any = { status: actualStatus };
    
    // Support both "rejectionReason" and "reason" fields for rejection reason
    if (actualStatus === 'rejected') {
      updateData.rejectionReason = rejectionReason || reason || (language === 'en' ? 'No reason provided.' : 'Sebep belirtilmedi.');
    } else {
      // Clear rejectionReason if approved
      updateData.rejectionReason = undefined;
    }

    const ad = await HomeAd.findByIdAndUpdate(id, updateData, { new: true });

    if (!ad) {
      return res.status(404).json({ message: language === 'en' ? 'Home ad not found.' : 'Anasayfa reklamı bulunamadı.' });
    }

    // TODO: Consider sending notifications to the user upon approval/rejection

    res.json({ message: language === 'en' ? `Home ad status updated to ${actualStatus}.` : `Anasayfa reklam durumu ${actualStatus} olarak güncellendi.`, ad });
  } catch (error) {
    console.error('Error updating home ad status:', error);
    res.status(500).json({ message: language === 'en' ? 'Failed to update home ad status.' : 'Anasayfa reklam durumu güncellenemedi.' });
  }
};

// Approve home ad
export const approveHomeAd = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const ad = await HomeAd.findById(id);

    if (!ad) {
      return res.status(404).json({ message: 'Ad not found' });
    }

    ad.status = 'approved';
    await ad.save();

    res.json(ad);
  } catch (error) {
    console.error('Error approving home ad:', error);
    res.status(500).json({ message: 'Failed to approve home ad' });
  }
};

// Reject home ad
export const rejectHomeAd = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;
    const ad = await HomeAd.findById(id);

    if (!ad) {
      return res.status(404).json({ message: 'Ad not found' });
    }

    ad.status = 'rejected';
    ad.rejectionReason = reason;
    await ad.save();

    res.json(ad);
  } catch (error) {
    console.error('Error rejecting home ad:', error);
    res.status(500).json({ message: 'Failed to reject home ad' });
  }
};

// Reactivate expired ad
export const reactivateHomeAd = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?._id;

    const ad = await HomeAd.findOne({ _id: id, userId });
    if (!ad) {
      return res.status(404).json({ message: 'Ad not found' });
    }

    // Check if user still has the addon
    const user: any = await User.findById(userId);
    if (!user?.addons?.includes('homepageAd')) {
      return res.status(403).json({ message: 'Homepage ad addon is required' });
    }

    ad.status = 'pending';
    ad.expiresAt = addDays(new Date(), 30);
    await ad.save();

    res.json(ad);
  } catch (error) {
    console.error('Error reactivating home ad:', error);
    res.status(500).json({ message: 'Failed to reactivate home ad' });
  }
};

// Increment ad clicks
export const incrementAdClicks = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const ad = await HomeAd.findById(id);

    if (!ad) {
      return res.status(404).json({ message: 'Ad not found' });
    }

    ad.clicks += 1;
    await ad.save();

    res.json({ clicks: ad.clicks });
  } catch (error) {
    console.error('Error incrementing ad clicks:', error);
    res.status(500).json({ message: 'Failed to increment ad clicks' });
  }
};

// Delete home ad (for users to delete their own ads)
export const deleteHomeAd = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?._id;

    // Find the ad and check if it belongs to the user
    const ad = await HomeAd.findOne({ _id: id, userId });

    if (!ad) {
      return res.status(404).json({ message: 'Ad not found or you do not have permission to delete it' });
    }

    // Delete the ad
    await HomeAd.findByIdAndDelete(id);

    res.status(200).json({ success: true, message: 'Ad deleted successfully' });
  } catch (error) {
    console.error('Error deleting home ad:', error);
    res.status(500).json({ message: 'Failed to delete home ad' });
  }
};

// Admin delete home ad (for admin to delete any ad)
export const adminDeleteHomeAd = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { id } = req.params;

    // Find the ad without user restriction
    const ad = await HomeAd.findById(id);

    if (!ad) {
      return res.status(404).json({ message: language === 'en' ? 'Ad not found.' : 'Reklam bulunamadı.' });
    }

    // Delete the ad
    await HomeAd.findByIdAndDelete(id);

    res.status(200).json({ success: true, message: language === 'en' ? 'Ad deleted successfully by admin.' : 'Reklam başarıyla silindi.' });
  } catch (error) {
    console.error('Error deleting home ad by admin:', error);
    res.status(500).json({ message: language === 'en' ? 'Failed to delete home ad.' : 'Reklam silinemedi.' });
  }
};
