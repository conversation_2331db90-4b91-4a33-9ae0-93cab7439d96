import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { createClient, Client, WSDL } from 'soap';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';
import { Country } from '../models/Country';
import { City } from '../models/City';
import { sendPasswordChangeSuccessEmail, sendValidationEmail, sendWelcomeEmail } from '../services/emailService';
import { sendEmailNotificationToStores } from './userController';
import { generateReferralCode } from '../utils/referralCodeGenerator';
import { Category } from '../models/Category';

// WSDL params is definition, uri, options
const WSDL_URL = "https://tckimlik.nvi.gov.tr/Service/KPSPublic.asmx?WSDL";

function turkishToUpper(str: string): string {
  return str
    .replace('ü', 'Ü').replace('i', 'İ').replace('ğ', 'Ğ')
    .replace('ş', 'Ş').replace('ç', 'Ç').replace('ö', 'Ö')
    .replace('ı', 'I')
    .toUpperCase();
}
async function tckn_approval(tcno: string, name: string, surname: string, birthyear: string): Promise<boolean> {
  let client: Client;

  try {
    client = await new Promise<Client>((resolve, reject) => {
      createClient(WSDL_URL, (err, soapClient) => {
        if (err) {
          return reject(err);
        }
        resolve(soapClient);
      });
    });
  } catch (error:any) {
    console.error('Error creating SOAP client:', error);
    throw error;
  }

  const args = {
    TCKimlikNo: tcno,
    Ad: turkishToUpper(name),
    Soyad: turkishToUpper(surname),
    DogumYili: birthyear
  };

  try {
    const result = await client.TCKimlikNoDogrulaAsync(args);
    return result[0].TCKimlikNoDogrulaResult;
  } catch (error:any) {
    console.error('TCKN approval error:', error);
    return false;
  }
}

export const registerUser = async (req: Request, res: Response) => {
  // Extract data from both URL query parameters and request body
  const queryParams = req.query;
  const {
    firstName,
    lastName,
    birthDate,
    phoneNumber,
    email,
    password,
    address,
    city,
    country,
    zipCode,
    categoryLevel1Id: bodyCategoryLevel1Id,
    categoryLevel2Id: bodyCategoryLevel2Id,
    categoryLevel3Id: bodyCategoryLevel3Id,
    referralCode
  } = req.body;

  // Prioritize query parameters if they exist, otherwise use body parameters
  const categoryLevel1Id = queryParams.categoryLevel1Id?.toString() || bodyCategoryLevel1Id;
  const categoryLevel2Id = queryParams.categoryLevel2Id?.toString() || bodyCategoryLevel2Id;
  const categoryLevel3Id = queryParams.categoryLevel3Id?.toString() || bodyCategoryLevel3Id;

  // Log the extracted category IDs for debugging
  console.log('Registration categories:', { categoryLevel1Id, categoryLevel2Id, categoryLevel3Id });
  console.log('Query parameters:', req.query);

  const language = req.headers['accept-language'] as string || 'tr';
  try {
    let user = await User.findOne({ email });
    if (user) {
      return res.status(200).json({ success: false, message: language === 'en' ? 'User already exists' : 'Kullanıcı zaten mevcut' });
    }

    // Find if a referring user exists
    let referringUser = null;
    if (referralCode) {
      referringUser = await User.findOne({ referralCode });
    }

    // Generate a unique referral code for the new user
    const userReferralCode = await generateReferralCode();

    // The frontend is directly sending MongoDB ObjectIds, not string IDs
    // We'll use these directly rather than looking them up
    console.log('Category IDs received from request:', {
      categoryLevel1Id,
      categoryLevel2Id,
      categoryLevel3Id
    });

    // Check if the IDs are valid MongoDB ObjectIds
    const isValidObjectId = (id: string) => {
      return mongoose.Types.ObjectId.isValid(id);
    };

    // Use the IDs directly if they're valid ObjectIds
    const categoryLevel1ObjectId = categoryLevel1Id && isValidObjectId(categoryLevel1Id) ? categoryLevel1Id : undefined;
    const categoryLevel2ObjectId = categoryLevel2Id && isValidObjectId(categoryLevel2Id) ? categoryLevel2Id : undefined;
    const categoryLevel3ObjectId = categoryLevel3Id && isValidObjectId(categoryLevel3Id) ? categoryLevel3Id : undefined;

    console.log('Category ObjectIds to be saved:', {
      categoryLevel1ObjectId,
      categoryLevel2ObjectId,
      categoryLevel3ObjectId
    });

    // Create new user
    const newUser = new User({
      firstName,
      lastName,
      email,
      password,
      birthDate,
      phoneNumber,
      address,
      city,
      country,
      zipCode,
      categoryLevel1Id: categoryLevel1ObjectId,
      categoryLevel2Id: categoryLevel2ObjectId,
      categoryLevel3Id: categoryLevel3ObjectId,
      referralCode: userReferralCode,
      referredBy: referringUser ? referringUser._id : null,
      ipAddress: req.ip || req.socket.remoteAddress || '127.0.0.1',
    });

    // Set referral relationship if referral code was provided
    if (referringUser) {
      referringUser?.referrals?.push({
        _id: newUser._id,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        email: newUser.email,
        createdAt: new Date().toISOString(),
        hasPackage: false
      });
      await referringUser.save();
    }

    await newUser.save();

    const payload = { id: newUser.id, role: newUser.role };
    const token = jwt.sign(payload, process.env.JWT_SECRET_USER as string, { expiresIn: '1h' });

    // Collect all category IDs (filtering out undefined/null values)
    const categoryIds = [categoryLevel1Id, categoryLevel2Id, categoryLevel3Id].filter(Boolean);

    console.log('Categories for email notification:', categoryIds);

    // Send welcome email to the new user
    console.log(`Sending welcome email to new user: ${firstName} ${lastName} <${email}>`);
    const welcomeEmailResult = await sendWelcomeEmail(email, firstName, lastName, language);

    if (welcomeEmailResult) {
      console.log(`Welcome email sent successfully to: ${email} (Message ID: ${welcomeEmailResult.messageId})`);
    } else {
      console.warn(`Welcome email could not be sent to: ${email}, but user registration will continue`);
    }

    // Start email notifications asynchronously to stores only if we have valid category IDs
    if (categoryIds.length > 0) {
      console.log(`Sending notification emails to stores for categories: ${categoryIds.join(', ')}`);
      try {
        // We want this to be non-blocking, so use Promise.resolve to avoid waiting
        // This ensures the registration completes even if store notifications are slow
        Promise.resolve().then(async () => {
          try {
            await sendEmailNotificationToStores(categoryIds, newUser);
            console.log('Store notification emails sent successfully');
          } catch (storeEmailError) {
            console.error('Error sending email notifications to stores:', storeEmailError);
          }
        });
      } catch (error) {
        console.error('Error initiating store notification process:', error);
      }
    } else {
      console.log('No category IDs provided, skipping store notifications');
    }

    res.json({ token, success: true });
  } catch (err: any) {
    if (err.code === 11000 && err.keyPattern && err.keyPattern.email) {
      return res.status(200).json({
        success: false,
        message: language === 'en'
          ? 'A user with this email already exists.'
          : 'Bu e-posta adresine sahip bir kullanıcı zaten mevcut.'
      });
    }
    console.log(err)
    res.status(500).json({
      success: false,
      message: language === 'en' ? 'Server error' : 'Sunucu hatası'
    });
  }
};

// User Login
export const loginUser = async (req: Request, res: Response) => {
  const { email, password } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const user = await User.findOne({ email, role: 'user' }).select('+password');
    if (!user) {
      return res.status(400).json({
        success: false,
        message: language === 'en' ? 'Invalid credentials' : 'Geçersiz kimlik bilgileri'
      });
    }

    try {
      const isMatch = await user.comparePassword(password);
      if (!isMatch) {
        return res.status(400).json({
          success: false,
          message: language === 'en' ? 'Invalid credentials' : 'Geçersiz kimlik bilgileri'
        });
      }
    } catch (passwordError) {
      console.error('Password comparison error:', passwordError);
      return res.status(500).json({
        success: false,
        message: language === 'en' ? 'Error verifying credentials' : 'Kimlik bilgileri doğrulanırken hata oluştu'
      });
    }

    const payload = { id: user.id, role: user.role };
    const token = jwt.sign(payload, process.env.JWT_SECRET_USER as string, { expiresIn: '1d' });

    // Return user data without sensitive information
    const { password: _, ...userWithoutPassword } = user.toObject();
    res.json({
      success: true,
      token,
      user: userWithoutPassword
    });
  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({
      success: false,
      message: language === 'en' ? 'Server error' : 'Sunucu hatası'
    });
  }
};

export const getCountries = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const turkeyCountry = await Country.findOne({ code: '90' }); // Find Turkey by country code
    const otherCountries = await Country.find({ code: { $ne: '90' } }).sort('name');
    const countries = turkeyCountry ? [turkeyCountry, ...otherCountries] : otherCountries;
    res.json(countries);
  } catch (err) {
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};

export const getCities = async (req: Request, res: Response) => {
  const { countryCode } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const country = await Country.findOne({ code: countryCode });
    if (!country) {
      return res.status(404).json({ message: language === 'en' ? 'Country not found' : 'Ülke bulunamadı' });
    }
    const cities = await City.find({ country: country._id }).sort('name');
    res.json(cities);
  } catch (err) {
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};

export const forgotPassword = async (req: Request, res: Response) => {
  const { email } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı' });
    }
    const resetToken = crypto.randomUUID();
    user.resetPasswordToken = resetToken;
    user.resetPasswordExpires = new Date(Date.now() + 600000); // 10 minutes
    await user.save();

    const validationLink = `${process.env.APP_URL}/reset-password?token=${resetToken}`;

    try {
      await sendValidationEmail(email, validationLink, language);
      res.status(200).json({ message: language === 'en' ? 'Password reset email sent' : 'Şifre sıfırlama e-postası gönderildi' });
    } catch (emailError) {
      console.error('Error sending validation email:', emailError);
      // Revert the user changes if email sending fails
      user.resetPasswordToken = undefined;
      user.resetPasswordExpires = undefined;
      await user.save();
      res.status(500).json({ message: language === 'en' ? 'Failed to send reset email' : 'Sıfırlama e-postası gönderilemedi' });
    }
  } catch (err) {
    console.error('Error in forgotPassword:', err);
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};
export const resetPassword = async (req: Request, res: Response) => {
  const { token, password } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const user = await User.findOne({ resetPasswordToken: token });
    if (!user) {
      return res.status(404).json({ message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı' });
    }
    user.password = password;
    user.resetPasswordToken = undefined;
    await user.save();

    // Send password change success email
    await sendPasswordChangeSuccessEmail(user.email, language);

    res.status(200).json({ message: language === 'en' ? 'Password reset successful' : 'Şifre başarıyla sıfırlandı' });
  } catch (err) {
    console.error('Error in resetPassword:', err);
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};
