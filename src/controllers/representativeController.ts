import { Request, Response } from 'express';
import Representative from '../models/Representative';
import { Country } from '../models/Country';
import { City } from '../models/City';
import bcrypt from 'bcryptjs';

// Get all active representatives
export const getActiveRepresentatives = async (req: Request, res: Response) => {
  try {
    const representatives = await Representative.find({ isActive: true })
      .select('-password')
      .sort({ createdAt: -1 });

    // Get all countries and cities
    const countries = await Country.find();
    const cities = await City.find();

    // Create lookup maps for faster access
    const countryMap = new Map(countries.map(c => [c.code, c.name]));
    const cityMap = new Map(cities.map(c => [c.city_id, c.name]));

    // Add country and city names to representatives using the maps
    const representativesWithNames = representatives.map(rep => {
      const repObj:any = rep.toObject();

      // Use the lookup maps to find country and city names
      const countryName = countryMap.get(repObj.country?.code || repObj.country);
      const cityName = cityMap.get(repObj.city?.city_id || repObj.city);
      console.log(countryName, cityName)
      return {
        ...repObj,
        countryName: countryName || undefined,
        cityName: cityName || undefined
      };
    });

    res.status(200).json({
      success: true,
      data: representativesWithNames
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

export const getAllRepresentatives = async (req: Request, res: Response) => {
  try {
    const representatives = await Representative.find()
      .select('-password')
      .sort({ createdAt: -1 });

    // Get all countries and cities
    const countries = await Country.find();
    const cities = await City.find();

    // Create lookup maps for faster access
    const countryMap = new Map(countries.map(c => [c.code, c.name]));
    const cityMap = new Map(cities.map(c => [c.city_id, c.name]));

    // Add country and city names to representatives using the maps
    const representativesWithNames = representatives.map(rep => {
      const repObj = rep.toObject();

      // Use the lookup maps to find country and city names
      const countryName = countryMap.get(repObj.country);
      const cityName = cityMap.get(repObj.city);

      return {
        ...repObj,
        countryName: countryName || undefined,
        cityName: cityName || undefined
      };
    });

    res.status(200).json({
      success: true,
      data: representativesWithNames
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Get representative by ID
export const getRepresentativeById = async (req: Request, res: Response) => {
  try {
    const representative = await Representative.findById(req.params.id).select('-password');

    if (!representative) {
      return res.status(404).json({
        success: false,
        error: 'Representative not found'
      });
    }

    // Get country and city names
    const repObj = representative.toObject();
    let countryName;
    let cityName;

    if (repObj.country) {
      const country = await Country.findOne({ code: repObj.country });
      if (country) {
        countryName = country.name;
      }
    }

    if (repObj.city) {
      const city = await City.findOne({ city_id: repObj.city });
      if (city) {
        cityName = city.name;
      }
    }

    // Add country and city names to the representative object
    const representativeWithNames = {
      ...repObj,
      countryName: countryName || undefined,
      cityName: cityName || undefined
    };

    res.status(200).json({
      success: true,
      data: representativeWithNames
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Create new representative
export const createRepresentative = async (req: Request, res: Response) => {
  try {
    const representativeData = req.body;
    const file = req.file;

    if (file) {
      representativeData.profilePicture = file.path.replace(/\\/g, '/').replace('uploads/', '');
    }

    const representative = new Representative(representativeData);
    await representative.save();

    // Remove password from response
    const representativeResponse = representative.toObject();

    res.status(201).json({
      success: true,
      data: representativeResponse
    });
  } catch (error: any) {
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        error: 'Email already exists'
      });
    }

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Update representative
export const updateRepresentative = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    console.log('Received update data:', updateData);

    const representative = await Representative.findByIdAndUpdate(
      id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    if (!representative) {
      return res.status(404).json({
        success: false,
        error: 'Representative not found'
      });
    }

    res.status(200).json({
      success: true,
      data: representative
    });
  } catch (error: any) {
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        error: 'Email already exists'
      });
    }

    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};

// Delete representative (soft delete by setting isActive to false)
export const deleteRepresentative = async (req: Request, res: Response) => {
  try {
    const representative = await Representative.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    ).select('-password');

    if (!representative) {
      return res.status(404).json({
        success: false,
        error: 'Representative not found'
      });
    }

    res.status(200).json({
      success: true,
      data: representative
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
};
