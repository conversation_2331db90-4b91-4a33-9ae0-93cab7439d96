import { Request, Response } from 'express';
import { CardAuditLog } from '../models/CardAuditLog';
import { StoredCard } from '../models/StoredCard';
import { User } from '../models/User';

export const getCardAuditLogs = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const { userId, cardId, action, startDate, endDate, page = 1, limit = 50 } = req.query;
  
  try {
    const query: any = {};
    
    if (userId) query.userId = userId;
    if (cardId) query.cardId = cardId;
    if (action) query.action = action;
    
    if (startDate || endDate) {
      query.timestamp = {};
      if (startDate) query.timestamp.$gte = new Date(startDate as string);
      if (endDate) query.timestamp.$lte = new Date(endDate as string);
    }
    
    const skip = (Number(page) - 1) * Number(limit);
    
    const [logs, total] = await Promise.all([
      CardAuditLog.find(query)
        .populate('userId', 'firstName lastName email')
        .populate('cardId', 'cardAlias lastFourDigits')
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(Number(limit))
        .lean(),
      CardAuditLog.countDocuments(query)
    ]);
    
    res.json({
      logs,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching audit logs' : 'Denetim kayıtları alınırken hata oluştu',
      error: error.message
    });
  }
};

export const getSuspiciousCards = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  
  try {
    // Find cards with high failed attempts or suspicious activity
    const suspiciousCards = await StoredCard.find({
      $or: [
        { failedAttempts: { $gt: 5 } },
        { 'suspiciousActivity.ipMismatch': true },
        { 'suspiciousActivity.unusualTime': true },
        { 'suspiciousActivity.highFrequency': true }
      ],
      isActive: true
    })
    .populate('userId', 'firstName lastName email')
    .lean();
    
    // Get recent failed payment attempts
    const recentFailures = await CardAuditLog.find({
      action: 'failed_payment',
      timestamp: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
    })
    .populate('userId', 'firstName lastName email')
    .populate('cardId', 'cardAlias lastFourDigits')
    .sort({ timestamp: -1 })
    .limit(100)
    .lean();
    
    res.json({
      suspiciousCards,
      recentFailures,
      summary: {
        totalSuspiciousCards: suspiciousCards.length,
        totalRecentFailures: recentFailures.length
      }
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching suspicious cards' : 'Şüpheli kartlar alınırken hata oluştu',
      error: error.message
    });
  }
};

export const getCardUsageStats = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const { startDate, endDate } = req.query;
  
  try {
    const dateQuery: any = {};
    if (startDate || endDate) {
      dateQuery.timestamp = {};
      if (startDate) dateQuery.timestamp.$gte = new Date(startDate as string);
      if (endDate) dateQuery.timestamp.$lte = new Date(endDate as string);
    }
    
    // Aggregate stats by action type
    const stats = await CardAuditLog.aggregate([
      { $match: dateQuery },
      {
        $group: {
          _id: '$action',
          count: { $sum: 1 },
          successCount: { $sum: { $cond: ['$success', 1, 0] } },
          failureCount: { $sum: { $cond: ['$success', 0, 1] } }
        }
      },
      { $sort: { count: -1 } }
    ]);
    
    // Get top users by card operations
    const topUsers = await CardAuditLog.aggregate([
      { $match: dateQuery },
      {
        $group: {
          _id: '$userId',
          totalOperations: { $sum: 1 },
          failedOperations: { $sum: { $cond: ['$success', 0, 1] } }
        }
      },
      { $sort: { totalOperations: -1 } },
      { $limit: 10 },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      { $unwind: '$user' },
      {
        $project: {
          userId: '$_id',
          totalOperations: 1,
          failedOperations: 1,
          failureRate: { $divide: ['$failedOperations', '$totalOperations'] },
          'user.firstName': 1,
          'user.lastName': 1,
          'user.email': 1
        }
      }
    ]);
    
    res.json({
      stats,
      topUsers,
      dateRange: {
        startDate: startDate || 'All time',
        endDate: endDate || 'Present'
      }
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching usage stats' : 'Kullanım istatistikleri alınırken hata oluştu',
      error: error.message
    });
  }
};