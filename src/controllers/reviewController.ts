import { Request, Response } from 'express';
import { Review } from '../models/Review';

export const createReview = async (req: Request, res: Response) => {
  const { content, rating, productId } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const review = new Review({ content, rating, userId: req.user?._id, productId });
    await review.save();
    res.status(201).json(review);
  } catch (err) {
    res.status(500).json({ message: language === 'en' ? 'Server error' : 'Sunucu hatası' });
  }
};

export const getReviews = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const reviews = await Review.find({ productId: req.params.productId });
    res.json(reviews);
  } catch (err) {
    res.status(500).json({ message: language === 'en' ? 'Server error' : '<PERSON><PERSON><PERSON> hatas<PERSON>' });
  }
};
