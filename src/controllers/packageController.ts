import { Request, Response } from 'express';
import { Package, IPackage } from '../models/Package';
import { ISubscription, Subscription } from '../models/Subscription';
import mongoose, { Document, Types } from 'mongoose';
import { User } from '../models/User';
import { Payment } from '../models/Payment';
import { initiateCheckoutPayment, initiateSubscription, storeCard, makePaymentWithStoredCard, initiatePayment } from '../services/iyzicoPaymentService';
import { toNumber } from 'lodash';
import jwt from 'jsonwebtoken';

interface ISubscriptionBase {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  packageId: IPackageDocument;
  startDate: Date;
  endDate: Date;
  status: SubscriptionStatus;
  paymentStatus: string;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  parentSubscriptionId?: Types.ObjectId;
}

interface ILeanSubscription {
  _id: string;
  userId: string;
  packageId: IPackageDocument;
  parentSubscriptionId?: string;
  startDate: Date;
  endDate: Date;
  status: SubscriptionStatus;
  paymentStatus: string;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  features: {
    emailNotification: boolean;
    smsNotification: boolean;
    messagingAllowed: boolean;
    homepageAd: boolean;
    languageIntroRights: number;
  };
}

interface IAddon {
  packageId: IPackageDocument;
  status: string;
}

interface ISubscriptionWithAddons extends Omit<ILeanSubscription, 'packageId'> {
  packageId: IPackageDocument;
  remainingDays?: number;
  addons?: IAddon[];
}

interface ISubscriptionPopulated {
  _id: Types.ObjectId;
  packageId: {
    _id: Types.ObjectId;
    price: number;
    duration: number;
    type: string;
    [key: string]: any;
  };
  endDate: Date;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  status: string;
  paymentStatus: string;
  parentSubscriptionId?: Types.ObjectId;
}

interface IPackageDocument extends Document {
  _id: Types.ObjectId;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  type: string;
  viewRequestLimit: number;
  createRequestLimit: number;
  features: string[];
  duration: number;
  currency: string;
  emailNotification: boolean;
  smsNotification: boolean;
  messagingAllowed: boolean;
  homepageAd: boolean;
  languageIntroRights: number;
}

type SubscriptionStatus = 'ACTIVE' | 'EXPIRED' | 'UNPAID' | 'CANCELED' | 'PENDING' | 'UPGRADED' | 'FAILED';

interface ISubscriptionDocument extends Document {
  _id: Types.ObjectId;
  userId: Types.ObjectId;
  packageId: IPackageDocument;
  startDate: Date;
  endDate: Date;
  status: SubscriptionStatus;
  paymentStatus: string;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  parentSubscriptionId?: Types.ObjectId;
  features: {
    emailNotification: boolean;
    smsNotification: boolean;
    messagingAllowed: boolean;
    homepageAd: boolean;
    languageIntroRights: number;
  };
}

interface LeanSubscriptionDocument {
  _id: string;
  userId: string;
  packageId: IPackageDocument;
  startDate: Date;
  endDate: Date;
  status: SubscriptionStatus;
  paymentStatus: string;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  parentSubscriptionId?: string;
}

// Helper function to calculate pro-rated price for package upgrade
const calculateProRatedPrice = async (userId: string, newPackage: IPackage) => {
  const currentSubscription = await Subscription.findOne({
    userId,
    status: 'ACTIVE',
    endDate: { $gt: new Date() }
  }).populate('packageId');

  if (currentSubscription && currentSubscription.packageId) {
    const currentPackage = currentSubscription.packageId as unknown as IPackage;
    const today = new Date();
    const endDate = new Date(currentSubscription.endDate);
    const remainingDays = Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));

    // Calculate daily rate for current package
    const currentDailyRate = currentPackage.price / currentPackage.duration;
    const remainingValue = currentDailyRate * remainingDays;

    // Calculate the upgrade price and ensure it's a valid number
    // const upgradePrice = Math.max(0, Math.round((newPackage.price - remainingValue) * 100) / 100);
    const upgradePrice = newPackage.price - currentPackage.price;

    return upgradePrice;
  }

  return newPackage.price;
};

export const createPackage = async (req: Request, res: Response) => {
  const { name, nameEn, description, descriptionEn, price, type, viewRequestLimit, createRequestLimit, emailNotification, features } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const newPackage = new Package({
      name,
      nameEn,
      description,
      descriptionEn,
      price,
      type,
      viewRequestLimit,
      createRequestLimit,
      emailNotification,
      features
    });
    await newPackage.save();
    res.status(201).json({ message: language === 'en' ? 'Package created successfully' : 'Paket başarıyla oluşturuldu', package: newPackage });
  } catch (error: any) {
    res.status(500).json({ message: language === 'en' ? 'Error creating package' : 'Paket oluşturulurken hata oluştu', error });
  }
};

export const updatePackage = async (req: Request, res: Response) => {
  const { packageId } = req.params;
  const packageData: Partial<IPackage> = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const updatedPackage = await Package.findByIdAndUpdate(packageId, packageData, { new: true });
    if (!updatedPackage) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }
    res.status(200).json({ message: language === 'en' ? 'Package updated successfully' : 'Paket başarıyla güncellendi', package: updatedPackage });
  } catch (error: any) {
    res.status(500).json({ message: language === 'en' ? 'Error updating package' : 'Paket güncellenirken hata oluştu', error });
  }
};

export const deletePackage = async (req: Request, res: Response) => {
  const { packageId } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const deletedPackage = await Package.findByIdAndDelete(packageId);
    if (!deletedPackage) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }
    res.status(200).json({ message: language === 'en' ? 'Package deleted successfully' : 'Paket başarıyla silindi' });
  } catch (error: any) {
    res.status(500).json({ message: language === 'en' ? 'Error deleting package' : 'Paket silinirken hata oluştu', error });
  }
};

export const getPackages = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';

  // Try to get user from auth token if available
  let userId: string | undefined;
  try {
    // Check if there's an authorization header
    const authHeader = req.headers.authorization;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      // Try to decode the token to get userId
      const token = authHeader.split(' ')[1];

      const decoded = jwt.verify(token, process.env.JWT_SECRET_USER?.toString() || '') as any;
      userId = decoded.userId || decoded.id || decoded._id;
    }
  } catch (error) {
    // Token verification failed, continue without user
    console.log('Token verification failed or no token provided');
  }

  try {
    // Get all active packages
    const packages = await Package.find({ isActive: true }).sort('order').lean();

    let currentSubscription = null;
    let addonSubscriptions: any[] = [];
    let currentPackageId: string | null = null;

    if (userId) {
      console.log('Looking for subscriptions for userId:', userId);
      console.log('Query criteria:', {
        userId: userId,
        status: 'ACTIVE',
        endDate: { $gte: new Date() }
      });

      // Find ALL active subscriptions for the user
      // Convert string userId to ObjectId for the query
      const userObjectId = new mongoose.Types.ObjectId(userId);
      const allUserSubscriptions = await Subscription.find({
        userId: userObjectId,
        status: 'ACTIVE',
        endDate: { $gte: new Date() }
      })
      .populate<{ packageId: IPackageDocument }>('packageId')
      .lean();

      console.log('Found subscriptions:', allUserSubscriptions.length);
      console.log('Subscriptions:', allUserSubscriptions.map(s => ({
        _id: s._id,
        userId: s.userId,
        status: s.status,
        packageType: (s.packageId as any)?.type
      })));

      // Find the standard subscription (main package)
      const standardSubscription = allUserSubscriptions.find(sub =>
        sub.packageId && (sub.packageId as any).type === 'standard'
      );

      console.log('Standard subscription found:', standardSubscription ? 'Yes' : 'No');

      if (standardSubscription) {
        currentPackageId = (standardSubscription.packageId as any)._id.toString();

        // Calculate remaining days
        const remainingDays = Math.ceil(
          (new Date(standardSubscription.endDate).getTime() - new Date().getTime())
          / (1000 * 60 * 60 * 24)
        );

        currentSubscription = {
          ...standardSubscription,
          _id: standardSubscription._id.toString(),
          userId: standardSubscription.userId.toString(),
          parentSubscriptionId: standardSubscription.parentSubscriptionId
            ? standardSubscription.parentSubscriptionId.toString()
            : undefined,
          remainingDays
        };

        // Get addon subscriptions
        addonSubscriptions = allUserSubscriptions
          .filter(sub => sub.parentSubscriptionId?.toString() === standardSubscription._id.toString())
          .map((addon: any) => ({
            _id: addon._id.toString(),
            packageId: addon.packageId,
            status: addon.status,
            remainingViewRequests: addon.remainingViewRequests,
            remainingCreateRequests: addon.remainingCreateRequests,
            startDate: addon.startDate,
            endDate: addon.endDate,
            userId: addon.userId.toString(),
            parentSubscriptionId: addon.parentSubscriptionId?.toString(),
            remainingDays: Math.ceil(
              (new Date(addon.endDate).getTime() - new Date().getTime())
              / (1000 * 60 * 60 * 24)
            )
          }));
      }

    }

    // Mark packages based on user's current subscription
    packages.forEach(pkg => {
      // Mark the current package
      pkg.isCurrentPackage = currentPackageId ? pkg._id.toString() === currentPackageId : false;

      // Set upgrade status for standard packages
      if (currentSubscription && currentSubscription.packageId && pkg.type === 'standard') {
        const currentPackagePrice = (currentSubscription.packageId as any).price || 0;

        if (pkg.price > currentPackagePrice) {
          pkg.isUpgradeable = true;
          // Calculate pro-rated upgrade price if needed
          pkg.upgradePrice = pkg.price; // You can calculate pro-rated price here
        } else {
          pkg.isUpgradeable = false;
          pkg.upgradePrice = pkg.price;
        }
      } else {
        pkg.isUpgradeable = false;
        pkg.upgradePrice = pkg.price;
      }

      // Ensure currency is set
      if (!pkg.currency) {
        pkg.currency = 'USD';
      }
    });

    return res.json({
      packages,
      currentSubscription,
      addonSubscriptions
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching packages' : 'Paketler alınırken hata oluştu',
      error: error.message
    });
  }
};

export const initiatePackagePurchase = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const { packageId } = req.params;
  const { cardInfo, storedCardId, saveCard, cardAlias } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    if (!userId) {
      return res.status(401).json({ message: language === 'en' ? 'User not authenticated' : 'Kullanıcı kimliği doğrulanmadı' });
    }

    if (!storedCardId && (!cardInfo || !cardInfo.cardHolderName || !cardInfo.cardNumber || !cardInfo.expireMonth || !cardInfo.expireYear || !cardInfo.cvc)) {
      return res.status(400).json({
        message: language === 'en' ? 'Invalid card information' : 'Geçersiz kart bilgileri',
        details: 'All card fields are required when not using a stored card'
      });
    }

    const packageDetails = await Package.findById(packageId);
    if (!packageDetails) {
      return res.status(404).json({ error: 'Package not found' });
    }

    const clientIp = req.ip || req.socket.remoteAddress || '127.0.0.1';

    let result;
    if (storedCardId) {
      result = await makePaymentWithStoredCard(
        userId.toString(),
        packageId,
        packageDetails.price,
        clientIp,
        storedCardId
      );
    } else if (cardInfo) {
      // If saveCard is true, we need to store the card first
      if (saveCard) {
        const storedCardResult = await storeCard(
          userId.toString(),
          cardInfo
        );

        if (storedCardResult?.cardUserKey && storedCardResult?.cardToken) {
          // Use the newly stored card for payment
          result = await makePaymentWithStoredCard(
            userId.toString(),
            packageId,
            packageDetails.price,
            clientIp,
            storedCardResult.cardToken
          );
        } else {
          return res.status(400).json({ error: 'Failed to save card' });
        }
      } else {
        // Regular payment without saving card
        result = await initiatePayment(
          userId.toString(),
          packageId,
          packageDetails.price,
          clientIp,
          cardInfo
        );
      }
    } else {
      return res.status(400).json({ error: 'Either cardInfo or storedCardId must be provided' });
    }

    const isPaymentWithToken = (result: any): result is { token: string, html3DS?: string } => {
      return result && typeof result === 'object' && 'token' in result;
    };

    const isPaymentWith3DS = (result: any): result is { html3DS: string, token?: string } => {
      return result && typeof result === 'object' && 'html3DS' in result;
    };

    if (isPaymentWith3DS(result)) {
      return res.json({ html3DS: result.html3DS, token: result.token });
    }

    if (isPaymentWithToken(result)) {
      return res.json({ token: result.token });
    }

    if (typeof result === 'string') {
      const newPayment = new Payment({
        userId: req.user?._id,
        packageId: packageDetails._id,
        amount: packageDetails.price,
        currency: 'USD',
        status: 'pending',
        paymentMethod: 'iyzico',
        paymentDate: new Date(),
        iyzico: {
          token: result,
          conversationData: result,
          conversationId: '',
          paymentId: '',
          paymentStatus: 'PENDING'
        }
      });
      await newPayment.save();

      return res.json({ html3DS: result });
    }

    return res.status(400).json({ error: 'Invalid payment response' });
  } catch (error: any) {
    return res.status(400).json({
      error: error.message || 'Error initiating payment',
      details: error.details || error
    });
  }
};

export const initiatePackageUpgrade = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const { packageId } = req.params;
  const { cardInfo, storedCardId, saveCard, cardAlias } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    if (!userId) {
      return res.status(401).json({ message: language === 'en' ? 'User not authenticated' : 'Kullanıcı kimliği doğrulanmadı' });
    }

    // Check if user has an active subscription
    const activeSubscription: any = await Subscription.findOne({
      userId,
      status: 'ACTIVE',
      endDate: { $gt: new Date() }
    }).populate('packageId');

    if (!activeSubscription) {
      return res.status(400).json({
        message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı'
      });
    }

    const newPackage = await Package.findById(packageId);
    if (!newPackage) {
      return res.status(404).json({
        message: language === 'en' ? 'Package not found' : 'Paket bulunamadı'
      });
    }

    // Calculate prorated price based on remaining days
    const currentPackage = activeSubscription.packageId as IPackageDocument;
    const remainingDays = Math.ceil((activeSubscription.endDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
    const dailyRateOld = currentPackage.price / currentPackage.duration;
    const dailyRateNew = newPackage.price / newPackage.duration;
    const remainingValue = dailyRateOld * remainingDays;
    const upgradeCost = newPackage.price - remainingValue;

    if (upgradeCost <= 0) {
      return res.status(400).json({
        message: language === 'en' ? 'Cannot upgrade to a lower-priced package' : 'Daha düşük fiyatlı bir pakete yükseltilemez'
      });
    }

    const clientIp = req.ip || req.socket.remoteAddress || '127.0.0.1';

    let result;
    if (storedCardId) {
      result = await makePaymentWithStoredCard(
        userId.toString(),
        packageId,
        upgradeCost,
        clientIp,
        storedCardId
      );
    } else if (cardInfo) {
      // If saveCard is true, we need to store the card first
      if (saveCard) {
        const storedCardResult = await storeCard(
          userId.toString(),
          cardInfo
        );

        if (storedCardResult?.cardUserKey && storedCardResult?.cardToken) {
          // Use the newly stored card for payment
          result = await makePaymentWithStoredCard(
            userId.toString(),
            packageId,
            upgradeCost,
            clientIp,
            storedCardResult.cardToken
          );
        } else {
          return res.status(400).json({ error: 'Failed to save card' });
        }
      } else {
        // Regular payment without saving card
        result = await initiatePayment(
          userId.toString(),
          packageId,
          upgradeCost,
          clientIp,
          cardInfo
        );
      }
    } else {
      return res.status(400).json({
        message: language === 'en' ? 'Either cardInfo or storedCardId must be provided' : 'Kart bilgileri veya kayıtlı kart ID\'si gereklidir'
      });
    }

    const isPaymentWithToken = (result: any): result is { token: string, html3DS?: string } => {
      return result && typeof result === 'object' && 'token' in result;
    };

    const isPaymentWith3DS = (result: any): result is { html3DS: string, token?: string } => {
      return result && typeof result === 'object' && 'html3DS' in result;
    };

    if (isPaymentWith3DS(result)) {
      return res.json({ html3DS: result.html3DS, token: result.token });
    }

    if (isPaymentWithToken(result)) {
      return res.json({ token: result.token });
    }

    if (typeof result === 'string') {
      const newPayment = new Payment({
        userId: req.user?._id,
        packageId: newPackage._id,
        amount: upgradeCost,
        currency: 'USD',
        status: 'pending',
        paymentMethod: 'iyzico',
        paymentDate: new Date(),
        isUpgrade: true,
        previousSubscriptionId: activeSubscription._id,
        iyzico: {
          token: result,
          conversationData: result,
          conversationId: '',
          paymentId: '',
          paymentStatus: 'PENDING'
        }
      });
      await newPayment.save();

      return res.json({ html3DS: result });
    }

    return res.status(400).json({
      message: language === 'en' ? 'Invalid payment response' : 'Geçersiz ödeme yanıtı'
    });
  } catch (error: any) {
    return res.status(400).json({
      error: error.message || 'Error initiating payment',
      details: error.details || error
    });
  }
};

export const completePackagePurchaseService = async (
  userId: string | mongoose.Types.ObjectId,
  packageId: string | mongoose.Types.ObjectId,
  token?: string
): Promise<{ success: boolean; message: string }> => {
  try {
    const payment = await Payment.findOne({ token }).populate('packageId');
    const packageDetails = await Package.findById(packageId);
    const user = await User.findById(userId);

    if (!payment || !packageDetails || !user) {
      throw new Error('payment.errors.userOrPackageNotFound');
    }

    // Check if there's a pending payment for this package and update it instead
    const existingPendingPayment = await Payment.findOne({
      userId,
      packageId,
      status: 'pending'
    });

    if (existingPendingPayment) {
      if (!existingPendingPayment.iyzico) {
        existingPendingPayment.iyzico = {};
      }
      existingPendingPayment.iyzico.token = token;
      existingPendingPayment.status = 'completed';
      await existingPendingPayment.save();
    } else {
      payment.status = 'completed';
      await payment.save();
    }

    // Only deactivate standard packages if the new package is also a standard package
    if (packageDetails.type === 'standard') {
      await Subscription.updateMany(
        {
          userId,
          status: 'ACTIVE',
          packageId: {
            $ne: packageId,
            $in: await Package.find({ type: 'standard' }).distinct('_id')
          }
        },
        {
          $set: {
            status: 'UPGRADED',
            endDate: new Date()
          }
        }
      );
    }

    // Create subscription for both standard and addon packages
    let endDate = new Date(Date.now() + packageDetails.duration * 24 * 60 * 60 * 1000);
    let parentSubscriptionId = null;

    if (packageDetails.type === 'addon') {
      const activeStandardSubscription = await Subscription.findOne({
        userId,
        status: 'ACTIVE',
        paymentStatus: 'paid',
        endDate: { $gt: new Date() } // Only reactivate if subscription hasn't expired
      }).populate({
        path: 'packageId',
        match: { type: 'standard' }
      });

      // Since populate with match returns null for non-matching documents
      if (!activeStandardSubscription || !activeStandardSubscription.packageId) {
        throw new Error('payment.errors.noActiveStandardPackage');
      }

      // Set the addon end date to match the standard package
      endDate = activeStandardSubscription.endDate;
      parentSubscriptionId = activeStandardSubscription._id;
    }

    const subscription = new Subscription({
      userId,
      packageId: packageDetails._id,
      startDate: new Date(),
      endDate,
      status: 'ACTIVE',
      paymentStatus: 'paid',
      remainingViewRequests: packageDetails.viewRequestLimit,
      remainingCreateRequests: packageDetails.createRequestLimit,
      parentSubscriptionId,
      features: {
        emailNotification: packageDetails.emailNotification,
        smsNotification: packageDetails.smsNotification,
        messagingAllowed: packageDetails.messagingAllowed,
        homepageAd: packageDetails.homepageAd,
        languageIntroRights: packageDetails.languageIntroRights
      }
    });

    await subscription.save();

    // Only update user's activeSubscription if it's a standard package
    if (packageDetails.type === 'standard') {
      await User.findByIdAndUpdate(userId, {
        activeSubscription: subscription._id
      });
    }

    // Clean up any pending payments for this package
    await Payment.deleteMany({
      userId,
      packageId: packageDetails._id,
      status: 'pending'
    });

    await Payment.findByIdAndUpdate(payment._id, {
      status: 'completed',
      completedAt: new Date(),
      'iyzico.paymentStatus': 'SUCCESS'
    });

    // Add this after a user successfully purchases a package
    const updateReferrer = async (userId: string) => {
      try {
        const user = await User.findById(userId);
        if (user && user.referredBy) {
          // Find the referrer and update the hasPackage flag for this referred user
          await User.updateOne(
            {
              _id: user.referredBy,
              "referrals._id": userId
            },
            {
              $set: { "referrals.$.hasPackage": true }
            }
          );
        }
      } catch (error) {
        console.error('Error updating referrer stats:', error);
      }
    };

    // Call this function after payment confirmation
    await updateReferrer(user._id.toString());

    return {
      success: true,
      message: 'packages.payment.messages.success'
    };
  } catch (error: any) {
    return {
      success: false,
      message: error instanceof Error ? error.message : 'packages.payment.messages.error'
    };
  }
};

export const completePackagePurchase = async (req: Request, res: Response) => {
  const userId = (req as any).user._id;
  const { packageId } = req.params;
  const { token } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const result = await completePackagePurchaseService(userId, packageId, token);

    if (result.success) {
      res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=success&packageType=${packageId}`);
    } else {
      res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=failed&messageKey=${encodeURIComponent(result.message)}`);
    }
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error completing package purchase' : 'Paket satın alma işlemi tamamlanırken hata oluştu',
      error: error.message
    });
  }
};

export const subscribeToPackage = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const { packageId } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    if (!userId) {
      return res.status(401).json({ message: language === 'en' ? 'User not authenticated' : 'Kullanıcı kimliği doğrulanmadı' });
    }

    const result = await completePackagePurchaseService(userId, packageId);

    if (result.success) {
      res.status(201).json({
        message: language === 'en' ? 'Subscription successful' : 'Abonelik başarılı'
      });
    } else {
      res.status(400).json({
        message: language === 'en' ? result.message : 'Abonelik oluşturulamadı'
      });
    }
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error subscribing to package' : 'Pakete abone olurken hata oluştu',
      error: error.message || error
    });
  }
};

export const cancelSubscription = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const userId = (req as any).user?._id;
  const subscriptionId = req.params.id;

  try {
    if (!userId) {
      return res.status(401).json({
        message: language === 'en' ? 'User not authenticated' : 'Kullanıcı kimliği doğrulanmadı'
      });
    }

    const subscription = await Subscription.findOne({
      _id: subscriptionId,
      userId,
      status: 'ACTIVE',
      endDate: { $gt: new Date() }
    }).populate<{ packageId: IPackageDocument }>('packageId');

    if (!subscription) {
      return res.status(404).json({
        message: language === 'en' ? 'Subscription not found' : 'Abonelik bulunamadı'
      });
    }

    // Mark subscription as cancelled but keep it active until end date
    subscription.status = 'CANCELED';
    subscription.autoRenewal = false; // Disable auto-renewal
    await subscription.save();

    // If this is a standard package, also cancel all related addon subscriptions
    if (subscription.packageId.type === 'standard') {
      await Subscription.updateMany(
        {
          userId,
          status: 'ACTIVE',
          parentSubscriptionId: subscription._id
        },
        {
          $set: {
            status: 'CANCELED',
            autoRenewal: false
          }
        }
      );
    }

    // Note: We don't clear the user subscription field here because the subscription
    // remains active until the end date. The cron job will handle clearing it when it expires.

    return res.json({
      message: language === 'en'
        ? 'Subscription cancelled. It will remain active until the end of the billing period.'
        : 'Abonelik iptal edildi. Fatura dönemi sonuna kadar aktif kalacaktır.',
      endDate: subscription.endDate
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error cancelling subscription' : 'Abonelik iptal edilirken hata oluştu',
      error: error.message
    });
  }
};

export const reactivateSubscription = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const userId = (req as any).user?._id;
  const subscriptionId = req.params.id;

  try {
    if (!userId) {
      return res.status(401).json({
        message: language === 'en' ? 'User not authenticated' : 'Kullanıcı kimliği doğrulanmadı'
      });
    }

    const subscription = await Subscription.findOne({
      _id: subscriptionId,
      userId,
      status: 'CANCELED',
      endDate: { $gt: new Date() } // Only reactivate if subscription hasn't expired
    }).populate<{ packageId: IPackageDocument }>('packageId');

    if (!subscription) {
      return res.status(404).json({
        message: language === 'en' ? 'Subscription not found or already expired' : 'Abonelik bulunamadı veya süresi dolmuş'
      });
    }

    // Reactivate the subscription
    subscription.status = 'ACTIVE';
    await subscription.save();

    // If this is a standard package, also reactivate all related addon subscriptions
    if (subscription.packageId.type === 'standard') {
      await Subscription.updateMany(
        {
          userId,
          status: 'CANCELED',
          parentSubscriptionId: subscription._id,
          endDate: { $gt: new Date() } // Only reactivate non-expired addons
        },
        {
          $set: { status: 'ACTIVE' }
        }
      );
    }

    return res.json({
      message: language === 'en'
        ? 'Subscription reactivated successfully'
        : 'Abonelik başarıyla yeniden aktifleştirildi'
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error reactivating subscription' : 'Abonelik yeniden aktifleştirilirken hata oluştu',
      error: error.message
    });
  }
};

/**
 * Get a package by its ID
 */
export const getPackageById = async (req: Request, res: Response) => {
  const { packageId } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const packageData = await Package.findById(packageId);

    if (!packageData) {
      return res.status(404).json({
        message: language === 'en' ? 'Package not found' : 'Paket bulunamadı'
      });
    }

    // Count active subscriptions for this package
    const activeSubscriptionsCount = await Subscription.countDocuments({
      packageId: packageId,
      status: 'ACTIVE'
    });

    // Get additional statistics
    const stats = {
      activeSubscriptions: activeSubscriptionsCount,
      // These fields would need more complex calculations in a real implementation
      averageRenewalRate: 0,
      totalRevenue: 0
    };

    res.status(200).json({
      package: packageData,
      stats
    });
  } catch (error: any) {
    console.error('Error fetching package:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error fetching package details' : 'Paket detayları alınırken hata oluştu',
      error: error.message
    });
  }
};