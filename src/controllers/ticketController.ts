import { Request, Response } from 'express';
import Ticket from '../models/Ticket';

export const createTicket = async (req: any, res: Response) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    console.log('Received request body:', req.body);
    console.log('Received files:', req.files);

    // Parse amount as number
    const amount = Number(req.body.amount);
    if (isNaN(amount)) {
      return res.status(400).json({ message: 'Invalid amount value' });
    }

    const ticketData = {
      ...req.body,
      amount,
      userId
    };

    const files = req.files?.images as Express.Multer.File[];

    // Save image paths if present
    let imagePaths: string[] = [];
    if (files && files.length > 0) {
      imagePaths = files.map(file => `/uploads/tickets/${file.filename}`);
    }

    const ticket = new Ticket({
      ...ticketData,
      images: imagePaths,
      status: 'pending'
    });

    await ticket.save();

    res.status(201).json(ticket);
  } catch (error: any) {
    console.error('Create ticket error:', error);
    res.status(500).json({ message: error.message });
  }
};

export const getUserTickets = async (req: any, res: Response) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const tickets = await Ticket.find({ userId }).sort({ createdAt: -1 });
    res.json(tickets);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

export const getTicketById = async (req: any, res: Response) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const ticket = await Ticket.findOne({
      _id: req.params.id,
      userId
    });

    if (!ticket) {
      return res.status(404).json({ message: 'Ticket not found' });
    }
    res.json(ticket);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

export const updateTicket = async (req: any, res: Response) => {
  try {
    const userId = req.user?._id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const ticket = await Ticket.findOne({ _id: req.params.id, userId });

    if (!ticket) {
      return res.status(404).json({ message: 'Ticket not found' });
    }

    Object.assign(ticket, req.body);
    await ticket.save();

    res.json(ticket);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

export const addResponse = async (req: any, res: Response) => {
  try {
    const { message } = req.body;
    const userId = req.user?._id;
    if (!userId) {
      return res.status(401).json({ message: 'Unauthorized' });
    }

    const isAdmin = req.user?.role === 'admin';
    const ticket = await Ticket.findById(req.params.id);

    if (!ticket) {
      return res.status(404).json({ message: 'Ticket not found' });
    }

    // Only allow response from ticket owner or admin
    if (!isAdmin && ticket.userId.toString() !== userId.toString()) {
      return res.status(403).json({ message: 'Not authorized' });
    }

    ticket.responses = ticket.responses || [];
    ticket.responses.push({
      message,
      isAdmin,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    if (isAdmin) {
      ticket.status = 'in_progress';
    }

    await ticket.save();
    res.json(ticket);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

// Admin ticket controllers
export const getAllTickets = async (req: any, res: Response) => {
  try {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const tickets = await Ticket.find()
      .sort({ createdAt: -1 })
      .populate('userId', 'email name'); // Populate user details
    res.json(tickets);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

export const getAdminTicketById = async (req: any, res: Response) => {
  try {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const ticket = await Ticket.findById(req.params.id)
      .populate('userId', 'email name');

    if (!ticket) {
      return res.status(404).json({ message: 'Ticket not found' });
    }
    res.json(ticket);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

export const updateAdminTicket = async (req: any, res: Response) => {
  try {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const ticket = await Ticket.findById(req.params.id);

    if (!ticket) {
      return res.status(404).json({ message: 'Ticket not found' });
    }

    Object.assign(ticket, req.body);
    await ticket.save();

    res.json(ticket);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};

export const adminRespondToTicket = async (req: any, res: Response) => {
  try {
    if (!req.user?.isAdmin) {
      return res.status(403).json({ message: 'Admin access required' });
    }

    const { response } = req.body;
    if (!response) {
      return res.status(400).json({ message: 'Response is required' });
    }

    const ticket: any = await Ticket.findById(req.params.id);
    if (!ticket) {
      return res.status(404).json({ message: 'Ticket not found' });
    }

    ticket?.responses?.push({
      userId: req.user._id,
      message: response,
      isAdmin: true
    });

    if (ticket.status === 'pending') {
      ticket.status = 'in_progress';
    }

    await ticket.save();
    res.json(ticket);
  } catch (error: any) {
    res.status(500).json({ message: error.message });
  }
};
