import { Request, Response } from 'express';
import path from 'path';
import fs from 'fs';

export const uploadFiles = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
      return res.status(400).json({
        message: language === 'en' ? 'No files uploaded' : 'Dosya yüklenmedi'
      });
    }

    // Get the uploaded files
    const files = req.files as Express.Multer.File[];

    // Create paths array for response
    const paths = files.map(file => {
      // Convert backslashes to forward slashes for consistency
      return file.path.replace(/\\/g, '/');
    });

    res.status(200).json({
      message: language === 'en' ? 'Files uploaded successfully' : 'Dosyalar başarıyla yüklendi',
      paths
    });

  } catch (error: any) {
    console.error('Error uploading files:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error uploading files' : '<PERSON>sy<PERSON> yükle<PERSON>ken hata olu<PERSON>',
      error: error.message
    });
  }
};
