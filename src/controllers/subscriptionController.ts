import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { Package } from '../models/Package';
import { Subscription, ISubscription } from '../models/Subscription';
import { User, IUser } from '../models/User';
import { initiatePayment, verifyPayment } from '../services/iyzicoPaymentService';
import { Payment } from '../models/Payment';
import { Item } from '../models/Item';
import { ViewedItem } from '../models/ViewedItem';
import { Store, IStore } from '../models/Store';
import { AuthRequest } from '../types/express';
import { sendEmail, sendPackagePurchaseEmail, sendPackageCancellationEmail } from '../services/emailService';
import { ItemStatsService } from '../services/itemStatsService';

// Initiate subscription to a package
export const initiateSubscription = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const { packageId, cardHolderName, cardNumber, expireMonth, expireYear, cvc } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const ipAddress = req.ip || req.socket.remoteAddress || '0.0.0.0';
    const selectedPackage = await Package.findById(packageId);

    if (!selectedPackage) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }

    const cardInfo = { cardHolderName, cardNumber, expireMonth, expireYear, cvc };

    const paymentResult = await initiatePayment(userId, selectedPackage.id, selectedPackage.price, ipAddress, cardInfo);

    // Check if paymentResult contains the expected script
    if (typeof paymentResult === 'string') {
      res.status(200).json({ paymentPageUrl: paymentResult });
    } else {
      throw new Error('Unexpected payment initiation result');
    }
  } catch (error:any) {
    console.error(error);
    res.status(500).json({ message: language === 'en' ? 'Error initiating subscription' : 'Abonelik başlatılırken hata oluştu', error: (error as Error).message });
  }
};

// Complete subscription after payment
export const completeSubscription = async (req: Request, res: Response) => {
  const { token } = req.query;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const isVerified = await verifyPayment(token as string);
    if (!isVerified) {
      return res.status(400).json({ message: language === 'en' ? 'Payment verification failed' : 'Ödeme doğrulaması başarısız oldu' });
    }

    const payment = await Payment.findOne({ 'iyzico.token': token });
    if (!payment) {
      return res.status(404).json({ message: language === 'en' ? 'Payment not found' : 'Ödeme bulunamadı' });
    }

    const selectedPackage = await Package.findById(payment.packageId);
    if (!selectedPackage) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }

    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30); // Assuming all packages are for 30 days

    const renewalDate = new Date(endDate); // Set renewal date same as end date

    const newSubscription = new Subscription({
      userId: payment.userId,
      packageId: selectedPackage._id,
      startDate: new Date(),
      endDate,
      renewalDate,
      remainingViewRequests: selectedPackage.viewRequestLimit,
      remainingCreateRequests: selectedPackage.createRequestLimit,
      status: 'ACTIVE',
      paymentStatus: 'paid',
      addons: [],
      usageHistory: {
        viewRequestsUsed: 0,
        createRequestsUsed: 0
      },
      features: {
        emailNotification: selectedPackage.emailNotification,
        smsNotification: selectedPackage.smsNotification,
        messagingAllowed: selectedPackage.messagingAllowed,
        homepageAd: selectedPackage.homepageAd,
        languageIntroRights: selectedPackage.languageIntroRights
      }
    });

    await newSubscription.save();

    // Update user with subscription and hasPackage flag
    await User.findByIdAndUpdate(payment.userId, {
      subscription: newSubscription._id,
      hasPackage: true
    });

    // Send package purchase email
    try {
      const user = await User.findById(payment.userId);
      if (user) {
        // Send the email asynchronously but log the result
        console.log(`Sending package purchase email to user: ${user.email}`);
        sendPackagePurchaseEmail(
          user.email,
          user.firstName,
          user.lastName,
          selectedPackage.name,
          selectedPackage.price,
          selectedPackage.currency || 'TRY',
          newSubscription.startDate,
          newSubscription.endDate,
          language
        )
        .then(() => {
          console.log(`Package purchase email sent successfully to ${user.email}`);
        })
        .catch(err => {
          console.error(`Error sending package purchase email to ${user.email}:`, err);
        });

        // Update user to reflect they have a package
        await User.findByIdAndUpdate(user._id, { hasPackage: true });
      } else {
        console.error(`User not found for package purchase email: ${payment.userId}`);
      }
    } catch (emailError) {
      console.error('Error preparing package purchase email:', emailError);
      // Continue with subscription process even if email fails
    }

    res.status(201).json({ message: language === 'en' ? 'Subscription successful' : 'Abonelik başarılı', subscription: newSubscription });
  } catch (error:any) {
    console.error(error);
    res.status(500).json({ message: language === 'en' ? 'Error completing subscription' : 'Abonelik tamamlanırken hata oluştu', error: (error as Error).message });
  }
};

// Get the current user's subscription
export const getUserSubscription = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const subscription = await Subscription.findOne({ userId, status: 'active' }).populate('packageId');
    if (!subscription) {
      return res.status(404).json({ message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı' });
    }

    res.status(200).json({ subscription });
  } catch (error:any) {
    console.error(error);
    res.status(500).json({ message: language === 'en' ? 'Error retrieving subscription' : 'Abonelik alınırken hata oluştu', error: (error as Error).message });
  }
};

// Cancel the current user's subscription
export const cancelSubscription = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    // Find the active subscription
    const subscription = await Subscription.findOne({ userId, status: 'ACTIVE' });
    if (!subscription) {
      return res.status(404).json({ message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı' });
    }

    // Get package details for the email
    const packageDetails = await Package.findById(subscription.packageId);
    if (!packageDetails) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }

    // Update subscription status
    subscription.status = 'EXPIRED';
    subscription.paymentStatus = 'expired';
    await subscription.save();

    // Update user
    const user = await User.findByIdAndUpdate(
      userId,
      {
        $unset: { subscription: 1 },
        hasPackage: false
      },
      { new: true }
    );

    // Send cancellation email
    if (user) {
      console.log(`Sending package cancellation email to user: ${user.email}`);
      sendPackageCancellationEmail(
        user.email,
        user.firstName,
        user.lastName,
        packageDetails.name,
        language
      )
      .then(() => {
        console.log(`Package cancellation email sent successfully to ${user.email}`);
      })
      .catch(err => {
        console.error(`Error sending package cancellation email to ${user.email}:`, err);
      });

      // Also send an admin notification about the cancellation
      try {
        const adminEmails = (process.env.ADMIN_EMAILS || '').split(',').filter(Boolean);
        if (adminEmails.length) {
          const adminSubject = `Subscription Cancelled: ${user.firstName} ${user.lastName}`;
          const adminMessage = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <div style="background-color: #2C7A7B; padding: 20px; text-align: center;">
                <h1 style="color: white; margin: 0;">E-Export City</h1>
              </div>
              <div style="padding: 20px; border: 1px solid #e5e5e5; border-top: none;">
                <h2 style="color: #2C7A7B;">Subscription Cancellation Notice</h2>
                <p>A user has cancelled their subscription:</p>
                <div style="background-color: #F0F9FA; padding: 15px; border-radius: 5px; margin: 15px 0;">
                  <ul style="list-style: none; padding-left: 0;">
                    <li><strong>User:</strong> ${user.firstName} ${user.lastName} (${user.email})</li>
                    <li><strong>Package:</strong> ${packageDetails.name}</li>
                    <li><strong>Cancellation Date:</strong> ${new Date().toLocaleString()}</li>
                  </ul>
                </div>
              </div>
            </div>
          `;

          // Send to all admin emails
          for (const adminEmail of adminEmails) {
            sendEmail(adminEmail.trim(), adminSubject, 'A user has cancelled their subscription', adminMessage)
              .catch(err => console.error(`Failed to send admin notification to ${adminEmail}:`, err));
          }
        }
      } catch (adminEmailError) {
        console.error('Error sending admin notification about cancellation:', adminEmailError);
      }
    }

    res.status(200).json({ message: language === 'en' ? 'Subscription cancelled successfully' : 'Abonelik başarıyla iptal edildi' });
  } catch (error:any) {
    console.error(error);
    res.status(500).json({ message: language === 'en' ? 'Error cancelling subscription' : 'Abonelik iptal edilirken hata oluştu', error: (error as Error).message });
  }
};

// Get the active subscription for the user
export const getActiveSubscription = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    // Find all active subscriptions for the user
    const subscriptions = await Subscription.find({
      userId: new mongoose.Types.ObjectId(userId),
      endDate: { $gte: new Date() },
      status: 'ACTIVE'
    })
    .populate('packageId')
    .populate('addons.packageId')
    .sort({ createdAt: -1 });

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(404).json({
        message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı'
      });
    }

    // Get the main subscription (most recent active one)
    const mainSubscription: any = subscriptions[0] as ISubscription;
    const addonSubscriptions = subscriptions.filter(sub => {
      const subscription = sub as any;
      return subscription._id.toString() !== mainSubscription._id.toString() &&
             subscription.parentSubscriptionId?.toString() === mainSubscription._id.toString();
    });

    // Calculate total remaining requests including all active subscriptions
    const totalViewRequests = addonSubscriptions.reduce(
      (total, sub) => total + ((sub as ISubscription).remainingViewRequests || 0),
      mainSubscription.remainingViewRequests || 0
    );

    const totalCreateRequests = addonSubscriptions.reduce(
      (total, sub) => total + ((sub as ISubscription).remainingCreateRequests || 0),
      mainSubscription.remainingCreateRequests || 0
    );

    // Format the response
    const formattedSubscription = {
      ...mainSubscription.toObject(),
      remainingViewRequests: totalViewRequests,
      remainingCreateRequests: totalCreateRequests,
      addons: addonSubscriptions.map(addon => {
        const subscription = addon as ISubscription;
        return {
          _id: subscription._id,
          packageId: subscription.packageId,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          status: subscription.status,
          remainingViewRequests: subscription.remainingViewRequests,
          remainingCreateRequests: subscription.remainingCreateRequests,
          features: subscription.features
        };
      })
    };

    res.status(200).json({
      subscription: formattedSubscription
    });
  } catch (error) {
    console.error('Error in getActiveSubscription:', error);
    res.status(500).json({
      message: language === 'en' ? 'Server error' : 'Sunucu hatası',
      error: (error as Error).message
    });
  }
};

// Renew a subscription
export const renewSubscription = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const { packageId, cardHolderName, cardNumber, expireMonth, expireYear, cvc } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const ipAddress = req.ip || req.socket.remoteAddress || '0.0.0.0';
    const selectedPackage = await Package.findById(packageId);
    if (!selectedPackage) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }

    const cardInfo = { cardHolderName, cardNumber, expireMonth, expireYear, cvc };

    const paymentResult = await initiatePayment(userId, selectedPackage.id, selectedPackage.price, ipAddress, cardInfo);

    // Check if paymentResult contains the expected script
    if (typeof paymentResult === 'string') {
      res.status(200).json({ paymentPageUrl: paymentResult });
    } else {
      throw new Error('Unexpected payment initiation result');
    }
  } catch (error:any) {
    console.error(error);
    res.status(500).json({ message: language === 'en' ? 'Error initiating renewal' : 'Yenileme başlatılırken hata oluştu', error: (error as Error).message });
  }
};

// Complete renewal subscription after payment
export const completeRenewalSubscription = async (req: Request, res: Response) => {
  const { token } = req.query;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const isVerified = await verifyPayment(token as string);
    if (!isVerified) {
      return res.status(400).json({ message: language === 'en' ? 'Payment verification failed' : 'Ödeme doğrulaması başarısız oldu' });
    }

    const payment = await Payment.findOne({ 'iyzico.token': token });
    if (!payment) {
      return res.status(404).json({ message: language === 'en' ? 'Payment not found' : 'Ödeme bulunamadı' });
    }

    const selectedPackage = await Package.findById(payment.packageId);
    if (!selectedPackage) {
      return res.status(404).json({ message: language === 'en' ? 'Package not found' : 'Paket bulunamadı' });
    }

    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 30); // Assuming all packages are for 30 days

    const renewalDate = new Date(endDate); // Set renewal date same as end date

    // Find current active subscription and its addons
    const currentSubscriptions = await Subscription.find({
      userId: payment.userId,
      status: 'ACTIVE'
    }).sort({ createdAt: -1 });

    if (currentSubscriptions.length === 0) {
      return res.status(404).json({ message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı' });
    }

    const mainSubscription = currentSubscriptions[0] as ISubscription;
    const addonSubscriptions = currentSubscriptions.slice(1) as ISubscription[];

    // Mark current main subscription as upgraded
    await Subscription.findByIdAndUpdate(mainSubscription._id, {
      status: 'UPGRADED',
      updatedAt: new Date()
    });

    // Create new subscription with upgraded package
    const newSubscription = new Subscription({
      userId: payment.userId,
      packageId: selectedPackage._id,
      startDate: new Date(),
      endDate,
      renewalDate,
      remainingViewRequests: selectedPackage.viewRequestLimit,
      remainingCreateRequests: selectedPackage.createRequestLimit,
      status: 'ACTIVE',
      paymentStatus: 'paid',
      parentSubscriptionId: mainSubscription._id,
      addons: [],
      usageHistory: {
        viewRequestsUsed: 0,
        createRequestsUsed: 0
      },
      features: {
        emailNotification: selectedPackage.emailNotification,
        smsNotification: selectedPackage.smsNotification,
        messagingAllowed: selectedPackage.messagingAllowed,
        homepageAd: selectedPackage.homepageAd,
        languageIntroRights: selectedPackage.languageIntroRights
      }
    });

    await newSubscription.save();

    // Keep addon subscriptions active and link them to the new main subscription
    for (const addon of addonSubscriptions) {
      await Subscription.findByIdAndUpdate(addon._id, {
        parentSubscriptionId: newSubscription._id,
        updatedAt: new Date()
      });
    }

    // Update user's subscription reference
    await User.findByIdAndUpdate(payment.userId, {
      subscription: newSubscription._id
    });

    const updatedSubscription = await Subscription.findById(newSubscription._id)
      .populate('packageId')
      .populate('addons.packageId');

    res.status(201).json({
      message: language === 'en' ? 'Subscription upgraded successfully' : 'Abonelik yükseltme başarılı',
      subscription: updatedSubscription
    });
  } catch (error: any) {
    console.error(error);
    res.status(500).json({
      message: language === 'en' ? 'Error completing upgrade subscription' : 'Abonelik yükseltme tamamlanırken hata oluştu',
      error: (error as Error).message
    });
  }
};

// Check if user has remaining view requests
export const checkViewRequest = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const itemId = req.query.itemId as string;
  const language = req.headers['accept-language'] as string || 'tr';

  console.log(`[DEBUG] checkViewRequest called for user ${userId}, item ${itemId}`);

  try {
    // Check if user is the store owner
    const item = await Item.findById(itemId).populate<{ store: IStore & { owner: IUser } }>({
      path: 'store',
      select: 'name logo location owner',
      populate: {
        path: 'owner',
        model: 'User'
      }
    });

    if (!item) {
      return res.status(404).json({
        message: language === 'en' ? 'Item not found' : 'Ürün bulunamadı'
      });
    }

    // If user is the store owner, allow viewing without using request
    if (item.store?.owner?._id?.toString() === userId) {
      return res.json({
        hasRemaining: true,
        isOwner: true,
        alreadyViewed: true,
        remainingCount: 0,
        message: language === 'en' ? 'You are the store owner' : 'Bu mağazanın sahibisiniz'
      });
    }

    // Check if user has already viewed this item
    const viewedItem = await ViewedItem.findOne({ userId, itemId, itemType: 'item' });
    console.log(`[DEBUG] ViewedItem query result:`, viewedItem);
    if (viewedItem) {
      console.log(`[DEBUG] User has already viewed this item - returning alreadyViewed: true`);
      return res.json({
        hasRemaining: true,
        isOwner: false,
        alreadyViewed: true,
        remainingCount: 0,
        message: language === 'en' ? 'Already viewed' : 'Bu ürünü daha önce görüntülediniz'
      });
    }

    // Get all active subscriptions including addons
    const subscriptions = await Subscription.find({
      userId,
      status: 'ACTIVE',
      endDate: { $gt: new Date() }
    });

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(403).json({
        hasRemaining: false,
        isOwner: false,
        alreadyViewed: false,
        message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı'
      });
    }

    // Calculate total remaining view requests from all subscriptions
    const totalRemainingViews = subscriptions.reduce((total, sub) => total + sub.remainingViewRequests, 0);
    const hasRemaining = totalRemainingViews > 0;

    res.json({
      hasRemaining,
      isOwner: false,
      alreadyViewed: false,
      remainingCount: totalRemainingViews,
      message: hasRemaining
        ? language === 'en' ? `${totalRemainingViews} views remaining` : `${totalRemainingViews} görüntüleme hakkı kaldı`
        : language === 'en' ? 'No remaining view requests' : 'Görüntüleme hakları kalmadı'
    });
  } catch (error:any) {
    res.status(500).json({
      message: language === 'en' ? 'Error checking view requests' : 'Görüntüleme hakları kontrol edilirken hata oluştu',
      error: (error as Error).message
    });
  }
};

// Use a view request
export const useViewRequest = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const itemId = req.body.itemId;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    // Check if user has already viewed this item
    const existingView = await ViewedItem.findOne({ userId, itemId, itemType: 'item' });
    if (existingView) {
      return res.json({
        alreadyViewed: true,
        message: language === 'en' ? 'Already viewed' : 'Bu ürünü daha önce görüntülediniz'
      });
    }

    // Get all active subscriptions including addons
    const subscriptions = await Subscription.find({
      userId,
      status: 'ACTIVE',
      endDate: { $gt: new Date() }
    }).sort({ endDate: 1 }); // Sort by end date to use the earliest expiring subscription first

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(403).json({
        message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı'
      });
    }

    // Find the first subscription with remaining views
    const subscriptionToUse = subscriptions.find(sub => sub.remainingViewRequests > 0);

    if (!subscriptionToUse) {
      return res.status(403).json({
        message: language === 'en' ? 'No remaining view requests' : 'Görüntüleme hakları kalmadı'
      });
    }

    // Record the viewed item
    const viewedItem = new ViewedItem({
      userId,
      itemId,
      itemType: 'item',
      viewedAt: new Date()
    });
    await viewedItem.save();

    // Update item stats (increment view count)
    await ItemStatsService.incrementViewCount(itemId);

    // Decrement the view request count
    subscriptionToUse.remainingViewRequests -= 1;
    subscriptionToUse.usageHistory.viewRequestsUsed += 1;
    await subscriptionToUse.save();

    // Calculate total remaining views from all subscriptions
    const totalRemainingViews = subscriptions.reduce((total, sub) => total + sub.remainingViewRequests, 0);

    res.json({
      remainingCount: totalRemainingViews,
      message: language === 'en'
        ? `View request used. ${totalRemainingViews} remaining`
        : `Görüntüleme hakkı kullanıldı. ${totalRemainingViews} hak kaldı`
    });
  } catch (error:any) {
    res.status(500).json({
      message: language === 'en' ? 'Error using view request' : 'Görüntüleme hakkı kullanılırken hata oluştu',
      error: (error as Error).message
    });
  }
};

// Check if user has remaining create requests
export const checkCreateRequest = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const subscription = await Subscription.findOne({ userId, status: 'ACTIVE' });

    if (!subscription) {
      return res.status(403).json({
        message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı',
        hasRemaining: false
      });
    }

    const hasRemaining = subscription.remainingCreateRequests > 0;

    res.json({
      hasRemaining,
      remainingCount: subscription.remainingCreateRequests,
      message: hasRemaining
        ? language === 'en' ? `${subscription.remainingCreateRequests} creates remaining` : `${subscription.remainingCreateRequests} oluşturma hakları kaldı`
        : language === 'en' ? 'No remaining create requests' : 'Oluşturma hakları kalmadı'
    });
  } catch (error:any) {
    res.status(500).json({
      message: language === 'en' ? 'Error checking create requests' : 'Oluşturma hakları kontrol edilirken hata oluştu',
      error: (error as Error).message
    });
  }
};

// Use a create request
export const useCreateRequest = async (req: Request, res: Response) => {
  const userId = req.user?.id;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const subscription = await Subscription.findOne({ userId, status: 'active' })
      .populate('packageId');

    if (!subscription) {
      return res.status(403).json({
        message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı'
      });
    }

    if (!subscription.packageId) {
      return res.status(403).json({
        message: language === 'en' ? 'Invalid subscription package' : 'Geçersiz abonelik paketi'
      });
    }

    // Check if package allows create requests
    if (!(subscription.packageId as any).createRequestsPerMonth) {
      return res.status(403).json({
        message: language === 'en' ? 'Your package does not include create requests' : 'Paketiniz oluşturma hakkı içermiyor'
      });
    }

    if (subscription.remainingCreateRequests <= 0) {
      return res.status(403).json({
        message: language === 'en' ? 'No remaining create requests' : 'Oluşturma hakkı kalmadı'
      });
    }

    // Log the usage
    console.log(`User ${userId} used create request. Remaining: ${subscription.remainingCreateRequests - 1}`);

    subscription.remainingCreateRequests -= 1;
    subscription.usageHistory.createRequestsUsed += 1;
    await subscription.save();

    res.json({
      remainingCount: subscription.remainingCreateRequests,
      message: language === 'en'
        ? `Create request used. ${subscription.remainingCreateRequests} remaining`
        : `Oluşturma hakkı kullanıldı. ${subscription.remainingCreateRequests} hak kaldı`
    });
  } catch (error: any) {
    console.error('Error in useCreateRequest:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error using create request' : 'Oluşturma hakkı kullanılırken hata oluştu',
      error: error.message
    });
  }
};

// Check if user has permission to view a store
export const checkStoreViewRequest = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const storeId = req.query.storeId as string;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    if (!storeId) {
      return res.status(400).json({
        message: language === 'en' ? 'Store ID is required' : 'Mağaza ID gerekli'
      });
    }

    // Check if user is the store owner
    const store = await Store.findById(storeId).populate<{ owner: IUser }>('owner');
    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    // If user is the store owner, allow viewing without using request
    if (store.owner?._id?.toString() === userId) {
      return res.json({
        hasRemaining: true,
        isOwner: true,
        alreadyViewed: true,
        totalViews: store.viewCount || 0,
        message: language === 'en' ? 'You are the store owner' : 'Bu mağazanın sahibisiniz'
      });
    }

    // Check if user has already viewed this store
    const viewedStore = await ViewedItem.findOne({ 
      userId, 
      itemId: storeId, 
      itemType: 'store' 
    });
    
    if (viewedStore) {
      return res.json({
        hasRemaining: true,
        isOwner: false,
        alreadyViewed: true,
        totalViews: store.viewCount || 0,
        message: language === 'en' ? 'Already viewed' : 'Bu mağazayı daha önce görüntülediniz'
      });
    }

    // Get all active subscriptions including addons
    const subscriptions = await Subscription.find({
      userId,
      status: 'ACTIVE',
      endDate: { $gt: new Date() }
    });

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(403).json({
        hasRemaining: false,
        isOwner: false,
        alreadyViewed: false,
        totalViews: store.viewCount || 0,
        message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı'
      });
    }

    // Calculate total remaining view requests from all subscriptions
    const totalRemainingViews = subscriptions.reduce((total, sub) => total + sub.remainingViewRequests, 0);
    const hasRemaining = totalRemainingViews > 0;

    res.json({
      hasRemaining,
      isOwner: false,
      alreadyViewed: false,
      totalViews: store.viewCount || 0,
      remainingCount: totalRemainingViews,
      message: hasRemaining
        ? language === 'en' ? `${totalRemainingViews} views remaining` : `${totalRemainingViews} görüntüleme hakkı kaldı`
        : language === 'en' ? 'No remaining view requests' : 'Görüntüleme hakları kalmadı'
    });
  } catch (error: any) {
    console.error('Error in checkStoreViewRequest:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error checking store view requests' : 'Mağaza görüntüleme hakları kontrol edilirken hata oluştu',
      error: error.message
    });
  }
};

// Use a store view request
export const useStoreViewRequest = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const { storeId } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    if (!storeId) {
      return res.status(400).json({
        message: language === 'en' ? 'Store ID is required' : 'Mağaza ID gerekli'
      });
    }

    // Check if store exists
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    // Check if user has already viewed this store
    const existingView = await ViewedItem.findOne({ 
      userId, 
      itemId: storeId, 
      itemType: 'store' 
    });
    
    if (existingView) {
      return res.json({
        alreadyViewed: true,
        message: language === 'en' ? 'Already viewed' : 'Bu mağazayı daha önce görüntülediniz'
      });
    }

    // Get all active subscriptions including addons
    const subscriptions = await Subscription.find({
      userId,
      status: 'ACTIVE',
      endDate: { $gt: new Date() }
    }).sort({ endDate: 1 }); // Sort by end date to use the earliest expiring subscription first

    if (!subscriptions || subscriptions.length === 0) {
      return res.status(403).json({
        message: language === 'en' ? 'No active subscription found' : 'Aktif abonelik bulunamadı'
      });
    }

    // Find the first subscription with remaining views
    const subscriptionToUse = subscriptions.find(sub => sub.remainingViewRequests > 0);

    if (!subscriptionToUse) {
      return res.status(403).json({
        message: language === 'en' ? 'No remaining view requests' : 'Görüntüleme hakları kalmadı'
      });
    }

    // Record the viewed store
    const viewedStore = new ViewedItem({
      userId,
      itemId: storeId,
      itemType: 'store',
      viewedAt: new Date()
    });
    await viewedStore.save();

    // Update store view count
    await Store.findByIdAndUpdate(storeId, {
      $inc: { viewCount: 1 }
    });

    // Decrement the view request count
    subscriptionToUse.remainingViewRequests -= 1;
    subscriptionToUse.usageHistory.viewRequestsUsed += 1;
    await subscriptionToUse.save();

    // Calculate total remaining views from all subscriptions
    const totalRemainingViews = subscriptions.reduce((total, sub) => total + sub.remainingViewRequests, 0);

    res.json({
      success: true,
      remainingCount: totalRemainingViews,
      message: language === 'en'
        ? `Store view request used. ${totalRemainingViews} remaining`
        : `Mağaza görüntüleme hakkı kullanıldı. ${totalRemainingViews} hak kaldı`
    });
  } catch (error: any) {
    console.error('Error in useStoreViewRequest:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error using store view request' : 'Mağaza görüntüleme hakkı kullanılırken hata oluştu',
      error: error.message
    });
  }
};