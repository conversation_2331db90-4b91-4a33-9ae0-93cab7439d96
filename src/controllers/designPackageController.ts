import { Request, Response } from 'express';
import { DesignPackage } from '../models/DesignPackage';
import { DesignPackageOrder } from '../models/DesignPackageOrder';
import { User } from '../models/User';
import { Payment } from '../models/Payment';
import { initiatePayment, makePaymentWithStoredCard } from '../services/iyzicoPaymentService';

// Get all active design packages
export const getDesignPackages = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const packages = await DesignPackage.find({ isActive: true })
      .sort('order')
      .lean();

    res.json({ packages });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching design packages' : 'Tasarım paketleri alınırken hata oluştu',
      error: error.message
    });
  }
};

// Get design package by ID
export const getDesignPackageById = async (req: Request, res: Response) => {
  const { id } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const designPackage = await DesignPackage.findById(id).lean();

    if (!designPackage) {
      return res.status(404).json({
        message: language === 'en' ? 'Design package not found' : 'Tasarım paketi bulunamadı'
      });
    }

    res.json(designPackage);
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching design package' : 'Tasarım paketi alınırken hata oluştu',
      error: error.message
    });
  }
};

// Create design package order
export const createDesignPackageOrder = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const { packageId } = req.params;
  const { cardInfo, storedCardId, saveCard, notes } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    if (!userId) {
      return res.status(401).json({
        message: language === 'en' ? 'User not authenticated' : 'Kullanıcı kimliği doğrulanmadı'
      });
    }

    // Check if package exists
    const designPackage = await DesignPackage.findById(packageId);
    if (!designPackage) {
      return res.status(404).json({
        message: language === 'en' ? 'Design package not found' : 'Tasarım paketi bulunamadı'
      });
    }

    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
      });
    }

    // Generate order number
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const orderNumber = `DP-${year}${month}${day}-${random}`;

    // Create order
    const order = new DesignPackageOrder({
      userId,
      packageId,
      orderNumber,
      price: designPackage.price,
      currency: designPackage.currency,
      notes,
      deliveryDate: new Date(Date.now() + designPackage.deliveryTime * 24 * 60 * 60 * 1000)
    });

    await order.save();

    // Initiate payment
    const clientIp = req.ip || req.socket.remoteAddress || '127.0.0.1';
    let paymentResult;

    if (storedCardId) {
      paymentResult = await makePaymentWithStoredCard(
        userId.toString(),
        packageId,  // Use the design package ID here
        designPackage.price,
        clientIp,
        storedCardId,
        'design_package',
        order._id.toString()  // Pass the order ID as a separate parameter
      );
    } else {
      if (!cardInfo || !cardInfo.cardHolderName || !cardInfo.cardNumber ||
          !cardInfo.expireMonth || !cardInfo.expireYear || !cardInfo.cvc) {
        await DesignPackageOrder.deleteOne({ _id: order._id });
        return res.status(400).json({
          message: language === 'en' ? 'Invalid card information' : 'Geçersiz kart bilgileri'
        });
      }

      const cardInfoWithOptions = {
        ...cardInfo,
        registerCard: saveCard || false
      };

      paymentResult = await initiatePayment(
        userId.toString(),
        packageId,  // Use the design package ID here
        designPackage.price,
        clientIp,
        cardInfoWithOptions,
        'design_package',
        order._id.toString()  // Pass the order ID as a separate parameter
      );
    }

    // Update order with payment info
    order.paymentId = paymentResult.paymentId;
    if (paymentResult.status === 'success') {
      order.status = 'PAID';
      order.paymentStatus = 'SUCCESS';
      order.iyzicoPaymentId = paymentResult.paymentId;
      order.iyzicoPaymentTransactionId = paymentResult.itemTransactions?.[0]?.paymentTransactionId;
    }
    await order.save();

    // Create payment record
    const payment = new Payment({
      userId,
      packageId: null, // Not a subscription package
      designPackageOrderId: order._id,
      amount: designPackage.price,
      currency: designPackage.currency || 'USD',
      paymentMethod: storedCardId ? 'stored_card' : 'credit_card',
      paymentStatus: paymentResult.status === 'success' ? 'SUCCESS' : 'PENDING',
      iyzicoPaymentId: paymentResult.paymentId,
      iyzicoPaymentTransactionId: paymentResult.itemTransactions?.[0]?.paymentTransactionId,
      ipAddress: clientIp
    });
    await payment.save();

    res.json({
      message: language === 'en' ? 'Order created successfully' : 'Sipariş başarıyla oluşturuldu',
      order: {
        _id: order._id,
        orderNumber: order.orderNumber,
        status: order.status,
        paymentStatus: order.paymentStatus
      },
      paymentPageUrl: paymentResult.paymentPageUrl || null
    });

  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error creating order' : 'Sipariş oluşturulurken hata oluştu',
      error: error.message
    });
  }
};

// Get user's design package orders
export const getUserDesignPackageOrders = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const orders = await DesignPackageOrder.find({ userId })
      .populate('packageId')
      .sort({ createdAt: -1 })
      .lean();

    res.json({ orders });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching orders' : 'Siparişler alınırken hata oluştu',
      error: error.message
    });
  }
};

// Admin: Create design package
export const createDesignPackage = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const designPackage = new DesignPackage(req.body);
    await designPackage.save();

    res.status(201).json({
      message: language === 'en' ? 'Design package created successfully' : 'Tasarım paketi başarıyla oluşturuldu',
      designPackage
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error creating design package' : 'Tasarım paketi oluşturulurken hata oluştu',
      error: error.message
    });
  }
};

// Admin: Update design package
export const updateDesignPackage = async (req: Request, res: Response) => {
  const { id } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const designPackage = await DesignPackage.findByIdAndUpdate(
      id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!designPackage) {
      return res.status(404).json({
        message: language === 'en' ? 'Design package not found' : 'Tasarım paketi bulunamadı'
      });
    }

    res.json({
      message: language === 'en' ? 'Design package updated successfully' : 'Tasarım paketi başarıyla güncellendi',
      designPackage
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error updating design package' : 'Tasarım paketi güncellenirken hata oluştu',
      error: error.message
    });
  }
};

// Admin: Delete design package
export const deleteDesignPackage = async (req: Request, res: Response) => {
  const { id } = req.params;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const designPackage = await DesignPackage.findByIdAndUpdate(
      id,
      { isActive: false },
      { new: true }
    );

    if (!designPackage) {
      return res.status(404).json({
        message: language === 'en' ? 'Design package not found' : 'Tasarım paketi bulunamadı'
      });
    }

    res.json({
      message: language === 'en' ? 'Design package deleted successfully' : 'Tasarım paketi başarıyla silindi'
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error deleting design package' : 'Tasarım paketi silinirken hata oluştu',
      error: error.message
    });
  }
};

// Admin: Get all design package orders
export const getAllDesignPackageOrders = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const { status, page = 1, limit = 20 } = req.query;

  try {
    const query: any = {};
    if (status) {
      query.status = status;
    }

    const orders = await DesignPackageOrder.find(query)
      .populate('userId', 'firstName lastName email')
      .populate('packageId')
      .sort({ createdAt: -1 })
      .limit(Number(limit))
      .skip((Number(page) - 1) * Number(limit))
      .lean();

    const total = await DesignPackageOrder.countDocuments(query);

    res.json({
      orders,
      total,
      page: Number(page),
      totalPages: Math.ceil(total / Number(limit))
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching orders' : 'Siparişler alınırken hata oluştu',
      error: error.message
    });
  }
};

// Admin: Update order status
export const updateOrderStatus = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { status, files } = req.body;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const order = await DesignPackageOrder.findById(id);

    if (!order) {
      return res.status(404).json({
        message: language === 'en' ? 'Order not found' : 'Sipariş bulunamadı'
      });
    }

    order.status = status;

    if (status === 'COMPLETED') {
      order.completedDate = new Date();
      if (files && files.length > 0) {
        order.files = files;
      }
    }

    await order.save();

    res.json({
      message: language === 'en' ? 'Order status updated successfully' : 'Sipariş durumu başarıyla güncellendi',
      order
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error updating order status' : 'Sipariş durumu güncellenirken hata oluştu',
      error: error.message
    });
  }
};