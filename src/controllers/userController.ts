import { Request, Response } from 'express';
import { User } from '../models/User';
import { PopulatedSubscription, Subscription } from '../models/Subscription';
import { IPackage } from '../models/Package';
import mongoose from 'mongoose';
import { Category } from '../models/Category';
import { sendEmail, sendWelcomeEmail } from '../services/emailService';
import { baseEmailTemplate, emailButton, infoBox, alertBox } from '../services/emailTemplates';


export const getUserProfile = async (req: Request, res: Response) => {
  const userId = (req as any).user?._id;
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    const user = await User.findById(userId).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
      });
    }

    // Only get active, not expired, not canceled, not failed, not unpaid standard packages
    const subscriptions = await Subscription.find({
      userId: new mongoose.Types.ObjectId(userId),
      endDate: { $gte: new Date() },
      status: 'ACTIVE', // Only active subscriptions, not canceled ones
      parentSubscriptionId: null  // This will get only standard packages
    })
    .populate<PopulatedSubscription>('packageId')
    .lean();

    // Only get active, not expired, not canceled, not failed, not unpaid addon packages
    const addonSubscriptions = await Subscription.find({
      userId: new mongoose.Types.ObjectId(userId),
      endDate: { $gte: new Date() },
      status: 'ACTIVE', // Only active subscriptions, not canceled ones
      parentSubscriptionId: { $ne: null }  // This will get only addon packages
    })
    .populate<PopulatedSubscription>('packageId')
    .lean();

    // Separate standard and addon subscriptions
    const standardSubscription = subscriptions[0] as PopulatedSubscription | undefined;
    const extraSubscriptions = addonSubscriptions as PopulatedSubscription[];

    // Calculate total remaining requests
    let totalViewRequests = 0;
    let totalCreateRequests = 0;

    if (standardSubscription) {
      totalViewRequests = standardSubscription.remainingViewRequests || 0;
      totalCreateRequests = standardSubscription.remainingCreateRequests || 0;

      // Add requests from extra subscriptions
      extraSubscriptions.forEach(sub => {
        const packageDoc = sub.packageId as IPackage;
        const additionalView = packageDoc.viewRequestLimit || 0;
        const additionalCreate = packageDoc.createRequestLimit || 0;

        totalViewRequests += additionalView;
        totalCreateRequests += additionalCreate;
      });
    }

    const subscriptionData = standardSubscription ? {
      standard: {
        _id: standardSubscription._id,
        packageId: {
          _id: standardSubscription.packageId._id,
          name: standardSubscription.packageId.name,
          type: standardSubscription.packageId.type,
          viewRequestLimit: standardSubscription.packageId.viewRequestLimit,
          createRequestLimit: standardSubscription.packageId.createRequestLimit,
          price: standardSubscription.packageId.price,
          currency: standardSubscription.packageId.currency || 'USD',
        },
        startDate: standardSubscription.startDate,
        endDate: standardSubscription.endDate,
        status: standardSubscription.status,
        features: standardSubscription.packageId.features,
        remainingViewRequests: totalViewRequests,
        remainingCreateRequests: totalCreateRequests,
      },
      addons: extraSubscriptions.map(sub => {
        const packageDoc = sub.packageId as IPackage;
        return {
          _id: sub._id,
          packageId: {
            _id: packageDoc._id,
            name: packageDoc.name,
            type: packageDoc.type,
            viewRequestLimit: packageDoc.viewRequestLimit,
            createRequestLimit: packageDoc.createRequestLimit,
            price: packageDoc.price,
            currency: packageDoc.currency || 'USD',
          },
          startDate: sub.startDate,
          endDate: sub.endDate,
          features: packageDoc.features,
          status: sub.status,
          remainingViewRequests: packageDoc.viewRequestLimit,
          remainingCreateRequests: packageDoc.createRequestLimit,
        };
      })
    } : null;

    // If user has a subscription ID but no active subscription, clear it
    if (user.subscription && !standardSubscription) {
      await User.findByIdAndUpdate(userId, {
        $unset: { subscription: 1 },
        hasPackage: false
      });
      user.subscription = undefined;
      user.hasPackage = false;
    }

    res.json({
      success: true,
      data: {
        ...user.toObject(),
        subscriptionDetails: subscriptionData
      }
    });
  } catch (err) {
    console.error('=== getUserProfile Error ===');
    console.error('Error details:', err);
    console.error('Stack trace:', (err as Error).stack);
    res.status(500).json({
      success: false,
      message: language === 'en' ? 'Server error' : 'Sunucu hatası',
      error: err
    });
  }
};

export const getUserById = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const { id } = req.params;

  try {
    const user = await User.findById(id).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
      });
    }

    res.json(user);
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: language === 'en' ? 'Error fetching user' : 'Kullanıcı getirilirken hata oluştu',
      error: error.message
    });
  }
};

export const updateUserProfile = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const userId = (req as any).user?._id;
  const {
    firstName,
    lastName,
    email,
    password,
    phoneNumber,
    birthDate,
    address,
    city,
    country,
    zipCode,
    categoryLevel1Id,
    categoryLevel2Id,
    categoryLevel3Id
  } = req.body;

  try {
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
      });
    }

    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (email) user.email = email;
    if (password) user.password = password;
    if (phoneNumber) user.phoneNumber = phoneNumber;
    if (birthDate) user.birthDate = birthDate;
    if (address) user.address = address;
    if (city) user.city = city;
    if (country) user.country = country;
    if (zipCode) user.zipCode = zipCode;
    if (categoryLevel1Id) user.categoryLevel1Id = new mongoose.Types.ObjectId(categoryLevel1Id);
    if (categoryLevel2Id) user.categoryLevel2Id = new mongoose.Types.ObjectId(categoryLevel2Id);
    if (categoryLevel3Id) user.categoryLevel3Id = new mongoose.Types.ObjectId(categoryLevel3Id);

    await user.save();

    res.status(200).json({
      success: true,
      message: language === 'en' ? 'Profile updated successfully' : 'Profil başarıyla güncellendi',
      data: user
    });
  } catch (error:any) {
    console.error('Error updating user profile:', error);
    res.status(500).json({
      success: false,
      message: language === 'en' ? 'Error updating profile' : 'Profil güncellenirken hata oluştu',
      error
    });
  }
};

// Helper function to get all parent categories
const getParentCategories = async (categoryId: string): Promise<string[]> => {
  const categoryIds: string[] = [categoryId];
  // Use findOne with {id: categoryId} instead of findById
  let currentCategory = await Category.findOne({ id: categoryId });

  while (currentCategory?.parent_id) {
    categoryIds.push(currentCategory.parent_id);
    // Use findOne with {id: parent_id} instead of findById
    currentCategory = await Category.findOne({ id: currentCategory.parent_id });
  }

  return categoryIds;
};

// Helper function to get users with products in specified categories
const getUsersWithProductsInCategories = async (categoryIds: string[]) => {
  // First, find Categories by id to get their ObjectIds
  const categories = await Category.find({ id: { $in: categoryIds } });
  const categoryObjectIds = categories.map(cat => cat._id);

  // Find all users who have products in any of the specified categories
  const users = await User.find({
    $or: [
      { categoryLevel1Id: { $in: categoryObjectIds } },
      { categoryLevel2Id: { $in: categoryObjectIds } },
      { categoryLevel3Id: { $in: categoryObjectIds } }
    ]
  }).select('email firstName lastName');

  return users;
};

// Helper function to send email notifications
export const sendEmailNotificationToStores = async (categoryIds: string[], newUser: any) => {
  // Fire and forget - don't wait for email sending to complete
  (async () => {
    try {
      // Skip if no category IDs are provided
      if (!categoryIds.length) {
        console.log('No categories provided for email notifications');
        return;
      }

      // Filter out any null/undefined categoryIds
      const validCategoryIds = categoryIds.filter(Boolean);
      if (!validCategoryIds.length) {
        console.log('No valid category IDs provided for email notifications');
        return;
      }

      // Get all parent category IDs for each provided category
      let allCategoryIds: string[] = [...validCategoryIds];

      // Get parent categories for each category ID
      for (const categoryId of validCategoryIds) {
        try {
          const parentIds = await getParentCategories(categoryId);
          allCategoryIds = [...allCategoryIds, ...parentIds];
        } catch (error) {
          console.error(`Error getting parent categories for ID ${categoryId}:`, error);
        }
      }

      // Remove duplicates
      allCategoryIds = [...new Set(allCategoryIds)];

      if (!allCategoryIds.length) {
        console.log('No categories found for notification');
        return;
      }

      // Get all users who have products in these categories
      const usersToNotify = await getUsersWithProductsInCategories(allCategoryIds);

      if (!usersToNotify.length) {
        console.log('No users found to notify about the new registration');
        return;
      }

      // Get category names for the email content
      const categories = await Category.find({ id: { $in: allCategoryIds } });
      const categoryNames = categories.map(cat => cat.name).join(', ');

      console.log(`Found ${usersToNotify.length} users to notify about new registration in categories: ${categoryNames}`);

      // Send email to each user
      for (const user of usersToNotify) {
        // Don't send email to the user who just registered
        if (user.email === newUser.email) {
          continue;
        }

        const subject = '🎉 Kategorinizde Yeni Kullanıcı - E-Export City';
        
        const content = `
          <div style="text-align: center; margin-bottom: 30px;">
            <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #667EEA 0%, #764BA2 100%); border-radius: 50%; line-height: 100px;">
              <span style="font-size: 50px; color: white;">👥</span>
            </div>
          </div>
          
          <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Yeni İş Fırsatı!</h2>
          
          <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 20px 0;">
            Sayın ${user.firstName} ${user.lastName},
          </p>
          
          <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
            Ürünlerinizin bulunduğu kategorilerde yeni bir kullanıcı kayıt oldu. Bu potansiyel bir iş fırsatı olabilir!
          </p>
          
          ${alertBox('Bu kullanıcı sizin ürünlerinizle ilgileniyor olabilir. Hemen iletişime geçin!', 'success')}
          
          ${infoBox('Yeni Kullanıcı Bilgileri', [
            { label: 'İsim', value: `${newUser.firstName} ${newUser.lastName}` },
            { label: 'E-posta', value: newUser.email },
            { label: 'Ülke', value: newUser.country },
            { label: 'Şehir', value: newUser.city || 'Belirtilmemiş' },
            { label: 'Kategoriler', value: categoryNames }
          ])}
          
          <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0; text-align: center;">Ne Yapabilirsiniz?</h3>
          
          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-bottom: 30px;">
            <tr>
              <td style="padding: 0 10px;">
                <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; text-align: center;">
                  <div style="font-size: 30px; margin-bottom: 10px;">📧</div>
                  <h4 style="color: #2D3748; font-size: 16px; margin: 0 0 10px 0;">Mesaj Gönderin</h4>
                  <p style="color: #718096; font-size: 14px; margin: 0;">Platform üzerinden hoş geldin mesajı gönderin</p>
                </div>
              </td>
              <td style="padding: 0 10px;">
                <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; text-align: center;">
                  <div style="font-size: 30px; margin-bottom: 10px;">📦</div>
                  <h4 style="color: #2D3748; font-size: 16px; margin: 0 0 10px 0;">Ürünlerinizi Güncelleyin</h4>
                  <p style="color: #718096; font-size: 14px; margin: 0;">Öne çıkan ürünlerinizi güncelleyin</p>
                </div>
              </td>
            </tr>
          </table>
          
          ${emailButton('Profilimi Güncelle', `${require('../config').config.frontendUrl}/profile`)}
          
          <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-top: 40px;">
            <tr>
              <td style="background-color: #EBF8FF; border-radius: 8px; padding: 20px;">
                <p style="color: #2B6CB0; font-size: 14px; line-height: 20px; margin: 0; text-align: center;">
                  <strong>💡 İpucu:</strong> İlk 24 saat içinde iletişime geçen kullanıcılar %80 daha fazla iş fırsatı yakalıyor!
                </p>
              </td>
            </tr>
          </table>
        `;
        
        const html = baseEmailTemplate(content);

        try {
          await sendEmail(
            user.email,
            subject,
            `Kategorinizde yeni kullanıcı kaydı: ${categoryNames}`,
            html
          );
          console.log(`Category match notification email sent successfully to ${user.email}`);
        } catch (emailError) {
          console.error(`Failed to send category match notification email to ${user.email}:`, emailError);
        }
      }
    } catch (error) {
      console.error('Error in sendEmailNotificationToStores:', error);
    }
  })().catch(error => {
    console.error('Error in email notification process:', error);
  });
};

export const registerUser = async (req: Request, res: Response) => {
  const {
    firstName,
    lastName,
    phoneNumber,
    email,
    password,
    address,
    city,
    country,
    zipCode,
    birthDate,
    categoryLevel1Id,
    categoryLevel2Id,
    categoryLevel3Id
  } = req.body;

  try {
    const newUser = new User({
      firstName,
      lastName,
      phoneNumber,
      email,
      password,
      address,
      city,
      country,
      zipCode,
      birthDate,
      categoryLevel1Id,
      categoryLevel2Id,
      categoryLevel3Id,
      ipAddress: req.ip
    });

    await newUser.save();

    // Always send welcome email to the new user, regardless of categories
    const language = req.headers['accept-language'] as string || 'tr';
    sendWelcomeEmail(email, firstName, lastName, language).catch(err => {
      console.error('Error sending welcome email:', err);
    });

    // Collect all category IDs (filtering out undefined values)
    const categoryIds = [categoryLevel1Id, categoryLevel2Id, categoryLevel3Id].filter(Boolean);

    // Start email notifications to users with matching categories asynchronously without waiting
    if (categoryIds.length > 0) {
      // Handle this in the background
      sendEmailNotificationToStores(categoryIds, newUser);
    }

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: newUser
    });
  } catch (err: any) {
    console.error('Error during user registration:', err);
    res.status(500).json({
      success: false,
      message: 'Error registering user',
      error: err.message
    });
  }
};