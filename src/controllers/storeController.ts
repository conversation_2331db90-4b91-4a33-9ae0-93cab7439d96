import { Request, Response } from 'express';
import { IStore, Store } from '../models/Store';
import { Item } from '../models/Item';
import { User } from '../models/User';
import { AuthRequest } from '../types/express';
import { IUser } from '../models/User';
import { upload } from '../config/multer';
import * as path from 'path';
import * as fs from 'fs';
import { ItemStats } from '../models/ItemStats';
import { sendStoreCreatedEmail, sendStoreApprovedEmail, sendStoreRejectedEmail, sendStoreDisabledEmail, sendStoreEnabledEmail } from '../services/emailService';
import logger from '../utils/logger';

export const getStores = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const totalStores = await Store.countDocuments();
    const activeApprovedCount = await Store.countDocuments({ isActive: true, isApproved: true });
    const allStores = await Store.find().sort({ date: -1 }).lean();
    const stores = await Store.find({ isActive: true, isApproved: true }).sort({ date: -1 }).lean();
    res.json(stores);
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching companies' : 'Firmalar getirilirken hata oluştu',
      error: error.message
    });
  }
};

export const getStoreById = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    // First, get the store
    const store = await Store.findOneAndUpdate(
      {
        _id: req.params.id,
        isActive: true,
        isApproved: true
      },
      { $inc: { viewCount: 1 } },
      { new: true }
    ).populate({
      path: 'owner',
      select: 'firstName lastName email',
    });

    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Company not found' : 'Firma bulunamadı'
      });
    }

    // Then, get the items separately
    const items = await Item.find({
      store: store._id,
      isApproved: true,
      status: 'ACTIVE'
    }).populate('category');

    console.log('Store ID:', store._id);
    console.log('Found items:', items.length);

    // Get view counts for all items
    const itemIds = items.map(item => item._id);
    const itemStats = await ItemStats.find({ itemId: { $in: itemIds } });

    // Create a map of item ID to view count
    const viewCountMap = new Map<string, number>();
    itemStats.forEach(stat => {
      viewCountMap.set(stat.itemId.toString(), stat.viewCount);
    });

    // Add view count to each item
    const itemsWithViewCount = items.map(item => {
      const itemObject: any = item.toObject();
      itemObject.viewCount = viewCountMap.get(item._id.toString()) || 0;
      return itemObject;
    });

    // Create response object with items
    const response: any = store.toObject();
    response.items = itemsWithViewCount;

    res.json(response);
  } catch (error: any) {
    console.log(error);
    res.status(500).json({
      message: language === 'en' ? 'Error fetching company' : 'Firma getirilirken hata oluştu',
      error: error.message
    });
  }
};

export const getStoreItems = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const { type, id } = req.query;
  try {
    const query: any = {
      store: req.params.id??id,
      isApproved: true
    };
    if (type) {
      query.type = type.toString().toLowerCase();
    }
    const items = await Item.find(query)
      .populate('category')
      .populate('store', 'name logo location')
      .sort({ date: -1 });

    // Get view counts for all items
    const itemIds = items.map(item => item._id);
    const itemStats = await ItemStats.find({ itemId: { $in: itemIds } });

    // Create a map of item ID to view count
    const viewCountMap = new Map<string, number>();
    itemStats.forEach(stat => {
      viewCountMap.set(stat.itemId.toString(), stat.viewCount);
    });

    // Add view count to each item
    const itemsWithViewCount = items.map(item => {
      const itemObject: any = item.toObject();
      itemObject.viewCount = viewCountMap.get(item._id.toString()) || 0;
      return itemObject;
    });

    res.json(itemsWithViewCount);
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching company items' : 'Firma ürünleri getirilirken hata oluştu',
      error: error.message
    });
  }
};

export const createStore = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    if (!req.user) {
      return res.status(401).json({
        message: language === 'en' ? 'Unauthorized' : 'Yetkisiz erişim'
      });
    }

    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    const { name, description, address, phone, email, website, socialMedia, city, country } = req.body;

    // Handle file paths
    const logo = files?.logo ? `uploads/stores/${files.logo[0].filename}` : undefined;
    const coverImage = files?.coverImage ? `uploads/stores/${files.coverImage[0].filename}` : undefined;

    const store = new Store({
      name,
      description,
      owner: req.user.id,
      logo,
      coverImage,
      address,
      phone,
      email,
      website,
      location: city || country ? {
        city,
        country
      } : undefined,
      socialMedia: socialMedia ? JSON.parse(socialMedia) : undefined
    });

    await store.save();

    // Get user details for sending email
    const user = await User.findById(req.user.id);
    if (user && user.email) {
      try {
        // Send creation confirmation email
        logger.info(`Sending store creation email for store: ${name}`);
        await sendStoreCreatedEmail(
          user.email,
          user.firstName || 'Kullanıcı',
          user.lastName || '',
          name || 'Mağaza',
          language
        );
        logger.info(`Store creation email sent successfully to: ${user.email}`);
      } catch (emailError) {
        // Log the error but don't fail the store creation
        logger.error('Error sending store creation email:', emailError);
      }
    }

    res.status(201).json(store);
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error creating company' : 'Firma oluşturulurken hata oluştu',
      error: error.message
    });
  }
};

export const updateStore = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const userId = req.user?.id;
  const storeId = req.params.id;
  try {
    const store = await Store.findOne({ _id: storeId, owner: userId });
    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Company not found or unauthorized' : 'Firma bulunamadı veya yetkisiz erişim'
      });
    }

    const files = req.files as { [fieldname: string]: Express.Multer.File[] };
    const { name, description, address, phone, email, website, socialMedia, city, country } = req.body;

    // Handle file paths
    const updates: any = {
      name,
      description,
      address,
      phone,
      email,
      website,
      location: city || country ? {
        city,
        country
      } : store.location, // Keep existing location if new values not provided
      socialMedia: socialMedia ? JSON.parse(socialMedia) : undefined
    };

    // Handle logo
    if (files?.logo) {
      // New logo uploaded - delete old one if exists
      if (store.logo) {
        const oldLogoPath = path.join(__dirname, '../../uploads', store.logo);
        if (fs.existsSync(oldLogoPath)) {
          fs.unlinkSync(oldLogoPath);
        }
      }
      updates.logo = `uploads/stores/${files.logo[0].filename}`;
    } else if (req.body.preserveLogo !== 'true') {
      // No new logo uploaded and not preserving existing - remove logo
      if (store.logo) {
        const oldLogoPath = path.join(__dirname, '../../uploads', store.logo);
        if (fs.existsSync(oldLogoPath)) {
          fs.unlinkSync(oldLogoPath);
        }
        updates.logo = null; // Clear logo field
      }
    }
    // If preserveLogo is true, we don't modify the logo field at all

    // Handle cover image
    if (files?.coverImage) {
      // New cover image uploaded - delete old one if exists
      if (store.coverImage) {
        const oldCoverPath = path.join(__dirname, '../../uploads', store.coverImage);
        if (fs.existsSync(oldCoverPath)) {
          fs.unlinkSync(oldCoverPath);
        }
      }
      updates.coverImage = `uploads/stores/${files.coverImage[0].filename}`;
    } else if (req.body.preserveCoverImage !== 'true') {
      // No new cover image uploaded and not preserving existing - remove cover image
      if (store.coverImage) {
        const oldCoverPath = path.join(__dirname, '../../uploads', store.coverImage);
        if (fs.existsSync(oldCoverPath)) {
          fs.unlinkSync(oldCoverPath);
        }
        updates.coverImage = null; // Clear cover image field
      }
    }
    // If preserveCoverImage is true, we don't modify the coverImage field at all

    const updatedStore = await Store.findByIdAndUpdate(
      storeId,
      { $set: updates },
      { new: true }
    );

    res.json(updatedStore);
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error updating company' : 'Firma güncellenirken hata oluştu',
      error: error.message
    });
  }
};

export const getUserStoreProfile = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const userId = req.user?._id;
  try {
    if (!userId) {
      return res.status(401).json({
        message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
      });
    }
    const store = await Store.findOne({ owner: userId })
      .populate({
        path: 'owner',
        select: 'firstName lastName email'
      })
      .lean();
    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'No company found' : 'Firma bulunamadı',
        code: 'NO_COMPANY'
      });
    }
    res.json({
      store,
      message: language === 'en' ? 'Company profile retrieved' : 'Firma profili getirildi'
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching company profile' : 'Firma profili getirilirken hata oluştu',
      error: error.message
    });
  }
};

export const updateUserProfile = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const userId = req.user?._id;
  try {
    if (!userId) {
      return res.status(401).json({
        message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
      });
    }
    const store = await Store.findOne({ owner: userId });
    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'No company found' : 'Firma bulunamadı'
      });
    }
    if (req.files) {
      const files = req.files as { [fieldname: string]: Express.Multer.File[] };
      if (files.logo) {
        if (store.logo) {
          const oldPath = path.join(__dirname, '../../', store.logo);
          if (fs.existsSync(oldPath)) {
            fs.unlinkSync(oldPath);
          }
        }
        store.logo = `uploads/stores/${files.logo[0].filename}`;
      }
      if (files.coverImage) {
        if (store.coverImage) {
          const oldPath = path.join(__dirname, '../../', store.coverImage);
          if (fs.existsSync(oldPath)) {
            fs.unlinkSync(oldPath);
          }
        }
        store.coverImage = `uploads/stores/${files.coverImage[0].filename}`;
      }
    }
    const updateData = req.body;
    Object.keys(updateData).forEach(key => {
      if (key !== '_id' && key !== 'owner' && key !== 'date') {
        if (key === 'socialMedia' && typeof updateData[key] === 'string') {
          store[key] = JSON.parse(updateData[key]);
        } else {
          (store as any)[key] = updateData[key];
        }
      }
    });
    await store.save();
    res.json({
      store,
      message: language === 'en' ? 'Company updated successfully' : 'Firma başarıyla güncellendi'
    });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error updating company' : 'Firma güncellenirken hata oluştu',
      error: error.message
    });
  }
};

export const deleteStore = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    if (!req.user) {
      return res.status(401).json({
        message: language === 'en' ? 'Unauthorized' : 'Yetkisiz erişim'
      });
    }
    const store = await Store.findOne({
      _id: req.params.id,
      owner: req.user.id
    });
    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Company not found' : 'Firma bulunamadı'
      });
    }
    store.isActive = false;
    await store.save();
    await Item.updateMany(
      { store: store._id },
      { isActive: false }
    );
    res.json({ message: language === 'en' ? 'Company deleted successfully' : 'Firma başarıyla silindi' });
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error deleting company' : 'Firma silinirken hata oluştu',
      error: error.message
    });
  }
};

export const getUserStores = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    if (!req.user) {
      return res.status(401).json({
        message: language === 'en' ? 'Unauthorized' : 'Yetkisiz erişim'
      });
    }
    const stores = await Store.find({
      owner: req.user.id,
      isActive: true
    });
    res.json(stores);
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching user companies' : 'Kullanıcı firmaları getirilirken hata oluştu',
      error: error.message
    });
  }
};

export const getMostViewedStores = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const stores = await Store.find({
      isActive: true,
      isApproved: true
    })
    .sort({ viewCount: -1 })
    .limit(12)
    .lean();

    res.json(stores);
  } catch (error: any) {
    res.status(500).json({
      message: language === 'en' ? 'Error fetching most viewed companies' : 'En çok görüntülenen firmalar getirilirken hata oluştu',
      error: error.message
    });
  }
};

// ADMIN STORE MANAGEMENT FUNCTIONS

/**
 * Get all stores with additional statistics for admin view
 */
export const getAdminStores = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    // Get all stores
    const stores = await Store.find()
      .populate({
        path: 'owner',
        select: 'firstName lastName email',
      })
      .sort({ createdAt: -1 })
      .lean();

    // Get statistics
    const totalStores = stores.length;
    const pendingApprovalCount = stores.filter(store => !store.isApproved).length;
    const activeCount = stores.filter(store => store.isActive && store.isApproved).length;
    const inactiveCount = stores.filter(store => !store.isActive && store.isApproved).length;

    res.json({
      stores,
      stats: {
        totalStores,
        pendingApprovalCount,
        activeCount,
        inactiveCount
      }
    });
  } catch (error: any) {
    console.error('Error fetching admin stores:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error fetching stores' : 'Mağazalar getirilirken hata oluştu',
      error: error.message
    });
  }
};

/**
 * Get a specific store by ID for admin view (includes all data regardless of approval status)
 */
export const getAdminStoreById = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { id } = req.params;

    const store = await Store.findById(id)
      .populate({
        path: 'owner',
        select: 'firstName lastName email',
      });

    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    // Get store items count
    const itemsCount = await Item.countDocuments({ store: store._id });

    // Return store with item count
    res.json({
      ...store.toObject(),
      itemsCount
    });
  } catch (error: any) {
    console.error('Error fetching admin store by ID:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error fetching store details' : 'Mağaza detayları getirilirken hata oluştu',
      error: error.message
    });
  }
};

/**
 * Approve a store
 */
export const approveStore = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { id } = req.params;

    const store = await Store.findByIdAndUpdate(
      id,
      { isApproved: true },
      { new: true }
    );

    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    // Get store owner details for sending email
    try {
      const storeWithOwner = await Store.findById(id).populate('owner');
      if (storeWithOwner && storeWithOwner.owner && 'email' in storeWithOwner.owner) {
        const owner = storeWithOwner.owner as any;
        logger.info(`Sending store approval email for store: ${store.name}`);

        await sendStoreApprovedEmail(
          owner.email,
          owner.firstName || 'Kullanıcı',
          owner.lastName || '',
          store.name || 'Mağaza',
          store._id.toString(),
          language
        );

        logger.info(`Store approval email sent successfully to: ${owner.email}`);
      }
    } catch (emailError) {
      // Log the error but don't fail the approval
      logger.error('Error sending store approval email:', emailError);
    }

    res.json({
      message: language === 'en' ? 'Store approved successfully' : 'Mağaza başarıyla onaylandı',
      store
    });
  } catch (error: any) {
    console.error('Error approving store:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error approving store' : 'Mağaza onaylanırken hata oluştu',
      error: error.message
    });
  }
};

/**
 * Reject a store
 */
export const rejectStore = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const store = await Store.findByIdAndUpdate(
      id,
      {
        isApproved: false,
        rejectionReason: reason || (language === 'en' ? 'Rejected by admin' : 'Admin tarafından reddedildi')
      },
      { new: true }
    );

    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    // Get store owner details for sending email
    try {
      const storeWithOwner = await Store.findById(id).populate('owner');
      if (storeWithOwner && storeWithOwner.owner && 'email' in storeWithOwner.owner) {
        const owner = storeWithOwner.owner as any;
        logger.info(`Sending store rejection email for store: ${store.name}`);

        await sendStoreRejectedEmail(
          owner.email,
          owner.firstName || 'Kullanıcı',
          owner.lastName || '',
          store.name || 'Mağaza',
          reason,
          language
        );

        logger.info(`Store rejection email sent successfully to: ${owner.email}`);
      }
    } catch (emailError) {
      // Log the error but don't fail the rejection
      logger.error('Error sending store rejection email:', emailError);
    }

    res.json({
      message: language === 'en' ? 'Store rejected successfully' : 'Mağaza başarıyla reddedildi',
      store
    });
  } catch (error: any) {
    console.error('Error rejecting store:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error rejecting store' : 'Mağaza reddedilirken hata oluştu',
      error: error.message
    });
  }
};

/**
 * Toggle store active status
 */
export const toggleStoreStatus = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { id } = req.params;

    // First get the current status
    const existingStore = await Store.findById(id);
    if (!existingStore) {
      return res.status(404).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    // Toggle the status
    const store: any = await Store.findByIdAndUpdate(
      id,
      { isActive: !existingStore.isActive },
      { new: true }
    );

    // Get store owner details for sending email
    try {
      const storeWithOwner = await Store.findById(id).populate('owner');
      if (storeWithOwner && storeWithOwner.owner && 'email' in storeWithOwner.owner) {
        const owner = storeWithOwner.owner as any;

        if (store.isActive) {
          logger.info(`Sending store enabled email for store: ${store.name}`);
          await sendStoreEnabledEmail(
            owner.email,
            owner.firstName || 'Kullanıcı',
            owner.lastName || '',
            store.name || 'Mağaza',
            store._id.toString(),
            language
          );
          logger.info(`Store enabled email sent successfully to: ${owner.email}`);
        } else {
          logger.info(`Sending store disabled email for store: ${store.name}`);
          await sendStoreDisabledEmail(
            owner.email,
            owner.firstName || 'Kullanıcı',
            owner.lastName || '',
            store.name || 'Mağaza',
            'Admin tarafından devre dışı bırakıldı',
            language
          );
          logger.info(`Store disabled email sent successfully to: ${owner.email}`);
        }
      }
    } catch (emailError) {
      // Log the error but don't fail the status change
      logger.error('Error sending store status change email:', emailError);
    }

    res.json({
      message: store.isActive
        ? (language === 'en' ? 'Store activated successfully' : 'Mağaza başarıyla aktifleştirildi')
        : (language === 'en' ? 'Store deactivated successfully' : 'Mağaza başarıyla devre dışı bırakıldı'),
      store
    });
  } catch (error: any) {
    console.error('Error toggling store status:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error updating store status' : 'Mağaza durumu güncellenirken hata oluştu',
      error: error.message
    });
  }
};

/**
 * Admin delete store
 */
export const adminDeleteStore = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const { id } = req.params;

    // First check if store exists
    const store = await Store.findById(id);
    if (!store) {
      return res.status(404).json({
        message: language === 'en' ? 'Store not found' : 'Mağaza bulunamadı'
      });
    }

    // Get store owner details for sending email before deletion
    try {
      const storeWithOwner = await Store.findById(id).populate('owner');
      if (storeWithOwner && storeWithOwner.owner && 'email' in storeWithOwner.owner) {
        const owner = storeWithOwner.owner as any;
        logger.info(`Sending store deletion email for store: ${store.name}`);

        await sendStoreDisabledEmail(
          owner.email,
          owner.firstName || 'Kullanıcı',
          owner.lastName || '',
          store.name || 'Mağaza',
          'Mağaza kalıcı olarak silindi',
          language
        );

        logger.info(`Store deletion email sent successfully to: ${owner.email}`);
      }
    } catch (emailError) {
      // Log the error but don't fail the deletion
      logger.error('Error sending store deletion email:', emailError);
    }

    // Delete all items associated with the store
    await Item.deleteMany({ store: id });

    // Delete the store
    await Store.findByIdAndDelete(id);

    res.json({
      message: language === 'en' ? 'Store and all associated items deleted successfully' : 'Mağaza ve ilgili tüm ürünler başarıyla silindi'
    });
  } catch (error: any) {
    console.error('Error deleting store by admin:', error);
    res.status(500).json({
      message: language === 'en' ? 'Error deleting store' : 'Mağaza silinirken hata oluştu',
      error: error.message
    });
  }
};
