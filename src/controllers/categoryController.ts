import { Request, Response } from 'express';
import { Category, ICategory } from '../models/Category';

export const getPublicCategories = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  try {
    const categories = await Category.find({ parent_id: { $in: ['', null] } });    // For each category, check if it has subcategories
    const categoriesWithMeta = await Promise.all(categories.map(async (cat) => {
      const hasSubCategories = await Category.exists({ parent_id: cat.id });
      return {
        ...cat.toObject(),
        hasSubCategories: !!hasSubCategories
      };
    }));

    res.status(200).json({
      success: true,
      data: categoriesWithMeta
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: language === 'en' ? 'Error fetching categories' : 'Kategoriler getirilirken hata <PERSON>',
      error: error.message
    });
  }
};

export const getPublicSubCategories = async (req: Request, res: Response) => {
  const language = req.headers['accept-language'] as string || 'tr';
  const { categoryId } = req.params;

  try {
    const subCategories = await Category.find({ parent_id: categoryId });
    // For each subcategory, fetch its subcategories and add them to the result
    const subCategoriesWithMeta = await Promise.all(subCategories.map(async (subCat) => {
      const childSubcategories = await Category.find({ parent_id: subCat.id });

      // Check if each child has further subcategories
      const childSubcategoriesWithMeta = await Promise.all(childSubcategories.map(async (childCat) => {
        const hasSubCategories = await Category.exists({ parent_id: childCat.id });
        return {
          ...childCat.toObject(),
          hasSubCategories: !!hasSubCategories
        };
      }));

      return {
        ...subCat.toObject(),
        hasSubCategories: childSubcategories.length > 0,
        subcategories: childSubcategoriesWithMeta
      };
    }));

    res.status(200).json({
      success: true,
      data: subCategoriesWithMeta
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      message: language === 'en' ? 'Error fetching sub-categories' : 'Alt kategoriler getirilirken hata oluştu',
      error: error.message
    });
  }
};