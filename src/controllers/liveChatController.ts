import { Request, Response } from 'express';
import { LiveChat, ILiveChatMessage, ILiveChat } from '../models/LiveChat';
import { User } from '../models/User';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { getIO } from '../services/socketService';

// Start or retrieve a chat session
export const startChat = async (req: Request, res: Response) => {
  try {
    const { name, email, subject } = req.body;
    const language = req.headers['accept-language'] as string || 'tr';
    
    // Generate an anonymous ID if the user is not logged in
    let userId: string | undefined;
    let anonymousId: string | undefined;
    
    if ((req as any).user?._id) {
      userId = (req as any).user?._id;
    } else {
      anonymousId = req.body.anonymousId || uuidv4();
    }
    
    // Validate required fields
    if (!name) {
      return res.status(400).json({
        message: language === 'en' ? 'Name is required' : '<PERSON><PERSON><PERSON> gere<PERSON>'
      });
    }
    
    // Check if there's an active chat session for this user
    let chatSession: any;
    
    if (userId) {
      chatSession = await LiveChat.findOne({
        userId,
        status: 'active'
      });
    } else if (anonymousId) {
      chatSession = await LiveChat.findOne({
        anonymousId,
        status: 'active'
      });
    }
    
    // If no active session, create a new one
    if (!chatSession) {
      chatSession = new LiveChat({
        userId: userId ? new mongoose.Types.ObjectId(userId) : undefined,
        anonymousId,
        name,
        email,
        subject,
        status: 'active',
        messages: [],
        unreadCount: 0
      });
      
      await chatSession.save();
    }
    
    return res.status(200).json({
      chatId: chatSession._id,
      anonymousId: chatSession.anonymousId,
      status: chatSession.status,
      messages: chatSession.messages
    });
    
  } catch (error: any) {
    console.error('Error in startChat:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error starting chat session' : 'Sohbet oturumu başlatılırken hata oluştu'
    });
  }
};

// Get chat history
export const getChatHistory = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const language = req.headers['accept-language'] as string || 'tr';
    
    // Check if the chat exists
    const chatSession = await LiveChat.findById(chatId);
    
    if (!chatSession) {
      return res.status(404).json({
        message: language === 'en' ? 'Chat session not found' : 'Sohbet oturumu bulunamadı'
      });
    }
    
    // Check if the user has access to this chat
    let hasAccess = false;
    
    // Admin always has access
    if ((req as any).user?.isAdmin) {
      hasAccess = true;
      console.log('Admin access granted to chat:', chatId);
    } 
    // Logged in user checks
    else if ((req as any).user?._id) {
      const userId = (req as any).user?._id;
      hasAccess = chatSession.userId?.toString() === userId.toString();
      console.log('User access check:', { userId, chatUserId: chatSession.userId, hasAccess });
    } 
    // Anonymous user checks
    else {
      const { anonymousId } = req.query;
      hasAccess = chatSession.anonymousId === anonymousId;
      console.log('Anonymous access check:', { providedAnonymousId: anonymousId, chatAnonymousId: chatSession.anonymousId, hasAccess });
    }
    
    if (!hasAccess) {
      console.log('Access denied to chat:', chatId, 'for user:', (req as any).user?._id, 'isAdmin:', (req as any).user?.isAdmin);
      return res.status(403).json({
        message: language === 'en' ? 'You do not have access to this chat session' : 'Bu sohbet oturumuna erişiminiz yok'
      });
    }
    
    // Mark messages as read if user is admin
    if ((req as any).user?.isAdmin) {
      await chatSession.markAsRead();
    }
    
    return res.status(200).json({
      chatId: chatSession._id,
      status: chatSession.status,
      messages: chatSession.messages,
      name: chatSession.name,
      email: chatSession.email,
      subject: chatSession.subject
    });
    
  } catch (error: any) {
    console.error('Error in getChatHistory:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error retrieving chat history' : 'Sohbet geçmişi alınırken hata oluştu'
    });
  }
};

// Send a message in a chat session
export const sendChatMessage = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const { content, anonymousId } = req.body;
    const language = req.headers['accept-language'] as string || 'tr';
    
    if (!content) {
      return res.status(400).json({
        message: language === 'en' ? 'Message content is required' : 'Mesaj içeriği gereklidir'
      });
    }
    
    // Find the chat session
    const chatSession = await LiveChat.findById(chatId);
    
    if (!chatSession) {
      return res.status(404).json({
        message: language === 'en' ? 'Chat session not found' : 'Sohbet oturumu bulunamadı'
      });
    }
    
    // Determine sender type and ID
    let senderId: string;
    let senderType: 'user' | 'admin' | 'anonymous';
    
    if ((req as any).user?.isAdmin) {
      senderId = (req as any).user?._id;
      senderType = 'admin';
    } else if ((req as any).user?._id) {
      senderId = (req as any).user?._id;
      senderType = 'user';
      
      // Ensure the user owns this chat
      if (chatSession.userId?.toString() !== senderId) {
        return res.status(403).json({
          message: language === 'en' ? 'You do not have access to this chat session' : 'Bu sohbet oturumuna erişiminiz yok'
        });
      }
    } else {
      // Anonymous user
      if (!anonymousId || chatSession.anonymousId !== anonymousId) {
        return res.status(403).json({
          message: language === 'en' ? 'Invalid anonymous ID' : 'Geçersiz anonim kimlik'
        });
      }
      
      senderId = anonymousId;
      senderType = 'anonymous';
    }
    
    // Create the message
    const message: ILiveChatMessage = {
      content,
      senderId,
      senderType,
      timestamp: new Date(),
      read: false
    };
    
    // Add the message to the chat
    chatSession.messages.push(message);
    chatSession.lastMessage = message;
    
    if (message.senderType !== 'admin') {
      chatSession.unreadCount = (chatSession.unreadCount || 0) + 1;
    }
    
    await chatSession.save();
    
    // Try to notify through socket if available
    try {
      const socketService = require('../services/socketService');
      const io = socketService.getIO();
      
      if (io) {
        io.to(`chat_${chatId}`).emit('livechat:message', {
          chatId,
          message
        });
        
        // If the sender is not admin, notify all admins
        if (senderType !== 'admin') {
          io.to('admin').emit('livechat:new_message', {
            chatId,
            message,
            name: chatSession.name,
            timestamp: new Date()
          });
        }
      }
    } catch (socketError) {
      // Socket notification is optional, just log the error
      console.log('Socket notification failed, continuing with REST response:', 
        socketError instanceof Error ? socketError.message : 'Unknown error');
    }
    
    return res.status(201).json({
      message,
      status: 'sent'
    });
    
  } catch (error: any) {
    console.error('Error in sendChatMessage:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error sending chat message' : 'Sohbet mesajı gönderilirken hata oluştu'
    });
  }
};

// Close a chat session
export const closeChat = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const language = req.headers['accept-language'] as string || 'tr';
    
    // Find the chat session
    const chatSession = await LiveChat.findById(chatId);
    
    if (!chatSession) {
      return res.status(404).json({
        message: language === 'en' ? 'Chat session not found' : 'Sohbet oturumu bulunamadı'
      });
    }
    
    // Check if the user has permission to close the chat
    if (!(req as any).user?.isAdmin && 
        chatSession.userId?.toString() !== (req as any).user?._id?.toString() &&
        chatSession.anonymousId !== req.body.anonymousId) {
      return res.status(403).json({
        message: language === 'en' ? 'You do not have permission to close this chat' : 'Bu sohbeti kapatma izniniz yok'
      });
    }
    
    // Close the chat
    chatSession.status = 'closed';
    await chatSession.save();
    
    // Try to notify through socket if available
    try {
      const socketService = require('../services/socketService');
      const io = socketService.getIO();
      
      if (io) {
        io.to(`chat_${chatId}`).emit('livechat:closed', {
          chatId,
          timestamp: new Date()
        });
      }
    } catch (socketError) {
      // Socket notification is optional, just log the error
      console.log('Socket notification failed during chat close, continuing with REST response:', 
        socketError instanceof Error ? socketError.message : 'Unknown error');
    }
    
    return res.status(200).json({
      message: language === 'en' ? 'Chat session closed successfully' : 'Sohbet oturumu başarıyla kapatıldı',
      status: chatSession.status
    });
    
  } catch (error: any) {
    console.error('Error in closeChat:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error closing chat session' : 'Sohbet oturumu kapatılırken hata oluştu'
    });
  }
};

// Archive a chat session (admin only)
export const archiveChat = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const language = req.headers['accept-language'] as string || 'tr';
    
    // Check if user is admin
    if (!(req as any).user?.isAdmin) {
      return res.status(403).json({
        message: language === 'en' ? 'Only administrators can archive chat sessions' : 'Yalnızca yöneticiler sohbet oturumlarını arşivleyebilir'
      });
    }
    
    // Find the chat session
    const chatSession = await LiveChat.findById(chatId);
    
    if (!chatSession) {
      return res.status(404).json({
        message: language === 'en' ? 'Chat session not found' : 'Sohbet oturumu bulunamadı'
      });
    }
    
    await chatSession.archiveChat();
    
    return res.status(200).json({
      message: language === 'en' ? 'Chat session archived successfully' : 'Sohbet oturumu başarıyla arşivlendi',
      status: chatSession.status
    });
    
  } catch (error: any) {
    console.error('Error in archiveChat:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error archiving chat session' : 'Sohbet oturumu arşivlenirken hata oluştu'
    });
  }
};

// Get all active chats (admin only)
export const getActiveChats = async (req: Request, res: Response) => {
  try {
    const language = req.headers['accept-language'] as string || 'tr';
    
    // Check if user is admin
    if (!(req as any).user?.isAdmin) {
      return res.status(403).json({
        message: language === 'en' ? 'Only administrators can view all chat sessions' : 'Yalnızca yöneticiler tüm sohbet oturumlarını görüntüleyebilir'
      });
    }
    
    const chats = await LiveChat.find({ status: 'active' })
      .sort({ updatedAt: -1 })
      .populate('userId', 'firstName lastName email');
    
    const formattedChats = chats.map(chat => ({
      chatId: chat._id,
      name: chat.name,
      email: chat.email,
      subject: chat.subject,
      lastMessage: chat.lastMessage,
      unreadCount: chat.unreadCount,
      createdAt: chat.createdAt,
      updatedAt: chat.updatedAt,
      user: chat.userId
    }));
    
    return res.status(200).json(formattedChats);
    
  } catch (error: any) {
    console.error('Error in getActiveChats:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error retrieving active chats' : 'Aktif sohbetler alınırken hata oluştu'
    });
  }
};

// Get all closed chats (admin only)
export const getClosedChats = async (req: Request, res: Response) => {
  try {
    const language = req.headers['accept-language'] as string || 'tr';
    
    // Check if user is admin
    if (!(req as any).user?.isAdmin) {
      return res.status(403).json({
        message: language === 'en' ? 'Only administrators can view closed chat sessions' : 'Yalnızca yöneticiler kapatılmış sohbet oturumlarını görüntüleyebilir'
      });
    }
    
    // Pagination parameters
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    
    const chats = await LiveChat.find({ status: 'closed' })
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('userId', 'firstName lastName email');
    
    const total = await LiveChat.countDocuments({ status: 'closed' });
    
    const formattedChats = chats.map(chat => ({
      chatId: chat._id,
      name: chat.name,
      email: chat.email,
      subject: chat.subject,
      lastMessage: chat.lastMessage,
      messageCount: chat.messages.length,
      createdAt: chat.createdAt,
      updatedAt: chat.updatedAt,
      user: chat.userId
    }));
    
    return res.status(200).json({
      chats: formattedChats,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error: any) {
    console.error('Error in getClosedChats:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error retrieving closed chats' : 'Kapatılmış sohbetler alınırken hata oluştu'
    });
  }
};

// Get all archived chats (admin only)
export const getArchivedChats = async (req: Request, res: Response) => {
  try {
    const language = req.headers['accept-language'] as string || 'tr';
    
    // Check if user is admin
    if (!(req as any).user?.isAdmin) {
      return res.status(403).json({
        message: language === 'en' ? 'Only administrators can view archived chat sessions' : 'Yalnızca yöneticiler arşivlenmiş sohbet oturumlarını görüntüleyebilir'
      });
    }
    
    // Pagination parameters
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    
    const chats = await LiveChat.find({ status: 'archived' })
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('userId', 'firstName lastName email');
    
    const total = await LiveChat.countDocuments({ status: 'archived' });
    
    const formattedChats = chats.map(chat => ({
      chatId: chat._id,
      name: chat.name,
      email: chat.email,
      subject: chat.subject,
      lastMessage: chat.lastMessage,
      messageCount: chat.messages.length,
      createdAt: chat.createdAt,
      updatedAt: chat.updatedAt,
      user: chat.userId
    }));
    
    return res.status(200).json({
      chats: formattedChats,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    });
    
  } catch (error: any) {
    console.error('Error in getArchivedChats:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error retrieving archived chats' : 'Arşivlenmiş sohbetler alınırken hata oluştu'
    });
  }
};

// Get chat statistics (admin only)
export const getChatStatistics = async (req: Request, res: Response) => {
  try {
    const language = req.headers['accept-language'] as string || 'tr';
    
    // Check if user is admin
    if (!(req as any).user?.isAdmin) {
      return res.status(403).json({
        message: language === 'en' ? 'Only administrators can view chat statistics' : 'Yalnızca yöneticiler sohbet istatistiklerini görüntüleyebilir'
      });
    }
    
    const [activeCount, closedCount, archivedCount, totalChats] = await Promise.all([
      LiveChat.countDocuments({ status: 'active' }),
      LiveChat.countDocuments({ status: 'closed' }),
      LiveChat.countDocuments({ status: 'archived' }),
      LiveChat.countDocuments()
    ]);
    
    // Total messages
    const totalMessagesResult = await LiveChat.aggregate([
      { $project: { messageCount: { $size: '$messages' } } },
      { $group: { _id: null, total: { $sum: '$messageCount' } } }
    ]);
    
    const totalMessages = totalMessagesResult.length > 0 ? totalMessagesResult[0].total : 0;
    
    // Get active chats with unread messages
    const activeChatsWithUnread = await LiveChat.countDocuments({
      status: 'active',
      unreadCount: { $gt: 0 }
    });
    
    // Calculate average response time (admin messages after user messages)
    // This is a simplified calculation and could be improved
    const averageResponseTimeResult = await LiveChat.aggregate([
      { $unwind: '$messages' },
      { $sort: { 'messages.timestamp': 1 } },
      { $group: {
          _id: '$_id',
          messages: {
            $push: {
              senderType: '$messages.senderType',
              timestamp: '$messages.timestamp'
            }
          }
        }
      },
      { $project: {
          responseTimes: {
            $reduce: {
              input: '$messages',
              initialValue: {
                prevType: null,
                prevTime: null,
                times: []
              },
              in: {
                prevType: '$$this.senderType',
                prevTime: '$$this.timestamp',
                times: {
                  $cond: {
                    if: { $and: [
                      { $eq: ['$$this.senderType', 'admin'] },
                      { $ne: ['$$value.prevType', 'admin'] },
                      { $ne: ['$$value.prevType', null] }
                    ]},
                    then: {
                      $concatArrays: [
                        '$$value.times',
                        [{
                          $subtract: ['$$this.timestamp', '$$value.prevTime']
                        }]
                      ]
                    },
                    else: '$$value.times'
                  }
                }
              }
            }
          }
        }
      },
      { $unwind: '$responseTimes.times' },
      { $group: {
          _id: null,
          averageTime: { $avg: '$responseTimes.times' }
        }
      }
    ]);
    
    const averageResponseTimeMs = averageResponseTimeResult.length > 0 
      ? Math.round(averageResponseTimeResult[0].averageTime) 
      : 0;
      
    // Convert to minutes
    const averageResponseTimeMinutes = Math.round(averageResponseTimeMs / 60000 * 10) / 10;
    
    return res.status(200).json({
      activeChats: activeCount,
      closedChats: closedCount,
      archivedChats: archivedCount,
      totalChats,
      totalMessages,
      activeChatsWithUnread,
      averageResponseTimeMinutes
    });
    
  } catch (error: any) {
    console.error('Error in getChatStatistics:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error retrieving chat statistics' : 'Sohbet istatistikleri alınırken hata oluştu'
    });
  }
};

// Mark messages as read
export const markMessagesAsRead = async (req: Request, res: Response) => {
  try {
    const { chatId } = req.params;
    const language = req.headers['accept-language'] as string || 'tr';
    
    console.log('Request to mark messages as read:', {
      chatId,
      userId: (req as any).user?._id,
      isAdmin: (req as any).user?.isAdmin,
      anonymousId: req.body.anonymousId
    });
    
    // Find the chat session
    const chatSession = await LiveChat.findById(chatId);
    
    if (!chatSession) {
      return res.status(404).json({
        message: language === 'en' ? 'Chat session not found' : 'Sohbet oturumu bulunamadı'
      });
    }
    
    // Verify user has access to this chat
    let hasAccess = false;
    
    // Admin always has access
    if ((req as any).user?.isAdmin) {
      hasAccess = true;
      console.log('Admin access granted to mark messages as read');
    } 
    // Logged in user
    else if ((req as any).user?._id) {
      hasAccess = chatSession.userId?.toString() === (req as any).user?._id.toString();
      console.log('User access check for marking messages as read:', { 
        hasAccess, 
        chatUserId: chatSession.userId?.toString(), 
        requestUserId: (req as any).user?._id.toString() 
      });
    } 
    // Anonymous user
    else {
      hasAccess = chatSession.anonymousId === req.body.anonymousId;
      console.log('Anonymous access check for marking messages as read:', { 
        hasAccess, 
        chatAnonymousId: chatSession.anonymousId, 
        requestAnonymousId: req.body.anonymousId 
      });
    }
    
    if (!hasAccess) {
      return res.status(403).json({
        message: language === 'en' ? 'You do not have access to this chat session' : 'Bu sohbet oturumuna erişiminiz yok'
      });
    }
    
    // Mark messages as read
    if (chatSession.unreadCount > 0) {
      chatSession.messages.forEach((message: ILiveChatMessage) => {
        if (!message.read) {
          message.read = true;
        }
      });
      
      chatSession.unreadCount = 0;
      await chatSession.save();
      console.log(`Marked all messages as read for chat ${chatId}`);
    } else {
      console.log(`No unread messages to mark for chat ${chatId}`);
    }
    
    return res.status(200).json({
      message: language === 'en' ? 'Messages marked as read' : 'Mesajlar okundu olarak işaretlendi'
    });
    
  } catch (error: any) {
    console.error('Error in markMessagesAsRead:', error);
    const language = req.headers['accept-language'] as string || 'tr';
    return res.status(500).json({
      message: language === 'en' ? 'Error marking messages as read' : 'Mesajları okundu olarak işaretlerken hata oluştu'
    });
  }
};