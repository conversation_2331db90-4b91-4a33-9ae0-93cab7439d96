import { ButtonProps, extendTheme } from '@chakra-ui/react';

const theme = extendTheme({
  colors: {
    primary: {
      50: '#E6FFFA',
      100: '#B2F5EA',
      200: '#81E6D9',
      300: '#4FD1C5',
      400: '#38B2AC',
      500: '#319795',
      600: '#2C7A7B',
      700: '#285E61',
      800: '#234E52',
      900: '#1D4044',
    },
    secondary: {
      50: '#FFF5F7',
      100: '#FED7E2',
      200: '#FBB6CE',
      300: '#F687B3',
      400: '#ED64A6',
      500: '#D53F8C',
      600: '#B83280',
      700: '#97266D',
      800: '#702459',
      900: '#521B41',
    },
  },
  fonts: {
    heading: `'Poppins', 'Noto Sans SC', 'Noto Sans Arabic', sans-serif`,
    body: `'Inter', 'Noto Sans SC', 'Noto Sans Arabic', sans-serif`,
  },
  styles: {
    global: (props: any) => ({
      'html, body': {
        backgroundColor: 'gray.50',
        color: 'gray.800',
        fontFamily: props.locale === 'zh' ? 
          `'Noto Sans SC', 'Inter', sans-serif` : 
          props.locale === 'ar' ? 
          `'Noto Sans Arabic', 'Inter', sans-serif` : 
          `'Inter', sans-serif`,
      },
    }),
  },
  components: {
    Button: {
      baseStyle: {
        fontWeight: 'semibold',
        borderRadius: 'md',
      },

      variants: {
        solid: (props: ButtonProps) => ({
          bg: `${props.colorScheme}.500`,
          color: 'white',
          _hover: {
            bg: `${props.colorScheme}.600`,
          },
        }),
        outline: (props: ButtonProps) => ({
          border: '2px solid',
          borderColor: `${props.colorScheme}.500`,
          color: `${props.colorScheme}.500`,
        }),
        ghost: (props: ButtonProps) => ({
          color: `${props.colorScheme}.500`,
          _hover: {
            bg: `${props.colorScheme}.50`,
          },
        }),
      },
    },
    Card: {
      baseStyle: {
        p: '6',
        bg: 'white',
        boxShadow: 'md',
        borderRadius: 'lg',
      },
    },
  },
});

export default theme;