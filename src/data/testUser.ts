export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  company: string;
  position: string;
  avatar: string;
  type: 'company' | 'individual' | 'broker';
}

export const testUser: User = {
  id: '1',
  firstName: 'Ahmet',
  lastName: 'Yılma<PERSON>',
  email: '<EMAIL>',
  company: 'TechGlobal Solutions',
  position: 'CEO',
  avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80",
  type: 'company'
};

export const setTestUser = () => {
  localStorage.setItem('isLoggedIn', 'true');
  localStorage.setItem('user', JSON.stringify(testUser));
};

export const getUser = (): User | null => {
  const userStr = localStorage.getItem('user');
  return userStr ? JSON.parse(userStr) : null;
};

export const isLoggedIn = (): boolean => {
  return localStorage.getItem('isLoggedIn') === 'true';
};

export const logout = () => {
  localStorage.removeItem('isLoggedIn');
  localStorage.removeItem('user');
  window.location.href = '/';
};