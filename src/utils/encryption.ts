import crypto from 'crypto';
import logger from './logger';

// Encryption configuration
const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const TAG_LENGTH = 16; // 128 bits
const SALT_LENGTH = 64; // 512 bits

// Get encryption key from environment or generate one
const getEncryptionKey = (): Buffer => {
  const envKey = process.env.CARD_ENCRYPTION_KEY;
  
  
  if (!envKey) {
    logger.warn('CARD_ENCRYPTION_KEY not found in environment variables. Using default key (NOT SECURE FOR PRODUCTION)');
    // In production, this should throw an error
    // For development, we'll use a default key
    const defaultKey = 'default-development-key-not-secure-change-this!!';
    return crypto.scryptSync(defaultKey, 'salt', KEY_LENGTH);
  }
  
  // If the key is base64 encoded
  if (envKey.length === 44 && /^[A-Za-z0-9+/=]+$/.test(envKey)) {
    return Buffer.from(envKey, 'base64');
  }
  
  // Otherwise, derive a key from the string
  return crypto.scryptSync(envKey, 'salt', KEY_LENGTH);
};

// Cache the key to avoid repeated derivation
const ENCRYPTION_KEY = getEncryptionKey();

/**
 * Encrypts sensitive data using AES-256-GCM
 * @param text The plain text to encrypt
 * @returns Encrypted text in format: iv:authTag:encrypted
 */
export const encrypt = (text: string | undefined | null): string => {
  if (!text) return '';
  
  try {
    // Generate a random initialization vector
    const iv = crypto.randomBytes(IV_LENGTH);
    
    // Create cipher
    const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
    
    // Encrypt the text
    const encrypted = Buffer.concat([
      cipher.update(text, 'utf8'),
      cipher.final()
    ]);
    
    // Get the authentication tag
    const authTag = cipher.getAuthTag();
    
    // Combine iv, authTag, and encrypted data
    // Format: base64(iv):base64(authTag):base64(encrypted)
    return `${iv.toString('base64')}:${authTag.toString('base64')}:${encrypted.toString('base64')}`;
  } catch (error) {
    logger.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
};

/**
 * Decrypts data encrypted with the encrypt function
 * @param encryptedText The encrypted text in format: iv:authTag:encrypted
 * @returns Decrypted plain text
 */
export const decrypt = (encryptedText: string | undefined | null): string => {
  if (!encryptedText) return '';
  
  try {
    // Check if this might be unencrypted data (backward compatibility)
    if (!isEncrypted(encryptedText)) {
      logger.warn('Attempting to decrypt unencrypted data');
      return encryptedText;
    }
    
    // Split the encrypted text
    const parts = encryptedText.split(':');
    if (parts.length !== 3) {
      logger.error('Invalid encrypted data format:', { parts: parts.length });
      throw new Error('Invalid encrypted data format');
    }
    
    const [ivBase64, authTagBase64, encryptedBase64] = parts;
    
    // Decode from base64
    const iv = Buffer.from(ivBase64, 'base64');
    const authTag = Buffer.from(authTagBase64, 'base64');
    const encrypted = Buffer.from(encryptedBase64, 'base64');
    
    // Create decipher
    const decipher = crypto.createDecipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
    decipher.setAuthTag(authTag);
    
    // Decrypt
    const decrypted = Buffer.concat([
      decipher.update(encrypted),
      decipher.final()
    ]);
    
    return decrypted.toString('utf8');
  } catch (error: any) {
    logger.error('Decryption error:', {
      error: error.message,
      stack: error.stack,
      hasEncryptionKey: !!process.env.CARD_ENCRYPTION_KEY,
      dataFormat: encryptedText ? 'encrypted' : 'empty'
    });
    
    // If decryption fails and we're in development, provide a more helpful error
    if (process.env.NODE_ENV === 'development') {
      throw new Error(
        'Failed to decrypt data. This usually happens when:\n' +
        '1. CARD_ENCRYPTION_KEY is not set in .env\n' +
        '2. The encryption key has changed\n' +
        '3. The data was encrypted with a different key\n' +
        'Run: npm run generate:encryption-key to create a new key'
      );
    }
    
    throw new Error('Failed to decrypt data');
  }
};

/**
 * Generates a secure random token
 * @param length Length of the token in bytes (default: 32)
 * @returns Hex-encoded random token
 */
export const generateSecureToken = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

/**
 * Hashes sensitive data using SHA-256 (one-way)
 * @param data The data to hash
 * @returns Hex-encoded hash
 */
export const hashData = (data: string): string => {
  return crypto.createHash('sha256').update(data).digest('hex');
};

/**
 * Masks card number showing only last 4 digits
 * @param cardNumber The full card number
 * @returns Masked card number (e.g., **** **** **** 1234)
 */
export const maskCardNumber = (cardNumber: string): string => {
  const cleaned = cardNumber.replace(/\s+/g, '');
  if (cleaned.length < 4) return cleaned;
  
  const last4 = cleaned.slice(-4);
  const masked = '*'.repeat(cleaned.length - 4) + last4;
  
  // Format with spaces every 4 digits
  return masked.match(/.{1,4}/g)?.join(' ') || masked;
};

/**
 * Validates if a string is properly encrypted
 * @param text The text to validate
 * @returns True if the text appears to be encrypted
 */
export const isEncrypted = (text: string): boolean => {
  if (!text) return false;
  
  const parts = text.split(':');
  if (parts.length !== 3) return false;
  
  // Check if all parts are valid base64
  return parts.every(part => /^[A-Za-z0-9+/=]+$/.test(part));
};

/**
 * Generates a new encryption key (for key rotation)
 * @returns Base64-encoded encryption key
 */
export const generateEncryptionKey = (): string => {
  return crypto.randomBytes(KEY_LENGTH).toString('base64');
};

// Export for testing purposes
export const _testExports = {
  ALGORITHM,
  KEY_LENGTH,
  IV_LENGTH,
  TAG_LENGTH
};