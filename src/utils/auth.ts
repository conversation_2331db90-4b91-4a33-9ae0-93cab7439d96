// utils/auth.ts
import jwt from 'jsonwebtoken';

export interface TokenPayload {
  id: string;
  role: string;
}

export const verifyToken = (token: string): TokenPayload | null => {
  try {
    // First try with user secret
    try {
      return jwt.verify(token, process.env.JWT_SECRET_USER as string) as TokenPayload;
    } catch (error:any) {
      // If user token verification fails, try admin secret
      return jwt.verify(token, process.env.JWT_SECRET_ADMIN as string) as TokenPayload;
    }
  } catch (error:any) {
    return null;
  }
};