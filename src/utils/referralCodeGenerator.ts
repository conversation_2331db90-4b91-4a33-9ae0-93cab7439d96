import { User } from '../models/User';

/**
 * Generates a unique referral code of 8 alphanumeric characters
 */
export async function generateReferralCode(): Promise<string> {
  const characters = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // Removed confusing chars like O/0, I/1
  const codeLength = 8;

  let isUnique = false;
  let referralCode = '';

  while (!isUnique) {
    // Generate code
    referralCode = '';
    for (let i = 0; i < codeLength; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      referralCode += characters.charAt(randomIndex);
    }

    // Check if code is unique
    const existingUser = await User.findOne({ referralCode });
    if (!existingUser) {
      isUnique = true;
    }
  }

  return referralCode;
}