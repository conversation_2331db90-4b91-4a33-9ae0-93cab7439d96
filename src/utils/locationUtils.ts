// Country and city data from useCountries hook
const countries = [
  { code: 'TR', name: 'Turkey' },
  { code: 'US', name: 'United States' },
  { code: 'GB', name: 'United Kingdom' },
  { code: 'DE', name: 'Germany' },
  { code: 'FR', name: 'France' },
  { code: 'IT', name: 'Italy' },
  { code: 'ES', name: 'Spain' },
  { code: 'CN', name: 'China' },
  { code: 'JP', name: 'Japan' },
  { code: 'KR', name: 'South Korea' },
  { code: 'RU', name: 'Russia' },
  { code: 'AE', name: 'United Arab Emirates' },
  { code: 'SA', name: 'Saudi Arabia' },
  { code: 'EG', name: 'Egypt' },
  { code: 'IN', name: 'India' },
];

// Cities by country code
const citiesByCountry: Record<string, { code: string; name: string }[]> = {
  'TR': [
    { code: 'IST', name: 'Istanbul' },
    { code: 'ANK', name: 'Ankara' },
    { code: '<PERSON><PERSON><PERSON>', name: '<PERSON><PERSON><PERSON>' },
    { code: 'ANT', name: '<PERSON><PERSON><PERSON>' },
    { code: 'B<PERSON>', name: '<PERSON><PERSON><PERSON>' },
  ],
  'US': [
    { code: 'NYC', name: 'New York' },
    { code: 'LAX', name: 'Los Angeles' },
    { code: 'CHI', name: 'Chicago' },
    { code: 'HOU', name: 'Houston' },
    { code: 'PHX', name: 'Phoenix' },
  ],
  'GB': [
    { code: 'LON', name: 'London' },
    { code: 'MAN', name: 'Manchester' },
    { code: 'BIR', name: 'Birmingham' },
    { code: 'LIV', name: 'Liverpool' },
    { code: 'EDI', name: 'Edinburgh' },
  ],
};

/**
 * Get the country name from country code
 * @param countryCode The two-letter country code
 * @returns The full country name or the original code if not found
 */
export const getCountryName = (countryCode: string): string => {
  if (!countryCode) return '';
  
  const country = countries.find(c => c.code === countryCode);
  return country ? country.name : countryCode;
};

/**
 * Get the city name from city code and country code
 * @param cityCode The city code
 * @param countryCode The country code to help look up the correct city
 * @returns The full city name or the original code if not found
 */
export const getCityName = (cityCode: string, countryCode: string): string => {
  if (!cityCode) return '';
  
  const countryCities = citiesByCountry[countryCode] || [];
  const city = countryCities.find(c => c.code === cityCode);
  
  return city ? city.name : cityCode;
};

/**
 * Get formatted location string (City, Country) from codes
 * @param cityCode City code
 * @param countryCode Country code
 * @returns Formatted location string
 */
export const getFormattedLocation = (cityCode: string, countryCode: string): string => {
  const cityName = getCityName(cityCode, countryCode);
  const countryName = getCountryName(countryCode);
  
  if (cityName && countryName) {
    return `${cityName}, ${countryName}`;
  } else if (cityName) {
    return cityName;
  } else if (countryName) {
    return countryName;
  }
  
  return '';
};