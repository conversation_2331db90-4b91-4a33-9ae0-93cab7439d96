/**
 * Masks a text by showing only the first character of each word and replacing the rest with dots
 * @param text The text to mask
 * @returns The masked text where each word starts with its first character followed by dots
 */
export const maskText = (text: string): string => {
  if (!text) return '';

  return text
    .split(' ')
    .map(word => {
      if (word.length <= 1) return word;
      return `${word.charAt(0)}${'•'.repeat(word.length - 1)}`;
    })
    .join(' ');
};
