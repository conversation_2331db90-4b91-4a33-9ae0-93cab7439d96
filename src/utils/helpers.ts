/**
 * Extract storeId from either an object with _id property or a string
 * This solves inconsistencies in how storeId is handled across the app
 */
export const getStoreId = (storeId: any): string => {
  if (!storeId) {
    console.warn('getStoreId called with empty value', storeId);
    return '';
  }

  // Log the type and value for debugging
  console.log('getStoreId called with:', {
    type: typeof storeId,
    value: storeId
  });

  // If storeId is an object with _id property, use that value
  if (typeof storeId === 'object' && storeId._id) {
    console.log('Using object._id:', storeId._id);
    return storeId._id;
  }

  // If it's a string, use it directly
  if (typeof storeId === 'string') {
    console.log('Using string value:', storeId);
    return storeId;
  }

  // If it's some other format, try to stringify it
  console.warn('Unexpected storeId format, trying to convert to string:', storeId);
  return String(storeId);
};