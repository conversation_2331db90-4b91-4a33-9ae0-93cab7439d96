import mongoose from 'mongoose';
import dotenv from 'dotenv';

dotenv.config();

const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGO_URI;
    if (!mongoURI) {
      throw new Error('MONGO_URI is not defined in the environment variables.');
    }

    await mongoose.connect(mongoURI);

    console.log('MongoDB connected successfully');

    mongoose.connection.on('error', (err) => {
      console.error('Mongoose connection error:', err);
    });

  } catch (err) {
    console.error('MongoDB connection error:', err);
    if (err instanceof mongoose.Error.MongooseServerSelectionError) {
      console.error('Failed to select a MongoDB server. Please ensure:');
      console.error('1. MongoDB is running: brew services list');
      console.error('2. If not running, start it: brew services start mongodb-community');
      console.error('3. Check MongoDB logs: cat /usr/local/var/log/mongodb/mongo.log');
    } else if (err instanceof mongoose.Error) {
      if (err.message.includes('Authentication failed')) {
        console.error('Authentication failed. Please ensure:');
        console.error('1. The username and password in MONGO_URI are correct');
        console.error('2. The user exists in the specified database');
        console.error('3. The user has the correct permissions');
      } else {
        console.error('Mongoose error:', err.message);
      }
    } else {
      console.error('An unexpected error occurred. Please check your MongoDB configuration and connection string.');
    }
    process.exit(1);
  }
};

export default connectDB;