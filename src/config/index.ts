import dotenv from 'dotenv';

dotenv.config();

export const config = {
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    // Only include password if it exists
    ...(process.env.REDIS_PASSWORD ? { password: process.env.REDIS_PASSWORD } : {}),
  },
  smtp: {
    host: process.env.EMAIL_SERVER_HOST || 'mail.e-exportcity.com',
    port: parseInt(process.env.EMAIL_SERVER_PORT || '465'),
    secure: process.env.EMAIL_SERVER_PORT === '465',
    user: process.env.EMAIL_SERVER_USER || '<EMAIL>',
    password: process.env.EMAIL_SERVER_PASS || 'Info13579!',
  }
};