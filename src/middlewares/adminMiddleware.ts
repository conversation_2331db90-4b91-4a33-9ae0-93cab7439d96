import { Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { User } from '../models/User';

export const adminMiddleware = async (req: any, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    if (!token) {
      return res.status(401).json({ message: 'Authorization denied. No token provided.' });
    }

    // Verify token with admin-specific secret if available
    const decoded = jwt.verify(token, process.env.JWT_SECRET_ADMIN ?? process.env.JWT_SECRET as jwt.Secret) as { id: string };

    const user = await User.findById(decoded.id);
    if (!user || user.role !== 'admin') {
      console.log(user,decoded,"info")
      return res.status(403).json({ message: 'Access denied. Admins only.' });
    }

    req.user = user; // Attach user to the request object
    req.user.isAdmin = true;
    next();
  } catch (error: any) {
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token has expired' });
    }
    console.error('Admin Middleware Error:', error); // Log the error for debugging
    return res.status(401).json({ message: 'Token is not valid' });
  }
};
