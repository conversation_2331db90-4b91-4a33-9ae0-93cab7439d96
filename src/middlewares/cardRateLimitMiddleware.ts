import { Request, Response, NextFunction } from 'express';
import { CardAuditLog } from '../models/CardAuditLog';
import { IUser } from '../models/User';

interface RateLimitConfig {
  windowMs: number;  // Time window in milliseconds
  maxAttempts: number;  // Maximum attempts in the window
  message?: string;
}

const defaultConfig: RateLimitConfig = {
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxAttempts: 5,
  message: 'Too many card operations. Please try again later.'
};

export const cardRateLimitMiddleware = (customConfig?: Partial<RateLimitConfig>) => {
  const config = { ...defaultConfig, ...customConfig };
  
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const user = req.user as IUser;
      if (!user) {
        return res.status(401).json({ message: 'Unauthorized' });
      }
      
      const userId = user._id;
      const windowStart = new Date(Date.now() - config.windowMs);
      
      // Count recent card operations
      const recentOperations = await CardAuditLog.countDocuments({
        userId,
        timestamp: { $gte: windowStart },
        action: { $in: ['created', 'used', 'failed_payment'] }
      });
      
      if (recentOperations >= config.maxAttempts) {
        // Log rate limit exceeded
        const ipAddress = (req.headers['x-forwarded-for'] as string)?.split(',')[0] || 
                         req.socket.remoteAddress || 
                         'unknown';
        
        await CardAuditLog.create({
          userId,
          action: 'rate_limit_exceeded' as any,
          ipAddress,
          details: { 
            recentOperations,
            windowMs: config.windowMs,
            maxAttempts: config.maxAttempts
          },
          success: false
        });
        
        const language = req.headers['accept-language'] as string || 'tr';
        const message = language === 'tr' 
          ? 'Çok fazla kart işlemi. Lütfen daha sonra tekrar deneyin.'
          : config.message;
        
        return res.status(429).json({ message });
      }
      
      next();
    } catch (error) {
      console.error('Rate limit middleware error:', error);
      // Don't block on error, let the request continue
      next();
    }
  };
};

// Specific rate limiters for different operations
export const addCardRateLimit = cardRateLimitMiddleware({
  windowMs: 60 * 60 * 1000, // 1 hour
  maxAttempts: 3, // Max 3 new cards per hour
  message: 'Too many card additions. Please try again later.'
});

export const paymentRateLimit = cardRateLimitMiddleware({
  windowMs: 5 * 60 * 1000, // 5 minutes
  maxAttempts: 10, // Max 10 payment attempts per 5 minutes
  message: 'Too many payment attempts. Please try again later.'
});

export const viewCardRateLimit = cardRateLimitMiddleware({
  windowMs: 60 * 1000, // 1 minute
  maxAttempts: 20, // Max 20 views per minute
  message: 'Too many requests. Please slow down.'
});