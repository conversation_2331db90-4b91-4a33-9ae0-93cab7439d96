import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { IUser, User } from '../models/User';
import { Store, IStore } from '../models/Store';

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  const language = req.headers['accept-language'] as string || 'tr';

  try {
    // Check for token in headers
    const authHeader = req.headers.authorization;
    if (!authHeader?.startsWith('Bearer ')) {
      return res.status(401).json({
        message: language === 'en' ? 'No token provided' : 'Token sağlanmadı'
      });
    }

    const token = authHeader.split(' ')[1];

    // Verify token
    const jwtSecret = process.env.JWT_SECRET_USER;
    if (!jwtSecret) {
      console.error('JWT_SECRET_USER is not defined');
      return res.status(500).json({
        message: language === 'en' ? 'Server configuration error' : '<PERSON><PERSON>u ya<PERSON> hatası'
      });
    }

    try {
      const decoded = jwt.verify(token, jwtSecret) as { id: string };

      // Find user without store info first
      const user = await User.findById(decoded.id).select('-password');
      if (!user) {
        return res.status(401).json({
          message: language === 'en' ? 'User not found' : 'Kullanıcı bulunamadı'
        });
      }

      // Find associated store
      const store = await Store.findOne({ owner: user._id });

      // Attach user and store to request
      (req as any).user = user;
      (req as any).store = store;
      next();

    } catch (jwtError) {
      return res.status(401).json({
        message: language === 'en' ? 'Invalid token' : 'Geçersiz token'
      });
    }
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(500).json({
      message: language === 'en' ? 'Server error' : 'Sunucu hatası'
    });
  }
};
