// Import the cron service
import mongoose from 'mongoose';
import { initCronJobs } from './services/cronService';
const MONGO_URI = process.env.MONGO_URI;

// Initialize cron jobs after DB connection is established
mongoose.connect(MONGO_URI as string)
  .then(() => {
    console.log('Connected to MongoDB');
    initCronJobs(); // Start cron jobs
  })
  .catch(err => console.error('MongoDB connection error:', err));