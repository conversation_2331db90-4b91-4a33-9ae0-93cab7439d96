import axios from 'axios';
import { ICategory } from './models/Category';
import { ICity } from './models/City';
import { ICountry } from './models/Country';

// Create a separate axios instance for public endpoints that doesn't use auth
const publicApi = axios.create({
  baseURL: process.env.VITE_API_BASE_URL,
  withCredentials: true
});

// Add language header but no auth token
publicApi.interceptors.request.use((config: any) => {
  const language = localStorage.getItem('i18nextLng') || 'tr';
  config.headers['Accept-Language'] = language;
  return config;
}, (error) => {
  return Promise.reject(error);
});

// Public Category Endpoints (no auth required)
export const getPublicCategories = async (): Promise<ICategory[]> => {
  try {
    // Use the correct endpoint from the backend routes
    const response = await axios.get<{success: boolean, data: ICategory[]}>(`${process.env.VITE_API_BASE_URL}/public/categories`);
    console.log('Public categories response:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('Error fetching public categories:', error);
    throw error;
  }
};

export const getPublicSubcategories = async (categoryId: string): Promise<ICategory[]> => {
  try {
    // Use the correct endpoint from the backend routes
    const response = await axios.get<{success: boolean, data: ICategory[]}>(`${process.env.VITE_API_BASE_URL}/public/categories/${categoryId}/subcategories`);
    console.log('Public subcategories response:', response.data);
    return response.data.data;
  } catch (error) {
    console.error('Error fetching public subcategories:', error);
    throw error;
  }
};

// Public Country and City Endpoints
export const getPublicCountries = async (): Promise<ICountry[]> => {
  try {
    // Use direct axios call without auth token
    const response = await axios.get<ICountry[]>(`${process.env.VITE_API_BASE_URL}/auth/countries`);
    console.log('Public countries response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching public countries:', error);
    throw error;
  }
};

export const getPublicCities = async (countryCode: string): Promise<ICity[]> => {
  try {
    // Use direct axios call without auth token
    const response = await axios.get<ICity[]>(`${process.env.VITE_API_BASE_URL}/auth/cities/${countryCode}`);
    console.log('Public cities response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching public cities:', error);
    throw error;
  }
};

export { publicApi };
