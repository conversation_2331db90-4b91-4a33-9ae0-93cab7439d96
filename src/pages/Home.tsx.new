import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { SearchIcon, ChevronDownIcon, ChevronUpIcon, ExternalLink, Store, Eye } from "lucide-react";
import { useAuthCheck } from "@/hooks/useAuthCheck";
import { useToast as useChakraToast } from "@chakra-ui/react";
import { api, checkViewRequest, getFilters, useViewRequest } from "@/api";
import { getActiveRepresentatives } from "../api/representativeApi";
import { getMostViewedStores } from "../api/storeApi";
import { getSliders } from "../api/sliderApi";
import { getStoreId } from "@/utils/helpers";
import { IItem } from "../types/item";
import { IStore } from "../types/store";

// Components
import HeroSlider from "../components/HeroSlider";
import FeaturedStores from "../components/FeaturedStores";
import FeaturedCompany from "../components/FeaturedCompany";
import FeaturedProducts from "../components/FeaturedProducts";
import ProductCard from "../components/common/ProductCard";
import Carousel from "../components/Carousel";

// Swiper styles
import "swiper/css/bundle";
import "swiper/css";

const ITEMS_PER_PAGE = 9;

const Home: React.FC = () => {
  const { t } = useTranslation("home");
  const { t: commonT } = useTranslation("common");
  const navigate = useNavigate();
  const chakraToast = useChakraToast();
  const [products, setProducts] = useState<IItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<IItem[]>([]);
  const [featuredStores, setFeaturedStores] = useState<IStore[]>([]);
  const [mostViewedItems, setMostViewedItems] = useState<IItem[]>([]);
  const [selectedStore, setSelectedStore] = useState<IStore | null>(null);
  const [type, setType] = useState("all");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<any>({
    categories: [],
    productTypes: [],
  });
  const [availableFilters, setAvailableFilters] = useState<any>({
    categories: [],
    productTypes: [],
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [viewRequestInfo, setViewRequestInfo] = useState<any>(null);
  const [representatives, setRepresentatives] = useState<any[]>([]);
  const [homepageAds, setHomepageAds] = useState<any[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedAd, setSelectedAd] = useState<any>(null);
  const [sliders, setSliders] = useState<any[]>([]);
  const [isSlidersLoading, setIsSlidersLoading] = useState(true);
  const { user } = useAuthCheck();

  // Modal states
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const cancelRef = useRef<HTMLButtonElement>(null);

  // Modal functions
  const onViewDialogOpen = () => setIsViewDialogOpen(true);
  const onViewDialogClose = () => setIsViewDialogOpen(false);
  const onImageModalOpen = () => setIsImageModalOpen(true);
  const onImageModalClose = () => setIsImageModalOpen(false);

  // Toast function
  const showToast = (title: string, description: string, status: 'info' | 'warning' | 'success' | 'error' = 'info') => {
    chakraToast({
      title,
      description,
      status,
      duration: 5000,
      isClosable: true,
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [
          filtersData,
          mostViewedStores,
          activeRepresentatives,
          approvedAds,
          mostViewed,
        ]: any = await Promise.all([
          getFilters("all"),
          getMostViewedStores(),
          getActiveRepresentatives(),
          api.get("/home-ads/active").then((res) => res.data),
          api.get("/items/most-viewed").then((res) => res.data),
        ]);

        setProducts(filtersData.items);
        setFilteredProducts(filtersData.items);
        setFilters(filtersData);
        setAvailableFilters(filtersData);
        setFeaturedStores(mostViewedStores);
        setRepresentatives(activeRepresentatives.data);
        setHomepageAds(approvedAds);
        setMostViewedItems(mostViewed || []);

        if (mostViewedStores.length > 0) {
          setSelectedStore(mostViewedStores[0]);
        }
      } catch (error: any) {
        console.error("Error fetching data:", error);
        setError("Failed to load data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchSliders = async () => {
      try {
        const data = await getSliders();
        setSliders(data);
      } catch (error) {
        console.error("Error fetching sliders:", error);
      } finally {
        setIsSlidersLoading(false);
      }
    };

    fetchSliders();
  }, []);

  const handleTypeChange = async (
    event: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    const selectedType = event.target.value;
    setType(selectedType);
    setCurrentPage(1);
    setSelectedCategories([]);

    try {
      const filtersData: any = await getFilters(
        selectedType === "all" ? undefined : selectedType,
      );
      setAvailableFilters(filtersData);
      setFilteredProducts(filtersData.items);
    } catch (error) {
      console.error("Error updating filters:", error);
    }
  };

  const handleCategoryChange = async (categoryId: string) => {
    const newSelectedCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter((id) => id !== categoryId)
      : [...selectedCategories, categoryId];

    setSelectedCategories(newSelectedCategories);
    setCurrentPage(1);

    try {
      const filtersData: any = await getFilters(
        type || "all",
        newSelectedCategories.length > 0
          ? newSelectedCategories.join(",")
          : undefined,
      );
      setAvailableFilters(filtersData);
      setFilteredProducts(filtersData.items);
    } catch (error) {
      console.error("Error updating filters:", error);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    const filtered = products.filter((product) => {
      const searchRegex = new RegExp(value, "i");
      return (
        searchRegex.test(product.name) ||
        searchRegex.test(product.description) ||
        searchRegex.test(product.type)
      );
    });

    setFilteredProducts(filtered);
    setCurrentPage(1);
  };

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId],
    );
  };

  const handleItemClick = async (itemId: string) => {
    if (!user) {
      navigate("/login");
      return;
    }

    try {
      const response = await checkViewRequest(itemId);
      setViewRequestInfo(response);
      setSelectedItemId(itemId);

      if (response.isOwner || response.alreadyViewed) {
        navigate(`/items/${itemId}`);
        return;
      }

      if (!response.hasRemaining) {
        showToast(
          response.message, 
          t("errors.upgradeRequired"), 
          "error"
        );
        return;
      }

      onViewDialogOpen();
    } catch (error: any) {
      console.error("Error checking view request:", error);
      if (error.response && error.response.status === 403) {
        showToast(
          t("errors.noViewRequests"),
          t("errors.upgradeRequired"),
          "error"
        );
        navigate("/packages");
      }
    }
  };

  const handleViewConfirm = async () => {
    if (!selectedItemId) return;

    try {
      await useViewRequest(selectedItemId);
      onViewDialogClose();
      navigate(`/items/${selectedItemId}`);
    } catch (error: any) {
      showToast(
        t("errors.viewFailed"),
        t("errors.tryAgain"),
        "error"
      );
    }
  };

  const handleViewCancel = () => {
    setSelectedItemId(null);
    onViewDialogClose();
  };

  const handleImageClick = (ad: any) => {
    setSelectedImage(import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image);
    setSelectedAd(ad);
    onImageModalOpen();
  };

  const totalPages = Math.ceil(filteredProducts.length / ITEMS_PER_PAGE);
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE,
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Slider */}
      {!isSlidersLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="relative w-full bg-gradient-to-b from-black/10 to-black/30"
        >
          <HeroSlider images={sliders} />
        </motion.div>
      )}
      
      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="container mx-auto py-8 font-sans"
      >
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <div className="spinner-border animate-spin inline-block w-8 h-8 border-4 rounded-full text-blue-600" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        ) : error ? (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        ) : (
          <>
            {/* Premium Homepage Ads Section */}
            {user && homepageAds.length > 0 && (
              <section className="mb-12 relative overflow-hidden">
                <div className="absolute top-[-100px] right-[-100px] w-[300px] h-[300px] bg-blue-300/15 rounded-full"></div>
                <div className="absolute bottom-[-50px] left-[-50px] w-[200px] h-[200px] bg-green-300/10 rounded-full"></div>

                <div className="relative z-10 py-8 px-4 md:px-8 rounded-2xl shadow-xl bg-white border border-gray-100">
                  <div className="flex mb-8 justify-center">
                    <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                      {t("homepageAds.title")}
                    </h2>
                  </div>

                  <Carousel
                    items={homepageAds.map((ad) => (
                      <div
                        key={ad._id}
                        className="rounded-xl overflow-hidden shadow-lg transition-all duration-300 bg-white border border-gray-100 hover:translate-y-[-8px] hover:shadow-2xl cursor-pointer"
                        onClick={async () => {
                          try {
                            const response = await api.post(`/home-ads/click/${ad._id}`);
                            const { itemId } = response.data;

                            if (itemId) {
                              navigate(`/items/${itemId}`);
                            } else if (ad.storeId) {
                              const storeId = getStoreId(ad.storeId);
                              navigate(`/stores/${storeId}`);
                            }
                          } catch (error) {
                            console.error("Failed to record click:", error);
                          }
                        }}
                      >
                        <div className="relative">
                          <img
                            src={
                              import.meta.env.VITE_SOCKET_URL +
                              (ad.image.startsWith('/') ? '' : '/') +
                              '/uploads/' +
                              (ad.image.startsWith('/') ? '' : '/') +
                              ad.image.replace(/^\/+/, '')
                            }
                            alt={ad.title}
                            className="h-[200px] md:h-[220px] w-full object-cover cursor-zoom-in transition-transform duration-500 hover:scale-105"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleImageClick(ad);
                            }}
                          />

                          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent pointer-events-none"></div>

                          <div className="absolute top-4 right-4 px-3 py-1 rounded-full bg-gradient-to-r from-blue-600 to-teal-600 text-white font-medium uppercase tracking-wide text-xs shadow-md">
                            {t("homepageAds.featured")}
                          </div>
                        </div>

                        <div className="p-4 md:p-5 bg-white">
                          <h3 className="text-base md:text-lg font-semibold text-gray-900 line-clamp-2">
                            {ad.title}
                          </h3>

                          <div className="flex justify-between items-center w-full pt-2">
                            <span className="text-sm text-gray-600">
                              {t("homepageAds.clickToView")}
                            </span>
                            <button className="text-sm px-3 py-1 rounded-full border border-blue-500 text-blue-500 inline-flex items-center space-x-1 hover:bg-gradient-to-r hover:from-blue-600 hover:to-teal-600 hover:text-white hover:border-transparent transition-colors">
                              <span>{t("homepageAds.view")}</span>
                              <ExternalLink className="h-3 w-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                    slidesPerView={{ base: 1, sm: 2, md: 3, lg: 3, xl: 4 }}
                    spaceBetween={24}
                    height={{ base: "350px", md: "400px" }}
                    autoplayDelay={4000}
                    autoplay={true}
                    loop={true}
                  />
                </div>
              </section>
            )}

            {/* Featured Stores */}
            {featuredStores.length > 0 && (
              <FeaturedStores
                stores={featuredStores}
                isAuthenticated={!!user}
                onStoreClick={() => !user && navigate("/login")}
              />
            )}

            {/* Featured Company */}
            {selectedStore && featuredStores.length > 0 && (
              <FeaturedCompany
                selectedStore={selectedStore}
                stores={featuredStores}
                onStoreSelect={(store) => {
                  if (!user) {
                    navigate("/login");
                    return;
                  }
                  setSelectedStore(store);
                }}
              />
            )}
            
            {/* Most Viewed Products and Services */}
            {mostViewedItems.length > 0 && (
              <FeaturedProducts
                items={mostViewedItems}
                onViewItem={handleItemClick}
              />
            )}

            {/* Representatives Section */}
            {representatives.length > 0 && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-6 text-center">
                  {t("representatives.title")}
                </h2>
                <Carousel
                  items={representatives.map((representative: any) => (
                    <div
                      key={representative.id}
                      className="bg-white p-4 rounded-lg shadow-md border border-gray-200 w-full h-full transition-all duration-300 hover:scale-105 hover:rotate-1 hover:shadow-blue-400/30 hover:shadow-lg"
                    >
                      <div className="flex flex-col items-center space-y-3">
                        <div className="w-20 h-20 rounded-full overflow-hidden">
                          <img
                            src={
                              representative.profilePicture &&
                              (representative.profilePicture.startsWith("data:") ||
                                representative.profilePicture.startsWith("http")
                                ? representative.profilePicture
                                : `${import.meta.env.VITE_SOCKET_URL}/uploads/${representative.profilePicture.replace(/^\/+/, '')}`)
                            }
                            alt={`${representative.firstName} ${representative.lastName}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </div>
                      <div className="flex flex-col items-center space-y-1 mt-2">
                        <span className="font-bold text-lg">
                          {`${representative.firstName} ${representative.lastName}`}
                        </span>
                        <span className="text-gray-500 text-sm">
                          {`${representative.countryName || representative.country}, ${representative.cityName || representative.city}`}
                        </span>
                        <span className="text-sm">
                          {representative.email}
                        </span>
                        <span className="text-sm">
                          {representative.phoneNumber}
                        </span>
                      </div>
                    </div>
                  ))}
                  slidesPerView={{ base: 1, sm: 2, md: 3, lg: 4 }}
                  spaceBetween={24}
                  height={{ base: "300px", md: "350px" }}
                  autoplayDelay={3000}
                  autoplay={true}
                  loop={true}
                />
              </div>
            )}

            {/* Products and Filters Section */}
            <div className="mt-8">
              <div className="mb-6">
                <div className="flex flex-col lg:flex-row items-start gap-4 lg:gap-8">
                  {/* Filters Section */}
                  <div className="w-full lg:w-64 bg-white/40 backdrop-blur-md backdrop-saturate-150 p-3 md:p-4 rounded-lg border border-white/30">
                    <div className="flex flex-col gap-3 md:gap-4">
                      <h2 className="text-base md:text-lg font-semibold">
                        {t("filters.title")}
                      </h2>
                      <hr className="border-gray-200" />

                      <div>
                        <label className="block font-medium text-sm md:text-base mb-2">
                          {t("filters.type")}
                        </label>
                        <select
                          value={type}
                          onChange={handleTypeChange}
                          className="w-full px-3 py-2 bg-white border border-gray-200 rounded-md text-sm md:text-base hover:border-blue-500 transition-colors"
                        >
                          <option value="all">{t("filters.allItems")}</option>
                          {filters.productTypes.map((type: string) => (
                            <option key={type} value={type}>
                              {type.charAt(0).toUpperCase() + type.slice(1)}s
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block font-medium text-sm md:text-base mb-2">
                          {t("filters.categories")}
                        </label>
                        <div className="space-y-1 md:space-y-2">
                          {availableFilters.categories.map((category: any) => (
                            <div key={category._id}>
                              <div className="flex justify-between items-center">
                                <label className="flex items-center">
                                  <input
                                    type="checkbox"
                                    checked={selectedCategories.includes(category._id)}
                                    onChange={() => handleCategoryChange(category._id)}
                                    className="mr-2"
                                  />
                                  <span className="text-sm md:text-base">
                                    {category.name}
                                  </span>
                                </label>
                                {category.children?.length > 0 && (
                                  <button
                                    aria-label={expandedCategories.includes(category._id) ? "Collapse" : "Expand"}
                                    className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                                    onClick={() => toggleCategory(category._id)}
                                  >
                                    {expandedCategories.includes(category._id) ? (
                                      <ChevronUpIcon className="h-4 w-4" />
                                    ) : (
                                      <ChevronDownIcon className="h-4 w-4" />
                                    )}
                                  </button>
                                )}
                              </div>
                              {expandedCategories.includes(category._id) && category.children?.length > 0 && (
                                <div className="pl-4 md:pl-6 mt-1 space-y-1">
                                  {category.children?.map((child: any) => (
                                    <div key={child._id}>
                                      <div className="flex justify-between items-center">
                                        <label className="flex items-center">
                                          <input
                                            type="checkbox"
                                            checked={selectedCategories.includes(child._id)}
                                            onChange={() => handleCategoryChange(child._id)}
                                            className="mr-2"
                                          />
                                          <span className="text-sm md:text-base">
                                            {child.name}
                                          </span>
                                        </label>
                                        {child.children?.length > 0 && (
                                          <button
                                            aria-label={expandedCategories.includes(child._id) ? "Collapse" : "Expand"}
                                            className="p-1 text-gray-500 hover:text-gray-700 transition-colors"
                                            onClick={() => toggleCategory(child._id)}
                                          >
                                            {expandedCategories.includes(child._id) ? (
                                              <ChevronUpIcon className="h-4 w-4" />
                                            ) : (
                                              <ChevronDownIcon className="h-4 w-4" />
                                            )}
                                          </button>
                                        )}
                                      </div>
                                      
                                      {expandedCategories.includes(child._id) && child.children?.length > 0 && (
                                        <div className="pl-4 md:pl-6 mt-1 space-y-1">
                                          {child.children?.map((grandchild: any) => (
                                            <label key={grandchild._id} className="flex items-center">
                                              <input
                                                type="checkbox"
                                                checked={selectedCategories.includes(grandchild._id)}
                                                onChange={() => handleCategoryChange(grandchild._id)}
                                                className="mr-2"
                                              />
                                              <span className="text-xs md:text-sm">
                                                {grandchild.name}
                                              </span>
                                            </label>
                                          ))}
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Products Section */}
                  <div className="flex-1 w-full lg:w-auto p-4">
                    <div className="relative mb-4 md:mb-6">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <SearchIcon className="h-5 w-5 text-gray-400" />
                      </div>
                      <input
                        type="text"
                        placeholder={t("search.placeholder")}
                        value={searchTerm}
                        onChange={handleSearch}
                        className="w-full pl-10 pr-4 py-2 bg-white border border-gray-200 rounded-md hover:border-blue-500 transition-colors"
                      />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
                      {paginatedProducts.map((product) => (
                        <ProductCard
                          key={product._id}
                          item={product}
                          onView={() => handleItemClick(product._id)}
                        />
                      ))}
                    </div>

                    {totalPages > 1 && (
                      <div className="flex justify-center items-center mt-6 md:mt-8 space-x-1 md:space-x-2">
                        <button
                          className={`px-3 py-1 rounded-md border ${
                            currentPage === 1
                              ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                              : 'border-blue-500 text-blue-500 hover:bg-blue-50'
                          }`}
                          onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                        >
                          Previous
                        </button>
                        
                        {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                          <button
                            key={page}
                            className={`px-3 py-1 rounded-md ${
                              currentPage === page
                                ? 'bg-blue-500 text-white'
                                : 'border border-gray-200 hover:border-blue-500 hover:text-blue-500'
                            }`}
                            onClick={() => setCurrentPage(page)}
                          >
                            {page}
                          </button>
                        ))}
                        
                        <button
                          className={`px-3 py-1 rounded-md border ${
                            currentPage === totalPages
                              ? 'border-gray-200 text-gray-400 cursor-not-allowed'
                              : 'border-blue-500 text-blue-500 hover:bg-blue-50'
                          }`}
                          onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                          disabled={currentPage === totalPages}
                        >
                          Next
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </motion.div>

      {/* View Request Dialog */}
      {isViewDialogOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            
            <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
              <div className="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <h3 className="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                      {t("dialogs.viewRequest.title")}
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        {t("dialogs.viewRequest.message")}
                      </p>
                      {viewRequestInfo && (
                        <p className="mt-3 text-sm text-gray-600">
                          {t("dialogs.viewRequest.remaining", {
                            count: viewRequestInfo.remainingCount,
                          })}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button 
                  type="button" 
                  className="inline-flex w-full justify-center rounded-md bg-teal-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-teal-500 sm:ml-3 sm:w-auto"
                  onClick={handleViewConfirm}
                >
                  {t("dialogs.viewRequest.confirm")}
                </button>
                <button 
                  type="button" 
                  className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                  onClick={handleViewCancel}
                  ref={cancelRef}
                >
                  {t("dialogs.viewRequest.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Modal */}
      {isImageModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"></div>
            
            <div className="relative bg-transparent max-w-5xl">
              <button
                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
                onClick={onImageModalClose}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
              
              <div className="p-0 relative">
                <img
                  src={selectedImage || ""}
                  alt="Enlarged view"
                  className="w-full h-auto max-h-[90vh] object-contain"
                />

                {selectedAd && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/80 p-4 rounded-md text-center">
                    <div className="flex space-x-4">
                      {selectedAd && selectedAd.itemId && selectedAd.itemId._id && (
                        <button
                          className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded flex items-center space-x-1 transition-colors"
                          onClick={() => {
                            onImageModalClose();
                            navigate(`/items/${selectedAd.itemId._id}`);
                          }}
                        >
                          <ExternalLink className="h-4 w-4" />
                          <span>{commonT("buttons.view")}</span>
                        </button>
                      )}

                      {selectedAd.storeId && (
                        <button
                          className="bg-teal-600 hover:bg-teal-700 text-white px-3 py-2 rounded flex items-center space-x-1 transition-colors"
                          onClick={() => {
                            onImageModalClose();
                            const storeId = getStoreId(selectedAd.storeId);
                            navigate(`/stores/${storeId}`);
                          }}
                        >
                          <Store className="h-4 w-4" />
                          <span>{commonT("visitStore")}</span>
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};


export default Home;