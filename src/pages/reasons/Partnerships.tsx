import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON><PERSON>, CheckCircle, ArrowRight, Building2, MapPin, Target, Award, Globe } from 'lucide-react';

const Partnerships = () => {
  const { t } = useTranslation(['partnerships']);

  const stats = [
    { label: t('stats.0.label'), value: '500+' },
    { label: t('stats.1.label'), value: '25+' },
    { label: t('stats.2.label'), value: '150+' },
    { label: t('stats.3.label'), value: '95%' }
  ];

  const benefits = [
    {
      icon: Globe,
      title: t('benefits.0.title'),
      description: t('benefits.0.description')
    },
    {
      icon: Target,
      title: t('benefits.1.title'),
      description: t('benefits.1.description')
    },
    {
      icon: Award,
      title: t('benefits.2.title'),
      description: t('benefits.2.description')
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-[#1CB3AF] to-[#0A9996] overflow-hidden">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?auto=format&fit=crop&q=80"
            alt="Partnerships"
            className="w-full h-full object-cover opacity-20"
          />
        </div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <Handshake className="w-16 h-16 text-white mx-auto mb-6" />
            <h1 className="text-4xl font-bold text-white mb-6">
              {t('hero.title')}
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto">
              {t('hero.description')}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-white rounded-xl shadow-xl p-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl font-bold text-primary mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t('benefitsSection.title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t('benefitsSection.description')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <div key={index} className="bg-gray-50 p-8 rounded-xl h-full">
              <benefit.icon className="w-12 h-12 text-primary mb-6" />
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                {benefit.title}
              </h3>
              <p className="text-gray-600">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Partner Types */}
      <div className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t('types.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('types.description')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[0, 1].map((index) => (
              <div key={index} className="bg-white rounded-xl p-8 shadow-sm">
                <h3 className="text-2xl font-bold text-primary mb-6">
                  {t(`types.partnerTypes.${index}.title`)}
                </h3>
                <p className="text-gray-600 mb-6">
                  {t(`types.partnerTypes.${index}.description`)}
                </p>
                <ul className="space-y-3">
                  {[0, 1, 2].map((pointIndex) => (
                    <li key={pointIndex} className="flex items-start space-x-3">
                      <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                      <span className="text-gray-600">
                        {t(`types.partnerTypes.${index}.points.${pointIndex}`)}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Success Stories */}
      <div className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t('successStories.title')}
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              {t('successStories.description')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[0, 1, 2].map((index) => (
              <div key={index} className="bg-white rounded-xl p-6 shadow-sm h-full flex flex-col">
                <div className="flex items-center space-x-4 mb-4">
                  <Building2 className="w-10 h-10 text-primary" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Global Partners LLC</h3>
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPin className="w-4 h-4 mr-1" />
                      <span>Bursa, Türkiye</span>
                    </div>
                  </div>
                </div>
                <p className="text-gray-600 flex-grow">
                  {t(`successStories.stories.${index}`)}
                </p>
                <div className="flex items-center justify-between text-sm mt-4">
                  <span className="text-gray-500">{t('successStories.timeAgo')}</span>
                  <button className="text-primary hover:text-[#0A9996] flex items-center">
                    <span>{t('successStories.detailsButton')}</span>
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-[#1CB3AF] to-[#0A9996] py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            {t('cta.title')}
          </h2>
          <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
            {t('cta.description')}
          </p>
          <button className="inline-flex items-center px-8 py-4 bg-white text-primary rounded-lg font-semibold hover:bg-gray-100 transition-colors">
            <Handshake className="w-5 h-5 mr-2" />
            {t('cta.button')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Partnerships;