import React from 'react';
import Layout from '../../components/Layout';
import { Shield, Lock, CheckCircle, ArrowRight, Building2, MapPin } from 'lucide-react';
import { motion } from 'framer-motion';

const SecurePlatform = () => {
  const stats = [
    { label: 'Doğrulanmış Firma', value: '5K+' },
    { label: 'Güvenli İşlem', value: '50K+' },
    { label: 'Başarılı Teslimat', value: '45K+' },
    { label: 'Güvenlik Skoru', value: '%99.9' }
  ];

  const features = [
    "SSL Şifreleme",
    "<PERSON><PERSON> Faktörlü Doğrulama",
    "Firma Doğrulama Sistemi",
    "Güvenli Ödeme Altyapısı",
    "7/24 Güvenlik İzleme",
    "Veri Yedekleme"
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <div className="relative bg-gradient-to-r from-[#1CB3AF] to-[#0A9996] overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1550751827-4bd374c3f58b?auto=format&fit=crop&q=80"
              alt="Secure Platform"
              className="w-full h-full object-cover opacity-20"
            />
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <Shield className="w-16 h-16 text-white mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-white mb-6">
                Güvenli Platform
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                Doğrulanmış firmalar ile güvenli ticaret
              </p>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <motion.div 
            className="bg-white rounded-xl shadow-xl p-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div 
                  key={index} 
                  className="text-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <motion.div 
                    className="text-3xl font-bold text-primary mb-2"
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ 
                      type: "spring",
                      stiffness: 260,
                      damping: 20,
                      delay: index * 0.1 
                    }}
                  >
                    {stat.value}
                  </motion.div>
                  <div className="text-gray-600">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Rest of the component remains unchanged */}
        {/* Security Features */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Güvenlik Özellikleri
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              E-exportcity'nin gelişmiş güvenlik özellikleri ile işlemlerinizi güvenle gerçekleştirin
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-xl h-full flex flex-col">
                <Lock className="w-8 h-8 text-primary mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature}
                </h3>
                <p className="text-gray-600 flex-grow">
                  En son teknoloji ile güvenliğiniz için tasarlandı.
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Güven Testimonials
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Platformumuzun güvenlik özelliklerini kullanan firmaların deneyimleri
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[1, 2, 3].map((_, index) => (
                <div key={index} className="bg-white rounded-xl p-6 shadow-sm h-full flex flex-col">
                  <div className="flex items-center space-x-4 mb-4">
                    <Building2 className="w-10 h-10 text-primary" />
                    <div>
                      <h3 className="font-semibold text-gray-900">Secure Trade Inc.</h3>
                      <div className="flex items-center text-sm text-gray-500">
                        <MapPin className="w-4 h-4 mr-1" />
                        <span>İzmir, Türkiye</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 flex-grow">
                    "E-exportcity'nin güvenlik altyapısı sayesinde uluslararası ticaretimizi güvenle gerçekleştiriyoruz."
                  </p>
                  <div className="flex items-center justify-between text-sm mt-4">
                    <span className="text-gray-500">1 ay önce</span>
                    <button className="text-primary hover:text-[#0A9996] flex items-center">
                      <span>Detaylar</span>
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-[#1CB3AF] to-[#0A9996] py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Güvenli Ticarete Başlayın
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              E-exportcity'nin güvenli altyapısı ile ticaretinizi güvence altına alın
            </p>
            <button className="inline-flex items-center px-8 py-4 bg-white text-primary rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              <Shield className="w-5 h-5 mr-2" />
              Güvenli Başlangıç
            </button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SecurePlatform;