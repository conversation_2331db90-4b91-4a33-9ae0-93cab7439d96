import React from 'react';
import Layout from '../../components/Layout';
import { TrendingUp, CheckCircle, ArrowRight, Building2, MapPin, Users, Target } from 'lucide-react';
import { motion } from 'framer-motion';

const FastGrowth = () => {
  const stats = [
    { label: 'Ortalama Büyüme', value: '%150' },
    { label: 'Başarılı Firma', value: '1K+' },
    { label: 'Yeni Pazar', value: '50+' },
    { label: 'Büyüme Oranı', value: '%200' }
  ];

  const strategies = [
    {
      title: "Pazar Analizi",
      description: "Hede<PERSON> pazarlar için detaylı analiz ve raporlar",
      icon: Target
    },
    {
      title: "Büyüme Stratejisi",
      description: "İşletmenize özel büyüme planları",
      icon: TrendingUp
    },
    {
      title: "İhracat Danışmanlığı",
      description: "Uzman ekibimizle ihracat süreçlerinde destek",
      icon: Users
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <div className="relative bg-gradient-to-r from-[#1CB3AF] to-[#0A9996] overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80"
              alt="Fast Growth"
              className="w-full h-full object-cover opacity-20"
            />
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <TrendingUp className="w-16 h-16 text-white mx-auto mb-6" />
              <h1 className="text-4xl font-bold text-white mb-6">
                Hızlı Büyüme
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                İhracat potansiyelinizi maksimize edin ve işinizi hızla büyütün
              </p>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <motion.div 
            className="bg-white rounded-xl shadow-xl p-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div 
                  key={index} 
                  className="text-center"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <motion.div 
                    className="text-3xl font-bold text-primary mb-2"
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ 
                      type: "spring",
                      stiffness: 260,
                      damping: 20,
                      delay: index * 0.1 
                    }}
                  >
                    {stat.value}
                  </motion.div>
                  <div className="text-gray-600">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Growth Strategies */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Büyüme Stratejileri
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              E-exportcity'nin sunduğu büyüme stratejileri ile işinizi bir üst seviyeye taşıyın
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {strategies.map((strategy, index) => (
              <motion.div 
                key={index} 
                className="bg-gray-50 p-8 rounded-xl h-full"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <strategy.icon className="w-12 h-12 text-primary mb-6" />
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {strategy.title}
                </h3>
                <p className="text-gray-600">
                  {strategy.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Success Stories */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Büyüme Hikayeleri
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Platformumuz üzerinden hızlı büyüme kaydeden firmaların başarı hikayeleri
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[1, 2, 3].map((_, index) => (
                <motion.div 
                  key={index} 
                  className="bg-white rounded-xl p-6 shadow-sm h-full flex flex-col"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <Building2 className="w-10 h-10 text-primary" />
                    <div>
                      <h3 className="font-semibold text-gray-900">Growth Masters Ltd.</h3>
                      <div className="flex items-center text-sm text-gray-500">
                        <MapPin className="w-4 h-4 mr-1" />
                        <span>Ankara, Türkiye</span>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 flex-grow">
                    "E-exportcity ile ihracat hacmimizi 6 ayda %200 artırdık ve 5 yeni pazara giriş yaptık."
                  </p>
                  <div className="flex items-center justify-between text-sm mt-4">
                    <span className="text-gray-500">2 ay önce</span>
                    <button className="text-primary hover:text-[#0A9996] flex items-center">
                      <span>Detaylar</span>
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-[#1CB3AF] to-[#0A9996] py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-4">
              Büyümeye Başlayın
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
              E-exportcity ile ihracat potansiyelinizi keşfedin ve hızla büyüyün
            </p>
            <button className="inline-flex items-center px-8 py-4 bg-white text-primary rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              <TrendingUp className="w-5 h-5 mr-2" />
              Hemen Başlayın
            </button>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default FastGrowth;