import React, { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Text, Heading, I<PERSON>, Spin<PERSON>, <PERSON><PERSON> } from "@chakra-ui/react";
import { CheckIcon, CloseIcon } from "@chakra-ui/icons";
import axios from "axios";

interface PaymentStatus {
  status: "success" | "error" | "loading";
  messageKey?: string;
  packageType?: string;
}

const IyzicoPaymentCallback: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { t } = useTranslation("packages");
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus>({
    status: "loading",
  });

  useEffect(() => {
    const handlePayment = async () => {
      try {
        // Check if we have HTML content in the document
        const htmlContent = document.documentElement.outerHTML;
        if (htmlContent.includes("iyzico-3ds-form")) {
          // Let the form submit itself
          console.log("Iyzico form found, letting it submit");
          return;
        }

        // Check if this is a POST request from Iyzico (form will be present in body)
        const form = document.querySelector("form");
        if (form) {
          console.log("Form found, submitting to backend");
          // Get form data
          const formData = new FormData(form);
          const token = formData.get("token");
          const conversationId = formData.get("conversationId");
          const paymentId = formData.get("paymentId");
          const status = formData.get("status");

          // Submit form data to our backend
          const response = await axios.post(
            `${import.meta.env.REACT_APP_API_URL}/packages/payment-callback`,
            {
              token,
              conversationId,
              paymentId,
              status,
              htmlContent: document.documentElement.outerHTML,
            },
          );

          if (response.data.status === "success" && response.status === 200) {
            setPaymentStatus({
              status: "success",
              packageType: response.data.packageType,
            });
            // Dispatch a custom event to notify the package selection page
            const event = new CustomEvent("payment-completed", {
              detail: {
                status: "success",
                packageType: response.data.packageType,
              },
            });
            window.dispatchEvent(event);
          } else {
            // Handle error state
            setPaymentStatus({
              status: "error",
              messageKey:
                response.data.messageKey || "payment.messages.error",
            });
            
            // Dispatch error event
            const event = new CustomEvent("payment-completed", {
              detail: {
                status: "failed",
                messageKey: response.data.messageKey || "payment.messages.error",
              },
            });
            window.dispatchEvent(event);
          }
          return;
        }

        // Handle GET request parameters
        const status = searchParams.get("status");
        const messageKey = searchParams.get("messageKey");
        const packageType: any = searchParams.get("packageType");

        if (status === "success") {
          setPaymentStatus({
            status: "success",
            packageType,
          });
          // Dispatch event for iframe parent
          const event = new CustomEvent("payment-completed", {
            detail: {
              status: "success",
              packageType,
            },
          });
          window.dispatchEvent(event);
        } else {
          setPaymentStatus({
            status: "error",
            messageKey: messageKey || "payment.messages.error",
          });
          
          // Dispatch error event to parent
          const event = new CustomEvent("payment-completed", {
            detail: {
              status: "failed",
              messageKey: messageKey || "payment.messages.error",
            },
          });
          window.dispatchEvent(event);
        }
      } catch (error) {
        console.error("Payment callback error:", error);
        setPaymentStatus({
          status: "error",
          messageKey: "payment.messages.error",
        });
        
        // Dispatch error event
        const event = new CustomEvent("payment-completed", {
          detail: {
            status: "failed",
            messageKey: "payment.messages.error",
          },
        });
        window.dispatchEvent(event);
      }
    };

    handlePayment();
  }, [searchParams, navigate]);

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      p={4}
    >
      {paymentStatus.status === "loading" ? (
        <Spinner size="xl" color="blue.500" />
      ) : (
        <>
          <Box textAlign="center">
            <Icon
              as={paymentStatus.status === "success" ? CheckIcon : CloseIcon}
              w={12}
              h={12}
              color={
                paymentStatus.status === "success" ? "green.500" : "red.500"
              }
              mb={4}
            />

            <Heading size="lg" mb={4}>
              {paymentStatus.status === "success"
                ? t("payment.messages.success.title")
                : t("payment.messages.error.title")}
            </Heading>
            {paymentStatus.messageKey && (
              <Text
                color={
                  paymentStatus.status === "success" ? "green.600" : "red.600"
                }
                mb={6}
              >
                {t(paymentStatus.messageKey)}
              </Text>
            )}
            <Button
              colorScheme="blue"
              onClick={() => {
                // Try to close iframe window first
                if (window.parent !== window) {
                  window.parent.postMessage(
                    { type: "CLOSE_PAYMENT_MODAL" },
                    "*",
                  );
                }
                // Fallback to window.close()
                window.close();
              }}
            >
              {t("system.close", { defaultValue: "Close" })}
            </Button>
          </Box>
        </>
      )}
    </Box>
  );
};

export default IyzicoPaymentCallback;
