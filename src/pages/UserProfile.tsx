import React, { useState, useEffect } from "react";
import {
  Box,
  Flex,
  VStack,
  Text,
  Icon,
  useColorModeValue,
  Container,
  Heading,
  useBreakpointValue,
  IconButton,
  Drawer,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON><PERSON>nt,
  Drawer<PERSON>loseButton,
  useDisclosure,
  Spinner,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useNavigate, useLocation } from "react-router-dom";
import ProfileTab from "../components/profile/ProfileTab";
import SubscriptionTab from "../components/profile/SubscriptionTab";
import StoreTab from "../components/profile/StoreTab";
import TicketsTab from "../components/profile/TicketsTab";
import ItemsTab from "../components/profile/ItemsTab";
import HomepageAdTab from "@/components/profile/HomePageAdTab";
import ReferralStats from "@/components/profile/ReferralStats";
import {
  FiUser,
  FiPackage,
  FiShoppingBag,
  FiMenu,
  FiLifeBuoy,
  FiImage,
  FiBox,
  FiUsers,
} from "react-icons/fi";
import { getUserProfile } from "../api/userApi";

interface NavItem {
  id: string;
  icon: React.ElementType;
  label: string;
  path: string;
}

const UserProfile: React.FC = () => {
  const { t } = useTranslation("userProfile");
  const navigate = useNavigate();
  const location = useLocation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [userData, setUserData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  console.log(userData?.subscriptionDetails?.addons, "1")
  console.log(userData?.subscriptionDetails?.standard?.features, "2")
  const navItems: NavItem[] = [
    { id: "profile", icon: FiUser, label: t("tabs.profile"), path: "/profile" },
    {
      id: "store",
      icon: FiShoppingBag,
      label: t("tabs.store"),
      path: "/profile/store",
    },
    {
      id: "items",
      icon: FiBox,
      label: t("tabs.items"),
      path: "/profile/items",
    },
    {
      id: "references",
      icon: FiUsers,
      label: t("tabs.references"),
      path: "/profile/references",
    },
    {
      id: "subscription",
      icon: FiPackage,
      label: t("tabs.subscription"),
      path: "/profile/subscription",
    },
    {
      id: "tickets",
      icon: FiLifeBuoy,
      label: t("tabs.support_tickets"),
      path: "/profile/tickets",
    },
    ...(userData?.subscriptionDetails?.addons?.some((addon: any) =>
      addon.packageId?.homepageAd === true
    ) ||
      userData?.subscriptionDetails?.standard?.packageId?.homepageAd === true ||
      userData?.subscriptionDetails?.standard?.features?.includes("homepage_ad")
      ? [
        {
          id: "homepageAd",
          icon: FiImage,
          label: t("tabs.homepage_ads"),
          path: "/profile/homepage-ads",
        },
      ]
      : []),
  ];

  const getCurrentTab = () => {
    const path = location.pathname;
    const currentItem = navItems.find((item) => path === item.path);
    return currentItem ? currentItem.id : "profile";
  };

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        setIsLoading(true);
        const response: any = await getUserProfile();
        setUserData(response.data);
      } catch (error) {
        console.error("Error fetching user data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const isMobile = useBreakpointValue({ base: true, md: false });

  const renderContent = () => {
    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" py={8}>
          <Spinner size="xl" />
        </Box>
      );
    }

    const currentTab = getCurrentTab();

    // Lazy load each tab component - only render the active tab
    // This ensures sections are loaded one by one when switching tabs
    switch (currentTab) {
      case "profile":
        return <ProfileTab />;
      case "items":
        return <ItemsTab />;
      case "references":
        return <ReferralStats user={userData} />;
      case "subscription":
        return <SubscriptionTab userData={userData} />;
      case "store":
        return <StoreTab />;
      case "cards":
        // Redirect to profile if cards tab is accessed directly
        navigate("/profile");
        return <ProfileTab />;
      case "tickets":
        return <TicketsTab />;
      case "homepageAd":
        return <HomepageAdTab />;
      default:
        return <ProfileTab />;
    }
  };

  const handleTabChange = (path: string) => {
    navigate(path);
    if (isMobile) onClose();
  };

  const NavContent = () => (
    <VStack spacing={2} align="stretch" width="100%">
      {navItems.map((item) => {
        const isSelected = location.pathname === item.path;
        return (
          <Flex
            key={item.id}
            align="center"
            p={3}
            cursor="pointer"
            borderRadius="md"
            bg={isSelected ? "blue.50" : "transparent"}
            color={isSelected ? "blue.500" : "inherit"}
            onClick={() => handleTabChange(item.path)}
            _hover={{
              bg: "blue.50",
              color: "blue.500",
            }}
          >
            <Icon as={item.icon} mr={3} />
            <Text fontWeight={isSelected ? "bold" : "normal"}>
              {item.label}
            </Text>
          </Flex>
        );
      })}
    </VStack>
  );

  return (
    <Container maxW="container.xl" py={8}>
      <Box position="relative">
        {isMobile && (
          <IconButton
            aria-label={t("common.open_menu")}
            icon={<FiMenu />}
            position="absolute"
            left={4}
            top={4}
            onClick={onOpen}
          />
        )}
        <Heading textAlign="center" size="lg" mb={8}>
          {t("profile.title")}
        </Heading>
      </Box>

      <Flex gap={8}>
        {!isMobile && (
          <Box
            w="250px"
            bg={bgColor}
            p={4}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
            height="fit-content"
          >
            <NavContent />
          </Box>
        )}

        <Box
          flex={1}
          bg={bgColor}
          p={6}
          borderRadius="lg"
          borderWidth="1px"
          borderColor={borderColor}
        >
          {renderContent()}
        </Box>
      </Flex>

      {isMobile && (
        <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
          <DrawerOverlay />
          <DrawerContent>
            <DrawerCloseButton aria-label={t("common.close")} />

            <DrawerHeader borderBottomWidth="1px">
              {t("common.navigation")}
            </DrawerHeader>
            <DrawerBody>
              <NavContent />
            </DrawerBody>
          </DrawerContent>
        </Drawer>
      )}
    </Container>
  );
};

export default UserProfile;
