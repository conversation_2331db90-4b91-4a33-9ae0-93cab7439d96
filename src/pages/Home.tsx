import React from 'react';
import Layout from '../components/Layout';
import Slider from '../components/Slider';
import Categories from '../components/Categories';
import FeaturedCompanies from '../components/FeaturedCompanies';
import FeaturedProducts from '../components/FeaturedProducts';
import Representatives from '../components/Representatives';
import DiscoverSection from '../components/DiscoverSection';
import IntroSections from '../components/IntroSections';
import AboutSection from '../components/AboutSection';
import { isLoggedIn } from '../data/testUser';

const Home = () => {
  const loggedIn = isLoggedIn();

  return (
    <Layout>
      <Slider />
      {loggedIn ? (
        <>
          <Categories />
          <FeaturedCompanies />
          <FeaturedProducts />
          <Representatives />
          <DiscoverSection />
        </>
      ) : (
        <>
          <AboutSection />
          <FeaturedProducts />
          <IntroSections />
        </>
      )}
    </Layout>
  );
};

export default Home;