import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ExternalLink, Store } from "lucide-react";
import { useAuthCheck } from "@/hooks/useAuthCheck";
import { useToast as useChakraToast } from "@chakra-ui/react";
import { api, checkViewRequest, useViewRequest } from "@/api";
import { getMostViewedStores } from "../api/storeApi";
import { getSliders } from "../api/sliderApi";
import { getStoreId } from "@/utils/helpers";
import { IItem } from "../types/item";
import { IStore } from "../types/store";

// Components
import HeroSlider from "../components/HeroSlider";
import FeaturedStores from "../components/FeaturedStores";
import FeaturedProducts from "../components/FeaturedProducts";
// import Carousel from "../components/Carousel";
import Categories from "../components/Categories";
import AboutSection from "../components/AboutSection";
import Representatives from "../components/Representatives";
import DiscoverSection from "../components/DiscoverSection";
import IntroSections from "../components/IntroSections";
import HomepageAds from "../components/HomepageAds";

// Swiper styles
import "swiper/css/bundle";
import "swiper/css";

const Home: React.FC = () => {
  const { t } = useTranslation("home");
  const { t: commonT } = useTranslation("common");
  const navigate = useNavigate();
  const chakraToast = useChakraToast();
  const [featuredStores, setFeaturedStores] = useState<IStore[]>([]);
  const [mostViewedItems, setMostViewedItems] = useState<IItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [viewRequestInfo, setViewRequestInfo] = useState<any>(null);
  const [homepageAds, setHomepageAds] = useState<any[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedAd, setSelectedAd] = useState<any>(null);
  const [sliders, setSliders] = useState<any[]>([]);
  const [isSlidersLoading, setIsSlidersLoading] = useState(true);
  const { user } = useAuthCheck();

  // Modal states
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const cancelRef = useRef<HTMLButtonElement>(null);

  // Modal functions
  const onViewDialogOpen = () => setIsViewDialogOpen(true);
  const onViewDialogClose = () => setIsViewDialogOpen(false);
  const onImageModalOpen = () => setIsImageModalOpen(true);
  const onImageModalClose = () => setIsImageModalOpen(false);

  // Toast function
  const showToast = (title: string, description: string, status: 'info' | 'warning' | 'success' | 'error' = 'info') => {
    chakraToast({
      title,
      description,
      status,
      duration: 5000,
      isClosable: true,
    });
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [
          mostViewedStores,
          approvedAds,
          mostViewed,
        ]: any = await Promise.all([
          getMostViewedStores(),
          api.get("/home-ads/active").then((res) => res.data),
          api.get("/items/most-viewed").then((res) => res.data),
        ]);

        setFeaturedStores(mostViewedStores);
        setHomepageAds(approvedAds);
        setMostViewedItems(mostViewed || []);

      } catch (error: any) {
        console.error("Error fetching data:", error);
        setError("Failed to load data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchSliders = async () => {
      try {
        const data = await getSliders();
        setSliders(data);
      } catch (error) {
        console.error("Error fetching sliders:", error);
      } finally {
        setIsSlidersLoading(false);
      }
    };

    fetchSliders();
  }, []);



  const handleItemClick = async (itemId: string) => {
    if (!user) {
      navigate("/login");
      return;
    }

    try {
      const response = await checkViewRequest(itemId);
      setViewRequestInfo(response);
      setSelectedItemId(itemId);

      if (response.isOwner || response.alreadyViewed) {
        navigate(`/items/${itemId}`);
        return;
      }

      if (!response.hasRemaining) {
        showToast(
          response.message,
          t("errors.upgradeRequired"),
          "error"
        );
        return;
      }

      onViewDialogOpen();
    } catch (error: any) {
      console.error("Error checking view request:", error);
      if (error.response && error.response.status === 403) {
        showToast(
          t("errors.noViewRequests"),
          t("errors.upgradeRequired"),
          "error"
        );
        navigate("/packages");
      }
    }
  };

  const handleViewConfirm = async () => {
    if (!selectedItemId) return;

    try {
      await useViewRequest(selectedItemId);
      onViewDialogClose();
      navigate(`/items/${selectedItemId}`);
    } catch (error: any) {
      showToast(
        t("errors.viewFailed"),
        t("errors.tryAgain"),
        "error"
      );
    }
  };

  const handleViewCancel = () => {
    setSelectedItemId(null);
    onViewDialogClose();
  };

  const handleImageClick = (ad: any) => {
    setSelectedImage(import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image);
    setSelectedAd(ad);
    onImageModalOpen();
  };


  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Slider - Full Width */}
      <div className="w-full">
        {!isSlidersLoading && <HeroSlider images={sliders} />}
      </div>

      {/* Main Content */}
      {isLoading ? (
        <div className="container mx-auto py-8">
          <div className="flex justify-center items-center py-10">
            <div className="spinner-border animate-spin inline-block w-8 h-8 border-4 rounded-full text-blue-600" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      ) : error ? (
        <div className="container mx-auto py-8">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        </div>
      ) : (
        <>
          {user ? (
            // Logged-in user content
            <>

              {/* Categories - Kept as is with original width */}
              <Categories />

              {/* Premium Homepage Ads Section - Full Width */}
              {homepageAds.length > 0 && (
                <HomepageAds
                  ads={homepageAds}
                  onImageClick={handleImageClick}
                />
              )}

              {/* Featured Stores */}
              {featuredStores.length > 0 && (
                <FeaturedStores
                  stores={featuredStores}
                  isAuthenticated={true}
                  onStoreClick={() => { }}
                />
              )}

              {/* Featured Products - Now full-width and separated from Categories */}
              <FeaturedProducts
                items={mostViewedItems.slice(0, 4)}
                onViewItem={handleItemClick}
              />

              {/* Representatives Section - new component */}
              <Representatives />

              {/* Discover Section - new component */}
              <DiscoverSection />
            </>
          ) : (
            // Non-logged-in user content
            <>
              {/* About Section - "Neden E-exportcity?" */}
              <AboutSection />

              {/* Featured Products - positioned after About Section */}
              <FeaturedProducts
                items={mostViewedItems.slice(0, 4)}
                onViewItem={handleItemClick}
              />

              {/* Representatives Section - added for non-logged-in users */}
              <Representatives />

              {/* Intro Sections - new component */}
              <IntroSections />
            </>
          )}
        </>
      )}

      {/* View Request Dialog */}
      {isViewDialogOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center md:items-center md:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

            <div className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all md:my-8 md:w-full md:max-w-lg">
              <div className="bg-white px-4 pb-4 pt-5 md:p-6 md:pb-4">
                <div className="md:flex md:items-start">
                  <div className="mt-3 text-center md:ml-4 md:mt-0 md:text-left">
                    <h3 className="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                      {t("dialogs.viewRequest.title")}
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        {t("dialogs.viewRequest.message")}
                      </p>
                      {viewRequestInfo && (
                        <p className="mt-3 text-sm text-gray-600">
                          {t("dialogs.viewRequest.remaining", {
                            count: viewRequestInfo.remainingCount,
                          })}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 md:flex md:flex-row-reverse md:px-6">
                <button
                  type="button"
                  className="inline-flex w-full justify-center rounded-md bg-teal-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-teal-500 md:ml-3 md:w-auto"
                  onClick={handleViewConfirm}
                >
                  {t("dialogs.viewRequest.confirm")}
                </button>
                <button
                  type="button"
                  className="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 md:mt-0 md:w-auto"
                  onClick={handleViewCancel}
                  ref={cancelRef}
                >
                  {t("dialogs.viewRequest.cancel")}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Modal */}
      {isImageModalOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4">
            <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"></div>

            <div className="relative bg-transparent max-w-5xl">
              <button
                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
                onClick={onImageModalClose}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              <div className="p-0 relative">
                <img
                  src={selectedImage || ""}
                  alt="Enlarged view"
                  className="w-full h-auto max-h-[90vh] object-contain"
                />

                {selectedAd && (
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/80 p-4 rounded-md text-center">
                    <div className="flex space-x-4">
                      {selectedAd && selectedAd.itemId && selectedAd.itemId._id && (
                        <button
                          className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded flex items-center space-x-1 transition-colors"
                          onClick={() => {
                            onImageModalClose();
                            navigate(`/items/${selectedAd.itemId._id}`);
                          }}
                        >
                          <ExternalLink className="h-4 w-4" />
                          <span>{commonT("buttons.view")}</span>
                        </button>
                      )}

                      {selectedAd.storeId && (
                        <button
                          className="bg-teal-600 hover:bg-teal-700 text-white px-3 py-2 rounded flex items-center space-x-1 transition-colors"
                          onClick={() => {
                            onImageModalClose();
                            const storeId = getStoreId(selectedAd.storeId);
                            navigate(`/stores/${storeId}`);
                          }}
                        >
                          <Store className="h-4 w-4" />
                          <span>{commonT("visitStore")}</span>
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};


export default Home;