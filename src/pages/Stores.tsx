import React, { useEffect, useState, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Search,
  BadgeCheck,
  MapPin,
  ExternalLink,
  X,
  Globe2,
  Building2,
  Eye,
  AlertCircle,
  Users,
  ChevronDown,
  ArrowUpDown,
  SlidersHorizontal
} from 'lucide-react';
import { getStoresWithItemViewCounts } from "../api/storeApi";
import { getCategories, getCountries, api } from "../api";
import { IStore } from "../types/store";
import { ICategory } from "../types/category";
import { ICountry } from "../types/user";

const Stores: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation("store");
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [stores, setStores] = useState<IStore[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    sector: 'all',
    location: 'all',
    city: 'all',
    sortBy: 'newest'
  });

  // Fetch categories and transform them for the dropdown
  const [sectors, setSectors] = useState([
    { id: 'all', name: t("filters.sectors.all") }
  ]);

  // Locations will be populated from countries API
  const [locations, setLocations] = useState([
    { id: 'all', name: t("filters.locations.all") }
  ]);
  
  // Cities will be populated based on selected country
  const [cities, setCities] = useState([
    { id: 'all', name: t("filters.cities.all") || "All Cities" }
  ]);

  const sortOptions = [
    { id: 'newest', name: t("filters.sort.newest") },
    { id: 'oldest', name: t("filters.sort.oldest") },
    { id: 'mostViewed', name: t("filters.sort.mostViewed") },
    { id: 'leastViewed', name: t("filters.sort.leastViewed") }
  ];

  const fetchCategories = useCallback(async () => {
    try {
      // Get categories with level 1 (main categories)
      const categoriesData = await getCategories('1');
      setCategories(categoriesData);

      // Transform categories into sectors for filtering
      // Using only level 1 categories helps make the UI cleaner
      const sectorOptions = [
        { id: 'all', name: t("filters.sectors.all") },
        ...categoriesData.map((category: ICategory) => ({
          id: category._id,
          name: category.name
        }))
      ];
      setSectors(sectorOptions);

      // Fetch subcategories for each main category to build the full category tree
      // This can be expensive if there are many categories, so we'll do it in the background
      const fetchAllCategoryLevels = async () => {
        try {
          const allCategories = [...categoriesData];

          // For each level 1 category, fetch its subcategories
          for (const category of categoriesData) {
            if (category.hasSubCategories) {
              const subCategories = await api.get(`/public/categories/${category._id}/subcategories`);
              if (subCategories.data && subCategories.data.data) {
                allCategories.push(...subCategories.data.data);

                // For each level 2 category, fetch its subcategories (level 3)
                for (const subCategory of subCategories.data.data) {
                  if (subCategory.hasSubCategories) {
                    const level3Categories = await api.get(`/public/categories/${subCategory._id}/subcategories`);
                    if (level3Categories.data && level3Categories.data.data) {
                      allCategories.push(...level3Categories.data.data);
                    }
                  }
                }
              }
            }
          }

          // Update categories with the full hierarchy
          setCategories(allCategories);
        } catch (error) {
          console.error("Error fetching category hierarchy:", error);
        }
      };

      // Start fetching the full category hierarchy in the background
      fetchAllCategoryLevels();
    } catch (err: any) {
      console.error("Failed to fetch categories:", err);
    }
  }, [t]);

  const fetchLocations = useCallback(async () => {
    try {
      const countriesData = await getCountries();
      setCountries(countriesData);

      // Transform countries into locations for filtering
      const locationOptions = [
        { id: 'all', name: t("filters.locations.all") },
        ...countriesData.map((country: ICountry) => ({
          id: country.code,
          name: country.name
        }))
      ];
      setLocations(locationOptions);
    } catch (err: any) {
      console.error("Failed to fetch countries:", err);
    }
  }, [t]);
  
  // Update cities when a country is selected
  useEffect(() => {
    if (filters.location === 'all') {
      setCities([{ id: 'all', name: t("filters.cities.all") || "All Cities" }]);
      return;
    }
    
    const selectedCountry = countries.find(c => c.code === filters.location);
    if (selectedCountry && selectedCountry.cities && selectedCountry.cities.length > 0) {
      const cityOptions = [
        { id: 'all', name: t("filters.cities.all") || "All Cities" },
        ...selectedCountry.cities.map((city: string) => ({
          id: city,
          name: city
        }))
      ];
      setCities(cityOptions);
      // Reset city selection when country changes
      setFilters(prev => ({ ...prev, city: 'all' }));
    } else {
      setCities([{ id: 'all', name: t("filters.cities.all") || "All Cities" }]);
    }
  }, [filters.location, countries, t]);

  const fetchStores = useCallback(async () => {
    setLoading(true);
    try {
      const data = await getStoresWithItemViewCounts();
      setStores(data);
      setError(null);
    } catch (err: any) {
      setError(err.message || t("errors.fetchFailed"));
    } finally {
      setLoading(false);
    }
  }, [t]);

  useEffect(() => {
    Promise.all([
      fetchStores(),
      fetchCategories(),
      fetchLocations()
    ]);
  }, [fetchStores, fetchCategories, fetchLocations]);

  const filteredStores = stores.filter((store: any) => {
    const matchesSearch =
      store.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (store.description && store.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (store.address && store.address.toLowerCase().includes(searchQuery.toLowerCase()));

    // Filter by sector (category)
    const isCategoryOrDescendant = () => {
      // If store has no category information, always include it when filtering by sector
      if (!store.categoryIds && !store.categoryLevel1Id && !store.categoryLevel2Id &&
        !store.categoryLevel3Id && !store.category && (!store.sectors || store.sectors.length === 0)) {
        return true; // Include stores without category data
      }

      // Selected category info
      const selectedCategory = categories.find(c => c._id === filters.sector);
      if (!selectedCategory) return false;

      // Check if store's category is directly matching the selected category
      const directCategoryMatch =
        (store.categoryIds && store.categoryIds.includes(filters.sector)) ||
        (store.categoryLevel1Id === filters.sector) ||
        (store.categoryLevel2Id === filters.sector) ||
        (store.categoryLevel3Id === filters.sector);

      if (directCategoryMatch) return true;

      // Check if the store's category is a child of the selected category
      // Check level 1 -> level 2 -> level 3 hierarchy
      if (selectedCategory.level === 1) {
        // If selected category is level 1, check if store's level 2 or 3 has it as parent
        return (
          (store.categoryLevel1Id === selectedCategory._id) ||
          (store.categoryPath && store.categoryPath.includes(selectedCategory._id))
        );
      } else if (selectedCategory.level === 2) {
        // If selected category is level 2, check if store's level 2 matches or level 3 has it as parent
        return (
          (store.categoryLevel2Id === selectedCategory._id) ||
          (store.categoryPath && store.categoryPath.includes(selectedCategory._id))
        );
      } else if (selectedCategory.level === 3) {
        // If selected category is level 3, check direct match
        return (store.categoryLevel3Id === selectedCategory._id);
      }

      // Also check if any store sector matches the selected category by name
      return store.sectors && store.sectors.some((sector: any) => {
        return sector.toLowerCase() === selectedCategory.name.toLowerCase() ||
          sector.toLowerCase() === selectedCategory.nameEn.toLowerCase();
      });
    };

    const matchesSector = filters.sector === 'all' || isCategoryOrDescendant();

    // Filter by location (country)
    const selectedCountry: any = countries.find((c: any) => c.code === filters.location);

    // If no location information is available for the store, include it in all location filters
    if (filters.location === 'all' && filters.city === 'all') {
      return matchesSearch && matchesSector;
    }
    
    // If we're only filtering by country (city is 'all')
    if (filters.city === 'all') {
      const matchesCountry =
        // If "all" is selected, don't filter
        filters.location === 'all' ||
        // Check if store country matches the selected country
        (store.country && selectedCountry &&
          store.country.toLowerCase() === selectedCountry.name.toLowerCase()) ||
        // Check if store location country matches
        (store.location?.country && selectedCountry && 
          store.location.country.toLowerCase() === selectedCountry.name.toLowerCase()) ||
        // Check if store city is in the selected country
        (store.city && selectedCountry &&
          selectedCountry.cities?.some((c: any) => c.toLowerCase() === store.city?.toLowerCase())) ||
        // Check if address contains the country name
        (store.address && selectedCountry &&
          store.address.toLowerCase().includes(selectedCountry.name.toLowerCase()));
          
      return matchesSearch && matchesSector && matchesCountry;
    }
    
    // If we're filtering by both country and city
    const matchesCountry =
      // Check if store country matches the selected country
      (store.country && selectedCountry &&
        store.country.toLowerCase() === selectedCountry.name.toLowerCase()) ||
      // Check if store location country matches
      (store.location?.country && selectedCountry && 
        store.location.country.toLowerCase() === selectedCountry.name.toLowerCase());
        
    const matchesCity = 
      // Check if store city matches the selected city
      (store.city && filters.city === store.city) ||
      // Check if store location city matches the selected city
      (store.location?.city && filters.city === store.location.city);

    return matchesSearch && matchesSector && matchesCountry && matchesCity;
  }).sort((a: any, b: any) => {
    switch (filters.sortBy) {
      case 'oldest':
        return (a.createdAt ? new Date(a.createdAt).getTime() : 0) -
          (b.createdAt ? new Date(b.createdAt).getTime() : 0);
      case 'mostViewed':
        // Ensure viewCount is a number, default to 0 if undefined
        const aViewsForMost = typeof a.viewCount === 'number' ? a.viewCount : 0;
        const bViewsForMost = typeof b.viewCount === 'number' ? b.viewCount : 0;
        return bViewsForMost - aViewsForMost;
      case 'leastViewed':
        // Ensure viewCount is a number, default to 0 if undefined
        const aViewsForLeast = typeof a.viewCount === 'number' ? a.viewCount : 0;
        const bViewsForLeast = typeof b.viewCount === 'number' ? b.viewCount : 0;
        return aViewsForLeast - bViewsForLeast;
      default: // newest
        return (b.createdAt ? new Date(b.createdAt).getTime() : 0) -
          (a.createdAt ? new Date(a.createdAt).getTime() : 0);
    }
  });

  // Loading skeleton for stores
  const StoresSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(6)].map((_, index) => (
        <div key={index} className="bg-white rounded-xl shadow-sm p-4 animate-pulse">
          <div className="aspect-[16/9] bg-gray-200 rounded-lg mb-4"></div>
          <div className="h-5 bg-gray-200 rounded w-3/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="h-10 bg-gray-200 rounded-lg"></div>
        </div>
      ))}
    </div>
  );

  if (error) {
    return (
      <div className="bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-12">
          <div className="bg-white p-8 rounded-xl shadow-sm flex flex-col items-center">
            <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">{t("errors.title")}</h2>
            <p className="text-gray-600 mb-6">{error}</p>
            <button
              onClick={() => fetchStores()}
              className="px-4 py-2 bg-primary text-white rounded-lg"
            >
              {t("errors.retry")}
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-8">
          <h1 className="text-2xl font-bold text-gray-900">{t("title")}</h1>
          <p className="mt-2 text-gray-600">{t("description")}</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="sticky top-0 z-10 bg-white border-b shadow-sm">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-4">
          <div className="flex flex-col md:flex-row md:items-center gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t("search.placeholder")}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            {/* Sort Dropdown */}
            <div className="relative">
              <div className="flex items-center space-x-2 px-4 py-2 rounded-lg border border-gray-200 bg-white cursor-pointer">
                <ArrowUpDown className="h-5 w-5 text-gray-400" />
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                  className="appearance-none bg-transparent border-none focus:outline-none pr-8"
                >
                  {sortOptions.map(option => (
                    <option key={option.id} value={option.id}>
                      {option.name}
                    </option>
                  ))}
                </select>
                <ChevronDown className="h-4 w-4 text-gray-400 absolute right-4" />
              </div>
            </div>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors ${showFilters ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              <SlidersHorizontal className="h-5 w-5" />
              <span>{t("filters.button")}</span>
            </button>
          </div>

          {/* Extended Filters */}
          {showFilters && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 animate-fadeIn">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Sector Filter */}
                <div>
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Building2 className="h-4 w-4 mr-2 text-gray-500" />
                    {t("filters.sectorLabel")}
                  </label>
                  <div className="relative">
                    <select
                      value={filters.sector}
                      onChange={(e) => setFilters(prev => ({ ...prev, sector: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white appearance-none"
                    >
                      {sectors.map(sector => (
                        <option key={sector.id} value={sector.id}>
                          {sector.name}
                        </option>
                      ))}
                    </select>
                    <ChevronDown className="h-4 w-4 text-gray-400 absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                </div>

                {/* Country Filter */}
                <div>
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <Globe2 className="h-4 w-4 mr-2 text-gray-500" />
                    {t("filters.countryLabel") || "Country"}
                  </label>
                  <div className="relative">
                    <select
                      value={filters.location}
                      onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white appearance-none"
                    >
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>
                          {location.name}
                        </option>
                      ))}
                    </select>
                    <ChevronDown className="h-4 w-4 text-gray-400 absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                </div>
                
                {/* City Filter */}
                <div>
                  <label className="flex items-center text-sm font-medium text-gray-700 mb-2">
                    <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                    {t("filters.cityLabel") || "City"}
                  </label>
                  <div className="relative">
                    <select
                      value={filters.city}
                      onChange={(e) => setFilters(prev => ({ ...prev, city: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white appearance-none"
                      disabled={filters.location === 'all' || cities.length <= 1}
                    >
                      {cities.map(city => (
                        <option key={city.id} value={city.id}>
                          {city.name}
                        </option>
                      ))}
                    </select>
                    <ChevronDown className="h-4 w-4 text-gray-400 absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                </div>
              </div>

              {/* Reset Filters */}
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => {
                    setFilters({
                      sector: 'all',
                      location: 'all',
                      city: 'all',
                      sortBy: 'newest'
                    });
                    setSearchQuery('');
                  }}
                  className="px-4 py-2 text-primary hover:text-[#0A9996] transition-colors flex items-center space-x-2"
                >
                  <X className="h-5 w-5" />
                  <span>{t("filters.clear")}</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Stores Grid */}
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-8">
        {loading ? (
          <StoresSkeleton />
        ) : filteredStores.length === 0 ? (
          <div className="bg-white p-8 rounded-xl shadow-sm flex flex-col items-center">
            <Building2 className="h-16 w-16 text-gray-400 mb-4" />
            <h2 className="text-xl font-bold text-gray-900 mb-2">{t("noStores.title")}</h2>
            <p className="text-gray-600 text-center max-w-md">{t("noStores.message")}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredStores.map((store: any) => (
              <div
                key={store._id}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
              >
                {/* Cover Image */}
                <div className="aspect-[4/3] relative rounded-t-xl overflow-hidden">
                  {store.coverImage ? (
                    <img
                      src={`${import.meta.env.VITE_SOCKET_URL}/${store.coverImage}`}
                      alt={store.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-100">
                      <Building2 className="w-12 h-12 text-gray-400" />
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>

                  {/* Company Logo */}
                  <div className="absolute bottom-4 left-4 flex items-center space-x-2">
                    <div className="w-12 h-12 rounded-lg overflow-hidden border-2 border-white bg-white">
                      {store.logo ? (
                        <img
                          src={`${import.meta.env.VITE_SOCKET_URL}/${store.logo}`}
                          alt={`${store.name} logo`}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Building2 className="w-full h-full p-2 text-gray-400" />
                      )}
                    </div>
                    {store.isVerified && (
                      <BadgeCheck className="h-6 w-6 text-primary bg-white rounded-full p-1" />
                    )}
                  </div>

                  {/* Product Views */}
                  <div className="absolute top-4 right-4">
                    <div className="flex items-center space-x-1 bg-white/90 rounded-full px-2 py-1" title={t("productViews") || "Total product views"}>
                      <Eye className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-600">
                        {typeof store.viewCount === 'number' ? store.viewCount : 0}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  {/* Store Info */}
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{store.name}</h3>

                    {/* Category */}
                    {store.sectors && store.sectors.length > 0 ? (
                      <div className="flex items-center space-x-2 mb-2">
                        <Building2 className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{store.sectors[0]}</span>
                      </div>
                    ) : store.type ? (
                      <div className="flex items-center space-x-2 mb-2">
                        <Building2 className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{t(`types.${store.type}`) || store.type}</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-2 mb-2">
                        <Building2 className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{t("types.company")}</span>
                      </div>
                    )}

                    {/* Location */}
                    {(store.address || store.location?.city || store.location?.country) && (
                      <div className="flex items-center space-x-2 mb-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {store.location?.city && store.location?.country 
                            ? `${store.location.city}, ${store.location.country}` 
                            : store.location?.city 
                            ? store.location.city 
                            : store.location?.country 
                            ? store.location.country 
                            : store.address || t('storeCard.noLocation')}
                        </span>
                      </div>
                    )}

                    {/* Employee Count - if available */}
                    {store.employeeCount && (
                      <div className="flex items-center space-x-2">
                        <Users className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{store.employeeCount} {t("employeeCount")}</span>
                      </div>
                    )}

                    {/* Website if available */}
                    {store.website && (
                      <div className="flex items-center space-x-2">
                        <Globe2 className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600 truncate">{store.website}</span>
                      </div>
                    )}
                  </div>

                  {/* View Profile Button */}
                  <button
                    onClick={() => navigate(`/stores/${store._id}`)}
                    className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
                  >
                    <span>{t("viewProfile")}</span>
                    <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default Stores;