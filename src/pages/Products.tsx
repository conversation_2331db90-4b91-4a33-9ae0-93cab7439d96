import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { 
  Search,
  BadgeCheck,
  MapPin,
  ExternalLink,
  Filter,
  ChevronDown,
  Eye,
  ShoppingBag,
  Headphones,
  Calendar,
  Star,
  X,
  Building2,
  Sliders,
  ArrowUpDown,
  SlidersHorizontal
} from 'lucide-react';

const Products = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    type: 'all',
    mode: 'all',
    category: 'all',
    location: 'all',
    sortBy: 'newest'
  });

  const products = [
    {
      id: 'industrial-automation',
      name: "Endüstriyel Otomasyon Yazılımı",
      company: "TechGlobal Solutions",
      category: "Yazılım",
      location: "İstanbul, Türkiye",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&q=80",
      views: 1250,
      type: "Hizmet",
      mode: "Satış",
      verified: true,
      description: "Endüstriyel tesisler için özel olarak geliştirilen, yüksek performanslı otomasyon yazılımı çözümü."
    },
    {
      id: 'organic-agriculture',
      name: "Organik Tarım Ürünleri",
      company: "EcoTrade International",
      category: "Gıda",
      location: "İzmir, Türkiye",
      image: "https://images.unsplash.com/photo-1470107355970-2ace9f20ab8d?auto=format&fit=crop&q=80",
      views: 890,
      type: "Ürün",
      mode: "Satış",
      verified: true,
      description: "Sertifikalı organik tarım ürünleri, sürdürülebilir üretim."
    },
    {
      id: 'medical-equipment',
      name: "Medikal Ekipman İhracatı",
      company: "MediCare Exports",
      category: "Sağlık",
      location: "Ankara, Türkiye",
      image: "https://images.unsplash.com/photo-1583947215259-38e31be8751f?auto=format&fit=crop&q=80",
      views: 750,
      type: "Ürün",
      mode: "Talep",
      verified: false,
      description: "Yüksek kaliteli medikal ekipman ve sarf malzemeleri."
    },
    {
      id: 'construction-materials',
      name: "İnşaat Malzemeleri",
      company: "BuildPro Construction",
      category: "İnşaat",
      location: "Bursa, Türkiye",
      image: "https://images.unsplash.com/photo-1581094288338-2314dddb7ece?auto=format&fit=crop&q=80",
      views: 1100,
      type: "Ürün",
      mode: "Satış",
      verified: true,
      description: "Geniş yelpazede inşaat ve yapı malzemeleri."
    },
    {
      id: 'smart-agriculture',
      name: "Akıllı Tarım Çözümleri",
      company: "AgroTech Solutions",
      category: "Teknoloji",
      location: "Antalya, Türkiye",
      image: "https://images.unsplash.com/photo-1563906267088-b029e7101114?auto=format&fit=crop&q=80",
      views: 980,
      type: "Hizmet",
      mode: "Talep",
      verified: true,
      description: "IoT tabanlı akıllı tarım ve sulama sistemleri."
    }
  ];

  const categories = [
    { id: 'all', name: 'Tüm Kategoriler' },
    { id: 'software', name: 'Yazılım' },
    { id: 'food', name: 'Gıda' },
    { id: 'health', name: 'Sağlık' },
    { id: 'construction', name: 'İnşaat' },
    { id: 'technology', name: 'Teknoloji' }
  ];

  const locations = [
    { id: 'all', name: 'Tüm Lokasyonlar' },
    { id: 'istanbul', name: 'İstanbul' },
    { id: 'izmir', name: 'İzmir' },
    { id: 'ankara', name: 'Ankara' },
    { id: 'bursa', name: 'Bursa' },
    { id: 'antalya', name: 'Antalya' }
  ];

  const sortOptions = [
    { id: 'newest', name: 'En Yeni' },
    { id: 'oldest', name: 'En Eski' },
    { id: 'mostViewed', name: 'En Çok Görüntülenen' },
    { id: 'leastViewed', name: 'En Az Görüntülenen' }
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = 
      product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      product.description.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesType = filters.type === 'all' || product.type.toLowerCase() === filters.type;
    const matchesMode = filters.mode === 'all' || product.mode.toLowerCase() === filters.mode;
    const matchesCategory = filters.category === 'all' || product.category.toLowerCase() === filters.category;
    const matchesLocation = filters.location === 'all' || product.location.includes(filters.location);

    return matchesSearch && matchesType && matchesMode && matchesCategory && matchesLocation;
  }).sort((a, b) => {
    switch (filters.sortBy) {
      case 'mostViewed':
        return b.views - a.views;
      case 'leastViewed':
        return a.views - b.views;
      default: // newest
        return b.views - a.views;
    }
  });

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Ürün ve Hizmetler</h1>
                <p className="mt-2 text-gray-600">Global ticarette öne çıkan ürün ve hizmetler</p>
              </div>
              <button
                onClick={() => navigate('/products/add')}
                className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors flex items-center space-x-2"
              >
                <ShoppingBag className="h-5 w-5" />
                <span>Yeni Ekle</span>
              </button>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="sticky top-0 z-10 bg-white border-b shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Ürün veya hizmet ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Sort Dropdown */}
              <select
                value={filters.sortBy}
                onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
              >
                {sortOptions.map(option => (
                  <option key={option.id} value={option.id}>
                    {option.name}
                  </option>
                ))}
              </select>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors ${
                  showFilters ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Sliders className="h-5 w-5" />
                <span>Filtreler</span>
                <span className="bg-white/20 px-2 py-0.5 rounded text-sm">
                  {Object.values(filters).filter(value => value !== 'all').length}
                </span>
              </button>
            </div>

            {/* Extended Filters */}
            {showFilters && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 animate-fadeIn">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Type Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tür
                    </label>
                    <select
                      value={filters.type}
                      onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                    >
                      <option value="all">Tümü</option>
                      <option value="ürün">Ürünler</option>
                      <option value="hizmet">Hizmetler</option>
                    </select>
                  </div>

                  {/* Mode Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mod
                    </label>
                    <select
                      value={filters.mode}
                      onChange={(e) => setFilters(prev => ({ ...prev, mode: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                    >
                      <option value="all">Tümü</option>
                      <option value="satış">Satış</option>
                      <option value="talep">Talep</option>
                    </select>
                  </div>

                  {/* Category Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Kategori
                    </label>
                    <select
                      value={filters.category}
                      onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                    >
                      {categories.map(category => (
                        <option key={category.id} value={category.id}>
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Location Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Lokasyon
                    </label>
                    <select
                      value={filters.location}
                      onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                    >
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>
                          {location.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Reset Filters */}
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => {
                      setFilters({
                        type: 'all',
                        mode: 'all',
                        category: 'all',
                        location: 'all',
                        sortBy: 'newest'
                      });
                      setSearchQuery('');
                    }}
                    className="px-4 py-2 text-primary hover:text-[#0A9996] transition-colors flex items-center space-x-2"
                  >
                    <X className="h-5 w-5" />
                    <span>Filtreleri Temizle</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Products Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredProducts.map((product) => (
              <div 
                key={product.id}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
              >
                <div className="aspect-[4/3] relative overflow-hidden rounded-t-xl">
                  <img 
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                  
                  <div className="absolute bottom-4 left-4">
                    <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                      product.mode === 'Talep' 
                        ? 'bg-primary/90 text-white' 
                        : 'bg-[#10B981]/90 text-white'
                    }`}>
                      {product.type} {product.mode}
                    </span>
                  </div>

                  <div className="absolute top-4 right-4">
                    <div className="flex items-center space-x-1 bg-white/90 rounded-full px-2 py-1">
                      <Eye className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-600">{product.views}</span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-primary">{product.company}</span>
                        {product.verified && (
                          <BadgeCheck className="h-4 w-4 text-primary flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mb-4">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{product.location}</span>
                  </div>

                  <button 
                    onClick={() => navigate(`/products/${product.id}`)}
                    className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
                  >
                    <span>Detayları Gör</span>
                    <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Products;