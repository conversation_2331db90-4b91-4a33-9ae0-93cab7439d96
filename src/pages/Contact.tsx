import React from 'react';
import { useTranslation } from 'react-i18next';
import { Mail, Phone, MapPin } from 'lucide-react';
import Header from '@/components/Header';
import Footer from '@/components/common/Footer';

const Contact: React.FC = () => {
  const { t } = useTranslation('common');

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-3xl font-bold mb-8 text-center">{t('footer.links.contact')}</h1>

        <div className="grid md:grid-cols-2 gap-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">{t('footer.sections.contact')}</h2>

            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-primary flex-shrink-0 mt-1" />
                <p className="text-gray-600">
                  {t('footer.contact.address')}
                </p>
              </div>

              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-primary" />
                <a href="mailto:<EMAIL>" className="text-gray-600 hover:text-primary transition-colors">
                  {t('footer.contact.email')}
                </a>
              </div>

              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-primary" />
                <a href="tel:+905400668000" className="text-gray-600 hover:text-primary transition-colors">
                  {t('footer.contact.phone')}
                </a>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">{t('sendMessage', { defaultValue: 'Send us a message' })}</h2>
            <p className="text-gray-600">
              {t('contactDescription', { defaultValue: 'For any inquiries or support, please feel free to contact us using the information provided or send us an email directly.' })}
            </p>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Contact;