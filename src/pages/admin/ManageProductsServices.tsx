import React, { useEffect, useState } from "react";
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  IconButton,
  Tooltip,
  useColorModeValue,
  HStack,
  Badge,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Button,
  Text,
  Image,
  Flex,
  VStack,
  ButtonGroup,
  Tag,
  TagLabel,
  RadioGroup,
  Radio,
  Stack,
  FormControl,
  FormLabel,
  Textarea,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import {
  FiCheck,
  FiX,
  FiEye
} from "react-icons/fi";
import { getItems, manageContent } from "@/adminApi";
import { IItem } from "@/types/item";

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL.replace(/\/api$/, '');

const ManageProductsServices: React.FC = () => {
  const [items, setItems] = useState<IItem[]>([]);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isRejectModalOpen, onOpen: onRejectModalOpen, onClose: onRejectModalClose } = useDisclosure();
  const [rejectionReason, setRejectionReason] = useState("");
  const [customRejectionReason, setCustomRejectionReason] = useState("");
  const toast = useToast();
  const { t } = useTranslation(["admin", "common"]);
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  useEffect(() => {
    fetchItems();
  }, []);

  const fetchItems = async () => {
    try {
      const items = await getItems();
      setItems(items);
    } catch (error: any) {
      console.error("Failed to fetch items:", error);
      toast({
        title: t("common:errors.general"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleAction = async (itemId: string, action: "approve" | "reject", reason?: string) => {
    try {
      if (action === "reject" && !reason) {
        setSelectedItem(items.find(item => item._id === itemId));
        onRejectModalOpen();
        return;
      }

      await manageContent(itemId, action, reason);
      fetchItems();
      toast({
        title: t("common:messages.updated.title"),
        description: t(`admin:productsServices.actions.${action}Success`),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      
      // Clear rejection reason state
      if (action === "reject") {
        setRejectionReason("");
        setCustomRejectionReason("");
      }
    } catch (error: any) {
      console.error(`Failed to ${action} item:`, error);
      toast({
        title: t("common:errors.general"),
        description: t(`admin:productsServices.actions.${action}Failed`),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleViewItem = (item: IItem) => {
    setSelectedItem(item);
    setCurrentImageIndex(0);
    onOpen();
  };

  return (
    <Box
      bg={bgColor}
      p={6}
      borderRadius="lg"
      boxShadow="md"
      borderColor={borderColor}
      borderWidth={1}
    >
      <Heading as="h1" size="lg" mb={6}>
        {t("admin:productsServices.title")}
      </Heading>

      <Box overflowX="auto">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>{t("admin:productsServices.table.name")}</Th>
              <Th>{t("admin:productsServices.table.description")}</Th>
              <Th>{t("admin:productsServices.table.category")}</Th>
              <Th>{t("admin:productsServices.table.type")}</Th>
              <Th>{t("admin:productsServices.table.status")}</Th>
              <Th>{t("admin:productsServices.table.actions")}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {items.map((item: any) => (
              <Tr key={item._id}>
                <Td>{item.name}</Td>
                <Td>
                  {item.description.length > 50
                    ? `${item.description.substring(0, 50)}...`
                    : item.description}
                </Td>
                <Td>{item.category?.name}</Td>
                <Td>
                  {t(`admin:productsServices.types.${item.type.toLowerCase()}`)}
                </Td>
                <Td>
                  <Badge colorScheme={item.isApproved ? "green" : (item.status === "INACTIVE" ? "red" : "yellow")}>
                    {t(
                      `admin:productsServices.status.${item.isApproved ? "approved" : (item.status === "INACTIVE" ? "rejected" : "pending")}`,
                    )}
                  </Badge>
                </Td>
                <Td>
                  <HStack spacing={2}>
                    <Tooltip label={t("admin:productsServices.actions.view")}>
                      <IconButton
                        aria-label={t("admin:productsServices.actions.view")}
                        icon={<FiEye />}
                        colorScheme="blue"
                        onClick={() => handleViewItem(item)}
                        size="sm"
                      />
                    </Tooltip>

                    {/* Always show the approve/activate button */}
                    <Tooltip
                      label={t(!item.isApproved && item.status === "INACTIVE" ? "admin:productsServices.actions.activate" : "admin:productsServices.actions.approve")}
                    >
                      <IconButton
                        aria-label={t(
                          !item.isApproved && item.status === "INACTIVE" ? "admin:productsServices.actions.activate" : "admin:productsServices.actions.approve",
                        )}
                        icon={<FiCheck />}
                        colorScheme="green"
                        onClick={() => handleAction(item._id, "approve")}
                        size="sm"
                        isDisabled={item.isApproved && item.status === "ACTIVE"} // Disable if already approved and active
                      />
                    </Tooltip>
                    
                    {/* Always show the reject button */}
                    <Tooltip
                      label={t("admin:productsServices.actions.reject")}
                    >
                      <IconButton
                        aria-label={t(
                          "admin:productsServices.actions.reject",
                        )}
                        icon={<FiX />}
                        colorScheme="red"
                        onClick={() => handleAction(item._id, "reject")}
                        size="sm"
                        // Remove isDisabled - always allow rejection/cancellation
                      />
                    </Tooltip>
                  </HStack>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>

      {/* Product/Service Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="md" isCentered>
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(10px)" />
        <ModalContent>
          <ModalHeader borderBottomWidth="1px" py={4}>
            <Flex justify="space-between" align="center">
              <Heading size="md">{selectedItem?.name}</Heading>
              <Badge colorScheme={selectedItem?.isApproved ? "green" : "yellow"} fontSize="0.8em" py={1} px={2}>
                {t(`admin:productsServices.status.${selectedItem?.isApproved ? "approved" : "pending"}`)}
              </Badge>
            </Flex>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody py={4}>
            {/* Image Section */}
            <Box mb={4} borderRadius="md" overflow="hidden">
              {selectedItem?.images && selectedItem.images.length > 0 && (
                <Box mt={4}>
                  <Flex justify="center" mb={4}>
                    <Image
                      src={`${API_BASE_URL}/${selectedItem.images[currentImageIndex]}`}
                      alt={selectedItem.name}
                      maxH="400px"
                      objectFit="contain"
                    />
                  </Flex>

                  {selectedItem.images.length > 1 && (
                    <Flex justify="center" wrap="wrap" gap={2}>
                      {selectedItem.images.map((image: any, idx: any) => (
                        <Box
                          key={idx}
                          cursor="pointer"
                          borderWidth={idx === currentImageIndex ? "2px" : "1px"}
                          borderColor={idx === currentImageIndex ? "blue.500" : "gray.200"}
                          p={1}
                          borderRadius="md"
                          onClick={() => setCurrentImageIndex(idx)}
                          _hover={{ borderColor: "blue.300" }}
                        >
                          <Image
                            src={image.startsWith('http') ? image : `${API_BASE_URL}${image}`}
                            alt={`Thumbnail ${idx + 1}`}
                            height="60px"
                            width="60px"
                            objectFit="cover"
                          />
                        </Box>
                      ))}
                    </Flex>
                  )}
                </Box>
              )}
            </Box>

            {/* Basic Info */}
            <VStack align="stretch" spacing={3}>
              <Box>
                <Text fontWeight="bold" mb={1}>{t("admin:productsServices.modal.type")}:</Text>
                <Tag colorScheme={selectedItem?.type === 'product' ? 'purple' : 'cyan'}>
                  <TagLabel>
                    {selectedItem ? t(`admin:productsServices.types.${selectedItem.type.toLowerCase()}`) : ''}
                  </TagLabel>
                </Tag>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={1}>{t("admin:productsServices.modal.category")}:</Text>
                <Text>{selectedItem?.category?.name || t("common:notSpecified")}</Text>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={1}>{t("admin:productsServices.modal.description")}:</Text>
                <Text>{selectedItem?.description || t("common:notSpecified")}</Text>
              </Box>
            </VStack>
          </ModalBody>

          <ModalFooter borderTopWidth="1px" py={4}>
            <ButtonGroup spacing={3}>
              {!selectedItem?.isApproved && (
                <Button
                  colorScheme="green"
                  leftIcon={<FiCheck />}
                  onClick={() => {
                    handleAction(selectedItem?._id || "", "approve");
                    onClose();
                  }}
                >
                  {t("admin:productsServices.actions.approve")}
                </Button>
              )}

              {/* Always show reject button */}
              <Button
                colorScheme="red"
                leftIcon={<FiX />}
                onClick={() => {
                  handleAction(selectedItem?._id || "", "reject");
                  onClose();
                }}
              >
                {t("admin:productsServices.actions.reject")}
              </Button>

              <Button
                variant="outline"
                onClick={onClose}
              >
                {t("common:buttons.close")}
              </Button>
            </ButtonGroup>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Rejection Reason Modal */}
      <Modal isOpen={isRejectModalOpen} onClose={onRejectModalClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("admin:productsServices.reject.title")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text mb={4}>{t("admin:productsServices.reject.selectReason")}</Text>
            <RadioGroup value={rejectionReason} onChange={setRejectionReason} mb={4}>
              <Stack spacing={2}>
                <Radio value="inappropriateContent">{t("admin:productsServices.reject.reasons.inappropriateContent")}</Radio>
                <Radio value="missingInformation">{t("admin:productsServices.reject.reasons.missingInformation")}</Radio>
                <Radio value="poorQualityImages">{t("admin:productsServices.reject.reasons.poorQualityImages")}</Radio>
                <Radio value="prohibitedItem">{t("admin:productsServices.reject.reasons.prohibitedItem")}</Radio>
                <Radio value="other">{t("admin:productsServices.reject.reasons.other")}</Radio>
              </Stack>
            </RadioGroup>
            
            {rejectionReason === "other" && (
              <FormControl>
                <FormLabel>{t("admin:productsServices.reject.customReason")}</FormLabel>
                <Textarea 
                  value={customRejectionReason}
                  onChange={(e) => setCustomRejectionReason(e.target.value)}
                  placeholder={t("admin:productsServices.reject.customReasonPlaceholder")}
                />
              </FormControl>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onRejectModalClose}>
              {t("common:cancel")}
            </Button>
            <Button 
              colorScheme="red" 
              isDisabled={!rejectionReason || (rejectionReason === "other" && !customRejectionReason)}
              onClick={() => {
                const finalReason = rejectionReason === "other" ? customRejectionReason : t(`admin:productsServices.reject.reasons.${rejectionReason}`);
                handleAction(selectedItem._id, "reject", finalReason);
                onRejectModalClose();
              }}
            >
              {t("admin:productsServices.actions.reject")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ManageProductsServices;
