import React, { useEffect, useState } from "react";
import {
  Box,
  Container,
  Heading,
  useColorModeValue,
  VStack,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  useToast,
  HStack,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  FormControl,
  FormLabel,
  Input,
  NumberInput,
  NumberInputField,
  useDisclosure,
  Image,
  IconButton,
  Stack,
  Center,
  Spinner,
  Link,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Textarea,
  SimpleGrid,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { adminApi, getDashboardStats } from "@/adminApi";
import { format } from "date-fns";
import { AddIcon, DeleteIcon, EditIcon } from "@chakra-ui/icons";
import { UserStatistics } from "@/components/admin/UserStatistics";

interface ISlider {
  _id: string;
  web: string;
  mobile: string;
  link: string;
  header?: string;
  description?: string;
  linkText?: string;
  order: number;
  active: boolean;
}

interface IHomepageAd {
  _id: string;
  userId: string;
  userName: string;
  title: string;
  status: "pending" | "approved" | "rejected" | "expired";
  createdAt: Date;
}

interface SliderFormData {
  webImage: File | null;
  mobileImage: File | null;
  webImagePreview: string;
  mobileImagePreview: string;
  link?: string;
  header?: string;
  description?: string;
  linkText?: string;
  order: number;
}

const AdminDashboard: React.FC = () => {
  const { t } = useTranslation("admin");
  const toast = useToast();
  const bgColor = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const [isLoading, setIsLoading] = useState(true);
  const [pendingAds, setPendingAds] = useState<IHomepageAd[]>([]);
  const [sliders, setSliders] = useState<ISlider[]>([]);
  const [selectedSlider, setSelectedSlider] = useState<ISlider | null>(null);
  const [formData, setFormData] = useState<SliderFormData>({
    webImage: null,
    mobileImage: null,
    webImagePreview: "",
    mobileImagePreview: "",
    link: "",
    header: "",
    description: "",
    linkText: "",
    order: 0,
  });
  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isPreviewOpen,
    onOpen: onPreviewOpen,
    onClose: onPreviewClose,
  } = useDisclosure();
  const [previewImage, setPreviewImage] = useState<{
    url: string;
    type: "web" | "mobile";
  } | null>(null);
  const [stats, setStats] = useState<any>({
    activeUsers: 0,
    newUsers: 0,
    userGrowth: 0,
    userActivityData: [],
    userRecruitmentData: [],
    userTypeDistribution: [],
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [statsResponse, slidersResponse, adsResponse] = await Promise.all(
          [
            getDashboardStats(),
            adminApi.get("/admin/sliders"),
            adminApi.get("/admin/home-ads/pending"),
          ],
        );
        setStats(statsResponse);
        setSliders(slidersResponse.data);
        setPendingAds(adsResponse.data);
      } catch (error) {
        toast({
          title: t("dashboard.error.fetchStats"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Dedicated function to fetch sliders
  const fetchSliders = async () => {
    try {
      const response = await adminApi.get("/admin/sliders");
      setSliders(response.data);
      return response.data;
    } catch (error) {
      console.error("Error fetching sliders:", error);
      toast({
        title: t("common.error"),
        description: t("sliders.error.fetch") || "Failed to fetch sliders", 
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return [];
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>, type: "web" | "mobile") => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const fileType = file.type.toLowerCase();
      const fileSizeInMB = file.size / (1024 * 1024);
      const MAX_FILE_SIZE_MB = 10;
      
      // Check if file type is an image
      if (!fileType.startsWith('image/')) {
        toast({
          title: t("sliders.error.invalidFileType") || "Invalid file type",
          description: t("sliders.error.onlyImageFiles") || "Only image files are allowed",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        // Reset the file input
        e.target.value = "";
        return;
      }
      
      // Check file size (max 10MB)
      if (fileSizeInMB > MAX_FILE_SIZE_MB) {
        toast({
          title: t("sliders.error.fileTooLarge") || "File too large",
          description: t("sliders.error.maxFileSize", { size: MAX_FILE_SIZE_MB }) || `Maximum file size is ${MAX_FILE_SIZE_MB}MB`,
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        // Reset the file input
        e.target.value = "";
        return;
      }
      
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target?.result) {
          if (type === "web") {
            setFormData({
              ...formData,
              webImage: file,
              webImagePreview: event.target.result as string,
            });
          } else {
            setFormData({
              ...formData,
              mobileImage: file,
              mobileImagePreview: event.target.result as string,
            });
          }
        }
      };

      reader.readAsDataURL(file);
    }
  };

  const handleImageClick = (imagePath: string, type: "web" | "mobile") => {
    setPreviewImage({
      url: import.meta.env.VITE_SOCKET_URL + imagePath,
      type,
    });
    onPreviewOpen();
  };

  const handleEdit = (slider: ISlider) => {
    setSelectedSlider(slider);
    setFormData({
      webImage: null,
      mobileImage: null,
      webImagePreview: import.meta.env.VITE_SOCKET_URL + slider.web,
      mobileImagePreview: import.meta.env.VITE_SOCKET_URL + slider.mobile,
      link: slider.link || "",
      header: slider.header || "",
      description: slider.description || "",
      linkText: slider.linkText || "",
      order: slider.order,
    });
    onOpen();
  };

  const handleDelete = async (sliderId: string) => {
    try {
      await adminApi.delete(`/admin/sliders/${sliderId}`);
      const response = await adminApi.get("/admin/sliders");
      setSliders(response.data);
      toast({
        title: t("sliders.success.delete"),
        status: "success",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: t("sliders.error.delete"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleSubmit = async () => {
    try {
      // Check for required images for new slider
      if (!selectedSlider && (!formData.webImage || !formData.mobileImage)) {
        toast({
          title: t("common.error"),
          description: t("common.requiredFields"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      setIsLoading(true);

      // Create FormData object for API request
      const formDataObj = new FormData();
      
      // Only append images if they exist (for both new and update cases)
      if (formData.webImage) {
        formDataObj.append("webImage", formData.webImage);
      }
      
      if (formData.mobileImage) {
        formDataObj.append("mobileImage", formData.mobileImage);
      }
      
      // Add other form fields
      if (formData.link) formDataObj.append("link", formData.link);
      if (formData.header) formDataObj.append("header", formData.header);
      if (formData.description) formDataObj.append("description", formData.description);
      if (formData.linkText) formDataObj.append("linkText", formData.linkText);
      formDataObj.append("order", formData.order.toString());
      formDataObj.append("active", "true");

      try {
        if (selectedSlider) {
          await adminApi.put(
            `/admin/sliders/${selectedSlider._id}`,
            formDataObj,
            {
              headers: {
                "Content-Type": "multipart/form-data",
              },
            },
          );
        } else {
          await adminApi.post("/admin/sliders", formDataObj, {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          });
        }

        // Successfully saved, now fetch updated sliders and close modal
        await fetchSliders(); // Use the existing fetchSliders function
        onClose();
        toast({
          title: selectedSlider
            ? t("sliders.success.update")
            : t("sliders.success.create"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } catch (apiError: any) {
        console.error("API Error saving slider:", apiError);
        
        // Default error message
        let errorMessage = t("common.serverError") || "An error occurred";
        
        // Check if the error response contains information about specific errors
        const errorResponseMessage = 
          apiError.response?.data?.message || 
          apiError.response?.data?.error || 
          apiError.message || '';
          
        if (errorResponseMessage.includes('Invalid file type')) {
          errorMessage = t("sliders.error.onlyImageFiles") || "Only image files are allowed";
        } else if (errorResponseMessage.includes('File too large') || errorResponseMessage.includes('too large')) {
          errorMessage = t("sliders.error.maxFileSize", { size: 10 }) || "Maximum file size is 10MB";
        }
        
        toast({
          title: selectedSlider
            ? t("sliders.error.update")
            : t("sliders.error.create"),
          description: errorMessage,
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error) {
      console.error("Error saving slider:", error);
      toast({
        title: selectedSlider
          ? t("sliders.error.update")
          : t("sliders.error.create"),
        description: t("sliders.error.genericError") || "An unexpected error occurred. Please try again.",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (adId: string) => {
    try {
      await adminApi.put(`/admin/home-ads/${adId}/approve`);
      const response = await adminApi.get("/admin/home-ads/pending");
      setPendingAds(response.data);
      toast({
        title: t("homepageAd.approveSuccess"),
        status: "success",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: t("homepageAd.approveError"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleReject = async (adId: string) => {
    try {
      await adminApi.put(`/admin/home-ads/${adId}/reject`);
      const response = await adminApi.get("/admin/home-ads/pending");
      setPendingAds(response.data);
      toast({
        title: t("homepageAd.rejectSuccess"),
        status: "success",
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: t("homepageAd.rejectError"),
        status: "error",
        duration: 3000,
      });
    }
  };

  return (
    <Container maxW="container.xl" py={5}>
      {isLoading ? (
        <Center h="200px">
          <Spinner size="xl" />
        </Center>
      ) : (
        <VStack spacing={8} align="stretch">
          <UserStatistics
            activeUsers={stats?.activeUsers}
            totalUsers={stats?.users}
            newUsers={stats?.newUsers}
            userGrowth={stats?.userGrowth}
            topReferrers={stats?.topReferrers}
            userActivityData={stats?.userActivityData}
            userRecruitmentData={stats?.userRecruitmentData}
            userTypeDistribution={stats?.userTypeDistribution}
          />

          {pendingAds.length > 0 ? (
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>{t("homepageAd.table.title")}</Th>
                  <Th>{t("homepageAd.table.user")}</Th>
                  <Th>{t("homepageAd.table.created_at")}</Th>
                  <Th>{t("homepageAd.table.actions")}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {pendingAds.map((ad) => (
                  <Tr key={ad._id}>
                    <Td>{ad.title}</Td>
                    <Td>{ad.userName}</Td>
                    <Td>{format(new Date(ad.createdAt), "PP")}</Td>
                    <Td>
                      <HStack spacing={2}>
                        <Button
                          size="sm"
                          colorScheme="green"
                          onClick={() => handleApprove(ad._id)}
                        >
                          {t("homepageAd.actions.approve")}
                        </Button>
                        <Button
                          size="sm"
                          colorScheme="red"
                          onClick={() => handleReject(ad._id)}
                        >
                          {t("homepageAd.actions.reject")}
                        </Button>
                      </HStack>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          ) : (
            <Text textAlign="center" color="gray.500">
              {t("homepageAd.messages.no_pending")}
            </Text>
          )}

          {/* Slider Management Section */}
          <Box
            mt={8}
            p={6}
            bg={bgColor}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
          >
            <HStack justify="space-between" mb={4}>
              <Heading size="md">{t("sliders.title")}</Heading>
              <Button
                colorScheme="blue"
                leftIcon={<AddIcon />}
                onClick={() => {
                  setSelectedSlider(null);
                  setFormData({
                    webImage: null,
                    mobileImage: null,
                    webImagePreview: "",
                    mobileImagePreview: "",
                    link: "",
                    header: "",
                    description: "",
                    linkText: "",
                    order: 0,
                  });
                  onOpen();
                }}
              >
                {t("sliders.addNew")}
              </Button>
            </HStack>

            {isLoading ? (
              <Center py={8}>
                <Spinner />
              </Center>
            ) : sliders.length === 0 ? (
              <Center py={8}>
                <Text color="gray.500">{t("common.noData")}</Text>
              </Center>
            ) : (
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>{t("sliders.previewTable")}</Th>
                    <Th>{t("sliders.header")}</Th>
                    <Th>{t("sliders.link")}</Th>
                    <Th>{t("sliders.order")}</Th>
                    <Th>{t("sliders.actions")}</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {sliders.map((slider) => (
                    <Tr key={slider._id}>
                      <Td>
                        <HStack spacing={4}>
                          <Box
                            position="relative"
                            cursor="pointer"
                            onClick={() => handleImageClick(slider.web, "web")}
                          >
                            <Image
                              src={import.meta.env.VITE_SOCKET_URL + slider.web}
                              alt="Web"
                              boxSize="100px"
                              objectFit="cover"
                              borderRadius="md"
                            />

                            <Badge
                              position="absolute"
                              bottom="2"
                              right="2"
                              colorScheme="blue"
                            >
                              Web
                            </Badge>
                          </Box>
                          <Box
                            position="relative"
                            cursor="pointer"
                            onClick={() =>
                              handleImageClick(slider.mobile, "mobile")
                            }
                          >
                            <Image
                              src={
                                import.meta.env.VITE_SOCKET_URL + slider.mobile
                              }
                              alt="Mobile"
                              boxSize="50px"
                              objectFit="cover"
                              borderRadius="md"
                            />

                            <Badge
                              position="absolute"
                              bottom="2"
                              right="2"
                              colorScheme="green"
                            >
                              Mobile
                            </Badge>
                          </Box>
                        </HStack>
                      </Td>
                      <Td>
                        {slider.header ? (
                          <Text noOfLines={2} maxW="200px">{slider.header}</Text>
                        ) : (
                          <Text color="gray.500">-</Text>
                        )}
                      </Td>
                      <Td>
                        {slider.link ? (
                          <Link href={slider.link} isExternal color="blue.500">
                            {slider.link}
                          </Link>
                        ) : (
                          <Text color="gray.500">-</Text>
                        )}
                      </Td>
                      <Td>{slider.order}</Td>
                      <Td>
                        <HStack spacing={2}>
                          <IconButton
                            aria-label={t("sliders.edit")}
                            icon={<EditIcon />}
                            size="sm"
                            colorScheme="blue"
                            onClick={() => handleEdit(slider)}
                          />

                          <IconButton
                            aria-label={t("common:delete")}
                            icon={<DeleteIcon />}
                            size="sm"
                            colorScheme="red"
                            onClick={() => handleDelete(slider._id)}
                          />
                        </HStack>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
          </Box>

          {/* Slider Modal */}
          <Modal isOpen={isOpen} onClose={onClose} size="4xl">
            <ModalOverlay
              bg="blackAlpha.300"
              backdropFilter="blur(5px)"
            />
            <ModalContent borderRadius="md" shadow="xl">
              <ModalHeader
                bg="blue.50"
                borderTopRadius="md"
                color="blue.700"
                fontSize="lg"
                fontWeight="bold"
                p={4}
              >
                {selectedSlider
                  ? t("sliders.edit")
                  : t("sliders.addNew")}
              </ModalHeader>
              <ModalCloseButton />
              <ModalBody pb={6}>
                <Stack spacing={6}>
                  {/* Images Section at the top */}
                  <Box
                    bg="gray.50"
                    p={4}
                    borderRadius="md"
                    borderWidth="1px"
                    borderColor="gray.200"
                  >
                    <Heading size="sm" mb={4} color="gray.700">
                      {t("sliders.form.imageSection")}
                    </Heading>
                    <SimpleGrid columns={2} spacing={6}>
                      {/* Web Image */}
                      <Box>
                        <FormControl isRequired mb={3}>
                          <FormLabel fontWeight="medium">{t("sliders.form.webImage")}</FormLabel>
                          <Input
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleImageChange(e, "web")}
                            p={1}
                            mb={2}
                          />
                        </FormControl>
                        {formData.webImagePreview ? (
                          <Box 
                            height="250px" 
                            borderRadius="md" 
                            overflow="hidden" 
                            position="relative"
                            boxShadow="md"
                          >
                            <Image
                              src={formData.webImagePreview}
                              alt="Web preview"
                              width="100%"
                              height="100%"
                              objectFit="cover"
                            />
                            <Badge
                              position="absolute"
                              bottom="2"
                              right="2"
                              colorScheme="blue"
                              fontSize="sm"
                              fontWeight="bold"
                              px={3}
                              py={1}
                              borderRadius="full"
                              boxShadow="sm"
                            >
                              Web
                            </Badge>
                          </Box>
                        ) : (
                          <Box 
                            height="250px" 
                            borderRadius="md" 
                            bg="gray.100" 
                            display="flex" 
                            alignItems="center" 
                            justifyContent="center"
                            borderWidth="1px"
                            borderStyle="dashed"
                            borderColor="gray.300"
                          >
                            <Text color="gray.500">{t("sliders.form.noImageSelected") || "No image selected"}</Text>
                          </Box>
                        )}
                      </Box>

                      {/* Mobile Image */}
                      <Box>
                        <FormControl isRequired mb={3}>
                          <FormLabel fontWeight="medium">{t("sliders.form.mobileImage")}</FormLabel>
                          <Input
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleImageChange(e, "mobile")}
                            p={1}
                            mb={2}
                          />
                        </FormControl>
                        {formData.mobileImagePreview ? (
                          <Box 
                            height="250px" 
                            borderRadius="md" 
                            overflow="hidden" 
                            position="relative"
                            boxShadow="md"
                          >
                            <Image
                              src={formData.mobileImagePreview}
                              alt="Mobile preview"
                              width="100%"
                              height="100%"
                              objectFit="cover"
                            />
                            <Badge
                              position="absolute"
                              bottom="2"
                              right="2"
                              colorScheme="green"
                              fontSize="sm"
                              fontWeight="bold"
                              px={3}
                              py={1}
                              borderRadius="full"
                              boxShadow="sm"
                            >
                              Mobile
                            </Badge>
                          </Box>
                        ) : (
                          <Box 
                            height="250px" 
                            borderRadius="md" 
                            bg="gray.100" 
                            display="flex" 
                            alignItems="center" 
                            justifyContent="center"
                            borderWidth="1px"
                            borderStyle="dashed"
                            borderColor="gray.300"
                          >
                            <Text color="gray.500">{t("sliders.form.noImageSelected") || "No image selected"}</Text>
                          </Box>
                        )}
                      </Box>
                    </SimpleGrid>
                  </Box>

                  {/* Form inputs below */}
                  <SimpleGrid columns={2} spacing={6}>
                    {/* LEFT COLUMN */}
                    <Stack spacing={4}>
                      {/* Content Section */}
                      <Box
                        bg="gray.50"
                        p={4}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor="gray.200"
                      >
                        <Heading size="sm" mb={4} color="gray.700">
                          {t("sliders.form.contentSection")}
                        </Heading>
                        <Stack spacing={4}>
                          {/* Header */}
                          <FormControl>
                            <FormLabel fontWeight="medium">{t("sliders.form.header")}</FormLabel>
                            <Input
                              value={formData.header}
                              onChange={(e) =>
                                setFormData({ ...formData, header: e.target.value })
                              }
                              placeholder={t("sliders.form.headerPlaceholder") || "Enter header text"}
                              bg="white"
                            />
                          </FormControl>

                          {/* Description */}
                          <FormControl>
                            <FormLabel fontWeight="medium">{t("sliders.form.description")}</FormLabel>
                            <Textarea
                              value={formData.description}
                              onChange={(e) =>
                                setFormData({ ...formData, description: e.target.value })
                              }
                              placeholder={t("sliders.form.descriptionPlaceholder") || "Enter description text"}
                              bg="white"
                              rows={4}
                            />
                          </FormControl>
                        </Stack>
                      </Box>
                    </Stack>

                    {/* RIGHT COLUMN */}
                    <Stack spacing={4}>
                      {/* Link Section */}
                      <Box
                        bg="gray.50"
                        p={4}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor="gray.200"
                      >
                        <Heading size="sm" mb={4} color="gray.700">
                          {t("sliders.form.linkSection")}
                        </Heading>
                        <Stack spacing={4}>
                          {/* Link */}
                          <FormControl>
                            <FormLabel fontWeight="medium">{t("sliders.form.link")}</FormLabel>
                            <Input
                              value={formData.link}
                              onChange={(e) =>
                                setFormData({ ...formData, link: e.target.value })
                              }
                              placeholder={t("sliders.form.linkPlaceholder") || "https://..."}
                              bg="white"
                            />
                          </FormControl>

                          {/* Link Text */}
                          <FormControl>
                            <FormLabel fontWeight="medium">{t("sliders.form.linkText")}</FormLabel>
                            <Input
                              value={formData.linkText}
                              onChange={(e) =>
                                setFormData({ ...formData, linkText: e.target.value })
                              }
                              placeholder={t("sliders.form.linkTextPlaceholder") || "Learn More"}
                              bg="white"
                            />
                          </FormControl>
                        </Stack>
                      </Box>

                      {/* Display Settings */}
                      <Box
                        bg="gray.50"
                        p={4}
                        borderRadius="md"
                        borderWidth="1px"
                        borderColor="gray.200"
                      >
                        <Heading size="sm" mb={4} color="gray.700">
                          {t("sliders.form.displaySection")}
                        </Heading>
                        <FormControl isRequired>
                          <FormLabel fontWeight="medium">{t("sliders.form.order")}</FormLabel>
                          <NumberInput
                            value={formData.order}
                            onChange={(_, value) =>
                              setFormData({ ...formData, order: value })
                            }
                            bg="white"
                            borderRadius="md"
                            min={0}
                          >
                            <NumberInputField />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                        </FormControl>
                      </Box>
                    </Stack>
                  </SimpleGrid>
                </Stack>
              </ModalBody>

              <ModalFooter bg="gray.50" borderBottomRadius="md" px={4} py={3}>
                <Button onClick={onClose} mr={3} variant="outline">
                  {t("common.cancel")}
                </Button>
                <Button
                  colorScheme="blue"
                  onClick={handleSubmit}
                  isLoading={isLoading}
                >
                  {t("common.save")}
                </Button>
              </ModalFooter>
            </ModalContent>
          </Modal>

          {/* Image Preview Modal */}
          <Modal isOpen={isPreviewOpen} onClose={onPreviewClose} size="6xl">
            <ModalOverlay />
            <ModalContent>
              <ModalHeader>
                {previewImage?.type === "web"
                  ? t("sliders.preview.web")
                  : t("sliders.preview.mobile")}
              </ModalHeader>
              <ModalCloseButton />
              <ModalBody pb={6}>
                <Center>
                  <Image
                    src={previewImage?.url}
                    alt={
                      previewImage?.type === "web"
                        ? "Web preview"
                        : "Mobile preview"
                    }
                    maxH="80vh"
                    objectFit="contain"
                    borderRadius="md"
                  />
                </Center>
              </ModalBody>
            </ModalContent>
          </Modal>
        </VStack>
      )}
    </Container>
  );
};

export default AdminDashboard;
