import React, { useState, useEffect, useRef } from 'react';
import { useSocket } from '../../context/SocketContext';
import { useTranslation } from 'react-i18next';
import { useAuthCheck } from '../../hooks/useAuthCheck';
import {
  Box,
  Flex,
  Text,
  Heading,
  Badge,
  Button,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Input,
  List,
  ListItem,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  SimpleGrid,
  VStack,
  Spinner,
  useToast,
} from '@chakra-ui/react';
import {
  getAdminActiveLiveChats,
  getAdminClosedLiveChats,
  getAdminArchivedLiveChats,
  archiveAdminLiveChat,
  getAdminLiveChatStatistics,
  getAdminLiveChatHistory,
  sendAdminLiveChatMessage,
  markAdminLiveChatMessagesAsRead
} from '../../adminApi';

interface ChatSession {
  chatId: string;
  name: string;
  email?: string;
  subject?: string;
  lastMessage?: {
    content: string;
    timestamp: Date;
  };
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface Message {
  content: string;
  senderId: string;
  senderType: 'user' | 'admin' | 'anonymous';
  timestamp: Date;
  read: boolean;
}

interface ChatStatistics {
  activeChats: number;
  closedChats: number;
  archivedChats: number;
  totalChats: number;
  totalMessages: number;
  activeChatsWithUnread: number;
  averageResponseTimeMinutes: number;
}

const ManageLiveChat: React.FC = () => {
  const { t } = useTranslation('admin');
  const toast = useToast();
  const { socket } = useSocket();
  const { user } = useAuthCheck();
  const userId = user?._id;

  const [activeChats, setActiveChats] = useState<ChatSession[]>([]);
  const [closedChats, setClosedChats] = useState<ChatSession[]>([]);
  const [archivedChats, setArchivedChats] = useState<ChatSession[]>([]);
  const [selectedChat, setSelectedChat] = useState<string | null>(null);
  const [chatHistory, setChatHistory] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [closedPageNumber, setClosedPageNumber] = useState(1);
  const [archivedPageNumber, setArchivedPageNumber] = useState(1);
  const [closedPagination, setClosedPagination] = useState({ total: 0, pages: 0 });
  const [archivedPagination, setArchivedPagination] = useState({ total: 0, pages: 0 });
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [chatInfo, setChatInfo] = useState<{ name: string; email?: string; subject?: string } | null>(null);
  const [chatStatistics, setChatStatistics] = useState<ChatStatistics | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<any>(null);

  // Load initial data
  useEffect(() => {
    fetchActiveChats();
    fetchChatStatistics();
  }, []);

  // Set up socket listeners
  useEffect(() => {
    if (!socket) return;

    // Join admin room
    socket.emit('admin:join', {});

    // Listen for new chat messages
    socket.on('livechat:message', (data: { chatId: string; message: Message }) => {
      if (selectedChat && data.chatId === selectedChat) {
        setChatHistory(prev => [...prev, data.message]);
        scrollToBottom();

        // Mark messages as read automatically
        markAdminLiveChatMessagesAsRead(data.chatId);
      }

      // Update the active chats list
      setActiveChats(prev =>
        prev.map(chat =>
          chat.chatId === data.chatId
            ? {
              ...chat,
              lastMessage: {
                content: data.message.content,
                timestamp: new Date(data.message.timestamp)
              },
              unreadCount: (chat.unreadCount || 0) + (data.message.senderType !== 'admin' ? 1 : 0)
            }
            : chat
        )
      );
    });

    // Listen for new chat sessions
    socket.on('livechat:new_chat', (data: {
      chatId: string;
      name: string;
      email?: string;
      subject?: string;
      timestamp: Date
    }) => {
      // Add to active chats
      setActiveChats(prev => [
        {
          chatId: data.chatId,
          name: data.name,
          email: data.email,
          subject: data.subject,
          unreadCount: 1,
          createdAt: new Date(data.timestamp),
          updatedAt: new Date(data.timestamp)
        },
        ...prev
      ]);

      // Play notification sound or show toast
      toast({
        title: 'New Chat',
        description: `${data.name} has started a new chat.`,
        status: 'info',
        duration: 5000,
        isClosable: true,
      });

      // Update statistics
      fetchChatStatistics();
    });

    // Listen for chat closed events
    socket.on('livechat:closed', (data: { chatId: string }) => {
      if (selectedChat === data.chatId) {
        toast({
          title: 'Chat Closed',
          description: 'This chat session has been closed by the visitor.',
          status: 'info',
          duration: 5000,
          isClosable: true,
        });
      }

      // Move from active to closed
      setActiveChats(prev => prev.filter(chat => chat.chatId !== data.chatId));
      fetchClosedChats();
      fetchChatStatistics();
    });

    // Listen for typing indicators
    socket.on('livechat:typing', (data: { chatId: string; isAdmin: boolean; isTyping: boolean }) => {
      if (selectedChat === data.chatId && !data.isAdmin) {
        setIsTyping(data.isTyping);
      }
    });

    return () => {
      socket.off('livechat:message');
      socket.off('livechat:new_chat');
      socket.off('livechat:closed');
      socket.off('livechat:typing');
    };
  }, [socket, selectedChat, toast]);

  // Auto-scroll when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [chatHistory]);

  // Fetch active chats
  const fetchActiveChats = async () => {
    try {
      setIsLoading(true);
      const response = await getAdminActiveLiveChats();
      console.log('Active chats response:', response);
      setActiveChats(response);
    } catch (error) {
      console.error('Error fetching active chats:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch active chats.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch closed chats
  const fetchClosedChats = async (page = 1) => {
    try {
      setIsLoadingMore(true);
      const response = await getAdminClosedLiveChats(page);
      console.log('Closed chats response:', response);

      if (page === 1) {
        setClosedChats(response.chats);
      } else {
        setClosedChats(prev => [...prev, ...response.chats]);
      }

      setClosedPagination({
        total: response.pagination.total,
        pages: response.pagination.pages
      });

      setClosedPageNumber(page);
    } catch (error) {
      console.error('Error fetching closed chats:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch closed chats.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Fetch archived chats
  const fetchArchivedChats = async (page = 1) => {
    try {
      setIsLoadingMore(true);
      const response = await getAdminArchivedLiveChats(page);
      console.log('Archived chats response:', response);

      if (page === 1) {
        setArchivedChats(response.chats);
      } else {
        setArchivedChats(prev => [...prev, ...response.chats]);
      }

      setArchivedPagination({
        total: response.pagination.total,
        pages: response.pagination.pages
      });

      setArchivedPageNumber(page);
    } catch (error) {
      console.error('Error fetching archived chats:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch archived chats.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Fetch chat statistics
  const fetchChatStatistics = async () => {
    try {
      const response = await getAdminLiveChatStatistics();
      console.log('Chat statistics response:', response);
      setChatStatistics(response);
    } catch (error) {
      console.error('Error fetching chat statistics:', error);
    }
  };

  // Select a chat to view
  const handleSelectChat = async (chatId: string, chatData: ChatSession) => {
    try {
      setSelectedChat(chatId);
      setIsLoadingHistory(true);
      setChatInfo({
        name: chatData.name,
        email: chatData.email,
        subject: chatData.subject
      });

      const response = await getAdminLiveChatHistory(chatId);
      setChatHistory(response.messages || []);

      // Mark messages as read
      await markAdminLiveChatMessagesAsRead(chatId);

      // Update unread count in active chats list
      setActiveChats(prev =>
        prev.map(chat =>
          chat.chatId === chatId ? { ...chat, unreadCount: 0 } : chat
        )
      );

      // Join the chat room via socket
      if (socket && socket.connected) {
        // Leave any previous chat rooms first
        if (selectedChat && selectedChat !== chatId) {
          socket.emit('livechat:leave', { chatId: selectedChat });
          console.log(`Admin left previous chat room: ${selectedChat}`);
        }

        // Join the new chat room
        socket.emit('livechat:join', { chatId });
        console.log(`Admin joined chat room: ${chatId} with socket ID ${socket.id}`);
      } else {
        console.warn('Socket not connected, unable to join chat room');
      }
    } catch (error) {
      console.error('Error fetching chat history:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch chat history.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoadingHistory(false);
      scrollToBottom();
    }
  };

  // Send a message
  const handleSendMessage = async () => {
    if (!inputValue.trim() || !selectedChat) return;

    try {
      const messageData = { content: inputValue.trim() };
      setInputValue('');

      // Create a temporary message object to show immediately
      const tempMessage: Message = {
        content: messageData.content,
        senderId: userId || 'admin',
        senderType: 'admin',
        timestamp: new Date(),
        read: true
      };

      // Add the message to the local state immediately so admin sees their message right away
      setChatHistory(prev => [...prev, tempMessage]);
      scrollToBottom();

      console.log('Sending message via socket to chat:', selectedChat);

      // Use socket if connected
      if (socket && socket.connected) {
        socket.emit('livechat:message', {
          chatId: selectedChat,
          content: messageData.content
        });

        console.log('Message sent via socket:', messageData.content);
      } else {
        // Fallback to API
        console.log('Socket not connected, using API fallback');
        await sendAdminLiveChatMessage(selectedChat, messageData);
      }

      // Stop typing indicator
      handleTypingStop();
    } catch (error) {
      console.error('Error sending message:', error);
      toast({
        title: 'Error',
        description: 'Failed to send message.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Archive a chat
  const handleArchiveChat = async (chatId: string) => {
    try {
      await archiveAdminLiveChat(chatId);

      // Remove from current list and refresh other lists
      if (selectedChat === chatId) {
        setSelectedChat(null);
        setChatHistory([]);
      }

      setActiveChats(prev => prev.filter(chat => chat.chatId !== chatId));
      setClosedChats(prev => prev.filter(chat => chat.chatId !== chatId));

      // Refresh archived chats and statistics
      fetchArchivedChats();
      fetchChatStatistics();

      toast({
        title: 'Success',
        description: 'Chat archived successfully.',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('Error archiving chat:', error);
      toast({
        title: 'Error',
        description: 'Failed to archive chat.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Handle typing start
  const handleTypingStart = () => {
    if (!socket || !selectedChat) return;

    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Emit typing start event
    socket.emit('livechat:typing', {
      chatId: selectedChat,
      isTyping: true
    });

    // Set timeout to stop typing after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(handleTypingStop, 2000);
  };

  // Handle typing stop
  const handleTypingStop = () => {
    if (!socket || !selectedChat) return;

    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }

    // Emit typing stop event
    socket.emit('livechat:typing', {
      chatId: selectedChat,
      isTyping: false
    });
  };

  // Handle input change and typing events
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);

    // Trigger typing indicator if input is not empty
    if (e.target.value.trim()) {
      handleTypingStart();
    } else {
      handleTypingStop();
    }
  };

  // Handle tab change
  const handleTabChange = (index: number) => {
    // Load data for the selected tab if not already loaded
    if (index === 1 && closedChats.length === 0) {
      fetchClosedChats();
    } else if (index === 2 && archivedChats.length === 0) {
      fetchArchivedChats();
    }
  };

  // Load more closed chats
  const handleLoadMoreClosed = () => {
    if (closedPageNumber < closedPagination.pages) {
      fetchClosedChats(closedPageNumber + 1);
    }
  };

  // Load more archived chats
  const handleLoadMoreArchived = () => {
    if (archivedPageNumber < archivedPagination.pages) {
      fetchArchivedChats(archivedPageNumber + 1);
    }
  };

  // Format date
  const formatTime = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Format date with day
  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Scroll to bottom of chat
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Refresh data
  const handleRefresh = () => {
    fetchActiveChats();
    fetchChatStatistics();

    if (selectedChat) {
      handleSelectChat(selectedChat, activeChats.find(c => c.chatId === selectedChat) || closedChats.find(c => c.chatId === selectedChat) || archivedChats.find(c => c.chatId === selectedChat) as ChatSession);
    }
  };

  // Render chat item
  const renderChatItem = (chat: ChatSession, isActive: boolean = true) => (
    <ListItem
      key={chat.chatId}
      padding={3}
      borderBottom="1px solid"
      borderColor="gray.200"
      bg={selectedChat === chat.chatId ? 'gray.50' : 'white'}
      cursor="pointer"
      _hover={{ bg: 'gray.50' }}
      onClick={() => handleSelectChat(chat.chatId, chat)}
    >
      <Flex justifyContent="space-between" alignItems="flex-start">
        <Box>
          <Flex alignItems="center">
            <Heading size="sm">{chat.name}</Heading>
            {chat.unreadCount > 0 && isActive && (
              <Badge ml={2} colorScheme="red" borderRadius="full">
                {chat.unreadCount}
              </Badge>
            )}
          </Flex>
          <Text fontSize="sm" color="gray.600" mt={1}>
            {chat.email || 'No email provided'}
          </Text>
          {chat.subject && (
            <Text fontSize="sm" fontWeight="medium" mt={1}>
              {chat.subject}
            </Text>
          )}
          {chat.lastMessage && (
            <Text fontSize="sm" color="gray.600" mt={1} noOfLines={1}>
              {chat.lastMessage.content}
            </Text>
          )}
        </Box>
        <Box>
          <Text fontSize="xs" color="gray.500">
            {formatDate(chat.updatedAt || chat.createdAt)}
          </Text>
        </Box>
      </Flex>
    </ListItem>
  );

  return (
    <Box p={4}>
      <Flex justifyContent="space-between" alignItems="center" mb={6}>
        <Heading size="lg">{t('liveChat.title')}</Heading>
        <Button colorScheme="blue" size="sm" onClick={handleRefresh}>
          {t('common.refresh')}
        </Button>
      </Flex>

      {/* Statistics */}
      {chatStatistics && (
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={5} mb={6}>
          <Stat bg="white" p={4} shadow="md" borderRadius="md">
            <StatLabel color="gray.500">{t('liveChat.statistics.activeChats')}</StatLabel>
            <StatNumber>{chatStatistics.activeChats}</StatNumber>
            <StatHelpText>
              {chatStatistics.activeChatsWithUnread} {t('liveChat.statistics.withUnread')}
            </StatHelpText>
          </Stat>
          <Stat bg="white" p={4} shadow="md" borderRadius="md">
            <StatLabel color="gray.500">{t('liveChat.statistics.totalMessages')}</StatLabel>
            <StatNumber>{chatStatistics.totalMessages}</StatNumber>
          </Stat>
          <Stat bg="white" p={4} shadow="md" borderRadius="md">
            <StatLabel color="gray.500">{t('liveChat.statistics.totalChats')}</StatLabel>
            <StatNumber>{chatStatistics.totalChats}</StatNumber>
            <StatHelpText>
              {chatStatistics.closedChats} {t('liveChat.statistics.closedChats')}
            </StatHelpText>
          </Stat>
          <Stat bg="white" p={4} shadow="md" borderRadius="md">
            <StatLabel color="gray.500">{t('liveChat.statistics.avgResponseTime')}</StatLabel>
            <StatNumber>{chatStatistics.averageResponseTimeMinutes} min</StatNumber>
          </Stat>
        </SimpleGrid>
      )}

      {/* Chat interface */}
      <Flex direction={{ base: 'column', md: 'row' }} h={{ base: 'auto', md: '600px' }} gap={4}>
        {/* Left side - Chat list */}
        <Box
          width={{ base: '100%', md: '40%', lg: '30%' }}
          borderWidth="1px"
          borderRadius="md"
          overflow="hidden"
          bg="white"
        >
          <Tabs onChange={handleTabChange}>
            <TabList>
              <Tab fontWeight="medium">{t('liveChat.tabs.active')} ({activeChats.length})</Tab>
              <Tab fontWeight="medium">{t('liveChat.tabs.closed')}</Tab>
              <Tab fontWeight="medium">{t('liveChat.tabs.archived')}</Tab>
            </TabList>

            <TabPanels>
              {/* Active chats */}
              <TabPanel p={0}>
                <Box height={{ base: '300px', md: '520px' }} overflowY="auto">
                  {isLoading ? (
                    <Flex justify="center" align="center" height="100%">
                      <Spinner />
                    </Flex>
                  ) : activeChats.length === 0 ? (
                    <Flex justify="center" align="center" height="100%" color="gray.500">
                      {t('liveChat.noActiveChats')}
                    </Flex>
                  ) : (
                    <List>
                      {activeChats.map(chat => renderChatItem(chat))}
                    </List>
                  )}
                </Box>
              </TabPanel>

              {/* Closed chats */}
              <TabPanel p={0}>
                <Box height={{ base: '300px', md: '520px' }} overflowY="auto">
                  {isLoading ? (
                    <Flex justify="center" align="center" height="100%">
                      <Spinner />
                    </Flex>
                  ) : closedChats.length === 0 ? (
                    <Flex justify="center" align="center" height="100%" color="gray.500">
                      {t('liveChat.noClosedChats')}
                    </Flex>
                  ) : (
                    <>
                      <List>
                        {closedChats.map(chat => renderChatItem(chat, false))}
                      </List>

                      {closedPageNumber < closedPagination.pages && (
                        <Flex justify="center" py={3}>
                          <Button
                            size="sm"
                            onClick={handleLoadMoreClosed}
                            isLoading={isLoadingMore}
                          >
                            {t('common.loadMore')}
                          </Button>
                        </Flex>
                      )}
                    </>
                  )}
                </Box>
              </TabPanel>

              {/* Archived chats */}
              <TabPanel p={0}>
                <Box height={{ base: '300px', md: '520px' }} overflowY="auto">
                  {isLoading ? (
                    <Flex justify="center" align="center" height="100%">
                      <Spinner />
                    </Flex>
                  ) : archivedChats.length === 0 ? (
                    <Flex justify="center" align="center" height="100%" color="gray.500">
                      {t('liveChat.noArchivedChats')}
                    </Flex>
                  ) : (
                    <>
                      <List>
                        {archivedChats.map(chat => renderChatItem(chat, false))}
                      </List>

                      {archivedPageNumber < archivedPagination.pages && (
                        <Flex justify="center" py={3}>
                          <Button
                            size="sm"
                            onClick={handleLoadMoreArchived}
                            isLoading={isLoadingMore}
                          >
                            {t('common.loadMore')}
                          </Button>
                        </Flex>
                      )}
                    </>
                  )}
                </Box>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </Box>

        {/* Right side - Chat messages */}
        <Box
          flex="1"
          borderWidth="1px"
          borderRadius="md"
          overflow="hidden"
          bg="white"
          display="flex"
          flexDirection="column"
        >
          {selectedChat ? (
            <>
              {/* Chat header */}
              <Flex
                justify="space-between"
                p={4}
                borderBottomWidth="1px"
                bgColor="gray.50"
                align="center"
              >
                <Box>
                  <Heading size="md">
                    {chatInfo?.name}
                    <Text as="span" ml={2} fontSize="sm" fontWeight="normal" color="gray.500">
                      {chatInfo?.email}
                    </Text>
                  </Heading>
                  {chatInfo?.subject && (
                    <Text fontSize="sm" color="gray.600">
                      {chatInfo.subject}
                    </Text>
                  )}
                </Box>
                <Button
                  colorScheme="yellow"
                  size="sm"
                  onClick={() => handleArchiveChat(selectedChat)}
                >
                  {t('liveChat.archiveChat')}
                </Button>
              </Flex>

              {/* Chat messages */}
              <Box
                flex="1"
                p={4}
                overflowY="auto"
                bg="gray.50"
                display="flex"
                flexDirection="column"
              >
                {isLoadingHistory ? (
                  <Flex justify="center" align="center" height="100%">
                    <Spinner />
                  </Flex>
                ) : chatHistory.length === 0 ? (
                  <Flex justify="center" align="center" height="100%" color="gray.500">
                    {t('liveChat.noMessages')}
                  </Flex>
                ) : (
                  <VStack align="stretch" spacing={4} width="100%">
                    {chatHistory.map((message, index) => (
                      <Flex
                        key={index}
                        direction="column"
                        alignSelf={message.senderType === 'admin' ? 'flex-end' : 'flex-start'}
                        maxWidth="70%"
                      >
                        <Box
                          p={3}
                          borderRadius="lg"
                          bg={message.senderType === 'admin' ? 'blue.500' : 'gray.200'}
                          color={message.senderType === 'admin' ? 'white' : 'gray.800'}
                        >
                          <Text>{message.content}</Text>
                        </Box>
                        <Flex
                          fontSize="xs"
                          color="gray.500"
                          mt={1}
                          justify={message.senderType === 'admin' ? 'flex-end' : 'flex-start'}
                        >
                          {formatTime(message.timestamp)}
                        </Flex>
                      </Flex>
                    ))}

                    {/* Typing indicator */}
                    {isTyping && (
                      <Flex
                        direction="column"
                        alignSelf="flex-start"
                      >
                        <Box
                          p={3}
                          borderRadius="lg"
                          bg="gray.100"
                          color="gray.500"
                        >
                          <Flex gap={1}>
                            <Box h={2} w={2} borderRadius="full" bg="gray.400" animation="pulse 1s infinite" />
                            <Box h={2} w={2} borderRadius="full" bg="gray.400" animation="pulse 1s infinite 0.3s" />
                            <Box h={2} w={2} borderRadius="full" bg="gray.400" animation="pulse 1s infinite 0.6s" />
                          </Flex>
                        </Box>
                      </Flex>
                    )}

                    {/* Auto-scroll helper */}
                    <div ref={messagesEndRef} />
                  </VStack>
                )}
              </Box>

              {/* Chat input */}
              <Box p={4} borderTopWidth="1px">
                <Flex>
                  <Input
                    placeholder={t('liveChat.typeMessage')}
                    value={inputValue}
                    onChange={handleInputChange}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        handleSendMessage();
                      }
                    }}
                    mr={2}
                  />
                  <Button
                    colorScheme="blue"
                    onClick={handleSendMessage}
                    isDisabled={!inputValue.trim()}
                  >
                    {t('liveChat.send')}
                  </Button>
                </Flex>
              </Box>
            </>
          ) : (
            <Flex justify="center" align="center" height="100%" color="gray.500">
              {t('liveChat.selectChat')}
            </Flex>
          )}
        </Box>
      </Flex>
    </Box>
  );
};

export default ManageLiveChat;