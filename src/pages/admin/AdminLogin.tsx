import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { loginAdmin } from "../../adminApi";
import { useAuth } from "../../context/AuthContext";
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  Container,
  Heading,
  Text,
  useToast,
  Alert,
  AlertIcon,
  Flex,
  useColorModeValue,
  IconButton,
  InputGroup,
  InputRightElement,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { FiEyeOff, FiEye, FiLogIn } from "react-icons/fi";

const AdminLogin: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { login, isAuthenticated, isAdmin } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation(["adminLogin", "common"]);
  const toast = useToast();

  const bgColor = useColorModeValue("gray.50", "gray.900");
  const cardBgColor = useColorModeValue("white", "gray.800");
  const textColor = useColorModeValue("gray.600", "gray.200");
  const brandColor = useColorModeValue("primary.500", "primary.300");
  const buttonBgColor = useColorModeValue("blue.500", "blue.400");
  const buttonHoverBgColor = useColorModeValue("blue.600", "blue.500");

  useEffect(() => {
    document.title = t("adminLogin:pageTitle");
    if (isAuthenticated && isAdmin) {
      navigate("/admin/dashboard");
    }
  }, [isAuthenticated, isAdmin, navigate, t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setIsLoading(true);

    try {
      const response = await loginAdmin({ email, password });
      if (response.token) {
        login(response.user, response.token, true); // Pass true for isAdmin
        toast({
          title: t("adminLogin:loginSuccess.title"),
          description: t("adminLogin:loginSuccess.description"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (err: any) {
      console.error("Login error:", err);
      // Use the title and description properties of the loginError object
      setError(t("adminLogin:loginError.description"));
      toast({
        title: t("adminLogin:loginError.title"),
        description: t("adminLogin:loginError.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Flex
      minHeight="100vh"
      width="full"
      align="center"
      justifyContent="center"
      bg={bgColor}
    >
      <Container
        maxW="lg"
        py={{ base: "16", md: "32" }}
        px={{ base: "0", sm: "8" }}
      >
        <Box
          py="8"
          px={{ base: "4", md: "10" }}
          shadow="base"
          rounded={{ sm: "lg" }}
          bg={cardBgColor}
        >
          <VStack spacing="8">
            <VStack spacing="6" align="stretch">
              <VStack spacing="1">
                <Heading
                  fontSize="3xl"
                  fontWeight="extrabold"
                  color={brandColor}
                >
                  {t("adminLogin:title")}
                </Heading>
                <Text fontSize="md" color={textColor}>
                  {t("adminLogin:subtitle")}
                </Text>
              </VStack>
              {error && (
                <Alert status="error" rounded="md">
                  <AlertIcon />
                  {error}
                </Alert>
              )}
              <form onSubmit={handleSubmit} style={{ width: "100%" }}>
                <VStack spacing="5">
                  <FormControl id="email">
                    <FormLabel>{t("adminLogin:form.email")}</FormLabel>
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder={t("adminLogin:form.emailPlaceholder")}
                      required
                      isDisabled={isLoading}
                    />
                  </FormControl>
                  <FormControl id="password">
                    <FormLabel>{t("adminLogin:form.password")}</FormLabel>
                    <InputGroup>
                      <Input
                        type={showPassword ? "text" : "password"}
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder={t("adminLogin:form.passwordPlaceholder")}
                        required
                        isDisabled={isLoading}
                      />

                      <InputRightElement>
                        <IconButton
                          aria-label={
                            showPassword
                              ? t("adminLogin:form.hidePassword")
                              : t("adminLogin:form.showPassword")
                          }
                          icon={showPassword ? <FiEyeOff /> : <FiEye />}
                          onClick={() => setShowPassword(!showPassword)}
                          variant="ghost"
                          isDisabled={isLoading}
                        />
                      </InputRightElement>
                    </InputGroup>
                  </FormControl>
                  <Button
                    type="submit"
                    colorScheme="primary"
                    size="lg"
                    fontSize="md"
                    isLoading={isLoading}
                    loadingText={t("adminLogin:form.loggingIn")}
                    width="full"
                    mt={4}
                    py={6}
                    bg={buttonBgColor}
                    _hover={{ bg: buttonHoverBgColor }}
                    boxShadow="md"
                    leftIcon={<FiLogIn />}
                  >
                    {t("adminLogin:form.loginButton")}
                  </Button>
                </VStack>
              </form>
            </VStack>
          </VStack>
        </Box>
      </Container>
    </Flex>
  );
};

export default AdminLogin;
