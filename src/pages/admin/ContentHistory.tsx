import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Input, Select, Table } from "@chakra-ui/react";

interface ContentItem {
  id: string;
  title: string;
  type: "product" | "service" | "advertisement";
  status: "approved" | "rejected";
  reason?: string;
  submittedBy: string;
  reviewedAt: string;
}

export default function ContentHistory() {
  const { t } = useTranslation("admin");
  const [filter, setFilter] = useState({
    status: "all",
    type: "all",
    search: "",
  });

  // Mock data - replace with actual API call
  const contentHistory: ContentItem[] = [];

  const filteredContent = contentHistory.filter((item) => {
    if (filter.status !== "all" && item.status !== filter.status) return false;
    if (filter.type !== "all" && item.type !== filter.type) return false;
    if (
      filter.search &&
      !item.title.toLowerCase().includes(filter.search.toLowerCase())
    )
      return false;
    return true;
  });

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">{t("contentHistory.title")}</h1>

      <div className="flex gap-4 mb-6">
        <Input
          placeholder={t("contentHistory.searchPlaceholder")}
          value={filter.search}
          onChange={(e: any) =>
            setFilter({ ...filter, search: e.target.value })
          }
          className="max-w-xs"
        />

        <Select
          value={filter.status}
          onChange={(e: any) =>
            setFilter({ ...filter, status: e.target.value })
          }
        >
          <option value="all">{t("contentHistory.allStatuses")}</option>
          <option value="approved">{t("contentHistory.approved")}</option>
          <option value="rejected">{t("contentHistory.rejected")}</option>
        </Select>

        <Select
          value={filter.type}
          onChange={(e: any) => setFilter({ ...filter, type: e.target.value })}
        >
          <option value="all">{t("contentHistory.allTypes")}</option>
          <option value="product">{t("contentHistory.product")}</option>
          <option value="service">{t("contentHistory.service")}</option>
          <option value="advertisement">
            {t("contentHistory.advertisement")}
          </option>
        </Select>
      </div>

      <Table>
        <thead>
          <tr>
            <th>{t("contentHistory.table.title")}</th>
            <th>{t("contentHistory.table.type")}</th>
            <th>{t("contentHistory.table.status")}</th>
            <th>{t("contentHistory.table.reason")}</th>
            <th>{t("contentHistory.table.submittedBy")}</th>
            <th>{t("contentHistory.table.reviewedAt")}</th>
          </tr>
        </thead>
        <tbody>
          {filteredContent.map((item) => (
            <tr key={item.id}>
              <td>{item.title}</td>
              <td>{t(`contentHistory.${item.type}`)}</td>
              <td>
                <span
                  className={`px-2 py-1 rounded ${item.status === "approved" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}
                >
                  {t(`contentHistory.${item.status}`)}
                </span>
              </td>
              <td>{item.reason || "-"}</td>
              <td>{item.submittedBy}</td>
              <td>{new Date(item.reviewedAt).toLocaleDateString()}</td>
            </tr>
          ))}
        </tbody>
      </Table>
    </div>
  );
}
