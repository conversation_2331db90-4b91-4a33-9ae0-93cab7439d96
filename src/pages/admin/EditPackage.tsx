import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useParams, useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Heading,
  Input,
  Select,
  useToast,
  Checkbox,
  useColorModeValue,
  Flex,
  Spacer,
  Grid,
  GridItem,
} from "@chakra-ui/react";
// Import getPackage from adminApi instead of getAdminPackageById from packageApi
import { getPackage, updatePackage } from "@/adminApi";

interface IPackage {
  _id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  type: "standard" | "addon";
  viewRequestLimit: number;
  createRequestLimit: number;
  emailNotification: boolean;
  smsNotification: boolean;
  languageIntroRights: number;
  messagingAllowed: boolean;
  homepageAdDuration: number;
  yearEndSectorReport: boolean;
  isActive: boolean;
  features: string[];
  order: number;
  duration: number;
}

const EditPackage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation("admin");
  const bgColor = useColorModeValue("white", "gray.800");

  const [packageData, setPackageData] = useState<IPackage>({
    _id: "",
    name: "",
    nameEn: "",
    description: "",
    descriptionEn: "",
    price: 0,
    type: "standard",
    viewRequestLimit: 0,
    createRequestLimit: 0,
    emailNotification: false,
    smsNotification: false,
    languageIntroRights: 0,
    messagingAllowed: false,
    homepageAdDuration: 0,
    yearEndSectorReport: false,
    isActive: true,
    features: [],
    order: 0,
    duration: 1,
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchPackageData();
  }, [id]);

  const fetchPackageData = async () => {
    if (!id) return;

    try {
      // Use getPackage from adminApi instead of getAdminPackageById
      const data = await getPackage(id);
      setPackageData(data as any);
    } catch (error: any) {
      toast({
        title: t("common:fetchFailed"),
        description: t("packages.fetchFailed"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      
      // If package not found (404 error), navigate back to packages list
      if (error.response && error.response.status === 404) {
        navigate("/admin/packages");
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;

    // Convert duration select value to number
    if (name === "duration") {
      const durationMap = {
        monthly: 1,
        quarterly: 3,
        semiannual: 6,
        annual: 12,
      };
      setPackageData((prev) => ({
        ...prev,
        [name]: durationMap[value as keyof typeof durationMap],
      }));
    } else {
      setPackageData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setPackageData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!id) return;

    try {
      await updatePackage(id, packageData);
      toast({
        title: t("common:actionSuccess"),
        description: t("packages.form.updateSuccess"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      navigate("/admin/packages");
    } catch (error) {
      toast({
        title: t("common:actionFailed"),
        description: t("packages.form.updateFailed"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  if (isLoading) {
    return <Container>{t("common:loading")}</Container>;
  }

  return (
    <Box maxW="full" py={8} px={4}>
      <Heading as="h1" size="xl" mb={6}>
        {t("packages.editPackage")}
      </Heading>

      <Box bg={bgColor} p={6} borderRadius="lg" boxShadow="md">
        <Box as="form" onSubmit={handleSubmit}>
          <Grid templateColumns="repeat(2, 1fr)" gap={4}>
            {/* Name Fields */}
            <FormControl isRequired>
              <FormLabel>{t("packages.form.name")} (TR)</FormLabel>
              <Input
                name="name"
                value={packageData.name}
                onChange={handleInputChange}
              />
            </FormControl>
            <FormControl isRequired>
              <FormLabel>{t("packages.form.name")} (EN)</FormLabel>
              <Input
                name="nameEn"
                value={packageData.nameEn}
                onChange={handleInputChange}
              />
            </FormControl>

            {/* Description Fields */}
            <FormControl isRequired>
              <FormLabel>{t("packages.form.description")} (TR)</FormLabel>
              <Input
                name="description"
                value={packageData.description}
                onChange={handleInputChange}
              />
            </FormControl>
            <FormControl isRequired>
              <FormLabel>{t("packages.form.description")} (EN)</FormLabel>
              <Input
                name="descriptionEn"
                value={packageData.descriptionEn}
                onChange={handleInputChange}
              />
            </FormControl>

            {/* Price and Type */}
            <FormControl isRequired>
              <FormLabel>{t("packages.form.price")}</FormLabel>
              <Input
                name="price"
                type="number"
                value={packageData.price}
                onChange={handleInputChange}
              />
            </FormControl>
            <FormControl isRequired>
              <FormLabel>{t("packages.form.type")}</FormLabel>
              <Select
                name="type"
                value={packageData.type}
                onChange={handleInputChange}
              >
                <option value="standard">{t("packages.type.standard")}</option>
                <option value="addon">{t("packages.type.addon")}</option>
              </Select>
            </FormControl>

            {/* Duration and Order */}
            <FormControl isRequired>
              <FormLabel>{t("packages.form.duration")}</FormLabel>
              <Select
                name="duration"
                value={
                  packageData.duration === 12
                    ? "annual"
                    : packageData.duration === 6
                      ? "semiannual"
                      : packageData.duration === 3
                        ? "quarterly"
                        : "monthly"
                }
                onChange={handleInputChange}
              >
                <option value="monthly">
                  {t("packages.duration.monthly")}
                </option>
                <option value="quarterly">
                  {t("packages.duration.quarterly")}
                </option>
                <option value="semiannual">
                  {t("packages.duration.semiannual")}
                </option>
                <option value="annual">{t("packages.duration.annual")}</option>
              </Select>
            </FormControl>
            <FormControl isRequired>
              <FormLabel>{t("packages.form.order")}</FormLabel>
              <Input
                name="order"
                type="number"
                value={packageData.order}
                onChange={handleInputChange}
              />
            </FormControl>

            {/* Request Limits */}
            <FormControl isRequired>
              <FormLabel>{t("packages.form.viewRequests")}</FormLabel>
              <Input
                name="viewRequestLimit"
                type="number"
                value={packageData.viewRequestLimit}
                onChange={handleInputChange}
              />
            </FormControl>
            <FormControl isRequired>
              <FormLabel>{t("packages.form.createRequests")}</FormLabel>
              <Input
                name="createRequestLimit"
                type="number"
                value={packageData.createRequestLimit}
                onChange={handleInputChange}
              />
            </FormControl>

            {/* Additional Features */}
            <FormControl>
              <FormLabel>{t("packages.form.languageIntroRights")}</FormLabel>
              <Input
                name="languageIntroRights"
                type="number"
                value={packageData.languageIntroRights}
                onChange={handleInputChange}
              />
            </FormControl>
            <FormControl>
              <FormLabel>{t("packages.form.homepageAdDuration")}</FormLabel>
              <Input
                name="homepageAdDuration"
                type="number"
                value={packageData.homepageAdDuration}
                onChange={handleInputChange}
              />
            </FormControl>

            {/* Checkboxes */}
            <GridItem colSpan={1}>
              <FormControl>
                <Checkbox
                  name="emailNotification"
                  isChecked={packageData.emailNotification}
                  onChange={handleCheckboxChange}
                >
                  {t("packages.form.emailNotification")}
                </Checkbox>
              </FormControl>
              <FormControl mt={2}>
                <Checkbox
                  name="smsNotification"
                  isChecked={packageData.smsNotification}
                  onChange={handleCheckboxChange}
                >
                  {t("packages.form.smsNotification")}
                </Checkbox>
              </FormControl>
            </GridItem>
            <GridItem colSpan={1}>
              <FormControl>
                <Checkbox
                  name="messagingAllowed"
                  isChecked={packageData.messagingAllowed}
                  onChange={handleCheckboxChange}
                >
                  {t("packages.form.messagingAllowed")}
                </Checkbox>
              </FormControl>
              <FormControl mt={2}>
                <Checkbox
                  name="yearEndSectorReport"
                  isChecked={packageData.yearEndSectorReport}
                  onChange={handleCheckboxChange}
                >
                  {t("packages.form.yearEndSectorReport")}
                </Checkbox>
              </FormControl>
            </GridItem>
          </Grid>
          <Flex gap={4} mt={6}>
            <Button onClick={() => navigate("/admin/packages")}>
              {t("common:cancel")}
            </Button>
            <Spacer />
            <Button type="submit" colorScheme="blue" size="lg">
              {t("packages.form.update")}
            </Button>
          </Flex>
        </Box>
      </Box>
    </Box>
  );
};

export default EditPackage;
