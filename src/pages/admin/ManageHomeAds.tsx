import React from "react";
import { Box, Heading, useColorModeValue } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import HomepageAdList from "@/components/admin/homepage-ads/HomepageAdList";

const ManageHomeAds: React.FC = () => {
  const { t } = useTranslation("admin");
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Box
      bg={bgColor}
      p={6}
      borderRadius="lg"
      borderWidth={1}
      borderColor={borderColor}
    >
      <Heading size="lg" mb={6}>
        {t("header.homepageAds")}
      </Heading>
      <HomepageAdList />
    </Box>
  );
};

export default ManageHomeAds;
