import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  useToast,
  Box,
  Heading,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Badge,
  HStack,
  Tooltip,
  IconButton,
  Button,
  useColorModeValue,
  Container,
  Flex,
  Spacer,
  Text,
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
  VStack,
  Stat,
  StatLabel,
  StatNumber,
  StatGroup,
  Card,
  CardBody,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  SimpleGrid,
  useBreakpointValue,
} from "@chakra-ui/react";
import { FiTrash2, FiEdit, FiPlus, FiSearch, FiPackage, FiDollarSign, FiUsers, FiEye } from "react-icons/fi";
import { Link, useNavigate } from "react-router-dom";
import {
  getAdminDesignPackages,
  deleteAdminDesignPackage,
  IDesignPackageAdmin,
} from "@/api/adminApi"; // Adjusted import path

const ManageDesignPackages: React.FC = () => {
  const [designPackages, setDesignPackages] = useState<IDesignPackageAdmin[]>([]);
  const [filteredPackages, setFilteredPackages] = useState<IDesignPackageAdmin[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const cardBg = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [packageToDelete, setPackageToDelete] = useState<string | null>(null);
  const cancelRef = useRef<any>(null);
  const navigate = useNavigate();
  const isMobile = useBreakpointValue({ base: true, md: false });

  const toast = useToast();
  const { t } = useTranslation(["admin", "common"]); // Added "common" for generic terms

  useEffect(() => {
    fetchDesignPackages();
  }, []);

  // Filter packages based on search term, status, and category
  useEffect(() => {
    let filtered = designPackages;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(pkg =>
        pkg.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pkg.nameEn.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pkg.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(pkg =>
        statusFilter === "active" ? pkg.isActive : !pkg.isActive
      );
    }

    // Category filter - removed since category field doesn't exist in the model

    setFilteredPackages(filtered);
  }, [designPackages, searchTerm, statusFilter]);

  const fetchDesignPackages = async () => {
    setIsLoading(true);
    try {
      const fetchedPackages: any = await getAdminDesignPackages();
      setDesignPackages(fetchedPackages?.packages ?? []);
    } catch (error: any) {
      console.error("Failed to fetch design packages:", error);
      toast({
        title: t("common:fetchFailed"),
        description: t("designPackages.fetchFailedMessage"), // Specific message for design packages
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Statistics calculations
  const totalPackages = designPackages?.length;
  const activePackages = designPackages?.filter(pkg => pkg.isActive).length;
  // const inactivePackages = totalPackages - activePackages; // Removed unused variable
  const averagePrice = totalPackages > 0
    ? (designPackages?.reduce((sum, pkg) => sum + pkg.price, 0) / totalPackages).toFixed(2)
    : "0";
  // Categories removed since the field doesn't exist in the model
  const uniqueCategories = 0;

  const handleDeleteClick = (packageId: string) => {
    setPackageToDelete(packageId);
    setIsDeleteAlertOpen(true);
  };

  const confirmDelete = async () => {
    if (packageToDelete) {
      try {
        await deleteAdminDesignPackage(packageToDelete);
        toast({
          title: t("common:actionSuccess"),
          description: t("designPackages.deleteSuccessMessage"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        fetchDesignPackages(); // Refresh list
      } catch (error: any) {
        toast({
          title: t("common:actionFailed"),
          description: t("designPackages.deleteFailedMessage"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    }
    setIsDeleteAlertOpen(false);
    setPackageToDelete(null);
  };

  if (isLoading) {
    return (
      <Container centerContent py={10}>
        <VStack spacing={4}>
          <Text fontSize="lg" color="gray.500">{t("common:loading")}</Text>
        </VStack>
      </Container>
    );
  }

  return (
    <Box maxW="full" py={6} px={4}>
      {/* Header Section */}
      <VStack spacing={6} align="stretch">
        <Flex direction={{ base: "column", md: "row" }} gap={4} alignItems={{ md: "center" }}>
          <Box>
            <Heading as="h1" size="xl" color="teal.600" mb={2}>
              {t("designPackages.manageTitle")}
            </Heading>
            <Text color="gray.600" fontSize="md">
              {t("designPackages.manageDescription")}
            </Text>
          </Box>
          <Spacer />
          <Button
            as={Link}
            to="/admin/design-packages/new"
            colorScheme="teal"
            leftIcon={<FiPlus />}
            size="lg"
            borderRadius="xl"
            boxShadow="lg"
            _hover={{ transform: "translateY(-2px)", boxShadow: "xl" }}
            transition="all 0.2s"
          >
            {t("designPackages.addNewButton")}
          </Button>
        </Flex>

        {/* Statistics Cards */}
        <SimpleGrid columns={{ base: 2, md: 4 }} spacing={4}>
          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden">
            <CardBody>
              <Stat>
                <StatLabel color="gray.500" fontSize="sm" fontWeight="medium">
                  <HStack>
                    <FiPackage />
                    <Text>{t("designPackages.totalPackages")}</Text>
                  </HStack>
                </StatLabel>
                <StatNumber fontSize="2xl" fontWeight="bold" color="teal.500">
                  {totalPackages}
                </StatNumber>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden">
            <CardBody>
              <Stat>
                <StatLabel color="gray.500" fontSize="sm" fontWeight="medium">
                  <HStack>
                    <FiEye />
                    <Text>{t("designPackages.activePackages")}</Text>
                  </HStack>
                </StatLabel>
                <StatNumber fontSize="2xl" fontWeight="bold" color="green.500">
                  {activePackages}
                </StatNumber>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden">
            <CardBody>
              <Stat>
                <StatLabel color="gray.500" fontSize="sm" fontWeight="medium">
                  <HStack>
                    <FiDollarSign />
                    <Text>{t("designPackages.averagePrice")}</Text>
                  </HStack>
                </StatLabel>
                <StatNumber fontSize="2xl" fontWeight="bold" color="blue.500">
                  ${averagePrice}
                </StatNumber>
              </Stat>
            </CardBody>
          </Card>

          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden">
            <CardBody>
              <Stat>
                <StatLabel color="gray.500" fontSize="sm" fontWeight="medium">
                  <HStack>
                    <FiUsers />
                    <Text>{t("designPackages.categories")}</Text>
                  </HStack>
                </StatLabel>
                <StatNumber fontSize="2xl" fontWeight="bold" color="purple.500">
                  {uniqueCategories}
                </StatNumber>
              </Stat>
            </CardBody>
          </Card>
        </SimpleGrid>

        {/* Filter and Search Section */}
        <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden">
          <CardBody>
            <VStack spacing={4}>
              <Flex direction={{ base: "column", md: "row" }} gap={4} w="full">
                <InputGroup flex="2">
                  <InputLeftElement pointerEvents="none">
                    <FiSearch color="gray.300" />
                  </InputLeftElement>
                  <Input
                    placeholder={t("designPackages.searchPlaceholder")}
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    borderRadius="lg"
                    bg={useColorModeValue("gray.50", "gray.700")}
                    _focus={{ bg: useColorModeValue("white", "gray.600"), borderColor: "teal.500" }}
                  />
                </InputGroup>

                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  flex="1"
                  borderRadius="lg"
                  bg={useColorModeValue("gray.50", "gray.700")}
                  _focus={{ bg: useColorModeValue("white", "gray.600"), borderColor: "teal.500" }}
                >
                  <option value="all">{t("designPackages.statusFilter.all")}</option>
                  <option value="active">{t("designPackages.statusFilter.active")}</option>
                  <option value="inactive">{t("designPackages.statusFilter.inactive")}</option>
                </Select>

              </Flex>
            </VStack>
          </CardBody>
        </Card>

        {/* Packages Table */}
        <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden" boxShadow="lg">
          <CardBody p={0}>
            {filteredPackages.length === 0 ? (
              <VStack py={10} spacing={4}>
                <FiPackage size={48} color="gray.400" />
                <Text textAlign="center" fontSize="lg" color="gray.500">
                  {searchTerm || statusFilter !== "all"
                    ? t("designPackages.noPackagesFound")
                    : t("designPackages.noPackagesFound")
                  }
                </Text>
                {(searchTerm || statusFilter !== "all") && (
                  <Button
                    variant="outline"
                    colorScheme="teal"
                    size="sm"
                    onClick={() => {
                      setSearchTerm("");
                      setStatusFilter("all");
                      // Reset removed
                    }}
                  >
                    Clear Filters
                  </Button>
                )}
              </VStack>
            ) : (
              <Box overflowX="auto">
                <Table variant="simple">
                  <Thead bg={useColorModeValue("gray.50", "gray.700")}>
                    <Tr>
                      <Th borderColor={borderColor} color="gray.600" fontWeight="semibold" letterSpacing="wide">
                        {t("designPackages.table.name")}
                      </Th>
                      <Th borderColor={borderColor} color="gray.600" fontWeight="semibold" letterSpacing="wide">
                        {t("designPackages.table.iconTitle")}
                      </Th>
                      <Th isNumeric borderColor={borderColor} color="gray.600" fontWeight="semibold" letterSpacing="wide">
                        {t("designPackages.table.price")}
                      </Th>
                      <Th borderColor={borderColor} color="gray.600" fontWeight="semibold" letterSpacing="wide">
                        {t("designPackages.table.status")}
                      </Th>
                      <Th borderColor={borderColor} color="gray.600" fontWeight="semibold" letterSpacing="wide">
                        {t("designPackages.table.order")}
                      </Th>
                      <Th borderColor={borderColor} color="gray.600" fontWeight="semibold" letterSpacing="wide">
                        {t("designPackages.table.actions")}
                      </Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {filteredPackages.map((pkg) => (
                      <Tr
                        key={pkg._id}
                        _hover={{ bg: useColorModeValue("gray.50", "gray.700") }}
                        transition="background-color 0.2s"
                        borderColor={borderColor}
                      >
                        <Td borderColor={borderColor} py={4}>
                          <VStack align="start" spacing={1}>
                            <Text fontWeight="medium" color={useColorModeValue("gray.900", "white")}>
                              {pkg.nameEn || pkg.name}
                            </Text>
                            {pkg.nameEn && pkg.name && pkg.nameEn !== pkg.name && (
                              <Text fontSize="sm" color="gray.500">
                                {pkg.name}
                              </Text>
                            )}
                          </VStack>
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme="purple"
                            variant="subtle"
                            borderRadius="full"
                            px={3}
                            py={1}
                            fontSize="xs"
                          >
                            {pkg.icon}
                          </Badge>
                        </Td>
                        <Td isNumeric borderColor={borderColor}>
                          <Text fontWeight="semibold" color="green.600">
                            {pkg.price} {pkg.currency}
                          </Text>
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge
                            colorScheme={pkg.isActive ? "green" : "red"}
                            variant="subtle"
                            borderRadius="full"
                            px={3}
                            py={1}
                            fontSize="xs"
                          >
                            {pkg.isActive ? t("designPackages.statusFilter.active") : t("designPackages.statusFilter.inactive")}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <Badge variant="outline" borderRadius="full" px={2} py={1}>
                            {pkg.order}
                          </Badge>
                        </Td>
                        <Td borderColor={borderColor}>
                          <HStack spacing={2}>
                            {/*        <Tooltip label={t("common:edit")}>
                              <IconButton
                                icon={<FiEdit />}
                                aria-label={t("common:edit")}
                                colorScheme="blue"
                                variant="ghost"
                                size="sm"
                                borderRadius="lg"
                                _hover={{ bg: "blue.100" }}
                                onClick={() => navigate(`/admin/design-packages/edit/${pkg._id}`)}
                              />
                            </Tooltip>
                            <Tooltip label="View Orders">
                              <IconButton
                                icon={<FiEye />}
                                aria-label="View Orders"
                                colorScheme="teal"
                                variant="ghost"
                                size="sm"
                                borderRadius="lg"
                                _hover={{ bg: "teal.100" }}
                                onClick={() => navigate(`/admin/design-packages/${pkg._id}/orders`)}
                              />
                            </Tooltip>
                            */}
                            <Tooltip label={t("common:delete")}>
                              <IconButton
                                icon={<FiTrash2 />}
                                aria-label={t("common:delete")}
                                colorScheme="red"
                                variant="ghost"
                                size="sm"
                                borderRadius="lg"
                                _hover={{ bg: "red.100" }}
                                onClick={() => handleDeleteClick(pkg._id)}
                              />
                            </Tooltip>
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              </Box>
            )}
          </CardBody>
        </Card>
      </VStack>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteAlertOpen}
        leastDestructiveRef={cancelRef}
        onClose={() => setIsDeleteAlertOpen(false)}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {t("common:deleteConfirmationTitle")}
            </AlertDialogHeader>
            <AlertDialogBody>
              {t("common:deleteConfirmationMessage")}
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={() => setIsDeleteAlertOpen(false)}>
                {t("common:cancel")}
              </Button>
              <Button colorScheme="red" onClick={confirmDelete} ml={3}>
                {t("common:delete")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default ManageDesignPackages;
