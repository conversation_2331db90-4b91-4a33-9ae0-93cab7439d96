import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head<PERSON>,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  VStack,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import RepresentativeList from "@/components/admin/representatives/RepresentativeList";
import RepresentativeForm from "@/components/admin/representatives/RepresentativeForm";

const ManageRepresentatives: React.FC = () => {
  const { t } = useTranslation("admin");
  const { isOpen, onOpen, onClose } = useDisclosure();

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Heading size="lg">{t("representatives.title")}</Heading>
          <Button colorScheme="blue" onClick={onOpen}>
            {t("representatives.add_new")}
          </Button>
        </Box>

        <RepresentativeList />

        <Modal isOpen={isOpen} onClose={onClose} size="4xl">
          <ModalOverlay />
          <ModalContent>
            <ModalHeader>{t("representatives.createRepresentative")}</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <RepresentativeForm
                onSuccess={() => {
                  onClose();
                }}
              />
            </ModalBody>
          </ModalContent>
        </Modal>
      </VStack>
    </Container>
  );
};

export default ManageRepresentatives;
