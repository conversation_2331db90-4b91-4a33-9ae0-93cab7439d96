import React, { useState, useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  useToast,
  Box,
  Heading,
  Tabs,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  Table,
  Thead,
  Tr,
  Th,
  Tbody,
  Td,
  Badge,
  HStack,
  Tooltip,
  IconButton,
  VStack,
  FormControl,
  FormLabel,
  Input,
  Select,
  Button,
  useColorModeValue,
  Container,
  Flex,
  Spacer,
  Text,
  Checkbox,
  AlertDialog,
  AlertDialogBody,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogOverlay,
} from "@chakra-ui/react";
import { FiX, FiCheck, FiTrash2, FiEdit } from "react-icons/fi";
import { Link } from "react-router-dom";
import {
  createPackage,
  deletePackage,
  updatePackage,
  getAdminPackages,
} from "@/adminApi";

interface IPackage {
  _id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  type: "standard" | "addon";
  viewRequestLimit: number;
  createRequestLimit: number;
  emailNotification: boolean;
  smsNotification: boolean;
  languageIntroRights: number;
  messagingAllowed: boolean;
  homepageAdDuration: number;
  yearEndSectorReport: boolean;
  isActive: boolean;
  features: string[];
  order: number;
  duration: number; // Changed from string enum to number
}

const ManagePackages: React.FC = () => {
  const [packages, setPackages] = useState<IPackage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const bgColor = useColorModeValue("white", "gray.800");
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [packageToDelete, setPackageToDelete] = useState<string | null>(null);
  const cancelRef = useRef<any>(null);

  const [newPackage, setNewPackage] = useState<Partial<IPackage>>({
    name: "",
    nameEn: "",
    description: "",
    descriptionEn: "",
    price: 0,
    type: "standard",
    viewRequestLimit: 0,
    createRequestLimit: 0,
    emailNotification: false,
    smsNotification: false,
    languageIntroRights: 0,
    messagingAllowed: false,
    homepageAdDuration: 0,
    yearEndSectorReport: false,
    isActive: true,
    features: [],
    order: 0,
    duration: 1, // Default to 1 month
  });

  const toast = useToast();
  const { t } = useTranslation("admin");

  useEffect(() => {
    fetchPackages();
  }, []);

  const fetchPackages = async () => {
    setIsLoading(true);
    try {
      const fetchedPackages = await getAdminPackages();
      setPackages(fetchedPackages as unknown as IPackage[]);
    } catch (error: any) {
      console.error("Failed to fetch packages:", error);
      toast({
        title: t("common:fetchFailed"),
        description: t("packages.fetchFailed"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
  ) => {
    const { name, value } = e.target;

    // Convert duration select value to number
    if (name === "duration") {
      const durationMap = {
        monthly: 1,
        quarterly: 3,
        semiannual: 6,
        annual: 12,
      };
      setNewPackage((prev) => ({
        ...prev,
        [name]: durationMap[value as keyof typeof durationMap],
      }));
    } else {
      setNewPackage((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setNewPackage((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createPackage(newPackage);
      fetchPackages();
      setNewPackage({
        name: "",
        nameEn: "",
        description: "",
        descriptionEn: "",
        price: 0,
        type: "standard",
        viewRequestLimit: 0,
        createRequestLimit: 0,
        emailNotification: false,
        smsNotification: false,
        languageIntroRights: 0,
        messagingAllowed: false,
        homepageAdDuration: 0,
        yearEndSectorReport: false,
        isActive: true,
        features: [],
        order: 0,
        duration: 1,
      });
      toast({
        title: t("common:creationSuccess"),
        description: t("packages.form.createSuccess"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error("Failed to create package:", error);
      toast({
        title: t("common:creationFailed"),
        description: t("packages.form.createFailed"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const handleDeleteClick = (packageId: string) => {
    setPackageToDelete(packageId);
    setIsDeleteAlertOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (packageToDelete) {
      try {
        await deletePackage(packageToDelete);
        fetchPackages();
        toast({
          title: t("common:actionSuccess"),
          description: t("packages.actions.deleteSuccess"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } catch (error: any) {
        console.error("Failed to delete package:", error);
        toast({
          title: t("common:actionFailed"),
          description: t("packages.actions.deleteFailed"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsDeleteAlertOpen(false);
        setPackageToDelete(null);
      }
    }
  };

  const handlePackageAction = async (
    packageId: string,
    action: "toggleStatus",
  ) => {
    try {
      const packageToUpdate = packages.find((p) => p._id === packageId);
      if (packageToUpdate) {
        await updatePackage(packageId, {
          ...packageToUpdate,
          isActive: !packageToUpdate.isActive,
        });
      }
      fetchPackages();
      toast({
        title: t("common:actionSuccess"),
        description: t(`packages.actions.${action}Success`),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error(`Failed to ${action} package:`, error);
      toast({
        title: t("common:actionFailed"),
        description: t(`packages.actions.${action}Failed`),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  return (
    <Container maxW="container.xl" py={8}>
      <Heading as="h1" size="xl" mb={6}>
        {t("packages.title")}
      </Heading>
      <Tabs>
        <TabList>
          <Tab>{t("packages.tabs.list")}</Tab>
          <Tab>{t("packages.tabs.create")}</Tab>
        </TabList>
        <TabPanels>
          <TabPanel>
            <Box
              overflowX="auto"
              bg={bgColor}
              p={4}
              borderRadius="lg"
              boxShadow="md"
            >
              {isLoading ? (
                <Text>{t("common:loading")}</Text>
              ) : (
                <Table variant="simple">
                  <Thead>
                    <Tr>
                      <Th>{t("packages.table.name")}</Th>
                      <Th>{t("packages.table.price")}</Th>
                      <Th>{t("packages.table.type")}</Th>
                      <Th>{t("packages.table.viewRequests")}</Th>
                      <Th>{t("packages.table.createRequests")}</Th>
                      <Th>{t("packages.table.status")}</Th>
                      <Th>{t("packages.table.actions")}</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {packages?.map((pkg) => (
                      <Tr key={pkg._id}>
                        <Td>{pkg.name}</Td>
                        <Td>${pkg.price.toFixed(2)}</Td>
                        <Td>{t(`packages.type.${pkg.type}`)}</Td>
                        <Td>{pkg.viewRequestLimit}</Td>
                        <Td>{pkg.createRequestLimit}</Td>
                        <Td>
                          <Badge colorScheme={pkg.isActive ? "green" : "red"}>
                            {t(
                              `packages.status.${pkg.isActive ? "active" : "inactive"}`,
                            )}
                          </Badge>
                        </Td>
                        <Td>
                          <HStack spacing={2}>
                            <Tooltip
                              label={t(
                                pkg.isActive
                                  ? "packages.actions.deactivate"
                                  : "packages.actions.activate",
                              )}
                            >
                              <IconButton
                                aria-label={t(
                                  pkg.isActive
                                    ? "packages.actions.deactivate"
                                    : "packages.actions.activate",
                                )}
                                icon={pkg.isActive ? <FiX /> : <FiCheck />}
                                colorScheme={pkg.isActive ? "red" : "green"}
                                onClick={() =>
                                  handlePackageAction(pkg._id, "toggleStatus")
                                }
                                size="sm"
                              />
                            </Tooltip>
                            <Tooltip label={t("common:edit")}>
                              <IconButton
                                as={Link}
                                to={`/admin/packages/${pkg._id}`}
                                aria-label={t("common:edit")}
                                icon={<FiEdit />}
                                colorScheme="blue"
                                variant="ghost"
                              />
                            </Tooltip>
                            <Tooltip label={t("common:delete")}>
                              <IconButton
                                aria-label={t("common:delete")}
                                icon={<FiTrash2 />}
                                colorScheme="red"
                                variant="ghost"
                                onClick={() => handleDeleteClick(pkg._id)}
                              />
                            </Tooltip>
                          </HStack>
                        </Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              )}
            </Box>
          </TabPanel>
          <TabPanel>
            <Box bg={bgColor} p={6} borderRadius="lg" boxShadow="md">
              <VStack
                as="form"
                onSubmit={handleSubmit}
                spacing={4}
                align="stretch"
              >
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.name")} (TR)</FormLabel>
                  <Input
                    name="name"
                    value={newPackage.name}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.name")} (EN)</FormLabel>
                  <Input
                    name="nameEn"
                    value={newPackage.nameEn}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.description")} (TR)</FormLabel>
                  <Input
                    name="description"
                    value={newPackage.description}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.description")} (EN)</FormLabel>
                  <Input
                    name="descriptionEn"
                    value={newPackage.descriptionEn}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.price")}</FormLabel>
                  <Input
                    name="price"
                    type="number"
                    value={newPackage.price}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.type")}</FormLabel>
                  <Select
                    name="type"
                    value={newPackage.type}
                    onChange={handleInputChange}
                  >
                    <option value="standard">
                      {t("packages.type.standard")}
                    </option>
                    <option value="addon">{t("packages.type.addon")}</option>
                  </Select>
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.duration")}</FormLabel>
                  <Select
                    name="duration"
                    value={
                      newPackage.duration === 12
                        ? "annual"
                        : newPackage.duration === 6
                          ? "semiannual"
                          : newPackage.duration === 3
                            ? "quarterly"
                            : "monthly"
                    }
                    onChange={handleInputChange}
                  >
                    <option value="monthly">
                      {t("packages.duration.monthly")}
                    </option>
                    <option value="quarterly">
                      {t("packages.duration.quarterly")}
                    </option>
                    <option value="semiannual">
                      {t("packages.duration.semiannual")}
                    </option>
                    <option value="annual">
                      {t("packages.duration.annual")}
                    </option>
                  </Select>
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.viewRequests")}</FormLabel>
                  <Input
                    name="viewRequestLimit"
                    type="number"
                    value={newPackage.viewRequestLimit}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.createRequests")}</FormLabel>
                  <Input
                    name="createRequestLimit"
                    type="number"
                    value={newPackage.createRequestLimit}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("packages.form.order")}</FormLabel>
                  <Input
                    name="order"
                    type="number"
                    value={newPackage.order}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl>
                  <Checkbox
                    name="emailNotification"
                    isChecked={newPackage.emailNotification}
                    onChange={handleCheckboxChange}
                  >
                    {t("packages.form.emailNotification")}
                  </Checkbox>
                </FormControl>
                <FormControl>
                  <Checkbox
                    name="smsNotification"
                    isChecked={newPackage.smsNotification}
                    onChange={handleCheckboxChange}
                  >
                    {t("packages.form.smsNotification")}
                  </Checkbox>
                </FormControl>
                <FormControl>
                  <FormLabel>
                    {t("packages.form.languageIntroRights")}
                  </FormLabel>
                  <Input
                    name="languageIntroRights"
                    type="number"
                    value={newPackage.languageIntroRights}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl>
                  <Checkbox
                    name="messagingAllowed"
                    isChecked={newPackage.messagingAllowed}
                    onChange={handleCheckboxChange}
                  >
                    {t("packages.form.messagingAllowed")}
                  </Checkbox>
                </FormControl>
                <FormControl>
                  <FormLabel>{t("packages.form.homepageAdDuration")}</FormLabel>
                  <Input
                    name="homepageAdDuration"
                    type="number"
                    value={newPackage.homepageAdDuration}
                    onChange={handleInputChange}
                  />
                </FormControl>
                <FormControl>
                  <Checkbox
                    name="yearEndSectorReport"
                    isChecked={newPackage.yearEndSectorReport}
                    onChange={handleCheckboxChange}
                  >
                    {t("packages.form.yearEndSectorReport")}
                  </Checkbox>
                </FormControl>
                <Flex>
                  <Spacer />
                  <Button type="submit" colorScheme="blue">
                    {t("packages.form.create")}
                  </Button>
                </Flex>
              </VStack>
            </Box>
          </TabPanel>
        </TabPanels>
      </Tabs>
      <AlertDialog
        isOpen={isDeleteAlertOpen}
        leastDestructiveRef={cancelRef}
        onClose={() => setIsDeleteAlertOpen(false)}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {t("packages.deleteConfirmation.title")}
            </AlertDialogHeader>

            <AlertDialogBody>
              {t("packages.deleteConfirmation.message")}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button
                ref={cancelRef}
                onClick={() => setIsDeleteAlertOpen(false)}
              >
                {t("common:cancel")}
              </Button>
              <Button colorScheme="red" onClick={handleDeleteConfirm} ml={3}>
                {t("common:delete")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Container>
  );
};

export default ManagePackages;
