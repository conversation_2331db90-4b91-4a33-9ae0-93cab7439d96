import React, { useEffect, useState } from 'react';
import {
  Box,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  IconButton,
  useToast,
  Flex,
  Text,
  Spinner,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  Divider,
  HStack,
  Image,
  Link,
  VStack,
  Stack,
  Tag,
  Tooltip,
  RadioGroup,
  Radio,
  FormControl,
  FormLabel,
  Textarea
} from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { IStore } from '../../types/store';
import {
  getAdminStores,
  approveStore,
  rejectStore,
  toggleStoreStatus,
  deleteStore,
  getAdminStoreById
} from '../../adminApi';
import { FiCheck, FiX, FiTrash2, FiEye, FiLink, FiToggleLeft, FiToggleRight } from 'react-icons/fi';
import { format } from 'date-fns';

const ManageStores: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const toast = useToast();
  const [stores, setStores] = useState<IStore[]>([]);
  const [selectedStore, setSelectedStore] = useState<IStore | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [detailsLoading, setDetailsLoading] = useState<boolean>(false);
  const [stats, setStats] = useState({
    totalStores: 0,
    pendingApprovalCount: 0,
    activeCount: 0,
    inactiveCount: 0
  });
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isRejectModalOpen, onOpen: onRejectModalOpen, onClose: onRejectModalClose } = useDisclosure();
  const [rejectionReason, setRejectionReason] = useState('');
  const [customRejectionReason, setCustomRejectionReason] = useState('');
  const [storeToReject, setStoreToReject] = useState<string>('');

  // Fetch all stores
  const fetchStores = async () => {
    setIsLoading(true);
    try {
      const response = await getAdminStores();
      setStores(response.stores || []);
      setStats(response.stats || {
        totalStores: 0,
        pendingApprovalCount: 0,
        activeCount: 0,
        inactiveCount: 0
      });
    } catch (error: any) {
      toast({
        title: t('stores.messages.fetchFailed'),
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setIsLoading(false);
    }
  };

  // View store details
  const handleViewStore = async (storeId: string) => {
    setDetailsLoading(true);
    try {
      const store = await getAdminStoreById(storeId);
      setSelectedStore(store);
      onOpen();
    } catch (error: any) {
      toast({
        title: t('stores.messages.actionFailed'),
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    } finally {
      setDetailsLoading(false);
    }
  };

  // Approve a store
  const handleApproveStore = async (storeId: string) => {
    if (window.confirm(t('stores.confirmations.approve'))) {
      try {
        await approveStore(storeId);
        toast({
          title: t('stores.messages.approveSuccess'),
          status: 'success',
          duration: 3000,
          isClosable: true
        });
        fetchStores();
      } catch (error: any) {
        toast({
          title: t('stores.messages.approveFailed'),
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      }
    }
  };

  // Reject a store
  const handleRejectStore = async (storeId: string, reason?: string) => {
    if (!reason) {
      setStoreToReject(storeId);
      onRejectModalOpen();
      return;
    }
    
    try {
      await rejectStore(storeId, reason);
      toast({
        title: t('stores.messages.rejectSuccess'),
        status: 'success',
        duration: 3000,
        isClosable: true
      });
      setRejectionReason('');
      setCustomRejectionReason('');
      fetchStores();
    } catch (error: any) {
      toast({
        title: t('stores.messages.rejectFailed'),
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true
      });
    }
  };

  // Toggle store active status
  const handleToggleStatus = async (storeId: string, isActive: boolean) => {
    const confirmMessage = isActive
      ? t('stores.confirmations.deactivate')
      : t('stores.confirmations.activate');

    if (window.confirm(confirmMessage)) {
      try {
        await toggleStoreStatus(storeId);
        toast({
          title: t('stores.messages.toggleSuccess'),
          status: 'success',
          duration: 3000,
          isClosable: true
        });
        fetchStores();
      } catch (error: any) {
        toast({
          title: t('stores.messages.toggleFailed'),
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      }
    }
  };

  // Delete a store
  const handleDeleteStore = async (storeId: string) => {
    if (window.confirm(t('stores.confirmations.delete'))) {
      try {
        await deleteStore(storeId);
        toast({
          title: t('stores.messages.deleteSuccess'),
          status: 'success',
          duration: 3000,
          isClosable: true
        });
        fetchStores();
      } catch (error: any) {
        toast({
          title: t('stores.messages.deleteFailed'),
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true
        });
      }
    }
  };

  // Load stores on component mount
  useEffect(() => {
    fetchStores();
  }, []);

  return (
    <Box>
      <Flex justifyContent="space-between" alignItems="center" mb={6}>
        <Heading size="lg">{t('stores.title')}</Heading>
        <HStack spacing={4}>
          <Tag size="md" colorScheme="blue" borderRadius="full">
            {t('common:total')}: {stats.totalStores}
          </Tag>
          <Tag size="md" colorScheme="orange" borderRadius="full">
            {t('stores.status.pending')}: {stats.pendingApprovalCount}
          </Tag>
          <Tag size="md" colorScheme="green" borderRadius="full">
            {t('stores.status.active')}: {stats.activeCount}
          </Tag>
          <Tag size="md" colorScheme="red" borderRadius="full">
            {t('stores.status.inactive')}: {stats.inactiveCount}
          </Tag>
        </HStack>
      </Flex>

      {isLoading ? (
        <Flex justifyContent="center" alignItems="center" height="60vh">
          <Spinner size="xl" />
        </Flex>
      ) : stores.length === 0 ? (
        <Box textAlign="center" py={10}>
          <Text fontSize="lg">{t('common:noResults')}</Text>
        </Box>
      ) : (
        <Box overflowX="auto">
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>{t('stores.table.name')}</Th>
                <Th>{t('stores.table.owner')}</Th>
                <Th>{t('stores.table.createdAt')}</Th>
                <Th>{t('common:storeType', 'Type')}</Th>
                <Th>{t('stores.status.title', 'Status')}</Th>
                <Th>{t('stores.table.approvalStatus')}</Th>
                <Th>{t('stores.table.actions')}</Th>
              </Tr>
            </Thead>
            <Tbody>
              {stores.map((store) => (
                <Tr key={store._id}>
                  <Td>
                    <Flex alignItems="center">
                      {store.name}
                    </Flex>
                  </Td>
                  <Td>
                    {store.owner ? `${store.owner.firstName} ${store.owner.lastName}` : 'N/A'}
                  </Td>
                  <Td>
                    {store.date ? format(new Date(store.date), 'dd/MM/yyyy') : 'N/A'}
                  </Td>
                  <Td>
                    <Badge colorScheme={store.type === 'company' ? 'purple' : 'blue'}>
                      {store.type || 'company'}
                    </Badge>
                  </Td>
                  <Td>
                    <Badge colorScheme={store.isActive ? 'green' : 'red'}>
                      {store.isActive ? t('stores.status.active') : t('stores.status.inactive')}
                    </Badge>
                  </Td>
                  <Td>
                    <Badge colorScheme={store.isApproved ? 'green' : 'orange'}>
                      {store.isApproved ? t('stores.status.approved') : t('stores.status.pending')}
                    </Badge>
                  </Td>
                  <Td>
                    <HStack spacing={2}>
                       {/* View button - Always visible */}
                       <Tooltip label={t('stores.actions.view')}>
                        <IconButton
                          aria-label={t('stores.actions.view')}
                          icon={<FiEye />}
                          size="sm"
                          colorScheme="blue"
                          onClick={() => handleViewStore(store._id)}
                        />
                      </Tooltip>

                      {/* Approve button - Only visible for pending stores */}
                      {!store.isApproved && (
                        <Tooltip label={t('stores.actions.approve')}>
                          <IconButton
                            aria-label={t('stores.actions.approve')}
                            icon={<FiCheck />}
                            size="sm"
                            colorScheme="green"
                            onClick={() => handleApproveStore(store._id)}
                          />
                        </Tooltip>
                      )}

                      {/* Reject button - Visible for both pending and approved stores */}
                      <Tooltip label={t('stores.actions.reject')}>
                        <IconButton
                          aria-label={t('stores.actions.reject')}
                          icon={<FiX />}
                          size="sm"
                          colorScheme="orange"
                          onClick={() => handleRejectStore(store._id)}
                          isDisabled={!store.isApproved && !store.isActive} // Disable if already rejected
                        />
                      </Tooltip>

                      {/* Activate/Deactivate button - Always visible for better admin control */}
                      <Tooltip label={store.isActive ? t('stores.actions.deactivate') : t('stores.actions.activate')}>
                        <IconButton
                          aria-label={store.isActive ? t('stores.actions.deactivate') : t('stores.actions.activate')}
                          icon={store.isActive ? <FiToggleRight /> : <FiToggleLeft />}
                          size="sm"
                          colorScheme={store.isActive ? 'green' : 'red'}
                          onClick={() => handleToggleStatus(store._id, store.isActive)}
                        />
                      </Tooltip>

                      {/* Delete button - Always visible */}
                      <Tooltip label={t('stores.actions.delete')}>
                        <IconButton
                          aria-label={t('stores.actions.delete')}
                          icon={<FiTrash2 />}
                          size="sm"
                          colorScheme="red"
                          onClick={() => handleDeleteStore(store._id)}
                        />
                      </Tooltip>
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      )}

      {/* Store Details Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t('stores.modal.viewStore')}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {detailsLoading ? (
              <Flex justifyContent="center" py={8}>
                <Spinner />
              </Flex>
            ) : selectedStore ? (
              <VStack spacing={4} align="stretch">
                {/* Cover Image */}
                {selectedStore.coverImage && (
                  <Box height="200px" overflow="hidden" borderRadius="md">
                    <Image
                        src={`${import.meta.env.VITE_SOCKET_URL}/${selectedStore.coverImage}`}
                      alt={selectedStore.name}
                      width="100%"
                        objectFit="cover"
                        fallbackSrc={`https://placehold.co/800x200/e2e8f0/1a202c?text=No+Image`}
                    />
                  </Box>
                )}

                {/* Basic Info */}
                <Flex align="center" justify="space-between">
                  <Flex align="center">
                    {selectedStore.logo && (
                      <Image
                          src={`${import.meta.env.VITE_SOCKET_URL}/${selectedStore.logo}`}
                        alt={selectedStore.name}
                        boxSize="80px"
                        mr={4}
                        borderRadius="md"
                        fallbackSrc={`https://placehold.co/80x80/e2e8f0/1a202c?text=No+Image`}
                      />
                    )}
                    <Box>
                      <Heading size="md">{selectedStore.name}</Heading>
                      <Text color="gray.500">
                        {t('stores.modal.createdAt')}: {format(new Date(selectedStore.date), 'PPP')}
                      </Text>
                    </Box>
                  </Flex>
                  <Stack direction="row">
                    <Badge colorScheme={selectedStore.isActive ? 'green' : 'red'} px={2} py={1}>
                      {selectedStore.isActive ? t('stores.status.active') : t('stores.status.inactive')}
                    </Badge>
                    <Badge colorScheme={selectedStore.isApproved ? 'green' : 'orange'} px={2} py={1}>
                      {selectedStore.isApproved ? t('stores.status.approved') : t('stores.status.pending')}
                    </Badge>
                  </Stack>
                </Flex>

                <Divider />

                {/* Description */}
                <Box>
                  <Heading size="sm" mb={2}>{t('stores.modal.description')}</Heading>
                  <Text>{selectedStore.description}</Text>
                </Box>

                <Divider />

                {/* Contact Info */}
                <Box>
                  <Heading size="sm" mb={2}>{t('stores.modal.contact')}</Heading>
                  <Stack spacing={2}>
                    {selectedStore.email && (
                      <Flex>
                        <Text fontWeight="bold" minWidth="100px">{t('common:email')}:</Text>
                        <Text>{selectedStore.email}</Text>
                      </Flex>
                    )}
                    {selectedStore.phone && (
                      <Flex>
                        <Text fontWeight="bold" minWidth="100px">{t('common:phone')}:</Text>
                        <Text>{selectedStore.phone}</Text>
                      </Flex>
                    )}
                    {selectedStore.address && (
                      <Flex>
                        <Text fontWeight="bold" minWidth="100px">{t('common:address')}:</Text>
                        <Text>{selectedStore.address}</Text>
                      </Flex>
                    )}
                    {selectedStore.website && (
                      <Flex>
                        <Text fontWeight="bold" minWidth="100px">{t('common:website')}:</Text>
                        <Link href={selectedStore.website} isExternal color="blue.500">
                          {selectedStore.website}
                        </Link>
                      </Flex>
                    )}
                  </Stack>
                </Box>

                {/* Location */}
                {selectedStore.location && (selectedStore.location.city || selectedStore.location.country) && (
                  <>
                    <Divider />
                    <Box>
                      <Heading size="sm" mb={2}>{t('stores.modal.location')}</Heading>
                      <Flex>
                        <Text fontWeight="bold" minWidth="100px">{t('common:details')}:</Text>
                        <Text>
                          {selectedStore.location.city && selectedStore.location.city}
                          {selectedStore.location.city && selectedStore.location.country && ', '}
                          {selectedStore.location.country && selectedStore.location.country}
                        </Text>
                      </Flex>
                    </Box>
                  </>
                )}

                {/* Social Media */}
                {selectedStore.socialMedia &&
                  (selectedStore.socialMedia.facebook ||
                   selectedStore.socialMedia.twitter ||
                   selectedStore.socialMedia.instagram ||
                   selectedStore.socialMedia.linkedin) && (
                  <>
                    <Divider />
                    <Box>
                      <Heading size="sm" mb={2}>{t('common:socialMedia')}</Heading>
                      <HStack spacing={4}>
                        {selectedStore.socialMedia.facebook && (
                          <Link href={selectedStore.socialMedia.facebook} isExternal>
                            <Button leftIcon={<FiLink />} size="sm">Facebook</Button>
                          </Link>
                        )}
                        {selectedStore.socialMedia.twitter && (
                          <Link href={selectedStore.socialMedia.twitter} isExternal>
                            <Button leftIcon={<FiLink />} size="sm">Twitter</Button>
                          </Link>
                        )}
                        {selectedStore.socialMedia.instagram && (
                          <Link href={selectedStore.socialMedia.instagram} isExternal>
                            <Button leftIcon={<FiLink />} size="sm">Instagram</Button>
                          </Link>
                        )}
                        {selectedStore.socialMedia.linkedin && (
                          <Link href={selectedStore.socialMedia.linkedin} isExternal>
                            <Button leftIcon={<FiLink />} size="sm">LinkedIn</Button>
                          </Link>
                        )}
                      </HStack>
                    </Box>
                  </>
                )}
              </VStack>
            ) : (
              <Text>{t('admin:stores.noStoreSelected')}</Text>
            )}
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={onClose}>
              {t('common:close')}
            </Button>
            {selectedStore && !selectedStore.isApproved && (
              <Button colorScheme="green" onClick={() => {
                handleApproveStore(selectedStore._id);
                onClose();
              }}>
                {t('admin:common.approve')}
              </Button>
            )}
            {selectedStore && selectedStore.isApproved && (
              <Button colorScheme="orange" onClick={() => {
                handleRejectStore(selectedStore._id);
                onClose();
              }}>
                {t('admin:common.reject')}
              </Button>
            )}
          </ModalFooter>
        </ModalContent>
      </Modal>
      {/* Rejection Reason Modal */}
      <Modal isOpen={isRejectModalOpen} onClose={onRejectModalClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t('admin:stores.reject.title')}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text mb={4}>{t('admin:stores.reject.selectReason')}</Text>
            <RadioGroup value={rejectionReason} onChange={setRejectionReason} mb={4}>
              <Stack spacing={2}>
                <Radio value="inappropriateName">{t('admin:stores.reject.reasons.inappropriateName')}</Radio>
                <Radio value="missingInformation">{t('admin:stores.reject.reasons.missingInformation')}</Radio>
                <Radio value="poorQualityLogo">{t('admin:stores.reject.reasons.poorQualityLogo')}</Radio>
                <Radio value="prohibitedBusiness">{t('admin:stores.reject.reasons.prohibitedBusiness')}</Radio>
                <Radio value="other">{t('admin:stores.reject.reasons.other')}</Radio>
              </Stack>
            </RadioGroup>
            
            {rejectionReason === "other" && (
              <FormControl>
                <FormLabel>{t('admin:stores.reject.customReason')}</FormLabel>
                <Textarea 
                  value={customRejectionReason}
                  onChange={(e) => setCustomRejectionReason(e.target.value)}
                  placeholder={t('admin:stores.reject.customReasonPlaceholder')}
                />
              </FormControl>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onRejectModalClose}>
              {t('common:cancel')}
            </Button>
            <Button 
              colorScheme="red" 
              isDisabled={!rejectionReason || (rejectionReason === "other" && !customRejectionReason)}
              onClick={() => {
                const finalReason = rejectionReason === "other" ? customRejectionReason : t(`admin:stores.reject.reasons.${rejectionReason}`);
                handleRejectStore(storeToReject, finalReason);
                onRejectModalClose();
              }}
            >
              {t('admin:stores.actions.reject')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ManageStores;
