import React, { useState } from "react";
import {
  Box,
  Flex,
  VStack,
  Text,
  Icon,
  Heading,
  IconButton,
  useColorModeValue,
  Drawer,
  Drawer<PERSON>ontent,
  DrawerOverlay,
  DrawerCloseButton,
  Drawer<PERSON>eader,
  DrawerBody,
  useDisclosure,
  useBreakpointValue
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { 
  FiMenu, 
  FiHome, 
  FiUsers, 
  FiShoppingBag, 
  FiPackage, 
  FiMessageSquare,
  FiImage,
  FiUser
} from "react-icons/fi";
import { FaStore } from "react-icons/fa";
import AdminDashboard from "./AdminDashboard";
import ManageUsers from "./ManageUsers";
import ManagePackages from "./ManagePackages";
import ManageProductsServices from "./ManageProductsServices";
import ManageStores from "./ManageStores";
import ManageTickets from "./ManageTickets";
import ManageHomeAds from "./ManageHomeAds";
import ManageRepresentatives from "./ManageRepresentatives";

// Define menu items with their components and icons
const menuItems = [
  { id: 'dashboard', icon: FiHome, component: AdminDashboard },
  { id: 'users', icon: FiUsers, component: ManageUsers },
  { id: 'stores', icon: FaStore, component: ManageStores },
  { id: 'productsServices', icon: FiShoppingBag, component: ManageProductsServices },
  { id: 'packages', icon: FiPackage, component: ManagePackages },
  { id: 'tickets', icon: FiMessageSquare, component: ManageTickets },
  { id: 'homeAds', icon: FiImage, component: ManageHomeAds },
  { id: 'representatives', icon: FiUser, component: ManageRepresentatives }
];

interface SidebarItemProps {
  icon: any;
  children: React.ReactNode;
  isActive?: boolean;
  onClick: () => void;
}

// Sidebar Item Component
const SidebarItem: React.FC<SidebarItemProps> = ({ icon, children, isActive, onClick }) => {
  const activeColor = useColorModeValue("blue.500", "blue.300");
  const hoverBg = useColorModeValue("gray.100", "gray.700");
  const bg = isActive ? useColorModeValue("blue.50", "blue.900") : undefined;
  const textColor = isActive ? activeColor : undefined;

  return (
    <Flex
      align="center"
      p="4"
      mx="4"
      borderRadius="md"
      role="group"
      cursor="pointer"
      bg={bg}
      color={textColor}
      _hover={{ bg: hoverBg }}
      fontWeight={isActive ? "bold" : "normal"}
      onClick={onClick}
    >
      <Icon mr="4" fontSize="18" as={icon} color={isActive ? activeColor : undefined} />
      <Text fontSize="md">{children}</Text>
    </Flex>
  );
};

interface MobileSidebarProps {
  isOpen: boolean; // Required for mobile
  onClose: () => void; // Required for mobile
  activeMenu: string;
  setActiveMenu: (menu: string) => void;
  t: (key: string) => string;
}

interface DesktopSidebarProps {
  activeMenu: string;
  setActiveMenu: (menu: string) => void;
  t: (key: string) => string;
}

// Mobile Sidebar Navigation
const MobileSidebar: React.FC<MobileSidebarProps> = ({ isOpen, onClose, activeMenu, setActiveMenu, t }) => {
  return (
    <Drawer isOpen={isOpen} placement="left" onClose={onClose} size="full">
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerHeader borderBottomWidth="1px">
          {t("panel.adminPanel")}
        </DrawerHeader>
        <DrawerBody>
          <VStack align="stretch" spacing="0">
            {menuItems.map((item) => (
              <SidebarItem
                key={item.id}
                icon={item.icon}
                isActive={activeMenu === item.id}
                onClick={() => {
                  setActiveMenu(item.id);
                  // Safe to call onClose since it's required in MobileSidebarProps
                  onClose();
                }}
              >
                {t(`panel.${item.id}`)}
              </SidebarItem>
            ))}
          </VStack>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
};

// Desktop Sidebar Navigation
const DesktopSidebar: React.FC<DesktopSidebarProps> = ({ activeMenu, setActiveMenu, t }) => {
  const bg = useColorModeValue("white", "gray.800");
  
  return (
    <Box
      position="fixed"
      minH="100vh"
      w={"250px"}
      bg={bg}
      borderRight="1px"
      borderRightColor={useColorModeValue("gray.200", "gray.700")}
      display={{ base: "none", md: "block" }}
    >
      <Flex h="20" alignItems="center" mx="8" justifyContent="space-between">
        <Heading size="md">{t("panel.adminPanel")}</Heading>
      </Flex>
      <VStack align="stretch" spacing="0">
        {menuItems.map((item) => (
          <SidebarItem
            key={item.id}
            icon={item.icon}
            isActive={activeMenu === item.id}
            onClick={() => setActiveMenu(item.id)}
          >
            {t(`panel.${item.id}`)}
          </SidebarItem>
        ))}
      </VStack>
    </Box>
  );
};

const AdminPanel: React.FC = () => {
  const { t } = useTranslation("admin");
  const [activeMenu, setActiveMenu] = useState<string>("dashboard");
  const { isOpen, onOpen, onClose } = useDisclosure();
  const isDesktop = useBreakpointValue({ base: false, md: true });
  
  // Get the active component based on the activeMenu state
  const ActiveComponent = menuItems.find(item => item.id === activeMenu)?.component || AdminDashboard;

  return (
    <Box minH="100vh">
      {isDesktop ? (
        <DesktopSidebar activeMenu={activeMenu} setActiveMenu={setActiveMenu} t={t} />
      ) : (
        <MobileSidebar isOpen={isOpen} onClose={onClose} activeMenu={activeMenu} setActiveMenu={setActiveMenu} t={t} />
      )}

      <Box ml={{ base: 0, md: "250px" }} p={"4"}>
        {!isDesktop && (
          <IconButton
            display={{ base: "flex", md: "none" }}
            onClick={onOpen}
            variant="outline"
            aria-label="open menu"
            icon={<FiMenu />}
            mb={4}
          />
        )}
        <Box>
          <ActiveComponent />
        </Box>
      </Box>
    </Box>
  );
};

export default AdminPanel;
