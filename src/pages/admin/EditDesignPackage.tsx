import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  useToast,
  Box,
  Heading,
  FormControl,
  FormLabel,
  Input,
  Button,
  Textarea,
  Checkbox,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Select,
  VStack,
  HStack,
  IconButton,
  Tag,
  TagLabel,
  TagCloseButton,
  useColorModeValue,
  Container,
  Spinner,
  Flex,
  Spacer,
  Card,
  CardBody,
  CardHeader,
  Divider,
  Grid,
  GridItem,
  useBreakpointValue,
  Text,
} from "@chakra-ui/react";
import { FiPlus, FiSave } from "react-icons/fi";
import { useParams, useNavigate, Link } from "react-router-dom";
import {
  getAdminDesignPackage,
  createAdminDesignPackage,
  updateAdminDesignPackage,
  IDesignPackageAdmin,
} from "@/api/adminApi"; // Adjusted import path

const EditDesignPackage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation(["admin", "common"]);

  const [pkg, setPkg] = useState<Partial<IDesignPackageAdmin>>({
    name: "",
    nameEn: "",
    description: "",
    descriptionEn: "",
    price: 0,
    currency: "USD",
    icon: "",
    features: [],
    featuresEn: [],
    deliveryTime: 7,
    revisionCount: 3,
    isPopular: false,
    isActive: true,
    order: 0,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [currentFeature, setCurrentFeature] = useState("");
  const [currentFeatureEn, setCurrentFeatureEn] = useState("");

  const cardBg = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const isNew = id === undefined || id === "new";

  useEffect(() => {
    if (!isNew && id) {
      setIsLoading(true);
      getAdminDesignPackage(id)
        .then((data) => {
          if (data) {
            setPkg(data);
          } else {
            toast({
              title: t("common:notFound"),
              description: t("designPackages.notFoundMessage"),
              status: "error",
              duration: 3000,
              isClosable: true,
            });
            navigate("/admin/design-packages");
          }
        })
        .catch((error) => {
          console.error("Failed to fetch design package:", error);
          toast({
            title: t("common:fetchFailed"),
            description: t("designPackages.fetchFailedMessage"),
            status: "error",
            duration: 3000,
            isClosable: true,
          });
        })
        .finally(() => setIsLoading(false));
    }
  }, [id, isNew, navigate, toast, t]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    if (type === "checkbox") {
      const { checked } = e.target as HTMLInputElement;
      setPkg((prev) => ({ ...prev, [name]: checked }));
    } else {
      setPkg((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleNumberChange = (name: string, valueAsNumber: number) => {
    setPkg((prev) => ({ ...prev, [name]: valueAsNumber }));
  };

  const handleFeatureAdd = (lang: 'tr' | 'en') => {
    if (lang === 'tr' && currentFeature.trim() !== "") {
      setPkg(prev => ({ ...prev, features: [...(prev.features || []), currentFeature.trim()] }));
      setCurrentFeature("");
    } else if (lang === 'en' && currentFeatureEn.trim() !== "") {
      setPkg(prev => ({ ...prev, featuresEn: [...(prev.featuresEn || []), currentFeatureEn.trim()] }));
      setCurrentFeatureEn("");
    }
  };

  const handleFeatureRemove = (index: number, lang: 'tr' | 'en') => {
    if (lang === 'tr') {
      setPkg(prev => ({ ...prev, features: prev.features?.filter((_, i) => i !== index) }));
    } else if (lang === 'en') {
      setPkg(prev => ({ ...prev, featuresEn: prev.featuresEn?.filter((_, i) => i !== index) }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    try {
      if (isNew) {
        await createAdminDesignPackage(pkg);
        toast({
          title: t("common:actionSuccess"),
          description: t("designPackages.createSuccessMessage"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else if (id) {
        await updateAdminDesignPackage(id, pkg);
        toast({
          title: t("common:actionSuccess"),
          description: t("designPackages.updateSuccessMessage"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
      navigate("/admin/design-packages");
    } catch (error: any) {
      console.error("Failed to save design package:", error);
      toast({
        title: t("common:actionFailed"),
        description: isNew ? t("designPackages.createFailedMessage") : t("designPackages.updateFailedMessage"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Container centerContent py={10}>
        <Spinner size="xl" />
      </Container>
    );
  }

  return (
    <Box maxW="5xl" mx="auto" py={6} px={4}>
      {/* Header */}
      <Flex mb={6} direction={{ base: "column", md: "row" }} gap={4} alignItems={{ md: "center" }}>
        <Box>
          <Heading as="h1" size="xl" color="teal.600" mb={2}>
            {isNew ? t("designPackages.addNewTitle") : t("designPackages.editTitle")}
          </Heading>
          <Text color="gray.600" fontSize="md">
            {isNew ? t("designPackages.createDescription") : t("designPackages.editDescription")}
          </Text>
        </Box>
        <Spacer />
        <Button as={Link} to="/admin/design-packages" variant="outline" colorScheme="gray">
          {t("common:backToList")}
        </Button>
      </Flex>

      <form onSubmit={handleSubmit}>
        <VStack spacing={6} align="stretch">
          {/* Basic Information Card */}
          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden" boxShadow="lg">
            <CardHeader>
              <Heading size="md" color="teal.600">{t("designPackages.sections.basicInformation")}</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={6} align="stretch">
                <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={6}>
                  <GridItem>
                    <FormControl isRequired>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.nameTr")}
                      </FormLabel>
                      <Input
                        name="name"
                        value={pkg.name}
                        onChange={handleChange}
                        borderRadius="lg"
                        _focus={{ borderColor: "teal.500", boxShadow: "0 0 0 1px var(--chakra-colors-teal-500)" }}
                      />
                    </FormControl>
                  </GridItem>
                  <GridItem>
                    <FormControl isRequired>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.nameEn")}
                      </FormLabel>
                      <Input
                        name="nameEn"
                        value={pkg.nameEn}
                        onChange={handleChange}
                        borderRadius="lg"
                        _focus={{ borderColor: "teal.500", boxShadow: "0 0 0 1px var(--chakra-colors-teal-500)" }}
                      />
                    </FormControl>
                  </GridItem>
                </Grid>

                <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={6}>
                  <GridItem>
                    <FormControl>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.descriptionTr")}
                      </FormLabel>
                      <Textarea
                        name="description"
                        value={pkg.description}
                        onChange={handleChange}
                        borderRadius="lg"
                        rows={4}
                        _focus={{ borderColor: "teal.500", boxShadow: "0 0 0 1px var(--chakra-colors-teal-500)" }}
                      />
                    </FormControl>
                  </GridItem>
                  <GridItem>
                    <FormControl>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.descriptionEn")}
                      </FormLabel>
                      <Textarea
                        name="descriptionEn"
                        value={pkg.descriptionEn}
                        onChange={handleChange}
                        borderRadius="lg"
                        rows={4}
                        _focus={{ borderColor: "teal.500", boxShadow: "0 0 0 1px var(--chakra-colors-teal-500)" }}
                      />
                    </FormControl>
                  </GridItem>
                </Grid>
              </VStack>
            </CardBody>
          </Card>

          {/* Pricing & Details Card */}
          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden" boxShadow="lg">
            <CardHeader>
              <Heading size="md" color="teal.600">{t("designPackages.sections.pricingDetails")}</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={6} align="stretch">
                <Grid templateColumns={{ base: "1fr", md: "1fr 1fr 1fr" }} gap={6}>
                  <GridItem>
                    <FormControl isRequired>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.price")}
                      </FormLabel>
                      <NumberInput
                        name="price"
                        value={pkg.price}
                        onChange={(_valueString, valueNumber) => handleNumberChange("price", valueNumber)}
                        min={0}
                      >
                        <NumberInputField borderRadius="lg" _focus={{ borderColor: "teal.500" }} />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </FormControl>
                  </GridItem>
                  <GridItem>
                    <FormControl isRequired>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.currency")}
                      </FormLabel>
                      <Select
                        name="currency"
                        value={pkg.currency}
                        onChange={handleChange}
                        borderRadius="lg"
                        _focus={{ borderColor: "teal.500" }}
                      >
                        <option value="USD">USD</option>
                        <option value="TRY">TRY</option>
                        <option value="EUR">EUR</option>
                      </Select>
                    </FormControl>
                  </GridItem>
                  <GridItem>
                    <FormControl>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.icon")}
                      </FormLabel>
                      <Input
                        name="icon"
                        value={pkg.icon}
                        onChange={handleChange}
                        placeholder={t("designPackages.form.iconPlaceholder")}
                        borderRadius="lg"
                        _focus={{ borderColor: "teal.500" }}
                      />
                    </FormControl>
                  </GridItem>
                </Grid>

                <Grid templateColumns={{ base: "1fr", md: "1fr 1fr 1fr" }} gap={6}>
                  <GridItem>
                    <FormControl>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.deliveryTime")}
                      </FormLabel>
                      <NumberInput
                        min={1}
                        max={365}
                        value={pkg.deliveryTime}
                        onChange={(value) => setPkg({ ...pkg, deliveryTime: parseInt(value) || 7 })}
                      >
                        <NumberInputField
                          borderRadius="lg"
                          _focus={{ borderColor: "teal.500" }}
                        />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </FormControl>
                  </GridItem>
                  <GridItem>
                    <FormControl>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.revisionCount")}
                      </FormLabel>
                      <NumberInput
                        name="revisionCount"
                        value={pkg.revisionCount}
                        onChange={(_valueString, valueNumber) => handleNumberChange("revisionCount", valueNumber)}
                        min={0}
                      >
                        <NumberInputField borderRadius="lg" _focus={{ borderColor: "teal.500" }} />
                        <NumberInputStepper>
                          <NumberIncrementStepper />
                          <NumberDecrementStepper />
                        </NumberInputStepper>
                      </NumberInput>
                    </FormControl>
                  </GridItem>
                  <GridItem>
                    <FormControl>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.order")}
                      </FormLabel>
                      <NumberInput
                        name="order"
                        value={pkg.order}
                        onChange={(_valueString, valueNumber) => handleNumberChange("order", valueNumber)}
                      >
                        <NumberInputField borderRadius="lg" _focus={{ borderColor: "teal.500" }} />
                      </NumberInput>
                    </FormControl>
                  </GridItem>
                </Grid>
              </VStack>
            </CardBody>
          </Card>

          {/* Features Card */}
          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden" boxShadow="lg">
            <CardHeader>
              <Heading size="md" color="teal.600">{t("designPackages.sections.features")}</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={6} align="stretch">
                <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={6}>
                  <GridItem>
                    <FormControl>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.featuresTr")}
                      </FormLabel>
                      <HStack>
                        <Input
                          value={currentFeature}
                          onChange={(e) => setCurrentFeature(e.target.value)}
                          placeholder={t("designPackages.form.addFeaturePlaceholder")}
                          borderRadius="lg"
                          _focus={{ borderColor: "teal.500" }}
                        />
                        <IconButton
                          aria-label={t("common:add")}
                          icon={<FiPlus />}
                          onClick={() => handleFeatureAdd('tr')}
                          colorScheme="teal"
                          borderRadius="lg"
                        />
                      </HStack>
                      <HStack spacing={2} mt={3} wrap="wrap">
                        {pkg.features?.map((feature, index) => (
                          <Tag key={index} size="md" borderRadius="full" variant="subtle" colorScheme="blue">
                            <TagLabel>{feature}</TagLabel>
                            <TagCloseButton onClick={() => handleFeatureRemove(index, 'tr')} />
                          </Tag>
                        ))}
                      </HStack>
                    </FormControl>
                  </GridItem>
                  <GridItem>
                    <FormControl>
                      <FormLabel fontSize="sm" fontWeight="semibold" color="gray.700">
                        {t("designPackages.form.featuresEn")}
                      </FormLabel>
                      <HStack>
                        <Input
                          value={currentFeatureEn}
                          onChange={(e) => setCurrentFeatureEn(e.target.value)}
                          placeholder={t("designPackages.form.addFeaturePlaceholder")}
                          borderRadius="lg"
                          _focus={{ borderColor: "teal.500" }}
                        />
                        <IconButton
                          aria-label={t("common:add")}
                          icon={<FiPlus />}
                          onClick={() => handleFeatureAdd('en')}
                          colorScheme="teal"
                          borderRadius="lg"
                        />
                      </HStack>
                      <HStack spacing={2} mt={3} wrap="wrap">
                        {pkg.featuresEn?.map((feature, index) => (
                          <Tag key={index} size="md" borderRadius="full" variant="subtle" colorScheme="green">
                            <TagLabel>{feature}</TagLabel>
                            <TagCloseButton onClick={() => handleFeatureRemove(index, 'en')} />
                          </Tag>
                        ))}
                      </HStack>
                    </FormControl>
                  </GridItem>
                </Grid>
              </VStack>
            </CardBody>
          </Card>

          {/* Settings Card */}
          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden" boxShadow="lg">
            <CardHeader>
              <Heading size="md" color="teal.600">{t("designPackages.sections.settings")}</Heading>
            </CardHeader>
            <CardBody>
              <VStack spacing={6} align="stretch">
                <Checkbox
                  name="isPopular"
                  isChecked={pkg.isPopular}
                  onChange={(e) => setPkg({ ...pkg, isPopular: e.target.checked })}
                  colorScheme="teal"
                  size="lg"
                >
                  <Text fontSize="sm" fontWeight="semibold" color="gray.700">
                    {t("designPackages.form.markAsPopular")}
                  </Text>
                </Checkbox>

                <Checkbox
                  name="isActive"
                  isChecked={pkg.isActive}
                  onChange={handleChange}
                  colorScheme="teal"
                  size="lg"
                >
                  <Text fontSize="sm" fontWeight="semibold" color="gray.700">
                    {t("designPackages.form.isActive")}
                  </Text>
                </Checkbox>
              </VStack>
            </CardBody>
          </Card>

          {/* Action Buttons */}
          <Card bg={cardBg} borderColor={borderColor} borderWidth="1px" borderRadius="xl" overflow="hidden" boxShadow="lg">
            <CardBody>
              <HStack justify="space-between">
                <Button
                  as={Link}
                  to="/admin/design-packages"
                  variant="outline"
                  colorScheme="gray"
                  size="lg"
                  borderRadius="lg"
                >
                  {t("common:cancel")}
                </Button>
                <Button
                  type="submit"
                  colorScheme="teal"
                  isLoading={isSaving}
                  loadingText={t("common:saving")}
                  leftIcon={<FiSave />}
                  size="lg"
                  borderRadius="lg"
                  boxShadow="lg"
                  _hover={{ transform: "translateY(-2px)", boxShadow: "xl" }}
                  transition="all 0.2s"
                >
                  {t("common:save")}
                </Button>
              </HStack>
            </CardBody>
          </Card>
        </VStack>
      </form>
    </Box>
  );
};

export default EditDesignPackage;