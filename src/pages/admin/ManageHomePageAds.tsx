import React, { useEffect, useState } from "react";
import {
  Box,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  useToast,
  Badge,
  HStack,
  Spinner,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Radio,
  RadioGroup,
  Stack,
  FormControl,
  FormLabel,
  Textarea,
  useDisclosure,
  Flex,
  Input,
  Select,
  VStack,
  Image,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import { adminApi } from "@/adminApi";

interface IHomePageAd {
  _id: string;
  image: string;
  url: string;
  title: string;
  status: string;
  createdAt: string;
  expiresAt: string;
  rejectionReason?: string;
  durationMonths?: number;
  clicks?: number;
  userId?: string;
  userName?: string;
}

const ManageHomePageAds: React.FC = () => {
  const { t } = useTranslation(["admin", "common"]);
  const toast = useToast();
  const [ads, setAds] = useState<IHomePageAd[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedAd, setSelectedAd] = useState<IHomePageAd | null>(null);
  const [rejectionReason, setRejectionReason] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isExpireModalOpen, onOpen: onExpireModalOpen, onClose: onExpireModalClose } = useDisclosure();
  const [selectedDuration, setSelectedDuration] = useState<number>(1);
  const [expirationDate, setExpirationDate] = useState<string>("");

  useEffect(() => {
    fetchAds();
  }, []);

  const fetchAds = async () => {
    try {
      const response = await adminApi.get("/admin/home-ads");
      setAds(response.data);
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.fetch_error"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (adId: string) => {
    try {
      const response = await adminApi.put(`/admin/home-ads/${adId}/approve`);

      if (response.status === 200) {
        toast({
          title: t("common:success"),
          description: t("admin:homepageAd.messages.approve_success"),
          status: "success",
          duration: 3000,
        });
        await fetchAds();
      } else {
        throw new Error("Failed to approve ad");
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.approve_error"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleReject = async () => {
    if (!selectedAd || !rejectionReason.trim()) return;

    try {
      const response = await adminApi.put(
        `/admin/home-ads/${selectedAd._id}/reject`,
        {
          reason: rejectionReason,
        },
      );

      if (response.status === 200) {
        toast({
          title: t("common:success"),
          description: t("admin:homepageAd.messages.reject_success"),
          status: "success",
          duration: 3000,
        });
        onClose();
        setRejectionReason("");
        setSelectedAd(null);
        await fetchAds();
      } else {
        throw new Error("Failed to reject ad");
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.reject_error"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleDelete = async (adId: string) => {
    if (!window.confirm(t("admin:homepageAd.messages.delete_confirm"))) return;

    try {
      const response = await adminApi.delete(`/admin/home-ads/${adId}`);

      if (response.status === 200) {
        toast({
          title: t("common:success"),
          description: t("admin:homepageAd.messages.delete_success"),
          status: "success",
          duration: 3000,
        });
        await fetchAds();
      } else {
        throw new Error("Failed to delete ad");
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.delete_error"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const openRejectModal = (ad: IHomePageAd) => {
    setSelectedAd(ad);
    setRejectionReason("");
    onOpen();
  };

  const handleExpiration = (ad: IHomePageAd) => {
    setSelectedAd(ad);
    // Calculate default expiration date (1 month from now)
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    setExpirationDate(date.toISOString().split('T')[0]);
    setSelectedDuration(1);
    onExpireModalOpen();
  };

  const updateAdExpiration = async () => {
    if (!selectedAd) return;

    try {
      const response = await adminApi.put(`/admin/home-ads/${selectedAd._id}/expiration`, {
        expiresAt: expirationDate,
        durationMonths: selectedDuration
      });

      if (response.status === 200) {
        toast({
          title: t("common:success"),
          description: t("admin:homepageAd.messages.expiration_success"),
          status: "success",
          duration: 3000,
        });
        onExpireModalClose();
        await fetchAds();
      } else {
        throw new Error("Failed to update ad expiration");
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.expiration_error"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleDurationChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const months = parseInt(e.target.value, 10);
    setSelectedDuration(months);
    
    // Update expiration date based on selected duration
    const date = new Date();
    date.setMonth(date.getMonth() + months);
    setExpirationDate(date.toISOString().split('T')[0]);
  };

  return (
    <Box p={4}>
      <Text fontSize="2xl" fontWeight="bold" mb={6}>
        {t("admin:homepageAd.title")}
      </Text>

      {isLoading ? (
        <Flex justify="center" mt={10}>
          <Spinner size="xl" />
        </Flex>
      ) : (
        <Box overflowX="auto" shadow="sm" borderRadius="md">
          <Table variant="simple" size="sm">
            <Thead bg="gray.50">
              <Tr>
                <Th>{t("admin:homepageAd.table.image")}</Th>
                <Th>{t("admin:homepageAd.table.title")}</Th>
                <Th>{t("admin:homepageAd.table.user")}</Th>
                <Th isNumeric>{t("admin:homepageAd.table.clicks")}</Th>
                <Th>{t("admin:homepageAd.table.status")}</Th>
                <Th>{t("admin:homepageAd.table.created_at")}</Th>
                <Th>{t("admin:homepageAd.table.expires_at")}</Th>
                <Th>{t("admin:homepageAd.table.duration")}</Th>
                <Th>{t("common:actions")}</Th>
              </Tr>
            </Thead>
            <Tbody>
              {ads.map((ad) => (
                <Tr key={ad._id}>
                  <Td>
                    <Image
                      src={ad.image}
                      alt={ad.title}
                      boxSize="50px"
                      objectFit="cover"
                      borderRadius="md"
                    />
                  </Td>
                  <Td>{ad.title}</Td>
                  <Td>{ad.userName || "-"}</Td>
                  <Td isNumeric>{ad.clicks || 0}</Td>
                  <Td>
                    <Badge
                      colorScheme={
                        ad.status === "approved"
                          ? "green"
                          : ad.status === "pending"
                          ? "yellow"
                          : "red"
                      }
                    >
                      {t(`admin:homepageAd.status.${ad.status}`)}
                    </Badge>
                  </Td>
                  <Td>{format(new Date(ad.createdAt), "PP")}</Td>
                  <Td>{format(new Date(ad.expiresAt), "PP")}</Td>
                  <Td>
                    {ad.durationMonths ? `${ad.durationMonths} ${t(ad.durationMonths === 1 ? "admin:homepageAd.expiration.month" : "admin:homepageAd.expiration.months")}` : "-"}
                  </Td>
                  <Td>
                    <HStack spacing={1} flexWrap="wrap">
                      {ad.status === "pending" && (
                        <>
                          <Button
                            size="xs"
                            colorScheme="green"
                            onClick={() => handleApprove(ad._id)}
                          >
                            {t("admin:homepageAd.actions.approve")}
                          </Button>
                          <Button
                            size="xs"
                            colorScheme="red"
                            onClick={() => openRejectModal(ad)}
                          >
                            {t("admin:homepageAd.actions.reject")}
                          </Button>
                        </>
                      )}
                      {ad.status === "approved" && (
                        <>
                          <Button
                            size="xs"
                            colorScheme="red"
                            onClick={() => openRejectModal(ad)}
                          >
                            {t("admin:homepageAd.actions.deactivate")}
                          </Button>
                          <Button
                            size="xs"
                            colorScheme="blue"
                            onClick={() => handleExpiration(ad)}
                          >
                            {t("admin:homepageAd.actions.setExpiration")}
                          </Button>
                        </>
                      )}
                      {ad.status === "rejected" && (
                        <Button
                          size="xs"
                          colorScheme="green"
                          onClick={() => handleApprove(ad._id)}
                        >
                          {t("admin:homepageAd.actions.activate")}
                        </Button>
                      )}
                      <Button
                        size="xs"
                        colorScheme="red"
                        onClick={() => handleDelete(ad._id)}
                      >
                        {t("admin:homepageAd.actions.delete")}
                      </Button>
                    </HStack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        </Box>
      )}

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("admin:homepageAd.modal.reject")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>
                  {t("admin:homepageAd.form.rejection_reason")}
                </FormLabel>
                <Textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder={t(
                    "admin:homepageAd.form.rejection_reason_placeholder",
                  )}
                />
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              {t("common:cancel")}
            </Button>
            <Button
              colorScheme="red"
              onClick={handleReject}
              isDisabled={!rejectionReason.trim()}
            >
              {t("admin:homepageAd.actions.confirm_reject")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Expiration Date Modal */}
      <Modal isOpen={isExpireModalOpen} onClose={onExpireModalClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("admin:homepageAd.expiration.title")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel>{t("admin:homepageAd.expiration.duration")}</FormLabel>
                <Select value={selectedDuration} onChange={handleDurationChange}>
                  <option value={1}>1 {t("admin:homepageAd.expiration.month")}</option>
                  <option value={3}>3 {t("admin:homepageAd.expiration.months")}</option>
                  <option value={6}>6 {t("admin:homepageAd.expiration.months")}</option>
                  <option value={12}>12 {t("admin:homepageAd.expiration.months")}</option>
                </Select>
              </FormControl>
              
              <FormControl>
                <FormLabel>{t("admin:homepageAd.expiration.expirationDate")}</FormLabel>
                <Input 
                  type="date" 
                  value={expirationDate}
                  onChange={(e) => setExpirationDate(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              </FormControl>
              
              <Text fontSize="sm" color="gray.500">
                {t("admin:homepageAd.expiration.explanation")}
              </Text>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onExpireModalClose}>
              {t("common:cancel")}
            </Button>
            <Button 
              colorScheme="blue" 
              onClick={updateAdExpiration}
              isDisabled={!expirationDate}
            >
              {t("admin:homepageAd.expiration.save")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ManageHomePageAds;
