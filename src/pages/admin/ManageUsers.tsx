import React, { useEffect, useState } from "react";
import {
  Box,
  Heading,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  useColorModeValue,
  Badge,
  IconButton,
  Tooltip,
  Avatar,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Select,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  useDisclosure,
  useToast,
  Divider,
  Text,
  Stack,
  Skeleton,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { IUser } from "../../types/user";
import { FiUserCheck, FiUserX, FiTrash2, FiPackage } from "react-icons/fi";
import { getUsers, manageUser, getAdminPackages, applyPackageToUser, getUserSubscriptions } from "@/adminApi";
import { format } from "date-fns";

// Interface for subscription data
interface ISubscription {
  _id: string;
  packageId: {
    _id: string;
    name: string;
    type: string;
    price: number;
  };
  status: string;
  startDate: string;
  endDate: string;
}

const ManageUsers: React.FC = () => {
  const [users, setUsers] = useState<IUser[]>([]);
  const [packages, setPackages] = useState<any[]>([]);
  const [selectedUser, setSelectedUser] = useState<IUser | null>(null);
  const [selectedPackage, setSelectedPackage] = useState<string>("");
  const [packageDuration, setPackageDuration] = useState<number>(1);
  const [userSubscriptions, setUserSubscriptions] = useState<ISubscription[]>([]);
  const [loadingSubscriptions, setLoadingSubscriptions] = useState<boolean>(false);
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const {
    isOpen: isDeleteAlertOpen,
    onOpen: onDeleteAlertOpen,
    onClose: onDeleteAlertClose
  } = useDisclosure();
  const cancelRef = React.useRef<HTMLButtonElement>(null);
  const { t } = useTranslation(["admin", "common"]);
  const toast = useToast();

  // Function to fetch users
  const fetchUsers = async () => {
    try {
      const response = await getUsers();
      setUsers(response);
    } catch (error: any) {
      console.error("Failed to fetch users:", error);
      toast({
        title: t("admin:users.messages.usersFetchFailed"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Function to fetch packages
  const fetchPackages = async () => {
    try {
      const response: any = await getAdminPackages();
      console.log('Packages response:', response);
      // Make sure we're setting the packages array correctly
      setPackages(Array.isArray(response) ? response : response.packages || []);
    } catch (error: any) {
      console.error("Failed to fetch packages:", error);
      toast({
        title: t("admin:packages.fetchFailed"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Function to fetch user subscriptions
  const fetchUserSubscriptions = async (userId: string) => {
    setLoadingSubscriptions(true);
    try {
      const response: any = await getUserSubscriptions(userId);
      setUserSubscriptions(response.subscriptions || []);
    } catch (error: any) {
      console.error("Failed to fetch user subscriptions:", error);
      toast({
        title: t("admin:users.messages.subscriptionsFetchFailed"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setLoadingSubscriptions(false);
    }
  };

  useEffect(() => {
    fetchUsers();
    fetchPackages();
  }, []);

  const handleUserAction = async (
    userId: string,
    action: "activate" | "deactivate" | "delete",
  ) => {
    try {
      await manageUser(userId, action);

      if (action === "delete") {
        // Remove the user from the list
        setUsers(users.filter((user) => user._id !== userId));
        toast({
          title: t("admin:users.messages.userDeleted", "User deleted successfully"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        // Update user status
        setUsers(
          users.map((user) =>
            user._id === userId
              ? { ...user, isActive: action === "activate" }
              : user,
          ),
        );
        toast({
          title: t(
            action === "activate"
              ? "admin:users.messages.userActivated"
              : "admin:users.messages.userDeactivated",
            action === "activate" ? "User activated" : "User deactivated"
          ),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
    } catch (error: any) {
      console.error(`Failed to ${action} user:`, error);
      toast({
        title: t(`admin:users.messages.${action}Failed`, `Failed to ${action} user`),
        description: error.response?.data?.message || error.message,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Open delete confirmation dialog
  const openDeleteConfirmation = (userId: string) => {
    setUserToDelete(userId);
    onDeleteAlertOpen();
  };

  // Confirm user deletion
  const confirmDeleteUser = () => {
    if (userToDelete) {
      handleUserAction(userToDelete, "delete");
      setUserToDelete(null);
    }
    onDeleteAlertClose();
  };

  const handleApplyPackage = (user: IUser) => {
    setSelectedUser(user);
    setSelectedPackage("");
    setPackageDuration(1);
    // Fetch user's subscriptions when modal opens
    fetchUserSubscriptions(user._id);
    // Refresh packages when opening the modal
    fetchPackages();
    onOpen();
  };

  const handleApplyPackageSubmit = async () => {
    if (!selectedUser || !selectedPackage) return;

    try {
      await applyPackageToUser(
        selectedUser._id,
        selectedPackage,
        Number(packageDuration)
      );
      toast({
        title: t("admin:users.messages.packageApplied"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      onClose();
      fetchUsers(); // Refresh users list
    } catch (error: any) {
      toast({
        title: t("admin:users.messages.packageApplyFailed"),
        description: error.response?.data?.message || error.message,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  // Format date function
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd MMM yyyy");
    } catch (e) {
      return dateString;
    }
  };

  // Get subscription status badge color
  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case "ACTIVE":
        return "green";
      case "CANCELED":
        return "red";
      case "EXPIRED":
        return "orange";
      default:
        return "gray";
    }
  };

  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Box
      bg={bgColor}
      p={6}
      borderRadius="lg"
      boxShadow="md"
      borderColor={borderColor}
      borderWidth={1}
    >
      <Heading as="h1" size="lg" mb={6}>
        {t("admin:users.title")}
      </Heading>
      <Box overflowX="auto">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>{t("admin:users.table.user")}</Th>
              <Th>{t("admin:users.table.email")}</Th>
              <Th>{t("admin:users.table.status")}</Th>
              <Th>{t("admin:users.table.actions")}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {users?.map((user: any) => (
              <Tr key={user._id}>
                <Td>
                  <Box display="flex" alignItems="center">
                    <Avatar size="sm" name={user.firstName} mr={3} />

                    {user.firstName}
                  </Box>
                </Td>
                <Td>{user.email}</Td>
                <Td>
                  <Badge colorScheme={user.isActive ? "green" : "red"}>
                    {t(
                      user.isActive
                        ? "admin:users.status.active"
                        : "admin:users.status.inactive",
                    )}
                  </Badge>
                </Td>
                <Td>
                  <Tooltip
                    label={t(
                      user.isActive
                        ? "admin:users.actions.deactivate"
                        : "admin:users.actions.activate",
                    )}
                  >
                    <IconButton
                      aria-label={t(
                        user.isActive
                          ? "admin:users.actions.deactivate"
                          : "admin:users.actions.activate",
                      )}
                      icon={user.isActive ? <FiUserX /> : <FiUserCheck />}
                      colorScheme={user.isActive ? "red" : "green"}
                      onClick={() =>
                        handleUserAction(
                          user._id,
                          user.isActive ? "deactivate" : "activate",
                        )
                      }
                      size="sm"
                      mr={2}
                    />
                  </Tooltip>
                  <Tooltip label={t("admin:users.actions.delete")}>
                    <IconButton
                      aria-label={t("admin:users.actions.delete")}
                      icon={<FiTrash2 />}
                      colorScheme="red"
                      onClick={() => openDeleteConfirmation(user._id)}
                      size="sm"
                      mr={2}
                    />
                  </Tooltip>
                  <Tooltip label={t("admin:users.actions.applyPackage")}>
                    <IconButton
                      aria-label={t("admin:users.actions.applyPackage")}
                      icon={<FiPackage />}
                      colorScheme="blue"
                      onClick={() => handleApplyPackage(user)}
                      size="sm"
                    />
                  </Tooltip>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>

      {/* Apply Package Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("admin:users.applyPackage.title")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {/* Current subscriptions section */}
            <Box mb={6}>
              <Heading size="sm" mb={3}>{t("admin:users.applyPackage.currentSubscriptions")}</Heading>

              {loadingSubscriptions ? (
                <Stack spacing={3}>
                  <Skeleton height="40px" />
                  <Skeleton height="40px" />
                </Stack>
              ) : userSubscriptions.length > 0 ? (
                <Table variant="simple" size="sm">
                  <Thead>
                    <Tr>
                      <Th>{t("admin:users.applyPackage.packageName")}</Th>
                      <Th>{t("admin:users.applyPackage.status")}</Th>
                      <Th>{t("admin:users.applyPackage.expiresOn")}</Th>
                    </Tr>
                  </Thead>
                  <Tbody>
                    {userSubscriptions.map((subscription) => (
                      <Tr key={subscription._id}>
                        <Td>{subscription.packageId?.name}</Td>
                        <Td>
                          <Badge colorScheme={getStatusColor(subscription.status)}>
                            {subscription.status}
                          </Badge>
                        </Td>
                        <Td>{formatDate(subscription.endDate)}</Td>
                      </Tr>
                    ))}
                  </Tbody>
                </Table>
              ) : (
                <Text>{t("admin:users.applyPackage.noSubscriptions")}</Text>
              )}
            </Box>

            <Divider my={4} />

            {/* Apply new package section */}
            <Heading size="sm" mb={3}>{t("admin:users.applyPackage.newPackage")}</Heading>

            <FormControl mb={4}>
              <FormLabel>{t("admin:users.applyPackage.package")}</FormLabel>
              <Select
                placeholder={t("admin:users.applyPackage.selectPackage")}
                value={selectedPackage}
                onChange={(e) => setSelectedPackage(e.target.value)}
              >
                {packages && packages.length > 0 ? (
                  packages.map((pkg) => (
                    <option key={pkg._id} value={pkg._id}>
                      {pkg.name} ({pkg.price ? `$${pkg.price}` : ''})
                    </option>
                  ))
                ) : (
                  <option disabled value="">{t("admin:packages.notFound")}</option>
                )}
              </Select>
            </FormControl>
            <FormControl>
              <FormLabel>{t("admin:users.applyPackage.duration")}</FormLabel>
              <NumberInput
                min={1}
                max={36}
                value={packageDuration}
                onChange={(_, value) => setPackageDuration(value)}
              >
                <NumberInputField />
                <NumberInputStepper>
                  <NumberIncrementStepper />
                  <NumberDecrementStepper />
                </NumberInputStepper>
              </NumberInput>
            </FormControl>
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="gray" mr={3} onClick={onClose}>
              {t("common:cancel")}
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleApplyPackageSubmit}
              isDisabled={!selectedPackage}
            >
              {t("admin:users.applyPackage.apply")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete User Confirmation */}
      <AlertDialog
        isOpen={isDeleteAlertOpen}
        leastDestructiveRef={cancelRef as any}
        onClose={onDeleteAlertClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {t("admin:users.deleteConfirmation.title", "Delete User")}
            </AlertDialogHeader>

            <AlertDialogBody>
              {t("admin:users.deleteConfirmation.message", "Are you sure to delete?")}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteAlertClose}>
                {t("common:cancel", "Cancel")}
              </Button>
              <Button colorScheme="red" onClick={confirmDeleteUser} ml={3}>
                {t("common:delete", "Delete")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default ManageUsers;
