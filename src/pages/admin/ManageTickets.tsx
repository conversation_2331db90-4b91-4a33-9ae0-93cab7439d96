import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  useDisclosure,
  VStack,
  HStack,
  Text,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Textarea,
  Spinner,
  Avatar,
  Divider,
  useColorModeValue,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import {
  getAdminTicketById,
  getAdminTickets,
  respondToTicket,
  updateAdminTicket,
} from "@/adminApi";
import { ITicket } from "@/types/ticket";
import { formatDistance } from "date-fns";

const ManageTickets: React.FC = () => {
  const [tickets, setTickets] = useState<ITicket[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<ITicket | null>(null);
  const [newResponse, setNewResponse] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    isOpen: isDetailOpen,
    onOpen: onDetailOpen,
    onClose: onDetailClose,
  } = useDisclosure();
  const { t } = useTranslation(["admin", "tickets", "common"]);
  const toast = useToast();

  const bgColor = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  useEffect(() => {
    fetchTickets();
  }, []);

  const fetchTickets = async () => {
    try {
      const response = await getAdminTickets();
      if (response) {
        setTickets(response || []);
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:tickets.messages.fetch_error"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewTicket = async (ticket: ITicket) => {
    try {
      const response = await getAdminTicketById(ticket._id);
      if (response) {
        setSelectedTicket(response || null);
        onDetailOpen();
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:tickets.messages.fetch_error"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleAddResponse = async () => {
    if (!selectedTicket || !newResponse.trim()) return;

    setIsSubmitting(true);
    try {
      const response = await respondToTicket(selectedTicket._id, newResponse);
      if (response) {
        setSelectedTicket(response || null);
        setNewResponse("");
        await fetchTickets();
        toast({
          title: t("common:success"),
          description: t("admin:tickets.messages.response_added"),
          status: "success",
          duration: 3000,
        });
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:tickets.messages.response_error"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCloseTicket = async () => {
    if (!selectedTicket) return;

    setIsSubmitting(true);
    try {
      const response = await updateAdminTicket(selectedTicket._id, {
        status: "resolved",
      });
      if (response) {
        setSelectedTicket(response || null);
        await fetchTickets();
        toast({
          title: t("common:success"),
          description: t("admin:tickets.messages.ticket_closed"),
          status: "success",
          duration: 3000,
        });
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:tickets.messages.update_error"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box p={4}>
      <Text fontSize="2xl" fontWeight="bold" mb={6}>
        {t("admin:tickets.title")}
      </Text>

      {isLoading ? (
        <Spinner />
      ) : (
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>{t("admin:tickets.table.title")}</Th>
              <Th>{t("admin:tickets.table.type")}</Th>
              <Th>{t("admin:tickets.table.category")}</Th>
              <Th>{t("admin:tickets.table.status")}</Th>
              <Th>{t("admin:tickets.table.created_at")}</Th>
              <Th>{t("admin:tickets.table.actions")}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {tickets.map((ticket) => (
              <Tr key={ticket._id}>
                <Td>{ticket.title}</Td>
                <Td>{t(`admin:tickets.types.${ticket.type}`)}</Td>
                <Td>{ticket.category}</Td>
                <Td>
                  <Badge
                    colorScheme={
                      ticket.status === "resolved"
                        ? "green"
                        : ticket.status === "in_progress"
                          ? "yellow"
                          : "red"
                    }
                  >
                    {t(`admin:tickets.status.${ticket.status}`)}
                  </Badge>
                </Td>
                <Td>{new Date(ticket.createdAt).toLocaleDateString()}</Td>
                <Td>
                  <Button size="sm" onClick={() => handleViewTicket(ticket)}>
                    {t("admin:tickets.table.view")}
                  </Button>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      )}

      {/* Ticket Detail Modal */}
      <Modal
        isOpen={isDetailOpen}
        onClose={onDetailClose}
        size="xl"
        scrollBehavior="inside"
      >
        <ModalOverlay />
        <ModalContent maxHeight="90vh">
          <ModalHeader>
            {selectedTicket?.title}
            <Badge
              ml={2}
              colorScheme={
                selectedTicket?.status === "resolved"
                  ? "green"
                  : selectedTicket?.status === "in_progress"
                    ? "yellow"
                    : "red"
              }
            >
              {selectedTicket?.status &&
                t(`admin:tickets.status.${selectedTicket.status}`)}
            </Badge>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              <Box>
                <Text fontWeight="bold">
                  {t("admin:tickets.form.description")}
                </Text>
                <Text>{selectedTicket?.description}</Text>
              </Box>

              {selectedTicket?.images && selectedTicket.images.length > 0 && (
                <Box>
                  <Text fontWeight="bold" mb={2}>
                    {t("admin:tickets.form.images")}
                  </Text>
                  <HStack spacing={2} overflowX="auto" p={2}>
                    {selectedTicket.images.map((image, index) => (
                      <Box
                        key={index}
                        as="img"
                        src={image}
                        alt={`Ticket image ${index + 1}`}
                        maxH="200px"
                        objectFit="cover"
                        borderRadius="md"
                      />
                    ))}
                  </HStack>
                </Box>
              )}

              <Divider />

              <Box>
                <Text fontWeight="bold" mb={2}>
                  {t("admin:tickets.responses")}
                </Text>
                <VStack
                  spacing={4}
                  align="stretch"
                  maxHeight="300px"
                  overflowY="auto"
                >
                  {selectedTicket?.responses?.map((response, index) => (
                    <Box
                      key={index}
                      p={4}
                      borderRadius="md"
                      bg={response.isAdmin ? "blue.50" : bgColor}
                      borderWidth="1px"
                      borderColor={borderColor}
                    >
                      <HStack spacing={3} mb={2}>
                        <Avatar
                          size="sm"
                          name={
                            response.isAdmin
                              ? t("admin:tickets.admin")
                              : t("admin:tickets.user")
                          }
                        />

                        <VStack align="start" spacing={0}>
                          <Text fontWeight="bold">
                            {response.isAdmin
                              ? t("admin:tickets.admin")
                              : t("admin:tickets.user")}
                          </Text>
                          <Text fontSize="sm" color="gray.500">
                            {formatDistance(
                              new Date(response.createdAt),
                              new Date(),
                              { addSuffix: true },
                            )}
                          </Text>
                        </VStack>
                      </HStack>
                      <Text>{response.message}</Text>
                    </Box>
                  ))}
                </VStack>
              </Box>

              {selectedTicket?.status !== "resolved" && (
                <Box mt={4}>
                  <FormControl>
                    <FormLabel>{t("admin:tickets.add_response")}</FormLabel>
                    <Textarea
                      value={newResponse}
                      onChange={(e) => setNewResponse(e.target.value)}
                      placeholder={t(
                        "admin:tickets.response_placeholder",
                      )}
                      minHeight="100px"
                    />
                  </FormControl>
                  <HStack mt={4} spacing={2} justifyContent="flex-end">
                    <Button
                      colorScheme="blue"
                      isLoading={isSubmitting}
                      onClick={handleAddResponse}
                      isDisabled={!newResponse.trim()}
                    >
                      {t("tickets:send")}
                    </Button>
                    <Button
                      colorScheme="red"
                      variant="outline"
                      isLoading={isSubmitting}
                      onClick={handleCloseTicket}
                    >
                      {t("admin:tickets.actions.close")}
                    </Button>
                  </HStack>
                </Box>
              )}
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default ManageTickets;
