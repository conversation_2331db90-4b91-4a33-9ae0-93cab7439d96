import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { SearchIcon, ChevronDownIcon, ChevronUpIcon, ExternalLinkIcon } from "lucide-react";
import "swiper/css/bundle";
import "swiper/css";

// API imports
import { getMostViewedStores } from "../api/storeApi";
import { IItem } from "../types/item";
import { IStore } from "../types/store";
import { api, checkViewRequest, getFilters, useViewRequest } from "@/api";
import { getActiveRepresentatives } from "../api/representativeApi";
import { getSliders } from "../api/sliderApi";
import { getStoreId } from "@/utils/helpers";

// Components
import HeroSlider from "../components/HeroSlider";
import FeaturedStores from "../components/FeaturedStores";
import FeaturedCompany from "../components/FeaturedCompany";
import FeaturedProducts from "../components/FeaturedProducts";
import ProductCard from "../components/ProductCard";
import { useAuthCheck } from "@/hooks/useAuthCheck";
import Carousel from "../components/Carousel";

const ITEMS_PER_PAGE = 9;

const MotionDiv = motion.div;
const MotionButton = motion.button;

const Home: React.FC = () => {
  const { t } = useTranslation("home");
  const { t: commonT } = useTranslation("common");
  const navigate = useNavigate();
  const [products, setProducts] = useState<IItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<IItem[]>([]);
  const [featuredStores, setFeaturedStores] = useState<IStore[]>([]);
  const [mostViewedItems, setMostViewedItems] = useState<IItem[]>([]);
  const [selectedStore, setSelectedStore] = useState<IStore | null>(null);
  const [type, setType] = useState("all");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<any>({
    categories: [],
    productTypes: [],
  });
  const [availableFilters, setAvailableFilters] = useState<any>({
    categories: [],
    productTypes: [],
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);
  const [viewRequestInfo, setViewRequestInfo] = useState<any>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const cancelRef = useRef<any>(null);
  const [representatives, setRepresentatives] = useState<any[]>([]);
  const [homepageAds, setHomepageAds] = useState<any[]>([]);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);
  const [selectedAd, setSelectedAd] = useState<any>(null);
  const [sliders, setSliders] = useState<any[]>([]);
  const [isSlidersLoading, setIsSlidersLoading] = useState(true);
  const { user } = useAuthCheck();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [
          filtersData,
          mostViewedStores,
          activeRepresentatives,
          approvedAds,
          mostViewed,
        ]: any = await Promise.all([
          getFilters("all"),
          getMostViewedStores(),
          getActiveRepresentatives(),
          api.get("/home-ads/active").then((res) => res.data),
          api.get("/items/most-viewed").then((res) => res.data),
        ]);

        setProducts(filtersData.items);
        setFilteredProducts(filtersData.items);
        setFilters(filtersData);
        setAvailableFilters(filtersData);
        setFeaturedStores(mostViewedStores);
        setRepresentatives(activeRepresentatives.data);
        setHomepageAds(approvedAds);
        setMostViewedItems(mostViewed || []);

        if (mostViewedStores.length > 0) {
          setSelectedStore(mostViewedStores[0]);
        }
      } catch (error: any) {
        console.error("Error fetching data:", error);
        setError("Failed to load data. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchSliders = async () => {
      try {
        const data = await getSliders();
        setSliders(data);
      } catch (error) {
        console.error("Error fetching sliders:", error);
      } finally {
        setIsSlidersLoading(false);
      }
    };

    fetchSliders();
  }, []);

  const handleTypeChange = async (
    event: React.ChangeEvent<HTMLSelectElement>,
  ) => {
    const selectedType = event.target.value;
    setType(selectedType);
    setCurrentPage(1);
    setSelectedCategories([]);

    try {
      // Fetch new filters based on selected type
      const filtersData: any = await getFilters(
        selectedType === "all" ? undefined : selectedType,
      );
      setAvailableFilters(filtersData);
      setFilteredProducts(filtersData.items);
    } catch (error) {
      console.error("Error updating filters:", error);
    }
  };

  const handleCategoryChange = async (categoryId: string) => {
    const newSelectedCategories = selectedCategories.includes(categoryId)
      ? selectedCategories.filter((id) => id !== categoryId)
      : [...selectedCategories, categoryId];

    setSelectedCategories(newSelectedCategories);
    setCurrentPage(1);

    try {
      const filtersData: any = await getFilters(
        type || "all",
        newSelectedCategories.length > 0
          ? newSelectedCategories.join(",")
          : undefined,
      );
      setAvailableFilters(filtersData);
      setFilteredProducts(filtersData.items);
    } catch (error) {
      console.error("Error updating filters:", error);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    // Filter products based on search term
    const filtered = products.filter((product) => {
      const searchRegex = new RegExp(value, "i");
      return (
        searchRegex.test(product.name) ||
        searchRegex.test(product.description) ||
        searchRegex.test(product.type)
      );
    });

    setFilteredProducts(filtered);
    setCurrentPage(1); // Reset to first page when searching
  };

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories((prev) =>
      prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId],
    );
  };

  const handleItemClick = async (itemId: string) => {
    if (!user) {
      navigate("/login");
      return;
    }

    try {
      const response = await checkViewRequest(itemId);
      setViewRequestInfo(response);
      setSelectedItemId(itemId);

      if (response.isOwner || response.alreadyViewed) {
        navigate(`/items/${itemId}`);
        return;
      }

      if (!response.hasRemaining) {
        // Show toast error
        console.error(response.message);
        return;
      }

      setIsViewDialogOpen(true);
    } catch (error: any) {
      console.error("Error checking view request:", error);
      if (error.response && error.response.status === 403) {
        navigate("/packages");
      }
    }
  };

  const handleViewConfirm = async () => {
    if (!selectedItemId) return;

    try {
      await useViewRequest(selectedItemId);
      setIsViewDialogOpen(false);
      navigate(`/items/${selectedItemId}`);
    } catch (error: any) {
      console.error("Error using view request:", error);
    }
  };

  const handleViewCancel = () => {
    setSelectedItemId(null);
    setIsViewDialogOpen(false);
  };

  const totalPages = Math.ceil(filteredProducts.length / ITEMS_PER_PAGE);
  const paginatedProducts = filteredProducts.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE,
  );

  const handleImageClick = (ad: any) => {
    setSelectedImage(import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image);
    setSelectedAd(ad);
    setIsImageModalOpen(true);
  };

  return (
    <>
      {/* Hero Slider */}
      {!isSlidersLoading && (
        <MotionDiv
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1 }}
          className="bg-gradient-to-b from-black/10 to-black/30"
        >
          <HeroSlider images={sliders} />
        </MotionDiv>
      )}
      
      <MotionDiv
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 font-sans"
      >
        {isLoading ? (
          <div className="flex items-center justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          </div>
        ) : error ? (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* Premium Homepage Ads Section */}
            {user && homepageAds.length > 0 && (
              <section className="mb-12 relative overflow-hidden">
                {/* Background decoration elements */}
                <div className="absolute -top-[100px] -right-[100px] w-[300px] h-[300px] bg-blue-300/15 rounded-full z-0"></div>
                <div className="absolute -bottom-[50px] -left-[50px] w-[200px] h-[200px] bg-green-300/10 rounded-full z-0"></div>

                {/* Content container with premium styling */}
                <div className="relative z-1 py-8 px-4 md:px-8 rounded-2xl shadow-xl bg-white border border-gray-100">
                  {/* Section header with refined typography */}
                  <div className="flex justify-center mb-8">
                    <h2 className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                      {t("homepageAds.title")}
                    </h2>
                  </div>

                  {/* Enhanced carousel with professional styling */}
                  <Carousel
                    items={homepageAds.map((ad) => (
                      <div
                        key={ad._id}
                        className="rounded-xl overflow-hidden shadow-lg transition-all duration-300 bg-white border border-gray-100 hover:translate-y-[-8px] hover:shadow-2xl cursor-pointer"
                        onClick={async () => {
                          try {
                            // First track the click with correct route format
                            const response = await api.post(`/home-ads/click/${ad._id}`);
                            const { itemId } = response.data;
                            console.log('Ad clicked, tracking complete');

                            // If ad has an associated product, redirect to product page
                            if (itemId) {
                              console.log('Navigating to product:', itemId);
                              navigate(`/items/${itemId}`);
                            }
                            // Otherwise navigate to store page if storeId exists
                            else if (ad.storeId) {
                              const storeId = getStoreId(ad.storeId);
                              console.log('Navigating to store:', storeId);
                              navigate(`/stores/${storeId}`);
                            }
                          } catch (error) {
                            console.error("Failed to record click:", error);
                          }
                        }}
                      >
                        {/* Premium image presentation */}
                        <div className="relative">
                          <img
                            src={
                              import.meta.env.VITE_SOCKET_URL +
                              (ad.image.startsWith('/') ? '' : '/') +
                              '/uploads/' +
                              (ad.image.startsWith('/') ? '' : '/') +
                              ad.image.replace(/^\/+/, '')
                            }
                            alt={ad.title}
                            className="h-[200px] md:h-[220px] w-full object-cover transition-transform duration-500 hover:scale-105 cursor-zoom-in"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleImageClick(ad);
                            }}
                          />

                          {/* Gradient overlay for better visual appeal */}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent pointer-events-none"></div>

                          {/* Floating badge - premium touch */}
                          <div className="absolute top-4 right-4 px-3 py-1 rounded-full bg-gradient-to-r from-blue-600 to-teal-600 text-white font-medium uppercase tracking-wide text-xs shadow-md">
                            {t("homepageAds.featured")}
                          </div>
                        </div>

                        {/* Content area with enhanced typography */}
                        <div className="p-4 md:p-5 space-y-2 md:space-y-3 bg-white">
                          <h3 className="text-sm md:text-base font-bold line-clamp-2">
                            {ad.title}
                          </h3>

                          {/* Call-to-action button */}
                          <div className="flex justify-between items-center w-full pt-2">
                            <p className="text-sm text-gray-600">
                              {t("homepageAds.clickToView")}
                            </p>
                            <button
                              className="inline-flex items-center px-3 py-1 text-sm rounded-full border border-blue-600 text-blue-600 hover:bg-gradient-to-r hover:from-blue-600 hover:to-teal-600 hover:text-white hover:border-transparent transition-colors"
                            >
                              {t("homepageAds.view")}
                              <ExternalLinkIcon className="ml-1 w-3 h-3" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                    slidesPerView={{ base: 1, sm: 2, md: 3, lg: 3, xl: 4 }}
                    spaceBetween={24}
                    height={{ base: "350px", md: "400px" }}
                    autoplayDelay={4000}
                    autoplay={true}
                    loop={true}
                  />
                </div>
              </section>
            )}

            {/* Featured Stores - Pass auth status */}
            {featuredStores.length > 0 && (
              <FeaturedStores
                stores={featuredStores}
                isAuthenticated={!!user}
                onStoreClick={() => !user && navigate("/login")}
              />
            )}

            {/* Featured Company - Pass auth status */}
            {selectedStore && featuredStores.length > 0 && (
              <FeaturedCompany
                selectedStore={selectedStore}
                stores={featuredStores}
                onStoreSelect={(store) => {
                  if (!user) {
                    navigate("/login");
                    return;
                  }
                  setSelectedStore(store);
                }}
              />
            )}
            
            {/* Most Viewed Products and Services */}
            {mostViewedItems.length > 0 && (
              <FeaturedProducts
                items={mostViewedItems}
                onViewItem={handleItemClick}
              />
            )}

            {/* Representatives Section */}
            {representatives.length > 0 && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold mb-6">
                  {t("representatives.title")}
                </h2>
                <Carousel
                  items={representatives.map((representative: any) => (
                    <div
                      key={representative.id}
                      className="bg-white p-4 rounded-lg shadow-md border border-gray-200 w-full h-full transition-all duration-300 hover:scale-105 hover:rotate-1 hover:shadow-blue-200/60"
                    >
                      <div className="flex flex-col items-center space-y-3">
                        <div className="w-16 h-16 rounded-full overflow-hidden">
                          <img
                            className="w-full h-full object-cover"
                            src={
                              representative.profilePicture &&
                              (representative.profilePicture.startsWith("data:") ||
                                representative.profilePicture.startsWith("http")
                                ? representative.profilePicture
                                : `${import.meta.env.VITE_SOCKET_URL}/uploads/${representative.profilePicture.replace(/^\/+/, '')}`)
                            }
                            alt={`${representative.firstName} ${representative.lastName}`}
                          />
                        </div>
                      </div>
                      <div className="flex flex-col items-center mt-3">
                        <h3 className="font-bold text-lg">
                          {`${representative.firstName} ${representative.lastName}`}
                        </h3>
                        <p className="text-gray-500 text-sm">
                          {`${representative.countryName || representative.country}, ${representative.cityName || representative.city}`}
                        </p>
                        <p className="text-sm">
                          {representative.email}
                        </p>
                        <p className="text-sm">
                          {representative.phoneNumber}
                        </p>
                      </div>
                    </div>
                  ))}
                  slidesPerView={{ base: 1, sm: 2, md: 3, lg: 4 }}
                  spaceBetween={24}
                  height={{ base: "300px", md: "350px" }}
                  autoplayDelay={3000}
                  autoplay={true}
                  loop={true}
                />
              </div>
            )}

            {/* Main Content */}
            <div className="mt-8">
              <div className="mb-6">
                <div className="flex flex-col lg:flex-row items-start space-y-4 lg:space-y-0 lg:space-x-8">
                  {/* Filters Section */}
                  <div className="w-full lg:w-[250px] bg-white/40 backdrop-blur-[10px] backdrop-saturate-[180%] p-3 md:p-4 rounded-lg border border-white/30">
                    <div className="flex flex-col space-y-3 md:space-y-4">
                      <h3 className="text-base md:text-lg font-semibold">
                        {t("filters.title")}
                      </h3>
                      <hr className="border-gray-200" />

                      <div>
                        <label className="block font-semibold text-sm md:text-base mb-2">
                          {t("filters.type")}
                        </label>
                        <select
                          value={type}
                          onChange={handleTypeChange}
                          className="w-full bg-white border border-gray-200 rounded-lg px-3 py-2 text-sm md:text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="all">{t("filters.allItems")}</option>
                          {filters.productTypes.map((type: string) => (
                            <option key={type} value={type}>
                              {type.charAt(0).toUpperCase() + type.slice(1)}s
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block font-semibold text-sm md:text-base mb-2">
                          {t("filters.categories")}
                        </label>
                        <div className="space-y-1 md:space-y-2">
                          {availableFilters.categories.map((category: any) => (
                            <div key={category._id}>
                              <div className="flex justify-between items-center">
                                <label className="flex items-center space-x-2 cursor-pointer">
                                  <input
                                    type="checkbox"
                                    checked={selectedCategories.includes(
                                      category._id,
                                    )}
                                    onChange={() =>
                                      handleCategoryChange(category._id)
                                    }
                                    className="rounded text-blue-600 focus:ring-blue-500"
                                  />
                                  <span className="text-sm md:text-base">
                                    {category.name}
                                  </span>
                                </label>
                                {category.children?.length > 0 && (
                                  <button
                                    aria-label={
                                      expandedCategories.includes(category._id)
                                        ? "Collapse"
                                        : "Expand"
                                    }
                                    onClick={() => toggleCategory(category._id)}
                                    className="text-gray-400 hover:text-gray-600 p-1"
                                  >
                                    {expandedCategories.includes(
                                      category._id
                                    ) ? (
                                      <ChevronUpIcon className="w-4 h-4" />
                                    ) : (
                                      <ChevronDownIcon className="w-4 h-4" />
                                    )}
                                  </button>
                                )}
                              </div>
                              {expandedCategories.includes(category._id) &&
                                category.children?.length > 0 && (
                                  <div
                                    className="pl-4 md:pl-6 mt-1 space-y-1"
                                  >
                                    {category.children?.map((child: any) => (
                                      <div key={child._id}>
                                        <div className="flex justify-between items-center">
                                          <label className="flex items-center space-x-2 cursor-pointer">
                                            <input
                                              type="checkbox"
                                              checked={selectedCategories.includes(
                                                child._id,
                                              )}
                                              onChange={() =>
                                                handleCategoryChange(child._id)
                                              }
                                              className="rounded text-blue-600 focus:ring-blue-500"
                                            />
                                            <span className="text-sm md:text-base">
                                              {child.name}
                                            </span>
                                          </label>
                                          {child.children?.length > 0 && (
                                            <button
                                              aria-label={
                                                expandedCategories.includes(child._id)
                                                  ? "Collapse"
                                                  : "Expand"
                                              }
                                              onClick={() => toggleCategory(child._id)}
                                              className="text-gray-400 hover:text-gray-600 p-1"
                                            >
                                              {expandedCategories.includes(
                                                child._id
                                              ) ? (
                                                <ChevronUpIcon className="w-4 h-4" />
                                              ) : (
                                                <ChevronDownIcon className="w-4 h-4" />
                                              )}
                                            </button>
                                          )}
                                        </div>
                                        
                                        {/* Level 3 Categories (Grandchildren) */}
                                        {expandedCategories.includes(child._id) &&
                                          child.children?.length > 0 && (
                                            <div
                                              className="pl-4 md:pl-6 mt-1 space-y-1"
                                            >
                                              {child.children?.map((grandchild: any) => (
                                                <label 
                                                  key={grandchild._id}
                                                  className="flex items-center space-x-2 cursor-pointer"
                                                >
                                                  <input
                                                    type="checkbox"
                                                    checked={selectedCategories.includes(
                                                      grandchild._id,
                                                    )}
                                                    onChange={() =>
                                                      handleCategoryChange(grandchild._id)
                                                    }
                                                    className="rounded text-blue-600 focus:ring-blue-500"
                                                  />
                                                  <span className="text-xs md:text-sm">
                                                    {grandchild.name}
                                                  </span>
                                                </label>
                                              ))}
                                            </div>
                                          )}
                                      </div>
                                    ))}
                                  </div>
                                )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Products Section */}
                  <div className="flex-1 w-full lg:w-auto p-4">
                    <div className="relative mb-4 md:mb-6">
                      <input
                        type="text"
                        placeholder={t("search.placeholder")}
                        value={searchTerm}
                        onChange={handleSearch}
                        className="w-full pl-10 pr-4 py-2 text-sm md:text-base rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                      <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
                      {paginatedProducts.map((product) => (
                        <ProductCard
                          key={product._id}
                          item={product}
                          onView={() => handleItemClick(product._id)}
                        />
                      ))}
                    </div>

                    {totalPages > 1 && (
                      <div className="flex justify-center mt-6 md:mt-8 space-x-1 md:space-x-2">
                        <MotionButton
                          whileTap={{ scale: 0.95 }}
                          onClick={() =>
                            setCurrentPage((prev) => Math.max(prev - 1, 1))
                          }
                          disabled={currentPage === 1}
                          className={`px-4 py-2 text-sm md:text-base rounded-md border ${
                            currentPage === 1
                              ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                              : 'border-blue-600 text-blue-600 hover:bg-blue-50'
                          }`}
                        >
                          Previous
                        </MotionButton>
                        {Array.from(
                          { length: totalPages },
                          (_, i) => i + 1,
                        ).map((page) => (
                          <MotionButton
                            key={page}
                            whileTap={{ scale: 0.95 }}
                            onClick={() => setCurrentPage(page)}
                            className={`px-4 py-2 text-sm md:text-base rounded-md ${
                              currentPage === page
                                ? 'bg-blue-600 text-white'
                                : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </MotionButton>
                        ))}
                        <MotionButton
                          whileTap={{ scale: 0.95 }}
                          onClick={() =>
                            setCurrentPage((prev) =>
                              Math.min(prev + 1, totalPages),
                            )
                          }
                          disabled={currentPage === totalPages}
                          className={`px-4 py-2 text-sm md:text-base rounded-md border ${
                            currentPage === totalPages
                              ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                              : 'border-blue-600 text-blue-600 hover:bg-blue-50'
                          }`}
                        >
                          Next
                        </MotionButton>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}
      </MotionDiv>

      {/* View Request Dialog */}
      {isViewDialogOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg mx-4 md:mx-0 max-w-md w-full">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg md:text-xl font-bold">
                {t("dialogs.viewRequest.title")}
              </h3>
            </div>

            <div className="px-6 py-4">
              <div className="flex flex-col items-start space-y-3 md:space-y-4">
                <p className="text-sm md:text-base">
                  {t("dialogs.viewRequest.message")}
                </p>
                {viewRequestInfo && (
                  <p className="text-sm md:text-base text-gray-600">
                    {t("dialogs.viewRequest.remaining", {
                      count: viewRequestInfo.remainingCount,
                    })}
                  </p>
                )}
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
              <button
                ref={cancelRef}
                onClick={handleViewCancel}
                className="px-4 py-2 text-sm md:text-base rounded-md border border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                {t("dialogs.viewRequest.cancel")}
              </button>
              <button
                onClick={handleViewConfirm}
                className="px-4 py-2 text-sm md:text-base rounded-md border border-teal-600 bg-teal-600 text-white hover:bg-teal-700"
              >
                {t("dialogs.viewRequest.confirm")}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Image Modal */}
      {isImageModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-[9999]">
          <div className="relative bg-transparent max-w-[90vw]">
            <button 
              onClick={() => setIsImageModalOpen(false)}
              className="absolute top-2 right-2 text-white z-10 p-2 hover:bg-black/20 rounded-full"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
            <img
              src={selectedImage || ""}
              alt="Enlarged view"
              className="w-full h-auto max-h-[90vh] object-contain"
            />

            {selectedAd && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/80 p-4 rounded-md text-center">
                <div className="flex space-x-4">
                  {selectedAd && selectedAd.itemId && selectedAd.itemId._id && (
                    <button
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded flex items-center space-x-1"
                      onClick={() => {
                        setIsImageModalOpen(false);
                        navigate(`/items/${selectedAd.itemId._id}`);
                      }}
                    >
                      <ExternalLinkIcon className="h-4 w-4" />
                      <span>{commonT("buttons.view")}</span>
                    </button>
                  )}

                  {selectedAd.storeId && (
                    <button
                      className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded flex items-center space-x-1"
                      onClick={() => {
                        setIsImageModalOpen(false);
                        // Debug logging
                        console.log('Selected Ad storeId type:', typeof selectedAd.storeId);
                        console.log('Selected Ad storeId value:', selectedAd.storeId);

                        // Correctly handle storeId as an object
                        const storeId = getStoreId(selectedAd.storeId);
                        console.log('Using storeId:', storeId);
                        navigate(`/stores/${storeId}`);
                      }}
                    >
                      <svg className="h-4 w-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 21.0001H21M3 18.0001H21M5.99998 18.0001V13.0001M9.99998 18.0001V13.0001M14 18.0001V13.0001M18 18.0001V13.0001M3 13.0001H21M4 13.0001L5.44721 5.10558C5.786 3.8153 6.93571 3.00011 8.22372 3.00011H15.7761C17.0641 3.00011 18.2138 3.8153 18.5526 5.10558L20 13.0001" 
                          stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                      <span>{commonT("visitStore")}</span>
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default Home;