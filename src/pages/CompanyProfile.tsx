import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import * as Dialog from '@radix-ui/react-dialog';
import { 
  MapPin, 
  BadgeCheck, 
  Building2, 
  Package,
  ExternalLink,
  Send,
  X,
  MessageSquare,
  Eye,
  Globe,
  Award,
  Building,
  Users,
  Calendar,
  Star,
  TrendingUp,
  Briefcase,
  Mail,
  FileText,
  Download,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

const CompanyProfile = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isMessageModalOpen, setIsMessageModalOpen] = useState(false);
  const [messageForm, setMessageForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const companyData = {
    name: "TechGlobal Solutions",
    description: "Endüstriyel yazılım çözümleri ve teknoloji danışman<PERSON><PERSON><PERSON><PERSON> alanında öncü firma",
    type: 'company', // 'company', 'individual', 'broker'
    approvalStatus: 'approved', // 'pending', 'approved', 'rejected'
    image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&q=80",
    logo: "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?auto=format&fit=crop&q=80",
    location: "İstanbul, Türkiye",
    email: "<EMAIL>",
    phone: "+90 212 123 45 67",
    website: "www.techglobal.com",
    verified: true,
    stats: {
      employees: "50-100",
      founded: "2015",
      projects: "150+",
      clients: "100+"
    },
    about: {
      description: "Endüstriyel yazılım çözümleri ve teknoloji danışmanlığı alanında öncü firma olarak, işletmelerin dijital dönüşüm süreçlerinde yanlarındayız.",
      mission: "Teknoloji ile işletmelerin dijital dönüşümüne öncülük etmek",
      vision: "Global ölçekte teknoloji çözümleri sunan lider firma olmak",
      values: [
        "Müşteri Odaklılık",
        "İnovasyon",
        "Sürdürülebilirlik",
        "Kalite"
      ]
    },
    certificates: [
      {
        id: 1,
        name: "ISO 9001:2015",
        issuer: "Bureau Veritas",
        issueDate: "2023-01-15",
        expiryDate: "2026-01-14",
        file: "iso9001.pdf"
      },
      {
        id: 2,
        name: "ISO 27001",
        issuer: "TÜV SÜD",
        issueDate: "2023-03-20",
        expiryDate: "2026-03-19",
        file: "iso27001.pdf"
      }
    ]
  };

  const getApprovalBadge = () => {
    switch (companyData.approvalStatus) {
      case 'approved':
        return (
          <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
            Onaylandı
          </span>
        );
      case 'rejected':
        return (
          <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">
            Reddedildi
          </span>
        );
      default:
        return (
          <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
            Onay Bekliyor
          </span>
        );
    }
  };

  const getCompanyTypeBadge = () => {
    switch (companyData.type) {
      case 'company':
        return (
          <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
            Kurumsal Firma
          </span>
        );
      case 'individual':
        return (
          <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
            Şahıs Firması
          </span>
        );
      case 'broker':
        return (
          <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
            Broker
          </span>
        );
    }
  };

  const handleMessageSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Message sent:', messageForm);
    setIsMessageModalOpen(false);
    setMessageForm({
      name: '',
      email: '',
      subject: '',
      message: ''
    });
  };

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Hero Section */}
        <div className="relative h-[300px] md:h-[400px]">
          <img 
            src={companyData.image}
            alt={companyData.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
          
          {/* Fixed Message Button for Mobile */}
          <button
            onClick={() => setIsMessageModalOpen(true)}
            className="md:hidden fixed bottom-20 right-4 z-50 px-4 py-3 bg-primary text-white rounded-full shadow-lg font-medium hover:bg-[#0A9996] transition-colors flex items-center space-x-2"
          >
            <MessageSquare className="h-5 w-5" />
            <span>Mesaj Gönder</span>
          </button>

          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-full max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-24 h-24 rounded-xl overflow-hidden border-4 border-white shadow-lg">
                  <img 
                    src={companyData.logo}
                    alt={`${companyData.name} logo`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    {getCompanyTypeBadge()}
                    {getApprovalBadge()}
                  </div>
                  <div className="flex items-center space-x-2">
                    <h1 className="text-3xl font-bold text-white">{companyData.name}</h1>
                    {companyData.verified && (
                      <BadgeCheck className="h-6 w-6 text-primary" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2 mt-2 text-white/90">
                    <MapPin className="h-5 w-5" />
                    <span>{companyData.location}</span>
                  </div>
                </div>
              </div>
              <button
                onClick={() => setIsMessageModalOpen(true)}
                className="hidden md:flex px-4 py-2 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors items-center space-x-2"
              >
                <MessageSquare className="h-5 w-5" />
                <span>Mesaj Gönder</span>
              </button>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Left Column */}
            <div className="md:col-span-2 space-y-8">
              {/* About Section */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Hakkında</h2>
                <div className="space-y-6">
                  <p className="text-gray-600">{companyData.about.description}</p>
                  
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Misyon</h3>
                    <p className="text-gray-600">{companyData.about.mission}</p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Vizyon</h3>
                    <p className="text-gray-600">{companyData.about.vision}</p>
                  </div>

                  <div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Değerler</h3>
                    <div className="grid grid-cols-2 gap-4">
                      {companyData.about.values.map((value, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-5 w-5 text-primary" />
                          <span className="text-gray-600">{value}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Stats Section */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <Users className="h-6 w-6 text-primary mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{companyData.stats.employees}</div>
                  <div className="text-sm text-gray-600">Çalışan</div>
                </div>
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <Calendar className="h-6 w-6 text-primary mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{companyData.stats.founded}</div>
                  <div className="text-sm text-gray-600">Kuruluş</div>
                </div>
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <Package className="h-6 w-6 text-primary mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{companyData.stats.projects}</div>
                  <div className="text-sm text-gray-600">Proje</div>
                </div>
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <Users className="h-6 w-6 text-primary mb-2" />
                  <div className="text-2xl font-bold text-gray-900">{companyData.stats.clients}</div>
                  <div className="text-sm text-gray-600">Müşteri</div>
                </div>
              </div>

              {/* Certificates Section */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">Sertifikalar</h2>
                <div className="space-y-4">
                  {companyData.certificates.map((cert) => (
                    <div key={cert.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h3 className="font-medium text-gray-900">{cert.name}</h3>
                        <p className="text-sm text-gray-600">{cert.issuer}</p>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <span>Veriliş: {cert.issueDate}</span>
                          <span>Bitiş: {cert.expiryDate}</span>
                        </div>
                      </div>
                      <button
                        onClick={() => window.open(cert.file)}
                        className="p-2 text-primary hover:text-[#0A9996] transition-colors"
                      >
                        <Download className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-8">
              {/* Contact Card */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">İletişim</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <a href={`mailto:${companyData.email}`} className="text-gray-600 hover:text-primary">
                      {companyData.email}
                    </a>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Globe className="h-5 w-5 text-gray-400" />
                    <a href={`https://${companyData.website}`} target="_blank" rel="noopener noreferrer" className="text-gray-600 hover:text-primary">
                      {companyData.website}
                    </a>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-5 w-5 text-gray-400" />
                    <span className="text-gray-600">{companyData.location}</span>
                  </div>

                  <button
                    onClick={() => setIsMessageModalOpen(true)}
                    className="w-full py-2 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors flex items-center justify-center space-x-2"
                  >
                    <MessageSquare className="h-5 w-5" />
                    <span>Mesaj Gönder</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Message Modal */}
        <Dialog.Root open={isMessageModalOpen} onOpenChange={setIsMessageModalOpen}>
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40" />
            <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-lg bg-white rounded-xl shadow-xl p-6 z-50">
              <div className="flex items-center justify-between mb-4">
                <Dialog.Title className="text-xl font-semibold text-gray-900">
                  Mesaj Gönder
                </Dialog.Title>
                <Dialog.Close className="text-gray-400 hover:text-gray-600">
                  <X className="h-5 w-5" />
                </Dialog.Close>
              </div>

              <form onSubmit={handleMessageSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Adınız Soyadınız
                  </label>
                  <input
                    type="text"
                    value={messageForm.name}
                    onChange={(e) => setMessageForm(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    E-posta Adresiniz
                  </label>
                  <input
                    type="email"
                    value={messageForm.email}
                    onChange={(e) => setMessageForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Konu
                  </label>
                  <input
                    type="text"
                    value={messageForm.subject}
                    onChange={(e) => setMessageForm(prev => ({ ...prev, subject: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mesajınız
                  </label>
                  <textarea
                    value={messageForm.message}
                    onChange={(e) => setMessageForm(prev => ({ ...prev, message: e.target.value }))}
                    className="w-full h-32 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                    required
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <Dialog.Close asChild>
                    <button
                      type="button"
                      className="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                      İptal
                    </button>
                  </Dialog.Close>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors flex items-center space-x-2"
                  >
                    <Send className="h-4 w-4" />
                    <span>Gönder</span>
                  </button>
                </div>
              </form>
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>
      </div>
    </Layout>
  );
};

export default CompanyProfile;