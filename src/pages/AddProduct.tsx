import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { 
  Package,
  Tag,
  FileText,
  Image as ImageIcon,
  Globe,
  MapPin,
  Plus,
  X,
  Upload,
  Save,
  ShoppingBag,
  Headphones,
  Calendar,
  Clock,
  ArrowUpDown
} from 'lucide-react';

type ItemType = 'product' | 'service';
type Mode = 'sale' | 'request';

const AddProduct = () => {
  const navigate = useNavigate();
  const [type, setType] = useState<ItemType>('product');
  const [mode, setMode] = useState<Mode>('sale');
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    description: '',
    features: [''],
    requirements: [''],
    deadline: '',
    duration: '',
    location: '',
    availability: 'available'
  });

  const handleFeatureChange = (index: number, value: string) => {
    const newFeatures = [...formData.features];
    newFeatures[index] = value;
    setFormData({ ...formData, features: newFeatures });
  };

  const handleRequirementChange = (index: number, value: string) => {
    const newRequirements = [...formData.requirements];
    newRequirements[index] = value;
    setFormData({ ...formData, requirements: newRequirements });
  };

  const addFeature = () => {
    setFormData({ ...formData, features: [...formData.features, ''] });
  };

  const removeFeature = (index: number) => {
    const newFeatures = formData.features.filter((_, i) => i !== index);
    setFormData({ ...formData, features: newFeatures });
  };

  const addRequirement = () => {
    setFormData({ ...formData, requirements: [...formData.requirements, ''] });
  };

  const removeRequirement = (index: number) => {
    const newRequirements = formData.requirements.filter((_, i) => i !== index);
    setFormData({ ...formData, requirements: newRequirements });
  };

  const getTitle = () => {
    if (type === 'product') {
      return mode === 'sale' ? 'Ürün Satışı' : 'Ürün Talebi';
    }
    return mode === 'sale' ? 'Hizmet Satışı' : 'Hizmet Talebi';
  };

  const getDescription = () => {
    if (type === 'product') {
      return mode === 'sale' 
        ? 'Firma profilinize yeni bir ürün ekleyin'
        : 'İhtiyacınız olan ürün için talep oluşturun';
    }
    return mode === 'sale'
      ? 'Firma profilinize yeni bir hizmet ekleyin'
      : 'İhtiyacınız olan hizmet için talep oluşturun';
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', { type, mode, formData });
    navigate('/companies/techglobal-solutions');
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {getTitle()}
            </h1>
            <p className="text-gray-600">
              {getDescription()}
            </p>

            {/* Type and Mode Selectors */}
            <div className="mt-6 space-y-4">
              {/* Type Selector */}
              <div className="flex space-x-4">
                <button
                  onClick={() => setType('product')}
                  className={`flex-1 py-3 px-4 rounded-lg flex items-center justify-center space-x-2 ${
                    type === 'product'
                      ? 'bg-[#10B981] text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <ShoppingBag className="h-5 w-5" />
                  <span>Ürün</span>
                </button>
                <button
                  onClick={() => setType('service')}
                  className={`flex-1 py-3 px-4 rounded-lg flex items-center justify-center space-x-2 ${
                    type === 'service'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <Headphones className="h-5 w-5" />
                  <span>Hizmet</span>
                </button>
              </div>

              {/* Mode Selector */}
              <div className="flex space-x-4">
                <button
                  onClick={() => setMode('sale')}
                  className={`flex-1 py-3 px-4 rounded-lg flex items-center justify-center space-x-2 ${
                    mode === 'sale'
                      ? 'bg-[#10B981] text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <ShoppingBag className="h-5 w-5" />
                  <span>Satış</span>
                </button>
                <button
                  onClick={() => setMode('request')}
                  className={`flex-1 py-3 px-4 rounded-lg flex items-center justify-center space-x-2 ${
                    mode === 'request'
                      ? 'bg-primary text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  <ArrowUpDown className="h-5 w-5" />
                  <span>Talep</span>
                </button>
              </div>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Basic Information */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Temel Bilgiler</h2>
              
              <div className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    {mode === 'sale' ? `${type === 'product' ? 'Ürün' : 'Hizmet'} Adı` : 'Talep Başlığı'}*
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder={mode === 'sale' 
                      ? `${type === 'product' ? 'Ürün' : 'Hizmet'} adını girin`
                      : `${type === 'product' ? 'Ürün' : 'Hizmet'} talebinizi girin`}
                    required
                  />
                </div>

                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                    Kategori*
                  </label>
                  <div className="relative">
                    <Tag className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      id="category"
                      value={formData.category}
                      onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                      className="w-full pl-12 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder={`${type === 'product' ? 'Ürün' : 'Hizmet'} kategorisi`}
                      required
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                    {mode === 'sale' ? 'Açıklama' : 'Talep Detayları'}*
                  </label>
                  <div className="relative">
                    <FileText className="absolute left-4 top-4 text-gray-400" />
                    <textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="w-full pl-12 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent h-32 resize-none"
                      placeholder={mode === 'sale'
                        ? `${type === 'product' ? 'Ürününüzü' : 'Hizmetinizi'} detaylı bir şekilde açıklayın`
                        : `İhtiyacınız olan ${type === 'product' ? 'ürünü' : 'hizmeti'} detaylı bir şekilde açıklayın`}
                      required
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Features or Requirements */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">
                {mode === 'sale' ? 'Özellikler' : 'Gereksinimler'}
              </h2>
              
              <div className="space-y-4">
                {(mode === 'sale' ? formData.features : formData.requirements).map((item, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={item}
                      onChange={(e) => mode === 'sale' 
                        ? handleFeatureChange(index, e.target.value)
                        : handleRequirementChange(index, e.target.value)
                      }
                      placeholder={`${index + 1}. ${mode === 'sale' ? 'özellik' : 'gereksinim'}`}
                      className="flex-1 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                    <button
                      type="button"
                      onClick={() => mode === 'sale' ? removeFeature(index) : removeRequirement(index)}
                      className="p-2 text-red-500 hover:text-red-600"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                ))}
                
                <button
                  type="button"
                  onClick={mode === 'sale' ? addFeature : addRequirement}
                  className="flex items-center space-x-2 text-primary hover:text-[#0A9996]"
                >
                  <Plus className="h-5 w-5" />
                  <span>{mode === 'sale' ? 'Özellik' : 'Gereksinim'} Ekle</span>
                </button>
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">
                {mode === 'sale' ? 'Teslimat Bilgileri' : 'Zaman Bilgileri'}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="deadline" className="block text-sm font-medium text-gray-700 mb-2">
                    {mode === 'sale' ? 'Teslimat Süresi' : 'Son Teslim Tarihi'}
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type={mode === 'sale' ? 'text' : 'date'}
                      id="deadline"
                      value={formData.deadline}
                      onChange={(e) => setFormData({ ...formData, deadline: e.target.value })}
                      className="w-full pl-12 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder={mode === 'sale' ? 'Örn: 3-5 iş günü' : ''}
                    />
                  </div>
                </div>

                {type === 'service' && (
                  <div>
                    <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-2">
                      {mode === 'sale' ? 'Hizmet Süresi' : 'Proje Süresi'}
                    </label>
                    <div className="relative">
                      <Clock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                      <input
                        type="text"
                        id="duration"
                        value={formData.duration}
                        onChange={(e) => setFormData({ ...formData, duration: e.target.value })}
                        placeholder="Örn: 2 ay"
                        className="w-full pl-12 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Location */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Konum Bilgisi</h2>
              
              <div>
                <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                  {mode === 'sale' 
                    ? `${type === 'product' ? 'Ürün' : 'Hizmet'} Konumu`
                    : 'Tercih Edilen Konum'}
                </label>
                <div className="relative">
                  <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    id="location"
                    value={formData.location}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                    className="w-full pl-12 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder={mode === 'sale' ? 'Konum bilgisi' : 'Örn: İstanbul veya Uzaktan'}
                  />
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => navigate('/companies/techglobal-solutions')}
                className="px-6 py-3 text-gray-600 hover:text-gray-800"
              >
                İptal
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors flex items-center space-x-2"
              >
                <Save className="h-5 w-5" />
                <span>
                  {mode === 'sale' 
                    ? `${type === 'product' ? 'Ürünü' : 'Hizmeti'} Yayınla`
                    : 'Talebi Yayınla'}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </Layout>
  );
};

export default AddProduct;