import React, { useState, useEffect } from "react";
import { useNavigate, Link, useLocation } from "react-router-dom";
import { Mail, Lock, Eye, EyeOff } from "lucide-react";
import { loginUser } from "../api";
import { useTranslation } from "react-i18next";
import { useAuth } from "../context/AuthContext";
import OfflineHeader from "../components/OfflineHeader";

const Login: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const { t } = useTranslation(["login", "common"]);
  const { login } = useAuth();
  
  // Get return URL from location state if it exists
  const returnUrl = location.state?.returnUrl || "/";

  useEffect(() => {
    document.title = t("login:title") + " | " + t("common:appName");
  }, [t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Basic validation
    if (!email || !password) {
      setError(t("login:form.errors.allFieldsRequired") || "Please fill in all fields");
      return;
    }

    setIsLoading(true);
    try {
      const response = await loginUser({ email, password });
      const { user, token } = response;

      if (!token) {
        throw new Error("No token received from server");
      }

      login(user, token);
      // Navigate to the return URL instead of root
      navigate(returnUrl);
    } catch (err: any) {
      setError(err.response?.data?.message || t("login:toasts.loginFailed.description") || "Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => setShowPassword(!showPassword);

  return (
    <div className="relative w-full h-screen flex flex-col overflow-hidden">
      <OfflineHeader />

      <div
        className="relative flex-1 w-full bg-cover bg-center"
        style={{ backgroundImage: `url("/bg.jpg")` }}
      >
        {/* Blur overlay */}
        <div className="absolute inset-0 bg-black/30 backdrop-blur-md"></div>

        {/* Main Content */}
        <div className="min-h-[calc(100vh-80px)] flex items-center justify-center py-12 px-4 md:px-6 lg:px-8 relative">
          <div className="w-full max-w-md relative z-10">
            <div className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl p-8">
              <div className="text-center mb-8">
                <h1 className="text-2xl font-bold text-gray-900">
                  {t('login:form.title')}
                </h1>
                <p className="mt-2 text-gray-600">
                  {t('login:form.welcomeMessage')}
                </p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                {error && (
                  <div className="bg-red-50 text-red-600 p-4 rounded-lg text-sm">
                    {error}
                  </div>
                )}

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('login:form.emailLabel')}
                  </label>
                  <div className="relative">
                    <input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      placeholder={t('login:form.emailPlaceholder')}
                      className="w-full pl-12 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white/80"
                    />
                    <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  </div>
                </div>

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                    {t('login:form.passwordLabel')}
                  </label>
                  <div className="relative">
                    <input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                      placeholder={t('login:form.passwordPlaceholder')}
                      className="w-full pl-12 pr-12 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white/80"
                    />
                    <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? (
                        <EyeOff className="h-5 w-5" />
                      ) : (
                        <Eye className="h-5 w-5" />
                      )}
                    </button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <input
                      id="remember"
                      type="checkbox"
                      checked={rememberMe}
                      onChange={() => setRememberMe(!rememberMe)}
                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                    />
                    <label htmlFor="remember" className="ml-2 block text-sm text-gray-700">
                      {t('login:form.rememberMe')}
                    </label>
                  </div>
                  <Link
                    to="/forgot-password"
                    className="text-sm font-medium text-primary hover:text-[#0A9996]"
                  >
                    {t('login:form.forgotPasswordLink')}
                  </Link>
                </div>

                <button
                  type="submit"
                  className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-[#0A9996] transition-colors"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <span className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      {t('login:form.loggingIn')}
                    </span>
                  ) : (
                    t('login:form.loginButtonText')
                  )}
                </button>
              </form>

              <div className="text-center mt-6">
                <p className="text-sm text-gray-600">
                  {t('login:form.noAccountPrompt')}{' '}
                  <Link
                    to="/register"
                    className="font-medium text-primary hover:text-[#0A9996]"
                  >
                    {t('login:form.registerNowLink')}
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;