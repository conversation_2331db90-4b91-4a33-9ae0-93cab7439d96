import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useToast } from '@chakra-ui/react';
import {
  Check,
  Globe,
  PenTool,
  Layers,
  ShoppingCart,
  Clock,
  FileImage,
  Smartphone,
  Paintbrush
} from 'lucide-react';
import { api } from '../api';
import { useAuth } from '../context/AuthContext';
import DesignPackagePaymentModal from '../components/DesignPackagePaymentModal';

interface DesignPackage {
  _id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  currency: string;
  icon: string;
  features: string[];
  featuresEn: string[];
  deliveryTime: number;
  revisionCount: number;
  isPopular: boolean;
  isActive: boolean;
  order: number;
}

const iconMap: { [key: string]: any } = {
  Globe: Globe,
  Layers: Layers,
  PenTool: PenTool,
  Brush: Paintbrush
};

const DesignPackages: React.FC = () => {
  const { t, i18n } = useTranslation('packages');
  const navigate = useNavigate();
  const toast = useToast();
  const { isAuthenticated } = useAuth();
  const [isOpen, setIsOpen] = useState<boolean>(false);

  const [packages, setPackages] = useState<DesignPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPackage, setSelectedPackage] = useState<DesignPackage | null>(null);

  useEffect(() => {
    fetchDesignPackages();
  }, []);

  const fetchDesignPackages = async () => {
    try {
      const response = await api.get('/design-packages');
      setPackages(response.data.packages);
    } catch (error) {
      toast({
        title: t('error.fetchPackages', 'Paketler alınırken hata oluştu'),
        description: t('error.tryAgain', 'Lütfen daha sonra tekrar deneyin'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = (pkg: DesignPackage) => {
    if (!isAuthenticated) {
      // Save the intended action and redirect to login
      sessionStorage.setItem('redirectAfterLogin', window.location.pathname);
      sessionStorage.setItem('selectedDesignPackage', JSON.stringify(pkg));
      navigate('/login');
      return;
    }

    setSelectedPackage(pkg);
    setIsOpen(true);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="w-full mx-auto px-4 md:px-6 lg:px-8 py-8 md:py-16">
        <div className="text-center">
          <span className="inline-block px-3 py-1 md:px-4 md:py-2 rounded-lg bg-primary/10 text-primary text-xs md:text-sm font-medium mb-3 md:mb-4">
            {t('designPackages.badge') || 'Profesyonel Tasarım Çözümleri'}
          </span>
          <h1 className="text-2xl md:text-4xl font-bold text-gray-900 mb-3 md:mb-4 leading-tight">
            {t('designPackages.title') || 'Markanızı Öne Çıkaracak Tasarım Paketleri'}
          </h1>
          <p className="text-sm md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            {t('designPackages.subtitle') || 'İhracat yolculuğunuzda markanızı global pazarda güçlendirecek profesyonel tasarım çözümleriyle yanınızdayız.'}
          </p>
        </div>
      </div>

      {/* Packages Grid */}
      <div className="w-full mx-auto px-4 md:px-6 lg:px-8 pb-8 md:pb-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-8">
          {packages.map((pkg) => {
            const IconComponent = iconMap[pkg.icon] || Layers;
            const features = i18n.language === 'en' ? pkg.featuresEn : pkg.features;

            return (
              <div
                key={pkg._id}
                className={`relative rounded-2xl ${pkg.isPopular
                  ? 'border-2 border-primary md:scale-105 shadow-xl'
                  : 'border border-gray-200 shadow-sm'
                  } bg-white p-4 md:p-8 transform transition-all duration-200 hover:shadow-lg`}
                onClick={() => handlePurchase(pkg)}
              >
                {pkg.isPopular && (
                  <div className="absolute -top-3 md:-top-5 left-1/2 transform -translate-x-1/2">
                    <span className="inline-block px-3 py-1 md:px-4 md:py-2 rounded-full bg-primary text-white text-xs md:text-sm font-medium">
                      {t('designPackages.mostPopular') || 'En Çok Tercih Edilen'}
                    </span>
                  </div>
                )}

                <div className="flex items-center space-x-3 mb-3 md:mb-4">
                  <div className={`w-10 h-10 md:w-12 md:h-12 rounded-lg flex items-center justify-center ${pkg.isPopular ? 'bg-primary text-white' : 'bg-primary/10 text-primary'
                    }`}>
                    <IconComponent className="w-5 h-5 md:w-6 md:h-6" />
                  </div>
                  <h3 className="text-lg md:text-xl font-semibold text-gray-900 leading-tight">{i18n.language === 'en' ? pkg.nameEn : pkg.name}</h3>
                </div>

                <p className="text-sm md:text-base text-gray-600 mb-4 md:mb-6 leading-relaxed">
                  {i18n.language === 'en' ? pkg.descriptionEn : pkg.description}
                </p>

                <div className="mb-6 md:mb-8">
                  <div className="flex items-baseline">
                    <span className="text-3xl md:text-4xl font-bold text-gray-900">${pkg.price}</span>
                    <span className="ml-2 text-sm md:text-base text-gray-500">{t('designPackages.oneTime') || '/tek seferlik'}</span>
                  </div>
                </div>

                <ul className="space-y-2 md:space-y-4 mb-6 md:mb-8">
                  {features.map((feature, idx) => (
                    <li key={idx} className="flex items-start space-x-3">
                      <Check className="h-4 w-4 md:h-5 md:w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span className="text-sm md:text-base text-gray-600 leading-relaxed">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  className={`w-full py-3 md:py-4 rounded-lg flex items-center justify-center space-x-2 text-sm md:text-base font-medium transition-all duration-200 ${pkg.isPopular
                    ? 'bg-primary text-white hover:bg-primary/90'
                    : 'bg-primary/10 text-primary hover:bg-primary hover:text-white'
                    }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    handlePurchase(pkg);
                  }}
                >
                  <ShoppingCart className="w-4 h-4 md:w-5 md:h-5" />
                  <span>{t('designPackages.buyNow') || 'Satın Al'}</span>
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Features Section */}
      <div className="bg-gray-50 py-8 md:py-16">
        <div className="w-full mx-auto px-4 md:px-6 lg:px-8">
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-3 md:mb-4">
              {'Neden Bizimle Çalışmalısınız?'}
            </h2>
            <p className="text-sm md:text-lg text-gray-600">
              {'Profesyonel tasarım ekibimiz ve deneyimimizle markanızı bir adım öne taşıyoruz'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-8">
            <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 md:mb-4">
                <Paintbrush className="w-5 h-5 md:w-6 md:h-6 text-primary" />
              </div>
              <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2">
                {'Özgün Tasarımlar'}
              </h3>
              <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                {'Her marka için özel olarak tasarlanan, özgün ve akılda kalıcı çözümler'}
              </p>
            </div>

            <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 md:mb-4">
                <Clock className="w-5 h-5 md:w-6 md:h-6 text-primary" />
              </div>
              <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2">
                {'Hızlı Teslimat'}
              </h3>
              <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                {'Projeleriniz için özenle belirlenen zaman planına uygun teslimat'}
              </p>
            </div>

            <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 md:mb-4">
                <FileImage className="w-5 h-5 md:w-6 md:h-6 text-primary" />
              </div>
              <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2">
                {'Kaynak Dosyalar'}
              </h3>
              <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                {'Tüm çalışmalarınız için düzenlenebilir kaynak dosyalar size teslim edilir'}
              </p>
            </div>

            <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm">
              <div className="w-10 h-10 md:w-12 md:h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-3 md:mb-4">
                <Smartphone className="w-5 h-5 md:w-6 md:h-6 text-primary" />
              </div>
              <h3 className="text-base md:text-lg font-semibold text-gray-900 mb-2">
                {'Mobil Uyumluluk'}
              </h3>
              <p className="text-sm md:text-base text-gray-600 leading-relaxed">
                {'Tüm tasarımlarımız mobil cihazlara tam uyumlu olarak geliştirilir'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {isOpen && selectedPackage && (
        <DesignPackagePaymentModal
          isOpen={isOpen}
          onClose={() => setIsOpen(false)}
          package={selectedPackage}
        />
      )}
    </div>
  );
};

export default DesignPackages;