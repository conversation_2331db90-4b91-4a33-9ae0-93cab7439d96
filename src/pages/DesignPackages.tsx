import React from 'react';
import Layout from '../components/Layout';
import { 
  Palette,
  Brush,
  Globe,
  Check,
  ShoppingCart,
  Layers,
  FileImage,
  Clock,
  MonitorSmartphone,
  Database,
  Smartphone,
  Laptop,
  PenTool,
  FileType,
  Repeat,
  Layout as LayoutIcon
} from 'lucide-react';

const DesignPackages = () => {
  const packages = [
    {
      name: "Web Sitesi Tasarımı",
      description: "Modern ve profesyonel web sitesi tasarımı ile markanızı dijitalde güçlendirin.",
      price: "150",
      icon: Globe,
      features: [
        "Responsive tasarım",
        "5 sayfa tasarımı",
        "UI/UX odaklı tasarım",
        "SEO uyumlu yapı",
        "Sosyal medya entegrasyonu",
        "İletişim formu",
        "3 revizyon hakkı",
        "<PERSON><PERSON><PERSON> dosyalar (Figma, XD)"
      ],
      isPopular: false
    },
    {
      name: "Kurumsal Kimlik Tasarımı",
      description: "Markanızı yansıtan profesyonel kurumsal kimlik tasarımı ile fark yaratın.",
      price: "200",
      icon: Layers,
      features: [
        "Logo tasarımı",
        "Kartvizit tasarımı",
        "Antetli kağıt tasarımı",
        "Zarf tasarımı",
        "Dosya tasarımı",
        "E-posta imzası",
        "5 revizyon hakkı",
        "Tüm kaynak dosyalar"
      ],
      isPopular: true
    },
    {
      name: "Logo Tasarımı",
      description: "Yeni başlayan ihracatçılar için temel ihtiyaçlara odaklı ekonomik çözüm.",
      price: "100",
      icon: PenTool,
      features: [
        "Özgün logo tasarımı",
        "3 farklı konsept çalışması",
        "Kaynak dosyalar (AI, PNG, SVG)",
        "3 revizyon hakkı",
        "Vektörel çalışma",
        "Farklı versiyonlar",
        "Renk alternatifleri",
        "Kullanım kılavuzu"
      ],
      isPopular: false
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-white">
        {/* Header Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <span className="inline-block px-4 py-2 rounded-lg bg-primary/10 text-primary text-sm font-medium mb-4">
              Profesyonel Tasarım Çözümleri
            </span>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Markanızı Öne Çıkaracak Tasarım Paketleri
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              İhracat yolculuğunuzda markanızı global pazarda güçlendirecek profesyonel tasarım çözümleriyle yanınızdayız.
            </p>
          </div>
        </div>

        {/* Packages Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div 
                key={index}
                className={`relative rounded-2xl ${
                  pkg.isPopular 
                    ? 'border-2 border-primary scale-105 shadow-xl' 
                    : 'border border-gray-200 shadow-sm'
                } bg-white p-8 transform transition-all duration-200 hover:shadow-lg`}
              >
                {pkg.isPopular && (
                  <div className="absolute -top-5 left-1/2 transform -translate-x-1/2">
                    <span className="inline-block px-4 py-2 rounded-full bg-primary text-white text-sm font-medium">
                      En Çok Tercih Edilen
                    </span>
                  </div>
                )}

                <div className="flex items-center space-x-3 mb-4">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    pkg.isPopular ? 'bg-primary text-white' : 'bg-primary/10 text-primary'
                  }`}>
                    <pkg.icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{pkg.name}</h3>
                </div>

                <p className="text-gray-600 mb-6">
                  {pkg.description}
                </p>

                <div className="mb-8">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">${pkg.price}</span>
                    <span className="ml-2 text-gray-500">/tek seferlik</span>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-3">
                      <Check className="h-5 w-5 text-primary flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button 
                  className={`w-full py-4 rounded-lg flex items-center justify-center space-x-2 text-base font-medium transition-all duration-200 ${
                    pkg.isPopular
                      ? 'bg-primary text-white hover:bg-primary/90'
                      : 'bg-primary/10 text-primary hover:bg-primary hover:text-white'
                  }`}
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>Satın Al</span>
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Features Section */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900">
                Neden Bizimle Çalışmalısınız?
              </h2>
              <p className="mt-4 text-lg text-gray-600">
                Profesyonel tasarım ekibimiz ve deneyimimizle markanızı bir adım öne taşıyoruz
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Brush className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Özgün Tasarımlar
                </h3>
                <p className="text-gray-600">
                  Her marka için özel olarak tasarlanan, özgün ve akılda kalıcı çözümler
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <Clock className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Hızlı Teslimat
                </h3>
                <p className="text-gray-600">
                  Projeleriniz için özenle belirlenen zaman planına uygun teslimat
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <FileImage className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Kaynak Dosyalar
                </h3>
                <p className="text-gray-600">
                  Tüm tasarımların kaynak dosyaları ile birlikte teslimat
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-sm">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <MonitorSmartphone className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Responsive Tasarım
                </h3>
                <p className="text-gray-600">
                  Tüm cihazlarda kusursuz görüntülenen modern tasarımlar
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default DesignPackages;