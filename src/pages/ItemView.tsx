import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  MapPin,
  BadgeCheck,
  Building2,
  Package,
  MessageSquare,
  Eye,
} from "lucide-react";
import {
  Box,
  Container,
  Heading,
  Text,
  Button,
  Image,
  VStack,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useToast,
  Skeleton,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Grid,
  GridItem,
  AspectRatio,
  Flex,
  Divider,
  SimpleGrid,
  useColorModeValue,
} from "@chakra-ui/react";
import {
  getItemById,
  checkViewRequest,
  useViewRequest,
  getActiveSubscription,
} from "../api";
import ProductMessage from "../components/ProductMessage";
import { useSocket } from "../context/SocketContext";
import { useRef } from "react";
import { useAuthCheck } from "@/hooks/useAuthCheck";

const ItemView: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [item, setItem] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [viewRequestInfo, setViewRequestInfo] = useState<any>(null);
  const [subscription, setSubscription] = useState<any>(null);
  const {
    isOpen: isMessageOpen,
    onOpen: onMessageOpen,
    onClose: onMessageClose,
  } = useDisclosure();
  const {
    isOpen: isUpgradeDialogOpen,
    onOpen: onUpgradeDialogOpen,
    onClose: onUpgradeDialogClose,
  } = useDisclosure();
  const {
    isOpen: isImageModalOpen,
    onOpen: onImageModalOpen,
    onClose: onImageModalClose,
  } = useDisclosure();

  const { socket } = useSocket();
  const toast = useToast();
  const { user } = useAuthCheck();
  const navigate = useNavigate();
  const { t } = useTranslation("productDetail");
  const cancelRef = useRef<any>(null);



  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  const handleItemFetchError = (error: any) => {
    // Handle error when fetching item data
    if (error.response?.status === 404) {
      toast({
        title: t("errors.itemNotFound.title"),
        description: t("errors.itemNotFound.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } else {
      toast({
        title: t("errors.fetchFailed.title"),
        description: t("errors.tryAgain.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
    navigate("/");
  };

  const fetchItemAndCheckPermission = async () => {
    if (!id) return;

    try {
      setIsLoading(true);
      // Reset view request info to prevent stale data
      setViewRequestInfo(null);
      // Check if user has permission to view this item
      try {
        // First check if user has permission to view
        const response = await checkViewRequest(id);
        console.log(`[FRONTEND DEBUG] checkViewRequest response for item ${id}:`, response);
        // Store view request information
        setViewRequestInfo(response);

        // If user has permission to view
        if (
          response.isOwner ||
          response.alreadyViewed ||
          response.hasRemaining
        ) {
          // User has permission to view, proceed with fetching item data
          try {
            const itemData = await getItemById(id);
            // Set item data in state
            setItem(itemData);
            if (itemData.images && itemData.images.length > 0) {
              setSelectedImage(itemData.images[0]);
            }
            socket?.emit("joinProductRoom", id);

            // If they haven't viewed it before and aren't the owner, use a view request
            if (
              !response.isOwner &&
              !response.alreadyViewed &&
              response.hasRemaining
            ) {
              console.log(`[FRONTEND DEBUG] Using view request for item ${id}`);
              // No need for try/catch here as useViewRequest already handles 404 errors
              await useViewRequest(id);
              console.log(`[FRONTEND DEBUG] View request used, checking again`);
              // Update view request info after using a view
              const updatedResponse = await checkViewRequest(id);
              console.log(`[FRONTEND DEBUG] Updated checkViewRequest response:`, updatedResponse);
              setViewRequestInfo(updatedResponse);
            }

            // Fetch subscription data
            try {
              const subResponse = await getActiveSubscription();
              setSubscription(subResponse.subscription);
            } catch (error) {
              // Silently handle subscription fetch errors
            }
          } catch (error: any) {
            handleItemFetchError(error);
            return;
          }
        } else {
          toast({
            title: response.message || t("errors.noViewRequests.title"),
            description: t("errors.upgradeRequired.description"),
            status: "warning",
            duration: 5000,
            isClosable: true,
          });
          navigate("/packages");
          return;
        }
      } catch (error: any) {
        // If we get a 404, it means the API endpoint doesn't exist yet
        // In this case, we should still show the item details
        if (error.response?.status === 404) {
          // Continue anyway as view API might not be available yet
          try {
            const itemData = await getItemById(id);
            // Set item data in state
            setItem(itemData);
            if (itemData.images && itemData.images.length > 0) {
              setSelectedImage(itemData.images[0]);
            }
            socket?.emit("joinProductRoom", id);
            
            // Fetch subscription data
            try {
              const subResponse = await getActiveSubscription();
              setSubscription(subResponse.subscription);
            } catch (error) {
              // Silently handle subscription fetch errors
            }
          } catch (error: any) {
            handleItemFetchError(error);
            return;
          }
        } else if (error.response?.status === 403) {
          // Handle 403 Forbidden specifically
          toast({
            title: t("errors.noSubscription.title"),
            description: t("errors.upgradeRequired.description"),
            status: "warning",
            duration: 5000,
            isClosable: true,
          });
          navigate("/packages");
          return;
        } else {
          // Handle other errors from checkViewRequest
          toast({
            title: t("errors.accessDenied.title"),
            description: t("errors.tryAgain.description"),
            status: "error",
            duration: 5000,
            isClosable: true,
          });
          navigate("/");
          return;
        }
      }
    } catch (error: any) {
      // Handle any unexpected errors in the main try/catch block
      toast({
        title: t("errors.fetchFailed.title"),
        description: t("errors.fetchFailed.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      navigate("/");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchItemAndCheckPermission();
    return () => {
      socket?.emit("leaveProductRoom", id);
    };
  }, [id]);

  const handleMessageClick = () => {
    // Comprehensive checking of all possible ways messaging could be allowed
    // 1. Direct messagingAllowed property on subscription
    const hasDirectMessagingAllowed = !!subscription?.messagingAllowed;

    // 2. Check if the main package allows messaging
    const hasPackageWithMessaging = (() => {
      // If packageId is an object with messagingAllowed property
      if (subscription?.packageId?.messagingAllowed === true) {
        return true;
      }

      // If packageId has features array that includes messaging
      if (Array.isArray(subscription?.packageId?.features) &&
        subscription.packageId.features.includes("basic_messaging")) {
        return true;
      }

      // If subscription has features object with messagingAllowed
      if (subscription?.features?.messagingAllowed === true) {
        return true;
      }

      return false;
    })();

    // 3. Check if any addon package allows messaging
    const hasAddonWithMessaging = (() => {
      if (!Array.isArray(subscription?.addons) || subscription.addons.length === 0) {
        return false;
      }

      return subscription.addons.some((addon: any) => {
        // Check addon's packageId for messagingAllowed
        if (addon?.packageId?.messagingAllowed === true) {
          return true;
        }

        // Check addon's packageId features for basic_messaging
        if (Array.isArray(addon?.packageId?.features) &&
          addon.packageId.features.includes("basic_messaging")) {
          return true;
        }

        return false;
      });
    })();

    // Combined check for any messaging capability
    const hasBasicMessaging = hasDirectMessagingAllowed || hasPackageWithMessaging || hasAddonWithMessaging;

    if (!hasBasicMessaging) {
      // User does not have messaging permission
      onUpgradeDialogOpen();
    } else {
      // User has messaging permission
      onMessageOpen();
    }
  };

  if (isLoading) {
    return (
      <Container maxW="container.xl" py={8} bg="gray.50" minH="100vh">
        <VStack spacing={8} align="stretch">
          <Grid templateColumns={{ base: "1fr", md: "3fr 1fr" }} gap={8}>
            <GridItem>
              <Skeleton height="400px" borderRadius="xl" />
              <SimpleGrid columns={4} gap={4} mt={4}>
                {[1, 2, 3, 4].map((i) => (
                  <Skeleton key={i} height="80px" borderRadius="lg" />
                ))}
              </SimpleGrid>
              <Skeleton height="200px" mt={8} borderRadius="xl" />
            </GridItem>
            <GridItem>
              <Skeleton height="300px" borderRadius="xl" />
            </GridItem>
          </Grid>
        </VStack>
      </Container>
    );
  }

  if (!item) {
    return null;
  }

  const imageUrl = (path: string) => {
    if (path.startsWith('http')) {
      return path;
    }
    return `${import.meta.env.VITE_SOCKET_URL}/${path}`;
  };

  const displayedImage = selectedImage || (item.images?.length > 0 ? item.images[0] : null);

  return (
    <Box bg="gray.50" minH="100vh">
      <Container maxW="7xl" py={12} px={{ base: 4, md: 6, lg: 8 }}>
        <Grid templateColumns={{ base: "1fr", lg: "3fr 1fr" }} gap={8}>
          {/* Left Column - Images and Details */}
          <GridItem>
            <VStack spacing={8} align="stretch">
              {/* Main Image */}
              <Box
                bg="white"
                borderRadius="xl"
                overflow="hidden"
                position="relative"
                cursor="pointer"
                onClick={onImageModalOpen}
                shadow="sm"
              >
                {displayedImage ? (
                  <AspectRatio ratio={4 / 3}>
                    <Image
                      src={imageUrl(displayedImage)}
                      alt={item.name}
                      objectFit="cover"
                      w="100%"
                      h="100%"
                    />
                  </AspectRatio>
                ) : (
                  <AspectRatio ratio={4 / 3}>
                    <Flex
                      bg="gray.100"
                      alignItems="center"
                      justifyContent="center"
                    >
                      <Text color="gray.500">{t("noImage")}</Text>
                    </Flex>
                  </AspectRatio>
                )}
              </Box>

              {/* Thumbnail Images */}
              {item.images && item.images.length > 0 && (
                <SimpleGrid columns={4} gap={4}>
                  {item.images.map((image: string, index: number) => (
                    <Box
                      key={index}
                      borderRadius="lg"
                      overflow="hidden"
                      cursor="pointer"
                      borderWidth="2px"
                      borderColor={selectedImage === image ? "teal.500" : "transparent"}
                      _hover={{ borderColor: selectedImage === image ? "teal.500" : "gray.200" }}
                      transition="all 0.2s"
                      onClick={() => setSelectedImage(image)}
                    >
                      <AspectRatio ratio={1}>
                        <Image
                          src={imageUrl(image)}
                          alt={`${item.name} ${index + 1}`}
                          objectFit="cover"
                        />
                      </AspectRatio>
                    </Box>
                  ))}
                </SimpleGrid>
              )}

              {/* Description */}
              <Box
                bg="white"
                borderRadius="xl"
                p={6}
                shadow="sm"
              >
                <Heading as="h2" size="lg" mb={4}>
                  {t("details")}
                </Heading>
                <Box
                  dangerouslySetInnerHTML={{ __html: item.description }}
                  sx={{
                    img: {
                      maxWidth: "100%",
                      height: "auto",
                    },
                  }}
                />
              </Box>

              {/* Features/Specifications if available */}
              {item.features && item.features.length > 0 && (
                <Box
                  bg="white"
                  borderRadius="xl"
                  p={6}
                  shadow="sm"
                >
                  <Heading as="h2" size="lg" mb={4}>
                    {t("features")}
                  </Heading>
                  <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                    {item.features.map((feature: string, index: number) => (
                      <Flex key={index} align="center" gap={2}>
                        <Package size={20} color="teal" />
                        <Text color="gray.700">{feature}</Text>
                      </Flex>
                    ))}
                  </SimpleGrid>
                </Box>
              )}
            </VStack>
          </GridItem>

          {/* Right Column - Info Card */}
          <GridItem>
            <Box
              bg="white"
              borderRadius="xl"
              p={6}
              shadow="sm"
              position="sticky"
              top="20px"
            >
              <VStack align="stretch" spacing={4}>
                <Box>
                  <Flex align="center" gap={2} mb={2}>
                    <Box
                      px={3}
                      py={1.5}
                      borderRadius="full"
                      bg={item.listingType === "sale" ? "green.100" : "teal.100"}
                      color={item.listingType === "sale" ? "green.700" : "teal.700"}
                      fontSize="sm"
                      fontWeight="medium"
                    >
                      {item.listingType === "sale" ? t("sale") : t("demand")}
                    </Box>
                    <Flex align="center" gap={1} color="gray.500">
                      <Eye size={16} />
                      <Text fontSize="sm" title={t("item.viewCount", "View count")}>
                        {viewRequestInfo?.totalViews || (typeof item?.viewCount === 'number' ? item.viewCount : 0)}
                      </Text>
                    </Flex>
                  </Flex>
                  <Heading as="h1" size="xl" mb={2}>
                    {item.name}
                  </Heading>

                  {item.store && (
                    <Flex align="center" mt={2} gap={1}>
                      <Text fontWeight="medium" color="teal.500">
                        {item.store.name}
                      </Text>
                      <BadgeCheck size={18} color="teal" />
                    </Flex>
                  )}

                  {item.store?.location && (
                    <Flex align="center" mt={2} gap={2} color="gray.600">
                      <MapPin size={16} />
                      <Text>{item.store.location?.city || item.store.location?.country || "Turkey"}</Text>
                    </Flex>
                  )}

                  <Text fontSize="sm" color="gray.500" mt={4}>
                    {t("item.category")}: {item.category?.name || item.category?.nameEn || t("notSpecified")}
                  </Text>
                  <Text fontSize="sm" color="gray.500">
                    {t("item.type")}: {item.type === "product" ? t("product") : t("service")}
                  </Text>
                </Box>

                <Divider my={2} />

                {item.store && (
                  <VStack spacing={4}>
                    <Button
                      w="full"
                      py={3}
                      colorScheme="teal"
                      leftIcon={<Building2 size={18} />}
                      onClick={() => navigate(`/stores/${item.store?._id}`)}
                    >
                      Firma Profilini Görüntüle
                    </Button>

                    {user && user._id !== item.store?.owner?._id && (
                      <Button
                        w="full"
                        py={3}
                        bg="teal.50"
                        color="teal.700"
                        _hover={{ bg: "teal.500", color: "white" }}
                        transition="all 0.3s"
                        leftIcon={<MessageSquare size={18} />}
                        onClick={handleMessageClick}
                      >
                        Mesaj Gönder
                      </Button>
                    )}
                  </VStack>
                )}
              </VStack>
            </Box>
          </GridItem>
        </Grid>
      </Container>

      {/* Message Modal */}
      {item.store && (
        <Modal isOpen={isMessageOpen} onClose={onMessageClose} size="xl">
          <ModalOverlay backdropFilter="blur(3px)" />
          <ModalContent borderRadius="xl" shadow="2xl">
            <ModalHeader>{t("modals.message.title")}</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <ProductMessage
                productId={item._id}
                sellerId={{
                  _id: item.store?.owner?._id || "",
                  firstName: item.store?.owner?.firstName || "",
                  lastName: item.store?.owner?.lastName || "",
                  phoneNumber: item.store?.owner?.phoneNumber || "",
                  email: item.store?.owner?.email || "",
                }}
                productName={item.name}
                onMessageSent={onMessageClose}
              />
            </ModalBody>
          </ModalContent>
        </Modal>
      )}

      {/* Upgrade Dialog */}
      <AlertDialog
        isOpen={isUpgradeDialogOpen}
        leastDestructiveRef={cancelRef}
        onClose={onUpgradeDialogClose}
        isCentered
      >
        <AlertDialogOverlay backdropFilter="blur(10px)" />
        <AlertDialogContent
          bg={useColorModeValue("white", "gray.800")}
          boxShadow="xl"
          borderRadius="xl"
          p={6}
        >
          <AlertDialogHeader fontSize="lg" fontWeight="bold">
            {t("dialogs.upgrade.title")}
          </AlertDialogHeader>
          <AlertDialogBody>
            {t("dialogs.upgrade.messageFeature")}
          </AlertDialogBody>
          <AlertDialogFooter>
            <Button ref={cancelRef} onClick={onUpgradeDialogClose}>
              {t("buttons.cancel")}
            </Button>
            <Button
              colorScheme="teal"
              onClick={() => navigate("/packages")}
              ml={3}
            >
              {t("buttons.upgrade")}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Image Modal */}
      {displayedImage && (
        <Modal
          isOpen={isImageModalOpen}
          onClose={onImageModalClose}
          size="4xl"
          isCentered
        >
          <ModalOverlay backdropFilter="blur(5px)" />
          <ModalContent bg="transparent" boxShadow="none">
            <ModalCloseButton color="white" zIndex="tooltip" />
            <ModalBody p={0}>
              <Image
                src={imageUrl(displayedImage)}
                alt={item.name}
                w="100%"
                h="auto"
                maxH="90vh"
                objectFit="contain"
                borderRadius="md"
                boxShadow="dark-lg"
              />
            </ModalBody>
          </ModalContent>
        </Modal>
      )}
    </Box>
  );
};

export default ItemView;