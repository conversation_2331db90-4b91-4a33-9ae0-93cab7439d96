import React, { useEffect, useState } from 'react';
import { Box, Heading, Text, VStack, Container } from '@chakra-ui/react';
import { api } from '@/api';

const DebugStore: React.FC = () => {
  const [stores, setStores] = useState<any[]>([]);

  useEffect(() => {
    const fetchStores = async () => {
      try {
        // Fetch all stores
        const storeResponse = await api.get('/stores');
        console.log('All stores:', storeResponse.data);
        setStores(storeResponse.data);

        // Fetch a sample product to check the store data
        const productResponse = await api.get('/items/type/product');
        console.log('Sample products with store data:', productResponse.data.slice(0, 3));
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchStores();
  }, []);

  return (
    <Container maxW="container.xl" py={8}>
      <Heading mb={4}>Store Debug Page</Heading>

      <Box mb={8}>
        <Heading size="md" mb={2}>Store Data from API:</Heading>
        <Text mb={2}>Check the console for full data logs</Text>

        <VStack align="stretch" spacing={4}>
          {stores.slice(0, 5).map((store) => (
            <Box
              key={store._id}
              p={4}
              border="1px"
              borderColor="gray.200"
              borderRadius="md"
            >
              <Text fontWeight="bold">{store.name}</Text>
              <Text fontSize="sm">ID: {store._id}</Text>

              {/* Show location data if it exists */}
              {store.location && (
                <Box mt={2}>
                  <Text fontWeight="bold">Location:</Text>
                  <Text>City: {store.location.city || 'Not set'}</Text>
                  <Text>Country: {store.location.country || 'Not set'}</Text>
                </Box>
              )}

              {!store.location && (
                <Text color="red.500">No location data available</Text>
              )}

              <Text mt={2}>Raw data:</Text>
              <Box
                p={2}
                bg="gray.100"
                fontSize="xs"
                fontFamily="monospace"
                overflow="auto"
                maxH="100px"
              >
                {JSON.stringify(store, null, 2)}
              </Box>
            </Box>
          ))}
        </VStack>
      </Box>
    </Container>
  );
};

export default DebugStore;
