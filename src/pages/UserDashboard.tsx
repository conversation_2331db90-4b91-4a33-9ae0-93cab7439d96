import React, { useEffect, useState } from "react";
import {
  Box,
  Heading,
  Text,
  VStack,
  Avatar,
  SimpleGrid,
  Card,
  CardBody,
  Badge,
  Image,
  Button,
} from "@chakra-ui/react";
import { getUserProfile } from "../api";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";

interface IHomepageAd {
  _id: string;
  image: string;
  title: string;
  clicks: number;
  status: "pending" | "approved" | "rejected" | "expired";
  createdAt: Date;
  expiresAt: Date;
}

const UserDashboard: React.FC = () => {
  const { t } = useTranslation(["userProfile", "common"]);
  const navigate = useNavigate();
  const [user, setUser] = useState<any>(null);
  const [latestAds, setLatestAds] = useState<IHomepageAd[]>([]);

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const profile: any = await getUserProfile();
        setUser(profile);

        if (profile.addons?.includes("homepageAd")) {
          const response = await fetch("/api/homepage-ads/user");
          const data = await response.json();
          setLatestAds(data.slice(0, 2)); // Show only the latest 2 ads
        }
      } catch (error: any) {
        console.error("Failed to fetch user profile:", error);
      }
    };
    fetchUserProfile();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "green";
      case "rejected":
        return "red";
      case "expired":
        return "gray";
      default:
        return "yellow";
    }
  };

  return (
    <Box>
      <Box p={8} maxW="xl" mx="auto">
        <VStack spacing={8} align="stretch">
          <Box>
            <Heading as="h1" size="lg" mb={6}>
              {t("dashboard.welcome")}
            </Heading>
            {user ? (
              <VStack spacing={4} align="start">
                <Avatar size="xl" name={user.firstName} />
                <Text>
                  <strong>{t("profile.name")}:</strong> {user.firstName}{" "}
                  {user.lastName}
                </Text>
                <Text>
                  <strong>{t("profile.email")}:</strong> {user.email}
                </Text>
                <Text>
                  <strong>{t("profile.role")}:</strong>{" "}
                  {t(`roles.${user.role}`)}
                </Text>
                <Text>
                  <strong>{t("profile.status")}:</strong>{" "}
                  {user.isActive ? t("status.active") : t("status.inactive")}
                </Text>
              </VStack>
            ) : (
              <Text>{t("loading")}</Text>
            )}
          </Box>

          {user?.addons?.includes("homepageAd") && (
            <Box>
              <Heading as="h2" size="md" mb={4}>
                {t("homepageAd.latest")}
              </Heading>
              {latestAds.length > 0 ? (
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                  {latestAds.map((ad) => (
                    <Card key={ad._id}>
                      <CardBody>
                        <VStack align="stretch" spacing={3}>
                          <Image
                            src={import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image}
                            alt={ad.title}
                            borderRadius="md"
                            objectFit="cover"
                            height="150px"
                          />

                          <Text fontWeight="bold">{ad.title}</Text>
                          <Text fontSize="sm">
                            {t("homepageAd.table.clicks")}: {ad.clicks}
                          </Text>
                          <Badge colorScheme={getStatusColor(ad.status)}>
                            {t(`profile:homepageAd.status.${ad.status}`)}
                          </Badge>
                          <Text fontSize="sm">
                            {t("homepageAd.table.expires_at")}:{" "}
                            {format(new Date(ad.expiresAt), "PP")}
                          </Text>
                        </VStack>
                      </CardBody>
                    </Card>
                  ))}
                </SimpleGrid>
              ) : (
                <Text>{t("homepageAd.messages.no_ads")}</Text>
              )}
              <Button
                mt={4}
                colorScheme="blue"
                onClick={() => navigate("/profile/homepage-ads")}
              >
                {t("homepageAd.manage")}
              </Button>
            </Box>
          )}
        </VStack>
      </Box>
    </Box>
  );
};

export default UserDashboard;
