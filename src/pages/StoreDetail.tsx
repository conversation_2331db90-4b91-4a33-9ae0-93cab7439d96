import React, { useCallback, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import {
  Box,
  Container,
  Heading,
  Image,
  Text,
  VStack,
  HStack,
  Skeleton,
  Icon,
  Badge,
  Flex,
  SimpleGrid,
  useToast,
  Button,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Avatar,
  Grid,
  GridItem,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  IconButton,
  Tooltip,
  Tag,
  TagLabel,
  TagLeftIcon,
  Wrap,
  WrapItem,
  Link,
} from "@chakra-ui/react";
import {
  MapPin,
  BadgeCheck,
  Building2,
  Package,
  ExternalLink,
  Send,
  MessageSquare,
  Eye,
  Globe,
  Mail,
  Phone,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  Share2,
  Tag as TagIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { IItem } from "../types/item";
import { api, checkStoreViewRequest, useStoreViewRequest } from "@/api";
import ProductCard from "../components/common/ProductCard";

const StoreDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useTranslation("store");
  const navigate = useNavigate();
  const toast = useToast();
  const [store, setStore] = useState<any>(null);
  const [items, setItems] = useState<IItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [categories, setCategories] = useState<{ id: string, name: string }[]>([]);
  const [viewRequestInfo, setViewRequestInfo] = useState<any>(null);

  const {
    isOpen: isMessageModalOpen,
    onOpen: onMessageModalOpen,
    onClose: onMessageModalClose
  } = useDisclosure();
  const [messageForm, setMessageForm] = useState({
    name: "",
    email: "",
    subject: "",
    message: ""
  });

  const gradientOverlay = "linear-gradient(to top, rgba(0,0,0,0.7), rgba(0,0,0,0.5), rgba(0,0,0,0.3), rgba(0,0,0,0.1))";

  const fetchData = useCallback(async () => {
    if (!id) return;

    try {
      setLoading(true);

      try {
        // Check if store view endpoint is available (it might not be yet)
        try {
          // Check if user has permission to view store
          const response = await checkStoreViewRequest(id);
          // Process store view request response
          setViewRequestInfo(response);

          // If user has permission to view (owner, already viewed, or has remaining views)
          if (response.isOwner || response.alreadyViewed || response.hasRemaining) {
            // Track the view if needed
            if (!response.isOwner && !response.alreadyViewed && response.hasRemaining) {
              try {
                await useStoreViewRequest(id);
                // Update view request info after using a view
                const updatedResponse = await checkStoreViewRequest(id);
                setViewRequestInfo(updatedResponse);
              } catch (viewError: any) {
                console.log('Error using store view request:', viewError);
                // Continue anyway - might be API not implemented yet
              }
            }
          } else {
            // User doesn't have permission to view, show warning but allow viewing
            toast({
              title: t("warnings.noViewRequests.title") || "No View Requests Remaining",
              description: t("warnings.noViewRequests.description") || "You have no remaining view requests. Some features may be limited.",
              status: "warning",
              duration: 5000,
              isClosable: true,
            });
          }
        } catch (error: any) {
          // If we get a 404, it means the API endpoint doesn't exist yet
          // In this case, we should still show the store details
          console.log('Store view API not available yet:', error);
        }

        // Fetch store data regardless of view permission check result
        // since the view tracking API might not be implemented yet
        const storeResponse = await api.get(`/stores/${id}?includeItems=true&includeCategories=true`);
        setStore(storeResponse.data);
        setItems(storeResponse.data.items || []);

        // Extract unique categories from items
        if (storeResponse.data.items && storeResponse.data.items.length > 0) {
          const categoryIds = new Set();
          const extractedCategories: ((prevState: { id: string; name: string; }[]) => { id: string; name: string; }[]) | { id: any; name: any; }[] = [];

          storeResponse.data.items.forEach((item: { category: { _id: any; name: any; }; }) => {
            if (item.category) {
              // Handle both string ID and object references
              const catId = typeof item.category === 'string' ? item.category : item.category._id;
              const catName = typeof item.category === 'string' ? '' : item.category.name;

              if (!categoryIds.has(catId) && catName) {
                categoryIds.add(catId);
                extractedCategories.push({
                  id: catId,
                  name: catName
                });
              }
            }
          });

          setCategories(extractedCategories);
        }
      } catch (error: any) {
        // Handle errors from main API request
        if (error.response?.status === 404) {
          // Store not found error
          toast({
            title: t("errors.storeNotFound.title"),
            description: t("errors.storeNotFound.description"),
            status: "error",
            duration: 5000,
            isClosable: true,
          });
          navigate("/stores");
          return;
        } else {
          throw error; // Rethrow for the outer catch block
        }
      }
    } catch (error) {
      // Handle error fetching store data
      toast({
        title: t("errors.fetchFailed.title"),
        description: t("errors.fetchFailed.description"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      navigate("/stores");
    } finally {
      setLoading(false);
    }
  }, [id, t, toast, navigate]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleMessageSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Here you would make an API call to send the message
      // For now, we'll simulate it with a delay
      // In a real implementation, you'd use api.post('/messages', {...messageForm, storeId: id}) or similar

      // Simulating API call with setTimeout
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: t('contact.messageSent', 'Mesaj gönderildi'),
        description: t('contact.messageDelivered', 'Mesajınız firma yetkililerine iletilmiştir.'),
        status: "success",
        duration: 3000,
        isClosable: true,
      });

      // Reset form and close modal
      setMessageForm({
        name: "",
        email: "",
        subject: "",
        message: ""
      });
      onMessageModalClose();
    } catch (error) {
      // Handle error sending message
      toast({
        title: t('contact.messageFailed', 'Mesaj gönderilemedi'),
        description: t('contact.messageError', 'Mesajınız gönderilirken bir hata oluştu. Lütfen daha sonra tekrar deneyin.'),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const getStoreTypeBadge = (type: string) => {
    switch (type) {
      case 'broker':
        return (
          <Badge colorScheme="orange" px={3} py={1} borderRadius="full">
            {t('types.broker')}
          </Badge>
        );
      default:
        return (
          <Badge colorScheme="blue" px={3} py={1} borderRadius="full">
            {t('types.company')}
          </Badge>
        );
    }
  };

  const getApprovalBadge = (isApproved: boolean) => {
    return isApproved ? (
      <Badge colorScheme="green" px={3} py={1} borderRadius="full">
        {t('status.approved', 'Onaylandı')}
      </Badge>
    ) : (
      <Badge colorScheme="yellow" px={3} py={1} borderRadius="full">
        {t('status.pending', 'Onay Bekliyor')}
      </Badge>
    );
  };

  // Categories are now fetched from the API and populated in the categories state

  const handleShareClick = () => {
    if (navigator.share) {
      navigator.share({
        title: store?.name,
        text: store?.description,
        url: window.location.href,
      }).catch(() => {
        // Handle sharing error silently
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast({
        title: t('share.copied', 'Link Kopyalandı'),
        description: t('share.linkCopied', 'Sayfa linki panoya kopyalandı'),
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    }
  };

  if (loading) {
    return (
      <Box bg="gray.50" minH="100vh">
        <Skeleton height="300px" width="100%" />
        <Container maxW="7xl" py={8}>
          <Grid templateColumns={{ base: "1fr", md: "2fr 1fr" }} gap={8}>
            <GridItem>
              <Skeleton height="200px" borderRadius="xl" mb={8} />
              <SimpleGrid columns={{ base: 1, sm: 2, md: 4 }} spacing={4} mb={8}>
                <Skeleton height="100px" borderRadius="lg" />
                <Skeleton height="100px" borderRadius="lg" />
                <Skeleton height="100px" borderRadius="lg" />
                <Skeleton height="100px" borderRadius="lg" />
              </SimpleGrid>
              <Skeleton height="300px" borderRadius="xl" />
            </GridItem>
            <GridItem>
              <Skeleton height="200px" borderRadius="xl" />
            </GridItem>
          </Grid>
        </Container>
      </Box>
    );
  }

  if (!store) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack spacing={4}>
          <Icon as={Building2} boxSize={12} color="gray.500" />
          <Heading size="lg">{t("errors.storeNotFound.title")}</Heading>
          <Text>{t("errors.storeNotFound.description")}</Text>
        </VStack>
      </Container>
    );
  }

  const imageUrl = (path?: string) => {
    if (!path) return "https://via.placeholder.com/800x400?text=No+Image";
    if (path.startsWith('http')) return path;
    // Remove leading slashes to avoid double slashes in URL
    const cleanPath = path.replace(/^\/+/, '');
    return `${import.meta.env.VITE_SOCKET_URL}/${cleanPath}`;
  };

  return (
    <Box bg="gray.50" minH="100vh" w="full" position="absolute" left={0} top={106} right={0}>
      {/* Hero Section */}
      <Box position="relative" h={{ base: "300px", md: "500px" }}>
        <Image
          src={imageUrl(store.coverImage)}
          alt={t('coverImage', { storeName: store.name })}
          w="100%"
          h="100%"
          objectFit="cover"
          fallback={
            <Flex
              bg="gray.200"
              w="100%"
              h="100%"
              align="center"
              justify="center"
            >
              <Icon as={Building2} boxSize={16} color="gray.400" />
            </Flex>
          }
        />
        <Box
          position="absolute"
          inset={0}
          bgGradient={gradientOverlay}
        />

        {/* Mobile Floating Action Buttons */}
        <Flex
          display={{ base: "flex", md: "none" }}
          position="fixed"
          bottom="6"
          right="4"
          zIndex="overlay"
          direction="column"
          gap={3}
        >
          <IconButton
            aria-label={t('share.title', 'Paylaş')}
            icon={<Share2 size={20} />}
            colorScheme="blue"
            rounded="full"
            shadow="lg"
            size="lg"
            onClick={handleShareClick}
          />

          <Button
            colorScheme="teal"
            rounded="full"
            shadow="lg"
            size="lg"
            leftIcon={<MessageSquare size={20} />}
            onClick={onMessageModalOpen}
          >
            {t('contact.sendMessage', 'Mesaj Gönder')}
          </Button>
        </Flex>

        <Box
          position="absolute"
          bottom="8"
          left="50%"
          transform="translateX(-50%)"
          w="full"
          maxW="7xl"
          px={{ base: 4, md: 6, lg: 8 }}
        >
          <Flex justify="space-between" align="center" wrap="wrap">
            <Flex align="center" gap={4} mb={{ base: 4, md: 0 }}>
              <Avatar
                src={imageUrl(store.logo)}
                name={store.name}
                size={{ base: "xl", md: "2xl" }}
                borderWidth={4}
                borderColor="white"
                shadow="lg"
                borderRadius="xl"
              />
              <Box maxW={{ base: "200px", sm: "300px", md: "full" }}>
                <Flex gap={2} mb={2} flexWrap="wrap">
                  {getStoreTypeBadge(store.type || 'company')}
                  {getApprovalBadge(store.isApproved)}
                </Flex>
                <Flex align="center" gap={2} flexWrap="wrap">
                  <Heading color="white" size={{ base: "lg", md: "xl" }} noOfLines={1}>{store.name}</Heading>
                  {store.isApproved && <BadgeCheck size={24} color="#00ADA7" />}
                </Flex>
                {(store.location || store.location?.city) && (
                  <Flex align="center" gap={2} mt={2} color="whiteAlpha.900">
                    <MapPin size={20} />
                    <Text noOfLines={1}>
                      {store.location?.city ? `${store.location.city}, ${store.location.country}` : store.location}
                    </Text>
                  </Flex>
                )}
              </Box>
            </Flex>

            <Flex gap={2} display={{ base: "none", md: "flex" }}>
              <Button
                colorScheme="teal"
                leftIcon={<MessageSquare size={20} />}
                onClick={onMessageModalOpen}
              >
                {t('contact.sendMessage', 'Mesaj Gönder')}
              </Button>

              <Tooltip label={t('share.title', 'Paylaş')}>
                <IconButton
                  aria-label={t('share.title', 'Paylaş')}
                  icon={<Share2 />}
                  colorScheme="blue"
                  onClick={handleShareClick}
                />
              </Tooltip>
            </Flex>
          </Flex>
        </Box>
      </Box>

      {/* Main Content */}
      <Container maxW="7xl" py={8}>
        <Grid templateColumns={{ base: "1fr", md: "2fr 1fr" }} gap={8}>
          {/* Left Column */}
          <GridItem>
            <VStack spacing={8} align="stretch">
              {/* About Section */}
              <Box bg="white" borderRadius="xl" shadow="sm" p={6}>
                <Heading as="h2" size="lg" mb={6}>
                  {t('details.description', 'Hakkında')}
                </Heading>
                <Text color="gray.600">
                  {store.description}
                </Text>

                {/* Categories/Tags Section */}
                {categories.length > 0 && (
                  <Box mt={6}>
                    <Heading as="h3" size="md" mb={3}>
                      {t('categories', 'Kategoriler')}
                    </Heading>
                    <Wrap spacing={2}>
                      {categories.map(category => (
                        <WrapItem key={category.id}>
                          <Tag size="md" variant="subtle" colorScheme="teal">
                            <TagLeftIcon as={TagIcon} boxSize="12px" />
                            <TagLabel>{category.name}</TagLabel>
                          </Tag>
                        </WrapItem>
                      ))}
                    </Wrap>
                  </Box>
                )}
              </Box>

              {/* Store Stats */}
              <SimpleGrid columns={{ base: 2, sm: 2 }} spacing={4}>
                <Box bg="white" p={{ base: 4, md: 6 }} borderRadius="xl" shadow="sm">
                  <Package size={24} color="#00ADA7" />
                  <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" mt={2}>
                    {items.length}+
                  </Text>
                  <Text fontSize="sm" color="gray.600">{t('stats.products', 'Ürün/Hizmet')}</Text>
                </Box>

                <Box bg="white" p={{ base: 4, md: 6 }} borderRadius="xl" shadow="sm">
                  <Eye size={24} color="#00ADA7" />
                  <Text fontSize={{ base: "xl", md: "2xl" }} fontWeight="bold" mt={2}>
                    {viewRequestInfo ?
                      viewRequestInfo.totalViews || (typeof store.viewCount === 'number' ? store.viewCount : 0) :
                      (typeof store.viewCount === 'number' ? store.viewCount : 0)}
                  </Text>
                  <Text fontSize="sm" color="gray.600">{t('stats.storeViews', 'Firma Görüntüleme')}</Text>
                </Box>
              </SimpleGrid>

              {/* Products Section */}
              {items.length > 0 && (
                <Box bg="white" borderRadius="xl" shadow="sm" p={6}>
                  <Heading as="h2" size="lg" mb={6}>
                    {t('storeProducts', 'Ürünler ve Hizmetler')}
                  </Heading>
                  <SimpleGrid columns={{ base: 1, sm: 2, lg: 3 }} spacing={6}>
                    {items.map((item) => (
                      <ProductCard
                        key={item._id}
                        item={item}
                        showStore={false}
                      />
                    ))}
                  </SimpleGrid>
                </Box>
              )}
            </VStack>
          </GridItem>

          {/* Right Column */}
          <GridItem>
            <Box bg="white" borderRadius="xl" shadow="sm" p={6}>
              <Heading as="h3" size="md" mb={6}>
                {t('details.contact', 'İletişim Bilgileri')}
              </Heading>
              <VStack spacing={4} align="stretch">
                {store.owner?.email && (
                  <Flex align="center" gap={3}>
                    <Mail size={20} color="#718096" />
                    <Link href={`mailto:${store.owner.email}`} color="gray.600" isExternal>
                      {store.owner.email}
                    </Link>
                  </Flex>
                )}

                <Flex align="center" gap={3}>
                  <Globe size={20} color="#718096" />
                  {store.website ? (
                    <Link href={store.website.startsWith('http') ? store.website : `https://${store.website}`} color="gray.600" isExternal>
                      {store.website}
                      <ExternalLink size={14} style={{ display: 'inline', marginLeft: '4px' }} />
                    </Link>
                  ) : (
                    <Text color="gray.600">-</Text>
                  )}
                </Flex>

                {store.owner?.phoneNumber && (
                  <Flex align="center" gap={3}>
                    <Phone size={20} color="#718096" />
                    <Link href={`tel:${store.owner.phoneNumber}`} color="gray.600">
                      {store.owner.phoneNumber}
                    </Link>
                  </Flex>
                )}

                {(store.location || (store.location?.city && store.location?.country)) && (
                  <Flex align="center" gap={3}>
                    <MapPin size={20} color="#718096" />
                    <Text color="gray.600">
                      {store.location?.city ? `${store.location.city}, ${store.location.country}` : store.location}
                    </Text>
                  </Flex>
                )}

                {/* Social Media Links */}
                {store.socialMedia && (
                  <HStack spacing={3} mt={2} justify="center">
                    {store.socialMedia.facebook && (
                      <Tooltip label="Facebook">
                        <IconButton
                          as={Link}
                          href={store.socialMedia.facebook}
                          aria-label="Facebook"
                          icon={<Facebook size={20} />}
                          colorScheme="facebook"
                          variant="ghost"
                          isExternal
                        />
                      </Tooltip>
                    )}
                    {store.socialMedia.twitter && (
                      <Tooltip label="Twitter">
                        <IconButton
                          as={Link}
                          href={store.socialMedia.twitter}
                          aria-label="Twitter"
                          icon={<Twitter size={20} />}
                          colorScheme="twitter"
                          variant="ghost"
                          isExternal
                        />
                      </Tooltip>
                    )}
                    {store.socialMedia.instagram && (
                      <Tooltip label="Instagram">
                        <IconButton
                          as={Link}
                          href={store.socialMedia.instagram}
                          aria-label="Instagram"
                          icon={<Instagram size={20} />}
                          colorScheme="pink"
                          variant="ghost"
                          isExternal
                        />
                      </Tooltip>
                    )}
                    {store.socialMedia.linkedin && (
                      <Tooltip label="LinkedIn">
                        <IconButton
                          as={Link}
                          href={store.socialMedia.linkedin}
                          aria-label="LinkedIn"
                          icon={<Linkedin size={20} />}
                          colorScheme="linkedin"
                          variant="ghost"
                          isExternal
                        />
                      </Tooltip>
                    )}
                  </HStack>
                )}

                <Button
                  mt={4}
                  w="full"
                  colorScheme="teal"
                  leftIcon={<MessageSquare size={20} />}
                  onClick={onMessageModalOpen}
                >
                  {t('contact.sendMessage', 'Mesaj Gönder')}
                </Button>

                <Button
                  w="full"
                  colorScheme="blue"
                  variant="outline"
                  leftIcon={<Share2 size={20} />}
                  onClick={handleShareClick}
                >
                  {t('share.title', 'Paylaş')}
                </Button>
              </VStack>
            </Box>
          </GridItem>
        </Grid>
      </Container>

      {/* Message Modal */}
      <Modal isOpen={isMessageModalOpen} onClose={onMessageModalClose} size="xl">
        <ModalOverlay backdropFilter="blur(5px)" />
        <ModalContent borderRadius="xl" p={2}>
          <ModalHeader>{t('contact.sendMessage', 'Mesaj Gönder')}</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <form onSubmit={handleMessageSubmit}>
              <VStack spacing={4}>
                <FormControl isRequired>
                  <FormLabel>{t('contact.form.name', 'Adınız Soyadınız')}</FormLabel>
                  <Input
                    value={messageForm.name}
                    onChange={(e) => setMessageForm(prev => ({ ...prev, name: e.target.value }))}
                    placeholder={t('contact.form.namePlaceholder', 'Adınız Soyadınız')}
                  />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel>{t('contact.form.email', 'E-posta Adresiniz')}</FormLabel>
                  <Input
                    type="email"
                    value={messageForm.email}
                    onChange={(e) => setMessageForm(prev => ({ ...prev, email: e.target.value }))}
                    placeholder={t('contact.form.emailPlaceholder', 'E-posta adresiniz')}
                  />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel>{t('contact.form.subject', 'Konu')}</FormLabel>
                  <Input
                    value={messageForm.subject}
                    onChange={(e) => setMessageForm(prev => ({ ...prev, subject: e.target.value }))}
                    placeholder={t('contact.form.subjectPlaceholder', 'Mesaj konusu')}
                  />
                </FormControl>

                <FormControl isRequired>
                  <FormLabel>{t('contact.form.message', 'Mesajınız')}</FormLabel>
                  <Textarea
                    value={messageForm.message}
                    onChange={(e) => setMessageForm(prev => ({ ...prev, message: e.target.value }))}
                    placeholder={t('contact.form.messagePlaceholder', 'Mesajınız...')}
                    rows={5}
                  />
                </FormControl>

                <Flex justify="flex-end" gap={3} w="full">
                  <Button onClick={onMessageModalClose}>
                    {t('common.cancel', 'İptal')}
                  </Button>
                  <Button
                    type="submit"
                    colorScheme="teal"
                    leftIcon={<Send size={16} />}
                  >
                    {t('contact.form.send', 'Gönder')}
                  </Button>
                </Flex>
              </VStack>
            </form>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default StoreDetail;