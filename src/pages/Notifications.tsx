import React from 'react';
import Layout from '../components/Layout';
import { Bell, Package, Star, MessageSquare, User, Building2 } from 'lucide-react';

const Notifications = () => {
  const notifications = [
    {
      id: 1,
      type: 'message',
      title: 'Ye<PERSON>j',
      description: 'TechGlobal Solutions firmasından yeni bir mesajınız var.',
      time: '10 dakika önce',
      icon: MessageSquare,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50'
    },
    {
      id: 2,
      type: 'product',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      description: 'Endüstriyel Otomasyon Yazılımı ürününüz 50 kez görüntülendi.',
      time: '2 saat önce',
      icon: Package,
      color: 'text-green-500',
      bgColor: 'bg-green-50'
    },
    {
      id: 3,
      type: 'review',
      title: 'Ye<PERSON>lendirme',
      description: 'Firma profilinize yeni bir de<PERSON>lendirme yapıldı.',
      time: '1 gün önce',
      icon: Star,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50'
    },
    {
      id: 4,
      type: 'profile',
      title: '<PERSON><PERSON>üleme',
      description: 'Firma profiliniz 100 kez görüntülendi.',
      time: '3 gün önce',
      icon: Building2,
      color: 'text-indigo-500',
      bgColor: 'bg-indigo-50'
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-sm p-6 mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Bildirimler</h1>
            <p className="text-gray-600">Son aktiviteler ve güncellemeler</p>
          </div>

          {/* Notifications List */}
          <div className="bg-white rounded-xl shadow-sm divide-y">
            {notifications.map((notification) => (
              <div 
                key={notification.id}
                className="p-6 hover:bg-gray-50 transition-colors cursor-pointer"
              >
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg ${notification.bgColor}`}>
                    <notification.icon className={`h-6 w-6 ${notification.color}`} />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-gray-900">{notification.title}</h3>
                      <span className="text-sm text-gray-500">{notification.time}</span>
                    </div>
                    <p className="mt-1 text-gray-600">{notification.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Notifications;