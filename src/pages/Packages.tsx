// React is imported implicitly with JSX
import Layout from '../components/Layout';
import { 
  Zap,
  Star,
  Check,
  Building2,
  ShoppingCart,
  Plus,
  FileText,
  TrendingUp,
  ArrowRight
} from 'lucide-react';

const Packages = () => {
  const packages = [
    {
      name: "Başlangıç Paketi",
      description: "Yeni başlayan ihracatçılar için temel ihtiyaçlara odaklı ekonomik çözüm.",
      price: "15",
      icon: Zap,
      features: [
        "15 Görüntüleme Hakkı",
        "15 Talep Oluşturma",
        "15 Satış Oluşturma",
        "E-posta Bildirimleri",
        "Çoklu Dil Desteği"
      ],
      isPopular: false
    },
    {
      name: "Premium Paket",
      description: "Markasını uluslararası vitrine taşımak isteyen ihracatçılar için tam kapsamlı çözüm.",
      price: "100",
      icon: Star,
      features: [
        "100 Görüntüleme Hakkı",
        "100 Talep Oluşturma",
        "100 Satış Oluşturma",
        "E-posta Bildirimleri",
        "Mesajlaşma",
        "Yıl Sonu Sektör Raporu",
        "Anasayfa Reklam Alanı",
        "Çoklu Dil Desteği"
      ],
      isPopular: true
    },
    {
      name: "Standart Paket",
      description: "Operasyonlarını büyütmek ve daha fazla bağlantı kurmak isteyenler için.",
      price: "50",
      icon: Building2,
      features: [
        "50 Görüntüleme Hakkı",
        "50 Talep Oluşturma",
        "50 Satış Oluşturma",
        "E-posta Bildirimleri",
        "Mesajlaşma Özelliği"
      ],
      isPopular: false
    }
  ];

  const additionalPackages = [
    {
      name: "50 Talep Oluşturma",
      price: "25",
      icon: FileText,
      description: "İşletmeniz için aylık 50 adet talep oluşturma hakkı",
      features: [
        "50 adet talep oluşturma",
        "30 gün geçerlilik",
        "Detaylı talep istatistikleri"
      ]
    },
    {
      name: "50 Satış Oluşturma",
      price: "25",
      icon: Plus,
      description: "İşletmeniz için aylık 50 adet satış oluşturma hakkı",
      features: [
        "50 adet satış oluşturma",
        "30 gün geçerlilik",
        "Satış istatistikleri"
      ]
    },
    {
      name: "Ana Sayfa Reklamı",
      price: "100",
      icon: TrendingUp,
      description: "Ana sayfada 30 gün boyunca premium reklam gösterimi",
      features: [
        "30 gün reklam gösterimi",
        "Premium konum",
        "Detaylı analitik"
      ]
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-white">
        {/* Header Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <span className="inline-block px-4 py-2 rounded-lg bg-primary/10 text-primary text-sm font-medium mb-4">
              Ticaret Ağınızı Genişletin!
            </span>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              İhtiyacınıza Uygun Paketlerle E-exportcity Yanınızda!
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              İster ilk adımınızı atıyor olun, ister küresel pazarda büyümeyi hedefleyin e-exportcity'nin esnek ve hedef odaklı paketleriyle ticaretinizi dijitalleştirin.
            </p>
          </div>
        </div>

        {/* Main Packages Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div 
                key={index}
                className={`relative rounded-2xl ${
                  pkg.isPopular 
                    ? 'border-2 border-primary scale-105 shadow-xl' 
                    : 'border border-gray-200 shadow-sm'
                } bg-white p-8 transform transition-all duration-200 hover:shadow-lg`}
              >
                {pkg.isPopular && (
                  <div className="absolute -top-5 left-1/2 transform -translate-x-1/2">
                    <span className="inline-block px-4 py-2 rounded-full bg-primary text-white text-sm font-medium">
                      En Popüler
                    </span>
                  </div>
                )}

                <div className="flex items-center space-x-3 mb-4">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    pkg.isPopular ? 'bg-primary text-white' : 'bg-primary/10 text-primary'
                  }`}>
                    <pkg.icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{pkg.name}</h3>
                </div>

                <p className="text-gray-600 mb-6">
                  {pkg.description}
                </p>

                <div className="mb-8">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">${pkg.price}</span>
                    <span className="ml-2 text-gray-500">/aylık</span>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-3">
                      <Check className="h-5 w-5 text-primary flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button 
                  className={`w-full py-4 rounded-lg flex items-center justify-center space-x-2 text-base font-medium transition-all duration-200 ${
                    pkg.isPopular
                      ? 'bg-primary text-white hover:bg-primary/90'
                      : 'bg-primary/10 text-primary hover:bg-primary hover:text-white'
                  }`}
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>Satın Al</span>
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Additional Packages Section */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <span className="inline-block px-4 py-2 rounded-lg bg-primary/10 text-primary text-sm font-medium mb-4">
                Ek Paketler
              </span>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                İhtiyacınıza Göre Ek Paketler
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Mevcut paketinizi güçlendirin, daha fazla fırsata erişin
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {additionalPackages.map((pkg, index) => (
                <div 
                  key={index}
                  className="bg-white rounded-xl p-6 shadow-sm hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-12 h-12 rounded-lg bg-primary/10 text-primary flex items-center justify-center">
                      <pkg.icon className="w-6 h-6" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900">{pkg.name}</h3>
                  </div>

                  <p className="text-gray-600 mb-6">
                    {pkg.description}
                  </p>

                  <div className="mb-6">
                    <div className="flex items-baseline">
                      <span className="text-3xl font-bold text-gray-900">${pkg.price}</span>
                      <span className="ml-2 text-gray-500">/aylık</span>
                    </div>
                  </div>

                  <ul className="space-y-3 mb-6">
                    {pkg.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center space-x-3">
                        <Check className="h-5 w-5 text-primary flex-shrink-0" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button 
                    className="w-full py-3 bg-primary/10 text-primary rounded-lg font-medium hover:bg-primary hover:text-white transition-colors flex items-center justify-center space-x-2 group"
                  >
                    <span>Detaylı Bilgi</span>
                    <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              E-exportcity ile yeni nesil ticaret platformunda ağını büyüt!
            </h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              E-exportcity, Türkiye'nin önde gelen dijital ihracat platformu olarak, işletmelerin global pazarlara açılmasını kolaylaştırıyor. Gelişmiş teknoloji altyapımız, uzman ihracat danışmanlarımız ve kapsamlı hizmet ağımızla, ihracatçılarımızın uluslararası ticaretteki rekabet gücünü artırıyoruz. Siz de e-exportcity ailesine katılarak, dijital dünyanın sunduğu sınırsız fırsatlardan yararlanın ve işletmenizi global arenada büyütün.
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Packages;