import React, { useState } from 'react';
import Layout from '../components/Layout';
import { 
  MessageSquare, 
  Search, 
  Star, 
  Clock, 
  CheckCircle, 
  User,
  Send,
  Image as ImageIcon,
  Paperclip,
  ArrowLeft,
  MoreVertical,
  Phone,
  Video,
  Mail,
  Filter,
  ChevronDown,
  Pin,
  Archive,
  Trash2,
  X,
  Check,
  AlertCircle
} from 'lucide-react';

const Messages = () => {
  const [selectedMessage, setSelectedMessage] = useState<number | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'archived'
  const [showOptions, setShowOptions] = useState<number | null>(null);

  const messages = [
    {
      id: 1,
      sender: "Ahmet Yılmaz",
      company: "TechGlobal Solutions",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80",
      message: "Mer<PERSON><PERSON>, ürününüz hakkında detaylı bilgi alabilir miyim?",
      time: "10:30",
      unread: true,
      pinned: true,
      status: 'active', // 'active', 'archived'
      chat: [
        {
          id: 1,
          sender: "Ahmet Yılmaz",
          message: "Merhaba, ürününüz hakkında detaylı bilgi alabilir miyim?",
          time: "10:30",
          type: "received",
          status: "read" // 'sent', 'delivered', 'read'
        },
        {
          id: 2,
          sender: "Ben",
          message: "Merhaba, tabii ki. Hangi konuda bilgi almak istersiniz?",
          time: "10:32",
          type: "sent",
          status: "read"
        },
        {
          id: 3,
          sender: "Ahmet Yılmaz",
          message: "Özellikle entegrasyon süreçleri ve fiyatlandırma konusunda bilgi almak istiyorum.",
          time: "10:35",
          type: "received",
          status: "read"
        }
      ]
    },
    {
      id: 2,
      sender: "Ayşe Demir",
      company: "EcoTrade International",
      avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80",
      message: "Fiyat teklifiniz için teşekkürler. İnceleyip size dönüş yapacağım.",
      time: "14:20",
      unread: true,
      pinned: false,
      status: 'active',
      chat: [
        {
          id: 1,
          sender: "Ayşe Demir",
          message: "Fiyat teklifiniz için teşekkürler. İnceleyip size dönüş yapacağım.",
          time: "14:20",
          type: "received",
          status: "read"
        }
      ]
    },
    {
      id: 3,
      sender: "Mehmet Kaya",
      company: "MediCare Exports",
      avatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?auto=format&fit=crop&q=80",
      message: "Ürün teslimatı hakkında görüşebilir miyiz?",
      time: "Dün",
      unread: false,
      pinned: false,
      status: 'archived',
      chat: [
        {
          id: 1,
          sender: "Mehmet Kaya",
          message: "Ürün teslimatı hakkında görüşebilir miyiz?",
          time: "Dün",
          type: "received",
          status: "read"
        }
      ]
    }
  ];

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || !selectedMessage) return;

    const message = {
      id: Math.random(),
      sender: "Ben",
      message: newMessage,
      time: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      type: "sent" as const,
      status: "sent"
    };

    const updatedMessages = messages.map(msg => {
      if (msg.id === selectedMessage) {
        return {
          ...msg,
          chat: [...msg.chat, message]
        };
      }
      return msg;
    });

    // In a real app, you would update the messages through an API
    console.log('Updated messages:', updatedMessages);
    setNewMessage('');
  };

  const filteredMessages = messages.filter(message => {
    const matchesSearch = 
      message.sender.toLowerCase().includes(searchQuery.toLowerCase()) ||
      message.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
      message.message.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter = 
      filter === 'all' ? true :
      filter === 'unread' ? message.unread :
      filter === 'archived' ? message.status === 'archived' : true;

    return matchesSearch && matchesFilter;
  }).sort((a, b) => {
    // Sort by pinned first, then by unread, then by time
    if (a.pinned && !b.pinned) return -1;
    if (!a.pinned && b.pinned) return 1;
    if (a.unread && !b.unread) return -1;
    if (!a.unread && b.unread) return 1;
    return 0;
  });

  const selectedChat = messages.find(message => message.id === selectedMessage);

  const getMessageStatus = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="h-4 w-4 text-gray-400" />;
      case 'delivered':
        return <Check className="h-4 w-4 text-primary" />;
      case 'read':
        return <CheckCircle className="h-4 w-4 text-primary" />;
      default:
        return null;
    }
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-3 min-h-[calc(100vh-80px)]">
            {/* Messages List */}
            <div className={`bg-white border-r ${selectedMessage ? 'hidden md:block' : ''}`}>
              {/* Header */}
              <div className="p-6 border-b">
                <h1 className="text-2xl font-bold text-gray-900 mb-2">Mesajlar</h1>
                <p className="text-gray-600 mb-6">İş ortaklarınızla olan mesajlaşmalarınızı yönetin</p>

                {/* Search and Filter */}
                <div className="space-y-4">
                  <div className="relative">
                    <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Mesajlarda ara..."
                      className="w-full pl-12 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div className="flex space-x-2">
                    <button
                      onClick={() => setFilter('all')}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        filter === 'all'
                          ? 'bg-primary text-white'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      Tümü
                    </button>
                    <button
                      onClick={() => setFilter('unread')}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        filter === 'unread'
                          ? 'bg-primary text-white'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      Okunmamış
                    </button>
                    <button
                      onClick={() => setFilter('archived')}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        filter === 'archived'
                          ? 'bg-primary text-white'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      Arşivlenmiş
                    </button>
                  </div>
                </div>
              </div>

              {/* Messages */}
              <div className="divide-y">
                {filteredMessages.map((message) => (
                  <div 
                    key={message.id}
                    className={`relative p-4 hover:bg-gray-50 transition-colors cursor-pointer ${
                      selectedMessage === message.id ? 'bg-gray-50' : ''
                    }`}
                  >
                    <div 
                      className="flex items-start space-x-4"
                      onClick={() => setSelectedMessage(message.id)}
                    >
                      <img
                        src={message.avatar}
                        alt={message.sender}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <h3 className={`font-semibold ${message.unread ? 'text-gray-900' : 'text-gray-700'}`}>
                              {message.sender}
                            </h3>
                            {message.pinned && (
                              <Pin className="h-4 w-4 text-primary" />
                            )}
                          </div>
                          <span className="text-sm text-gray-500">{message.time}</span>
                        </div>
                        <p className="text-sm text-gray-600">{message.company}</p>
                        <p className={`mt-1 text-sm truncate ${message.unread ? 'font-medium text-gray-900' : 'text-gray-600'}`}>
                          {message.message}
                        </p>
                      </div>
                    </div>

                    {/* Message Options */}
                    <button
                      onClick={() => setShowOptions(showOptions === message.id ? null : message.id)}
                      className="absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    >
                      <MoreVertical className="h-5 w-5" />
                    </button>

                    {showOptions === message.id && (
                      <div className="absolute top-12 right-4 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-1 z-10">
                        <button
                          onClick={() => {
                            // Handle pin/unpin
                            setShowOptions(null);
                          }}
                          className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50"
                        >
                          <Pin className="h-4 w-4" />
                          <span>{message.pinned ? 'Sabitlemeyi Kaldır' : 'Sabitle'}</span>
                        </button>
                        <button
                          onClick={() => {
                            // Handle archive/unarchive
                            setShowOptions(null);
                          }}
                          className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50"
                        >
                          <Archive className="h-4 w-4" />
                          <span>{message.status === 'archived' ? 'Arşivden Çıkar' : 'Arşivle'}</span>
                        </button>
                        <button
                          onClick={() => {
                            // Handle delete
                            setShowOptions(null);
                          }}
                          className="w-full flex items-center space-x-2 px-4 py-2 text-red-600 hover:bg-gray-50"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span>Sil</span>
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Chat View */}
            {selectedMessage ? (
              <div className="md:col-span-2 bg-white flex flex-col h-[calc(100vh-80px)]">
                {/* Chat Header */}
                <div className="p-6 border-b flex items-center justify-between bg-white sticky top-0 z-10">
                  <div className="flex items-center space-x-4">
                    <button 
                      onClick={() => setSelectedMessage(null)}
                      className="md:hidden text-gray-400 hover:text-gray-600"
                    >
                      <ArrowLeft className="h-6 w-6" />
                    </button>
                    <img
                      src={selectedChat?.avatar}
                      alt={selectedChat?.sender}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{selectedChat?.sender}</h3>
                      <p className="text-sm text-gray-600">{selectedChat?.company}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                      <Phone className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                      <Video className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100">
                      <MoreVertical className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Chat Messages */}
                <div className="flex-1 overflow-y-auto p-6 space-y-6">
                  {selectedChat?.chat.map((msg) => (
                    <div
                      key={msg.id}
                      className={`flex ${msg.type === 'sent' ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-[70%] ${
                        msg.type === 'sent' 
                          ? 'bg-primary text-white rounded-l-xl rounded-tr-xl' 
                          : 'bg-gray-100 text-gray-900 rounded-r-xl rounded-tl-xl'
                      } p-4`}>
                        <p>{msg.message}</p>
                        <div className={`flex items-center justify-end space-x-2 mt-2 ${
                          msg.type === 'sent' ? 'text-white/70' : 'text-gray-500'
                        }`}>
                          <span className="text-xs">{msg.time}</span>
                          {msg.type === 'sent' && getMessageStatus(msg.status)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Message Input */}
                <div className="p-6 border-t bg-white sticky bottom-0">
                  <form onSubmit={handleSendMessage} className="flex items-center space-x-4">
                    <button
                      type="button"
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    >
                      <Paperclip className="h-5 w-5" />
                    </button>
                    <button
                      type="button"
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                    >
                      <ImageIcon className="h-5 w-5" />
                    </button>
                    <input
                      type="text"
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Mesajınızı yazın..."
                      className="flex-1 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                    <button
                      type="submit"
                      disabled={!newMessage.trim()}
                      className="p-2 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Send className="h-5 w-5" />
                    </button>
                  </form>
                </div>
              </div>
            ) : (
              <div className="hidden md:flex md:col-span-2 items-center justify-center bg-white">
                <div className="text-center">
                  <MessageSquare className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900">Mesajlarınız</h3>
                  <p className="text-gray-600 mt-2">Mesajlaşmaya başlamak için bir sohbet seçin</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Messages;