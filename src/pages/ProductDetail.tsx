import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import * as Dialog from '@radix-ui/react-dialog';
import { 
  MapPin, 
  BadgeCheck, 
  Building2, 
  Package,
  ExternalLink,
  Send,
  X,
  MessageSquare,
  Eye
} from 'lucide-react';

const ProductDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isContactModalOpen, setIsContactModalOpen] = React.useState(false);
  const [contactForm, setContactForm] = React.useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  });

  // This would typically come from an API call using the id parameter
  const product = {
    name: "Endüstriyel Otomasyon Yazılımı",
    company: "TechGlobal Solutions",
    category: "Yazılım",
    location: "İstanbul, Türkiye",
    images: [
      "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&q=80",
      "https://images.unsplash.com/photo-1581092334651-ddf26d9a09d0?auto=format&fit=crop&q=80",
      "https://images.unsplash.com/photo-1581092160562-40aa08e78837?auto=format&fit=crop&q=80",
      "https://images.unsplash.com/photo-1581092795360-fd1ca04f0952?auto=format&fit=crop&q=80"
    ],
    views: 1250,
    type: "Hizmet Talebi",
    verified: true,
    description: "Endüstriyel tesisler için özel olarak geliştirilen, yüksek performanslı otomasyon yazılımı çözümü. Üretim süreçlerini optimize eder ve verimliliği artırır.",
    features: [
      "Gerçek zamanlı izleme",
      "Uzaktan erişim desteği",
      "Özelleştirilebilir raporlama",
      "Çoklu dil desteği",
      "API entegrasyonu",
      "7/24 teknik destek"
    ],
    specifications: {
      platform: "Windows, Linux, macOS",
      version: "2.5.0",
      license: "Enterprise",
      support: "Premium",
      deployment: "On-premise / Cloud",
      updates: "Otomatik"
    }
  };

  const similarProducts = [
    {
      name: "ERP Yazılım Çözümü",
      company: "TechGlobal Solutions",
      category: "Yazılım",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80",
      views: 980,
      type: "Hizmet Talebi",
      verified: true
    },
    {
      name: "IoT Platform Hizmeti",
      company: "SmartTech Solutions",
      category: "Yazılım",
      image: "https://images.unsplash.com/photo-1518770660439-4636190af475?auto=format&fit=crop&q=80",
      views: 750,
      type: "Hizmet Talebi",
      verified: true
    },
    {
      name: "Veri Analiz Platformu",
      company: "DataTech Corp",
      category: "Yazılım",
      image: "https://images.unsplash.com/photo-**********-bebda4e38f71?auto=format&fit=crop&q=80",
      views: 890,
      type: "Hizmet Talebi",
      verified: true
    }
  ];

  const [selectedImage, setSelectedImage] = React.useState(product.images[0]);

  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle contact form submission
    console.log('Contact form submitted:', contactForm);
    setIsContactModalOpen(false);
    setContactForm({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: ''
    });
  };

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Images */}
            <div className="lg:col-span-2 space-y-8">
              {/* Main Image */}
              <div className="aspect-[4/3] relative rounded-xl overflow-hidden">
                <img 
                  src={selectedImage}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Thumbnail Images */}
              <div className="grid grid-cols-4 gap-4">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImage(image)}
                    className={`aspect-square rounded-lg overflow-hidden border-2 transition-all ${
                      selectedImage === image ? 'border-primary' : 'border-transparent hover:border-gray-200'
                    }`}
                  >
                    <img 
                      src={image}
                      alt={`${product.name} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>

              {/* Description */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Ürün Açıklaması</h2>
                <p className="text-gray-600">{product.description}</p>
              </div>

              {/* Features */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Özellikler</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {product.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Package className="h-5 w-5 text-primary" />
                      <span className="text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Specifications */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <h2 className="text-xl font-semibold text-gray-900 mb-4">Teknik Özellikler</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <span className="text-gray-600 capitalize">{key}</span>
                      <span className="text-gray-900 font-medium">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-8">
              {/* Product Info Card */}
              <div className="bg-white rounded-xl p-6 shadow-sm">
                <div className="mb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                      product.type === 'Hizmet Talebi' 
                        ? 'bg-primary/10 text-primary' 
                        : 'bg-[#10B981]/10 text-[#10B981]'
                    }`}>
                      {product.type}
                    </span>
                    <div className="flex items-center space-x-1 text-gray-500">
                      <Eye className="h-4 w-4" />
                      <span className="text-sm">{product.views}</span>
                    </div>
                  </div>
                  <h1 className="text-2xl font-bold text-gray-900">{product.name}</h1>
                  <div className="flex items-center space-x-2 mt-2">
                    <span className="text-lg font-medium text-primary">{product.company}</span>
                    {product.verified && (
                      <BadgeCheck className="h-5 w-5 text-primary" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2 mt-2 text-gray-600">
                    <MapPin className="h-5 w-5" />
                    <span>{product.location}</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <button 
                    onClick={() => navigate(`/companies/${product.company.toLowerCase().replace(/\s+/g, '-')}`)}
                    className="w-full py-3 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors flex items-center justify-center space-x-2"
                  >
                    <Building2 className="h-5 w-5" />
                    <span>Firma Profilini Görüntüle</span>
                  </button>

                  <button 
                    onClick={() => setIsContactModalOpen(true)}
                    className="w-full py-3 bg-primary/10 text-primary rounded-lg font-medium hover:bg-primary hover:text-white transition-colors flex items-center justify-center space-x-2"
                  >
                    <MessageSquare className="h-5 w-5" />
                    <span>Mesaj Gönder</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Similar Products */}
          <div className="mt-16">
            <h2 className="text-2xl font-bold text-gray-900 mb-8">Benzer Hizmetler</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {similarProducts.map((product, index) => (
                <div key={index} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
                  <div className="aspect-[4/3] relative overflow-hidden">
                    <img 
                      src={product.image}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                    
                    <div className="absolute bottom-4 left-4">
                      <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                        product.type === 'Hizmet Talebi' 
                          ? 'bg-primary/90 text-white' 
                          : 'bg-[#10B981]/90 text-white'
                      }`}>
                        {product.type}
                      </span>
                    </div>

                    <div className="absolute top-4 right-4">
                      <div className="flex items-center space-x-1 bg-white/90 rounded-full px-2 py-1">
                        <Eye className="h-4 w-4 text-gray-600" />
                        <span className="text-sm font-medium text-gray-600">{product.views}</span>
                      </div>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="mb-4">
                      <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-primary">{product.company}</span>
                          {product.verified && (
                            <BadgeCheck className="h-4 w-4 text-primary flex-shrink-0" />
                          )}
                        </div>
                      </div>
                    </div>

                    <button className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group">
                      <span>Detayları Gör</span>
                      <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Contact Modal */}
        <Dialog.Root open={isContactModalOpen} onOpenChange={setIsContactModalOpen}>
          <Dialog.Portal>
            <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40" />
            <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-lg bg-white rounded-xl shadow-xl p-6 z-50">
              <div className="flex items-center justify-between mb-4">
                <Dialog.Title className="text-xl font-semibold text-gray-900">
                  İletişim Talebi
                </Dialog.Title>
                <Dialog.Close className="text-gray-400 hover:text-gray-600">
                  <X className="h-5 w-5" />
                </Dialog.Close>
              </div>

              <form onSubmit={handleContactSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Adınız Soyadınız
                  </label>
                  <input
                    type="text"
                    value={contactForm.name}
                    onChange={(e) => setContactForm(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    E-posta Adresiniz
                  </label>
                  <input
                    type="email"
                    value={contactForm.email}
                    onChange={(e) => setContactForm(prev => ({ ...prev, email: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Telefon Numaranız
                  </label>
                  <input
                    type="tel"
                    value={contactForm.phone}
                    onChange={(e) => setContactForm(prev => ({ ...prev, phone: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Konu
                  </label>
                  <input
                    type="text"
                    value={contactForm.subject}
                    onChange={(e) => setContactForm(prev => ({ ...prev, subject: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mesajınız
                  </label>
                  <textarea
                    value={contactForm.message}
                    onChange={(e) => setContactForm(prev => ({ ...prev, message: e.target.value }))}
                    className="w-full h-32 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                    required
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <Dialog.Close asChild>
                    <button
                      type="button"
                      className="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                      İptal
                    </button>
                  </Dialog.Close>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors flex items-center space-x-2"
                  >
                    <Send className="h-4 w-4" />
                    <span>Gönder</span>
                  </button>
                </div>
              </form>
            </Dialog.Content>
          </Dialog.Portal>
        </Dialog.Root>
      </div>
    </Layout>
  );
};

export default ProductDetail;