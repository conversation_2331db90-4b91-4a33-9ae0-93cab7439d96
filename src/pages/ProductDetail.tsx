import React, { useEffect, useState, useRef } from "react";
import {
  Box,
  Container,
  Heading,
  Text,
  Image,
  VStack,
  HStack,
  Badge,
  Divider,
  Button,
  useToast,
  Spinner,
  Alert,
  AlertIcon,
  useColorModeValue,
  Grid,
  GridItem,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
} from "@chakra-ui/react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { getItemById, getUserPackages } from "../api";
import DOMPurify from "dompurify";
import { MessageCircle } from "lucide-react";

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { t } = useTranslation("productDetail");
  const navigate = useNavigate();
  const toast = useToast();
  const [item, setItem] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [canSendMessage, setCanSendMessage] = useState(false);
  const { isOpen: isUpgradeModalOpen, onOpen: onUpgradeModalOpen, onClose: onUpgradeModalClose } = useDisclosure();
  const cancelRef = useRef<any>(null);

  const cardBg = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  useEffect(() => {
    const fetchItem = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const data = await getItemById(id);
        setItem(data);

        // Check if user can send messages by checking all packages
        try {
          const packagesResponse: any = await getUserPackages();
          const userPackages = packagesResponse.packages || [];
          // First check addon packages, then standard packages
          const addonPackagesWithMessaging = userPackages.filter(
            (pkg: any) => pkg.type === 'addon' && pkg.messagingAllowed === true && pkg.status === 'active'
          );

          const standardPackagesWithMessaging = userPackages.filter(
            (pkg: any) => pkg.type === 'standard' && pkg.messagingAllowed === true && pkg.status === 'active'
          );

          // Check if any package (addon first, then standard) allows messaging
          setCanSendMessage(addonPackagesWithMessaging.length > 0 || standardPackagesWithMessaging.length > 0);

        } catch (packageError) {
          console.error('Error fetching user packages:', packageError);
          // Default to false if can't check
          setCanSendMessage(false);
        }
      } catch (error: any) {
        if (error.response?.status === 403) {
          // Subscription limit error
          const message = error.response.data.requiresViewRequest
            ? t("errors.requiresViewRequest.title")
            : t("errors.noViewRequests.title");

          toast({
            title: t("errors.accessDenied.title"),
            description: message,
            status: "error",
            duration: 5000,
            isClosable: true,
          });
          navigate("/");
        } else {
          setError(t("errors.fetchFailed.title"));
          toast({
            title: t("errors.fetchFailed.title"),
            description: t("errors.tryAgain.description"),
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchItem();
  }, [id, t, toast, navigate]);

  if (isLoading) {
    return (
      <Container maxW="container.xl" py={8}>
        <VStack spacing={4}>
          <Spinner size="xl" />
          <Text>{t("loading")}</Text>
        </VStack>
      </Container>
    );
  }

  if (error || !item) {
    return (
      <Container maxW="container.xl" py={8}>
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          {error || t("errors.itemNotFound.title")}
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxW="container.xl" py={8}>
      <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={8}>
        <GridItem>
          <Box
            bg={cardBg}
            borderRadius="lg"
            borderWidth="1px"
            borderColor={borderColor}
            overflow="hidden"
          >
            <Image
              src={
                item.images && item.images.length > 0
                  ? item.images[0]
                  : "https://via.placeholder.com/600x400?text=No+Image"
              }
              alt={item.name}
              width="100%"
              height="400px"
              objectFit="cover"
            />
          </Box>
        </GridItem>

        <GridItem>
          <VStack align="stretch" spacing={6}>
            <Box>
              <HStack justify="space-between" align="start">
                <Heading as="h1" size="xl">
                  {item.name}
                </Heading>
                <Badge
                  colorScheme={item.type === "Product" ? "blue" : "green"}
                  fontSize="md"
                  px={3}
                  py={1}
                  borderRadius="full"
                >
                  {t(`types.${item.type}`)}
                </Badge>
              </HStack>
              <Text color="gray.500" mt={2}>
                {item.description}
              </Text>
            </Box>

            <Divider />

            <Box>
              <VStack align="stretch" spacing={4}>
                <Heading size="lg">{item.name}</Heading>
                <Text color="gray.600">{item.description}</Text>
                <Box>
                  <Text fontSize="sm" color="gray.500">
                    {t("item.category")}: {item.category}
                  </Text>
                  <Text fontSize="sm" color="gray.500">
                    {t("item.type")}: {item.type}
                  </Text>
                </Box>
              </VStack>
            </Box>

            <Divider />

            <Box>
              <Heading as="h3" size="md" mb={4}>
                {t("detailedDescription")}
              </Heading>
              <Box
                dangerouslySetInnerHTML={{
                  __html: DOMPurify.sanitize(item.description),
                }}
                className="rich-text-content"
                p={4}
                bg="gray.50"
                borderRadius="md"
              />
            </Box>

            <Button
              colorScheme="teal"
              size="lg"
              leftIcon={<MessageCircle />}
              onClick={() => {
                if (canSendMessage) {
                  // Handle contact seller - add your messaging component here
                  navigate(`/messages/new?itemId=${item._id}&sellerId=${item.userId}`);
                } else {
                  onUpgradeModalOpen();
                }
              }}
            >
              {t("buttons.contactSeller")}
            </Button>

            {/* Upgrade Modal */}
            <Modal isOpen={isUpgradeModalOpen} onClose={onUpgradeModalClose} isCentered>
              <ModalOverlay />
              <ModalContent>
                <ModalHeader>Upgrade Required</ModalHeader>
                <ModalBody>
                  You need to upgrade your package to use the messaging feature.
                </ModalBody>
                <ModalFooter>
                  <Button ref={cancelRef} onClick={onUpgradeModalClose} mr={3}>
                    Cancel
                  </Button>
                  <Button colorScheme="blue" onClick={() => {
                    onUpgradeModalClose();
                    navigate('/packages');
                  }}>
                    Upgrade Package
                  </Button>
                </ModalFooter>
              </ModalContent>
            </Modal>
          </VStack>
        </GridItem>
      </Grid>
    </Container>
  );
};

export default ProductDetail;
