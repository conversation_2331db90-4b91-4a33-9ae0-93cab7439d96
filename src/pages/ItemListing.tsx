import React, { useEffect, useState, useRef, useCallback } from "react";
import { useLocation, useNavigate, Link } from "react-router-dom";
import {
  Search,
  BadgeCheck,
  MapPin,
  ExternalLink,
  Eye,
  ShoppingBag,
  X,
  Sliders,
  Plus,
  Save
} from "lucide-react";
import {
  Box,
  Button,
  Container,
  Text,
  Select,
  VStack,
  Image,
  Spinner,
  Alert,
  AlertIcon,
  Icon,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Grid,
  UnorderedList,
  ListItem,
  FormHelperText,
} from "@chakra-ui/react";
import {
  createItem,
  updateItem,
  deleteItem,
  getItemsByType,
  getItems,
} from "../api/itemApi";
import { getUserStore } from "../api/storeApi";
import {
  createHomeAd,
  getCategoriesByType,
  checkCreateRequest,
} from "../api";
import { categoryApi } from "../api/categoryApi";
import { IItem } from "../types/item";
import { ICategory } from "../types/category";
import { useTranslation } from "react-i18next";
import i18next from "i18next";
import RichTextEditor from "../components/common/RichTextEditor";

interface ItemFormData {
  name: string;
  description: string;
  category?: string;
  type: string;
  listingType: "demand" | "sale";
  images: File[];
}

const ItemListing: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation("itemListing");
  const location = useLocation();

  // Parse query parameters
  const queryParams = new URLSearchParams(location.search);
  const typeParam = queryParams.get('type');
  const categoryParam = queryParams.get('category');

  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    type: 'all',
    mode: 'all',
    category: categoryParam || 'all',
    location: 'all',
    sortBy: 'newest'
  });

  const [items, setItems] = useState<IItem[]>([]);
  const [allItems, setAllItems] = useState<IItem[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [selectedType, setSelectedType] = useState<string>(
    typeParam === 'service' ? 'service' : typeParam === 'product' ? 'product' : 'all'
  );
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [categoryLevels, setCategoryLevels] = useState<ICategory[][]>([]);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isHomeAdDialogOpen, setIsHomeAdDialogOpen] = useState(false);
  const [isUpgradeDialogOpen, setIsUpgradeDialogOpen] = useState(false);
  const [modalType, setModalType] = useState<string>(
    typeParam === 'service' ? 'service' : typeParam === 'product' ? 'product' : 'product'
  );
  const [modalCategories, setModalCategories] = useState<string[]>([]);
  const [modalCategoryLevels, setModalCategoryLevels] = useState<ICategory[][]>(
    [],
  );
  const [newItem, setNewItem] = useState<ItemFormData>({
    name: "",
    description: "",
    category: undefined,
    type: typeParam === 'service' ? 'service' : typeParam === 'product' ? 'product' : 'product',
    listingType: "sale",
    images: [],
  });
  const { isOpen: isCreateModalOpen, onClose: onCreateModalClose } =
    useDisclosure();
  const { isOpen: isEditModalOpen, onClose: onEditModalClose } =
    useDisclosure();
  const cancelRef = useRef<any>(null);

  const toast = useToast();

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const fileArray = Array.from(files);
      if (fileArray.length > 5) {
        toast({
          title: t("errors.tooManyFiles"),
          description: t("errors.maxFiveFiles"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        return;
      }
      setNewItem((prev) => ({ ...prev, images: fileArray }));
    }
  };

  const handleCreateItem = async () => {
    try {
      // Check required text fields
      if (!newItem.name || !newItem.description || !newItem.category) {
        toast({
          title: t("errors.validation.title"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // Check if images are provided
      if (newItem.images.length === 0) {
        toast({
          title: t("errors.missingImage.title") || "Image Required",
          description: t("errors.missingImage.description") || "Please upload at least one image.",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      const checkResponse = await checkCreateRequest();
      if (!checkResponse.hasRemaining) {
        toast({
          title: t("errors.noCreateRequests.title"),
          description: t("errors.upgradeRequired.description"),
          status: "error",
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      // Create form data with image files directly
      const formData = new FormData();
      formData.append("name", newItem.name);
      formData.append("description", newItem.description);
      if (newItem.category) {
        formData.append("category", newItem.category);
      }
      formData.append("type", newItem.type);
      formData.append("listingType", newItem.listingType);

      // Attach image files directly to FormData
      if (newItem.images.length > 0) {
        console.log("Attaching images to FormData:", newItem.images.length, "images");
        newItem.images.forEach((file) => {
          formData.append("images", file);
        });
      }

      const response = await createItem(formData);

      // Update items list
      setItems((prev) => [response, ...prev]);

      // Reset modal states
      onCreateModalClose();
      setNewItem({
        name: "",
        description: "",
        category: undefined,
        type: "product",
        listingType: "sale",
        images: [],
      });
      setModalType("product");
      setModalCategories([]);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }

      toast({
        title: t("success.itemCreated"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      console.error("Error creating item:", error);
      toast({
        title: error.response?.data?.message || t("errors.itemCreationFailed.title"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    const fetchInitialData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // If category parameter is provided, fetch items by category directly
        if (categoryParam) {
          setFilters(prev => {
            if (prev.category === categoryParam) return prev;
            return { ...prev, category: categoryParam };
          });
          
          // Fetch items by category - this will automatically get items of the correct type
          const itemList = await categoryApi.getItemsByCategory(categoryParam);
          
          // Filter out items with DELETED or INACTIVE status
          const activeItems = itemList.filter((item: IItem) => item.status === 'ACTIVE');

          // Ensure all items have a viewCount property, even if it's 0
          const itemsWithViewCounts = activeItems.map((item: IItem) => ({
            ...item,
            viewCount: item.viewCount || 0
          }));

          setAllItems(itemsWithViewCounts);
          
          // For categories, we still need to fetch all categories for the type selector
          // Determine type from the first item or default to 'product'
          const itemType = activeItems.length > 0 ? activeItems[0].type : (typeParam || 'product');
          const allTypeCategories = await getCategoriesByType(itemType);
          setCategories(allTypeCategories);
          
          // Find the selected category and build the hierarchy
          const selectedCategory = allTypeCategories.find((cat: ICategory) => cat._id === categoryParam);
          if (selectedCategory) {
            // Set the selected type based on the category
            setSelectedType(selectedCategory.type.toLowerCase());
            setFilters(prev => ({ ...prev, type: selectedCategory.type.toLowerCase() }));
            
            // Build the category hierarchy
            const buildCategoryPath = (targetId: string): string[] => {
              const path: string[] = [];
              let currentCat = allTypeCategories.find((cat: ICategory) => cat._id === targetId);
              
              while (currentCat) {
                path.unshift(currentCat._id);
                if (currentCat.parent_id) {
                  currentCat = allTypeCategories.find((cat: ICategory) => cat.id === currentCat.parent_id);
                } else {
                  currentCat = null;
                }
              }
              
              return path;
            };
            
            const categoryPath = buildCategoryPath(categoryParam);
            setSelectedCategories(categoryPath);
            
            // Build category levels for dropdowns
            const levels: ICategory[][] = [];
            
            // First level - root categories
            const firstLevelCategories = allTypeCategories.filter(
              (cat: any) => !cat.parent_id || cat.parent_id === "" || cat.parent_id === null
            );
            firstLevelCategories.sort((a: any, b: any) => a.name.localeCompare(b.name));
            levels.push(firstLevelCategories);
            
            // Build subsequent levels based on the path
            for (let i = 0; i < categoryPath.length - 1; i++) {
              const parentCat = allTypeCategories.find((cat: ICategory) => cat._id === categoryPath[i]);
              if (parentCat) {
                const childCategories = allTypeCategories.filter(
                  (cat: ICategory) => cat.parent_id === parentCat.id
                );
                childCategories.sort((a: any, b: any) => a.name.localeCompare(b.name));
                if (childCategories.length > 0) {
                  levels.push(childCategories);
                }
              }
            }
            
            setCategoryLevels(levels);
          } else {
            // If category not found, just set up the first level
            const firstLevelCategories = allTypeCategories.filter(
              (cat: any) => !cat.parent_id || cat.parent_id === "" || cat.parent_id === null
            );
            firstLevelCategories.sort((a: any, b: any) => a.name.localeCompare(b.name));
            setCategoryLevels([firstLevelCategories]);
          }
          
          return; // Exit early since we got category-specific items
        }

        // Determine the type to use - if no typeParam and no explicit type selection, show all items
        const shouldShowAllItems = !typeParam && selectedType === 'all';
        const effectiveType = typeParam || (selectedType !== 'all' ? selectedType.toLowerCase() : 'product');

        // Ensure filters and selected type are in sync with URL parameters
        if (typeParam) {
          setSelectedType(typeParam);
          setFilters(prev => {
            if (prev.type === typeParam) return prev;
            return { ...prev, type: typeParam };
          });
        }

        // Fetch items by type (only when no category is specified)
        const itemList = shouldShowAllItems ? await getItems() : await getItemsByType(effectiveType);
        
        // Fetch categories for the type
        const allTypeCategories = await getCategoriesByType(effectiveType);

        // Filter out items with DELETED or INACTIVE status
        const activeItems = itemList.filter((item: IItem) => item.status === 'ACTIVE');

        // Ensure all items have a viewCount property, even if it's 0
        const itemsWithViewCounts = activeItems.map((item: IItem) => ({
          ...item,
          viewCount: item.viewCount || 0
        }));

        setAllItems(itemsWithViewCounts);
        // Filters will be applied by the applyFiltersAndSearch useEffect

        // Store all categories for relationship lookups
        setCategories(allTypeCategories);

        if (allTypeCategories && allTypeCategories.length > 0) {
          // Start with level 1 categories (those with no parent_id)
          const firstLevelCategories = allTypeCategories.filter(
            (cat: any) => !cat.parent_id || cat.parent_id === "" || cat.parent_id === null
          );

          // Sort categories alphabetically
          firstLevelCategories.sort((a: any, b: any) => a.name.localeCompare(b.name));

          // Initialize with ONLY the top-level categories - consistent with type change behavior
          setCategoryLevels([firstLevelCategories]);

          // Reset any previous selections
          setSelectedCategories([]);
        }
      } catch (error: any) {
        console.error("Failed to fetch data:", error);
        setError(t("errors.loadData"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialData();
  }, [selectedType, typeParam, categoryParam]);

  useEffect(() => {
    const fetchUserItems = async () => {
      try {
        const store = await getUserStore();
        if (store) {
          let userItems;
          // Determine effective type for user items fetching
          const effectiveTypeForUser = typeParam || selectedType;
          
          // Use the type-specific endpoint when a type is selected
          if (effectiveTypeForUser === 'all') {
            userItems = await getItems();
            userItems = userItems.filter(
              (item: any) => item.status === 'ACTIVE'
            );
          } else {
            userItems = await getItemsByType(effectiveTypeForUser.toLowerCase());
            userItems = userItems.filter(
              (item: any) => item.status === 'ACTIVE'
            );
          }
          setAllItems(userItems);
          // Filters will be applied by the applyFiltersAndSearch useEffect
        }
      } catch (error: any) {
        // If error is 404 (no store), show all items instead
        if (error.response?.status === 404) {
          // Don't fetch again here - let the fetchInitialData handle this
          // This prevents double fetching and overriding the initial data
          return;
        } else {
          console.error("Error fetching user items:", error);
          setError("errors.loadData");
        }
      }
    };

    fetchUserItems();
  }, [selectedType, typeParam]);

  // Function to apply filters, sorting, and search
  const applyFiltersAndSearch = useCallback((itemsToFilter: IItem[]) => {
    if (!itemsToFilter || itemsToFilter.length === 0) {
      setItems([]);
      return;
    }

    // Filter by search query
    let filteredResult = itemsToFilter;

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filteredResult = filteredResult.filter(item =>
        item.name.toLowerCase().includes(query) ||
        (item.description && item.description.toLowerCase().includes(query))
      );
    }

    // Filter by type (product/service)
    if (filters.type !== 'all') {
      filteredResult = filteredResult.filter(item =>
        item.type.toLowerCase() === filters.type.toLowerCase()
      );
    }

    // Filter by mode (sale/demand)
    if (filters.mode !== 'all') {
      filteredResult = filteredResult.filter(item =>
        item.listingType === filters.mode
      );
    }

    // Filter by location
    if (filters.location !== 'all') {
      filteredResult = filteredResult.filter(item => {
        // Check store location
        if (item.store?.location?.country &&
          item.store.location.country.toLowerCase() === filters.location.toLowerCase()) {
          return true;
        }
        // Check item location as fallback
        return item.location && item.location.toLowerCase().includes(filters.location.toLowerCase());
      });
    }

    // Apply sorting
    switch (filters.sortBy) {
      case 'oldest':
        filteredResult.sort((a, b) => {
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        });
        break;
      case 'mostViewed':
        filteredResult.sort((a, b) => {
          return (b.viewCount || 0) - (a.viewCount || 0);
        });
        break;
      case 'leastViewed':
        filteredResult.sort((a, b) => {
          return (a.viewCount || 0) - (b.viewCount || 0);
        });
        break;
      case 'newest':
      default:
        filteredResult.sort((a, b) => {
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        });
    }

    setItems(filteredResult);
  }, [searchQuery, filters]);

  // Apply filters, search, and sorting when relevant state changes
  useEffect(() => {
    applyFiltersAndSearch(allItems);
  }, [applyFiltersAndSearch, allItems]);

  useEffect(() => {
    // This effect applies category filtering to allItems and then passes to applyFiltersAndSearch.
    const lastSelectedCategory = selectedCategories[selectedCategories.length - 1];

    if (lastSelectedCategory) {
      const categoryFilteredItems = allItems.filter((item) =>
        isItemInCategoryTree(item, lastSelectedCategory) && item.status === 'ACTIVE' && item.isApproved === true
      );
      applyFiltersAndSearch(categoryFilteredItems);
    } else {
      // No category selected, apply other filters to all (unfiltered by category) items
      applyFiltersAndSearch(allItems);
    }
  }, [selectedCategories, categories, allItems, applyFiltersAndSearch]);

  useEffect(() => {
    const loadCategories = async () => {
      if (!newItem.type) return;

      try {
        // Reset categories when type changes
        setSelectedCategories([]);
        setCategoryLevels([]);

        // Get root level categories for the selected type
        const response = await getCategoriesByType(newItem.type.toLowerCase());
        if (response && response.length > 0) {
          setCategoryLevels([response]);
        }
      } catch (error) {
        console.error("Error loading categories:", error);
        toast({
          title: t("errors.categoryLoad"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    };

    loadCategories();
  }, [newItem.type]);

  useEffect(() => {
    const loadModalCategories = async () => {
      if (!modalType) return;

      try {
        // Reset categories when type changes
        setModalCategories([]);
        setModalCategoryLevels([]);

        // Get root level categories for the selected type
        const response = await getCategoriesByType(modalType.toLowerCase());
        if (response && response.length > 0) {
          setModalCategoryLevels([response]);
        }
      } catch (error) {
        console.error("Error loading categories:", error);
        toast({
          title: t("errors.categoryLoad"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    };

    loadModalCategories();
  }, [modalType]);

  const handleCategoryChange = async (level: number, categoryId: string) => {
    try {
      setIsLoading(true);

      // If empty selection, clear this level and all subsequent levels
      if (!categoryId) {
        setSelectedCategories(selectedCategories.slice(0, level));
        setCategoryLevels(categoryLevels.slice(0, level + 1));
        setIsLoading(false);
        return;
      }

      // Find the selected category to get its string ID (needed for finding children)
      const selectedCategory = categories.find(cat => cat._id === categoryId);
      if (!selectedCategory) {
        console.error('Selected category not found:', categoryId);
        setIsLoading(false);
        return;
      }

      // Update selected categories up to this level
      const newSelectedCategories = [
        ...selectedCategories.slice(0, level),
        categoryId,
      ];
      setSelectedCategories(newSelectedCategories);

      // Find direct child categories using the selected category's string ID
      // This uses the custom id field (not MongoDB _id) for parent-child relationships
      const childCategories = categories.filter(
        cat => cat.parent_id === selectedCategory.id
      );

      // Sort child categories alphabetically
      childCategories.sort((a, b) => a.name.localeCompare(b.name));

      // Update category levels
      if (childCategories.length > 0) {
        // Create new category levels array keeping existing levels up to current
        // and adding the children as the next level
        const newCategoryLevels = [
          ...categoryLevels.slice(0, level + 1),
          childCategories,
        ];

        setCategoryLevels(newCategoryLevels);
      } else {
        // No children found, so remove any deeper levels
        setCategoryLevels(categoryLevels.slice(0, level + 1));
      }
    } catch (error: any) {
      console.error("Failed to handle category change:", error);
      setError(t("errors.loadSubcategories"));
    } finally {
      setIsLoading(false);
    }
  };

  const handleTypeChange = (newType: string) => {
    setSelectedType(newType);
    setSelectedCategories([]);
    setCategoryLevels([]);

    // Update URL with the new type parameter
    const newParams = new URLSearchParams(location.search);
    newParams.set('type', newType.toLowerCase());
    navigate(`${location.pathname}?${newParams.toString()}`, { replace: true });

    // Load all categories for the new type
    const loadCategories = async () => {
      try {
        setIsLoading(true);
        // Get all categories for this type
        const allTypeCategories = await getCategoriesByType(
          newType.toLowerCase()
        );

        // Store all categories for relationship lookups
        setCategories(allTypeCategories);

        if (allTypeCategories && allTypeCategories.length > 0) {
          // Filter for level 1 categories (no parent_id)
          const firstLevelCategories = allTypeCategories.filter(
            (cat: any) => !cat.parent_id || cat.parent_id === "" || cat.parent_id === null
          );

          // Sort categories alphabetically
          firstLevelCategories.sort((a: any, b: any) => a.name.localeCompare(b.name));

          // Set just the top level categories
          setCategoryLevels([firstLevelCategories]);
        }
      } catch (error) {
        console.error("Failed to load categories:", error);
        toast({
          title: t("errors.categoryLoad"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadCategories();
  };


  const handleEditItem = async () => {
    if (!selectedItem) return;

    try {
      // Check if name, description, and category are provided
      if (!selectedItem.name || !selectedItem.description || !selectedItem.category) {
        toast({
          title: t("errors.validation.title") || "Validation Error",
          description: t("errors.validation.description") || "Please fix the errors in the form and try again.",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      const formData: any = new FormData();
      formData.append("name", selectedItem.name);
      formData.append("description", selectedItem.description);
      formData.append("category", selectedItem.category._id);
      formData.append("type", selectedItem.type);
      formData.append("listingType", selectedItem.listingType);

      if (selectedItem.images) {
        Array.from(selectedItem.images).forEach((file: any) => {
          formData.append("images", file);
        });
      }

      const response = await updateItem(selectedItem._id, formData);

      setItems((prev) =>
        prev.map((item) => (item._id === selectedItem._id ? response : item)),
      );
      onEditModalClose();
      toast({
        title: t("success.itemUpdated"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: t("errors.updateFailed.title"),
        description: t("errors.tryAgain.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleDeleteItem = async () => {
    if (!selectedItem) return;

    try {
      await deleteItem(selectedItem._id);
      setItems((prev) => prev.filter((item) => item._id !== selectedItem._id));
      setIsDeleteDialogOpen(false);
      toast({
        title: t("success.itemDeleted"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: t("errors.deleteFailed.title"),
        description: t("errors.tryAgain.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const [adImageFile, setAdImageFile] = useState<File | null>(null);
  const [adTitle, setAdTitle] = useState<string>("");

  const handleCreateHomeAd = async () => {
    if (!selectedItem) {
      return;
    }

    if (!adImageFile) {
      toast({
        title: t("errors.missingImage.title") || "Image Required",
        description: t("errors.pleaseSelectImage.description") || "Please select an image for the ad.",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const title = adTitle || selectedItem.name;

    try {
      await createHomeAd(title, adImageFile);
      setIsHomeAdDialogOpen(false);
      setAdImageFile(null);
      setAdTitle("");
      toast({
        title: t("success.adCreated"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: t("errors.adCreateFailed.title"),
        description: error.response?.data?.message || t("errors.tryAgain.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };


  // Helper function to determine if an item belongs to a category or its subcategories (up to 3 levels deep)
  const isItemInCategoryTree = (item: any, categoryId: string): boolean => {
    if (!item.category || !categoryId) return false;

    // Direct match (level 0)
    if (item.category._id === categoryId) return true;

    // Check if item's category is a subcategory of the selected category
    const checkCategoryTree = (
      itemCategoryId: string,
      targetCategoryId: string,
      level = 0,
      maxDepth = 3
    ): boolean => {
      // Stop recursion after reaching max depth
      if (level >= maxDepth) return false;

      const category = categories.find((cat) => cat._id === itemCategoryId);
      if (!category) return false;

      // Find the target category to get its custom string ID (needed for parent-child relationships)
      const targetCategory = categories.find((cat) => cat._id === targetCategoryId);
      if (!targetCategory) return false;

      // Check if this category's parent_id matches our target's string ID (direct child relationship)
      if (category.parent_id && targetCategory.id && category.parent_id === targetCategory.id) {
        return true;
      }

      // If this category has a parent, check if it's related to our target (another level deeper)
      if (category.parent_id && level < maxDepth - 1) {
        // Find the parent category by its string ID
        const parentCategory = categories.find((cat) => cat.id === category.parent_id);
        if (parentCategory) {
          // Check if the parent matches our target
          if (parentCategory._id === targetCategoryId) return true;

          // Recursively check the parent's relationship to our target
          return checkCategoryTree(parentCategory._id, targetCategoryId, level + 1, maxDepth);
        }
      }

      return false;
    };

    return checkCategoryTree(item.category._id, categoryId);
  };

  if (error) {
    return (
      <Container maxW="container.xl" py={{ base: 4, md: 8 }}>
        <Alert status="error" borderRadius="md">
          <AlertIcon />
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{t("title")}</h1>
              <p className="mt-2 text-gray-600">{t("page.description")}</p>
            </div>
            <button
              onClick={() => navigate('/items/add')}
              className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors flex items-center space-x-2"
            >
              <ShoppingBag className="h-5 w-5" />
              <span>{t("buttons.create")}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="sticky top-0 z-10 bg-white border-b shadow-sm">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-4">
          <div className="flex flex-col md:flex-row md:items-center gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t("search.placeholder")}
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  // Search is applied through applyFiltersAndSearch useEffect
                }}
                className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>

            {/* Sort Dropdown */}
            <select
              value={filters.sortBy}
              onChange={(e) => {
                setFilters(prev => ({ ...prev, sortBy: e.target.value }));
                // Sorting is applied through applyFiltersAndSearch useEffect
              }}
              className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
            >
              <option value="newest">{t("sort.newest")}</option>
              <option value="oldest">{t("sort.oldest")}</option>
              <option value="mostViewed">{t("sort.mostViewed")}</option>
              <option value="leastViewed">{t("sort.leastViewed")}</option>
            </select>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors ${showFilters ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
            >
              <Sliders className="h-5 w-5" />
              <span>{t("filters.button")}</span>
              <span className="bg-white/20 px-2 py-0.5 rounded text-sm">
                {Object.values(filters).filter(value => value !== 'all').length}
              </span>
            </button>
          </div>

          {/* Extended Filters */}
          {showFilters && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 animate-fadeIn">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t("type")}
                  </label>
                  <select
                    value={filters.type}
                    onChange={(e) => {
                      setFilters(prev => ({ ...prev, type: e.target.value }));
                      handleTypeChange(e.target.value === 'product' ? 'product' : 'service');
                    }}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                  >
                    <option value="all">{t("all")}</option>
                    <option value="product">{t("products")}</option>
                    <option value="service">{t("services")}</option>
                  </select>
                </div>

                {/* Mode Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t("mode")}
                  </label>
                  <select
                    value={filters.mode}
                    onChange={(e) => setFilters(prev => ({ ...prev, mode: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                  >
                    <option value="all">{t("all")}</option>
                    <option value="sale">{t("sale")}</option>
                    <option value="demand">{t("demand")}</option>
                  </select>
                </div>

                {/* Category Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t("categories")}
                  </label>
                  <select
                    value={selectedCategories[0] || ""}
                    onChange={(e) => handleCategoryChange(0, e.target.value)}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                  >
                    <option value="">{t("all")}</option>
                    {categoryLevels[0]?.map((category) => (
                      <option key={category._id} value={category._id}>
                        {i18next.language !== 'tr' && category.nameEn ? category.nameEn : category.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Location Filter - Using a placeholder since we don't have location in original */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t("location.label", "Location")}
                  </label>
                  <select
                    value={filters.location}
                    onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                  >
                    <option value="all">{t("all")}</option>
                  </select>
                </div>
              </div>

              {/* Reset Filters */}
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => {
                    setFilters({
                      type: 'all',
                      mode: 'all',
                      category: 'all',
                      location: 'all',
                      sortBy: 'newest'
                    });
                    setSearchQuery('');
                    setSelectedCategories([]);
                    // Reset filters
                    applyFiltersAndSearch(allItems);
                  }}
                  className="px-4 py-2 text-primary hover:text-[#0A9996] transition-colors flex items-center space-x-2"
                >
                  <X className="h-5 w-5" />
                  <span>{t("clearFilters")}</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Products Grid */}
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-8">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6">
            <div className="flex items-center">
              <AlertIcon />
              <p>{t(error)}</p>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <Spinner size="xl" color="teal.500" />
          </div>
        ) : items.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {items.map((item: any) => (
              <div
                key={item._id}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
              >
                <div className="aspect-[4/3] relative overflow-hidden rounded-t-xl">
                  <img
                    src={item.images?.[0] || "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&q=80"}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>

                  <div className="absolute bottom-4 left-4">
                    <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${item.listingType === 'demand'
                      ? 'bg-primary/90 text-white'
                      : 'bg-[#10B981]/90 text-white'
                      }`}>
                      {item.type === 'product' ? t('product') : t('service')} {item.listingType === 'demand' ? t('demand') : t('sale')}
                    </span>
                  </div>

                  <div className="absolute top-4 right-4">
                    <div className="flex items-center space-x-1 bg-white/90 rounded-full px-2 py-1">
                      <Eye className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-600">{item.viewCount || 0}</span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">{item.name}</h3>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-primary">{item.user?.store?.name || t('store')}</span>
                        {item.verified && (
                          <BadgeCheck className="h-4 w-4 text-primary flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mb-4">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {item.user?.store?.location?.city && item.user?.store?.location?.country
                        ? `${item.user.store.location.city}, ${item.user.store.location.country}`
                        : item.user?.store?.location?.city
                          ? item.user.store.location.city
                          : item.user?.store?.location?.country
                            ? item.user.store.location.country
                            : item.location || t('location.unknown')}
                    </span>
                  </div>

                  <button
                    onClick={() => navigate(`/items/${item._id}`)}
                    className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
                  >
                    <span>{t("buttons.viewDetails")}</span>
                    <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-10">
            <Text fontSize="xl" color="gray.500">
              {t("noItemsFound")}
            </Text>
          </div>
        )}
      </div>

      <Modal isOpen={isCreateModalOpen} onClose={onCreateModalClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("modals.create.title")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>{t("fields.name")}</FormLabel>
                <Input
                  value={newItem.name}
                  onChange={(e) =>
                    setNewItem({ ...newItem, name: e.target.value })
                  }
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>{t("fields.description")}</FormLabel>
                <RichTextEditor
                  value={newItem.description}
                  onChange={(value) =>
                    setNewItem({ ...newItem, description: value })
                  }
                  placeholder={t("placeholders.description")}
                />
              </FormControl>

              <FormControl>
                <FormLabel>{t("fields.images")}</FormLabel>
                <Input
                  name="itemImages"
                  type="file"
                  accept="image/*"
                  multiple
                  ref={fileInputRef}
                  onChange={handleFileSelect}
                />

                <Text fontSize="sm" color="gray.500" mt={1}>
                  {t("fields.imageHelp")}
                </Text>
              </FormControl>

              {newItem.images.length > 0 && (
                <Grid templateColumns="repeat(5, 1fr)" gap={2}>
                  {Array.from(newItem.images).map((file, index) => (
                    <Box key={index} position="relative">
                      <Image
                        src={import.meta.env.VITE_API_URL + URL.createObjectURL(file)}
                        alt={`Preview ${index + 1}`}
                        boxSize="100px"
                        objectFit="cover"
                        height="100%"
                        width="100%"
                      />
                    </Box>
                  ))}
                </Grid>
              )}

              <FormControl isRequired>
                <FormLabel>{t("listingType")}</FormLabel>
                <Select
                  value={newItem.listingType}
                  onChange={(e) =>
                    setNewItem({
                      ...newItem,
                      listingType: e.target.value as "demand" | "sale",
                    })
                  }
                >
                  <option value="sale">{t("sale")}</option>
                  <option value="demand">{t("demand")}</option>
                </Select>
              </FormControl>
            </VStack>

            <FormControl isRequired mt={4}>
              <FormLabel>{t("type")}</FormLabel>
              <Select
                value={modalType}
                onChange={(e) => setModalType(e.target.value)}
                placeholder={t("selectType")}
              >
                <option value="product">{t("product")}</option>
                <option value="service">{t("service")}</option>
              </Select>
            </FormControl>

            {modalCategoryLevels.map((categories, levelIndex) => (
              <FormControl
                key={levelIndex}
                isRequired={levelIndex === 0}
                mt={4}
              >
                <FormLabel>
                  {levelIndex === 0
                    ? t("categoryLevels.main")
                    : t("categoryLevels.level", { level: levelIndex + 1 })}
                </FormLabel>
                <Select
                  value={modalCategories[levelIndex] || ""}
                  onChange={(e) => {
                    const newCategories = modalCategories.slice(0, levelIndex);
                    if (e.target.value) {
                      newCategories.push(e.target.value);
                    }
                    setModalCategories(newCategories);
                  }}
                  placeholder={t("categoryLevels.select", { level: levelIndex + 1 })}
                >
                  {categories.map((category) => (
                    <option key={category._id} value={category._id}>
                      {category.name}
                    </option>
                  ))}
                </Select>
              </FormControl>
            ))}
          </ModalBody>

          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onCreateModalClose}>
              {t("buttons.cancel")}
            </Button>
            <Button
              colorScheme="teal"
              onClick={handleCreateItem}
              leftIcon={<Icon as={Plus} />}
              size={{ base: "md", md: "lg" }}
            >
              {t("buttons.create")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <Modal isOpen={isEditModalOpen} onClose={onEditModalClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("modals.edit.title")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>{t("fields.name")}</FormLabel>
                <Input
                  value={selectedItem?.name || ""}
                  onChange={(e) =>
                    setSelectedItem((prev: any) =>
                      prev ? { ...prev, name: e.target.value } : null,
                    )
                  }
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>{t("fields.description")}</FormLabel>
                <RichTextEditor
                  value={selectedItem?.description || ""}
                  onChange={(value) =>
                    setSelectedItem((prev: any) =>
                      prev ? { ...prev, description: value } : null
                    )
                  }
                  placeholder={t("placeholders.description")}
                />
              </FormControl>

              <FormControl>
                <FormLabel>{t("fields.images")}</FormLabel>
                <Input
                  name="editItemImages"
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => {
                    const files = e.target.files;
                    if (files) {
                      const fileArray = Array.from(files);
                      if (fileArray.length > 5) {
                        toast({
                          title: t("errors.tooManyFiles"),
                          description: t("errors.maxFiveFiles"),
                          status: "error",
                          duration: 3000,
                          isClosable: true,
                        });
                        return;
                      }
                      setSelectedItem((prev: any) =>
                        prev ? { ...prev, images: fileArray } : null,
                      );
                    }
                  }}
                />

                {selectedItem?.images && (
                  <Grid templateColumns="repeat(5, 1fr)" gap={2} mt={2}>
                    {Array.from(selectedItem.images).map(
                      (image: any, index) => (
                        <Box key={index} position="relative">
                          <Image
                            src={
                              image instanceof File
                                ? import.meta.env.VITE_API_URL + URL.createObjectURL(image)
                                : image
                            }
                            alt={`Image ${index + 1}`}
                            boxSize="100px"
                            objectFit="cover"
                          />
                        </Box>
                      ),
                    )}
                  </Grid>
                )}
              </FormControl>

              <FormControl isRequired>
                <FormLabel>{t("listingType")}</FormLabel>
                <Select
                  value={selectedItem?.listingType}
                  onChange={(e) =>
                    setSelectedItem((prev: any) =>
                      prev
                        ? {
                          ...prev,
                          listingType: e.target.value as "demand" | "sale",
                        }
                        : null,
                    )
                  }
                >
                  <option value="sale">{t("sale")}</option>
                  <option value="demand">{t("demand")}</option>
                </Select>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="outline" mr={3} onClick={onEditModalClose}>
              {t("buttons.cancel")}
            </Button>
            <Button
              colorScheme="teal"
              onClick={handleEditItem}
              leftIcon={<Icon as={Save} />}
              size={{ base: "md", md: "lg" }}
            >
              {t("buttons.save")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <AlertDialog
        isOpen={isDeleteDialogOpen}
        leastDestructiveRef={cancelRef}
        onClose={() => setIsDeleteDialogOpen(false)}
        size={{ base: "sm", md: "md" }}
      >
        <AlertDialogOverlay>
          <AlertDialogContent mx={{ base: 4, md: 0 }}>
            <AlertDialogHeader fontSize={{ base: "lg", md: "xl" }}>
              {t("dialogs.delete.title")}
            </AlertDialogHeader>
            <AlertDialogBody>{t("dialogs.delete.message")}</AlertDialogBody>
            <AlertDialogFooter>
              <Button
                ref={cancelRef}
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                {t("buttons.cancel")}
              </Button>
              <Button colorScheme="red" onClick={handleDeleteItem} ml={3}>
                {t("buttons.delete")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      <AlertDialog
        isOpen={isHomeAdDialogOpen}
        leastDestructiveRef={cancelRef}
        onClose={() => setIsHomeAdDialogOpen(false)}
        size={{ base: "sm", md: "md" }}
      >
        <AlertDialogOverlay>
          <AlertDialogContent mx={{ base: 4, md: 0 }}>
            <AlertDialogHeader fontSize={{ base: "lg", md: "xl" }}>
              {t("dialogs.homeAd.title")}
            </AlertDialogHeader>
            <AlertDialogBody>
              <VStack spacing={4} align="stretch">
                <Text>
                  {t("dialogs.homeAd.description") ||
                    "Create a homepage advertisement to increase your product visibility."}
                </Text>

                <FormControl isRequired>
                  <FormLabel>
                    {t("fields.adTitle") || "Advertisement Title"}
                  </FormLabel>
                  <Input
                    value={adTitle}
                    placeholder={selectedItem?.name || ""}
                    onChange={(e) => setAdTitle(e.target.value)}
                  />

                  <FormHelperText>
                    {t("fields.adTitleHelper") ||
                      "Enter a catchy title for your advertisement"}
                  </FormHelperText>
                </FormControl>

                <FormControl mt={3}>
                  <FormLabel>
                    {t("fields.adImage") || "Advertisement Image"}
                  </FormLabel>
                  <Input
                    name="adImage"
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files;
                      if (file) {
                        setAdImageFile(file[0]);
                      }
                    }}
                  />

                  <FormHelperText>
                    {t("fields.adImageHelper") ||
                      "Image must be less than 2MB and in JPG, PNG or GIF format"}
                  </FormHelperText>

                  {adImageFile && (
                    <Box mt={3} borderWidth="1px" borderRadius="md" p={2}>
                      <Image
                        src={import.meta.env.VITE_API_URL + URL.createObjectURL(adImageFile)}
                        alt="Ad Preview"
                        maxH="150px"
                        objectFit="cover"
                        mx="auto"
                      />
                    </Box>
                  )}
                </FormControl>
              </VStack>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button
                ref={cancelRef}
                onClick={() => setIsHomeAdDialogOpen(false)}
              >
                {t("buttons.cancel")}
              </Button>
              <Button
                colorScheme="purple"
                onClick={handleCreateHomeAd}
                ml={3}
                isDisabled={!adImageFile}
              >
                {t("buttons.promote")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      <AlertDialog
        isOpen={isUpgradeDialogOpen}
        leastDestructiveRef={cancelRef}
        onClose={() => setIsUpgradeDialogOpen(false)}
        size={{ base: "sm", md: "md" }}
      >
        <AlertDialogOverlay>
          <AlertDialogContent mx={{ base: 4, md: 0 }}>
            <AlertDialogHeader fontSize={{ base: "lg", md: "xl" }}>
              {t("dialogs.upgrade.title")}
            </AlertDialogHeader>
            <AlertDialogBody>
              <VStack spacing={4} align="stretch">
                <Text>{t("dialogs.upgrade.message")}</Text>
                <Box
                  p={4}
                  bg="purple.50"
                  borderRadius="md"
                  borderWidth="1px"
                  borderColor="purple.200"
                >
                  <VStack spacing={2} align="stretch">
                    <Text fontWeight="bold" color="purple.700">
                      {t("dialogs.upgrade.benefits.title")}
                    </Text>
                    <UnorderedList pl={4} spacing={1}>
                      <ListItem>
                        {t("dialogs.upgrade.benefits.visibility")}
                      </ListItem>
                      <ListItem>
                        {t("dialogs.upgrade.benefits.traffic")}
                      </ListItem>
                      <ListItem>{t("dialogs.upgrade.benefits.sales")}</ListItem>
                    </UnorderedList>
                  </VStack>
                </Box>
              </VStack>
            </AlertDialogBody>
            <AlertDialogFooter>
              <Button
                ref={cancelRef}
                onClick={() => setIsUpgradeDialogOpen(false)}
              >
                {t("buttons.cancel")}
              </Button>
              <Link to="/packages">
                <Button colorScheme="purple" ml={3}>
                  {t("buttons.upgrade")}
                </Button>
              </Link>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </div>
  );
};

export default ItemListing;
