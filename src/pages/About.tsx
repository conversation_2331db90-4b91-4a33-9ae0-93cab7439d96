import React from 'react';
import Layout from '../components/Layout';
import { 
  Building2, 
  Target, 
  Globe2, 
  Users, 
  Shield, 
  TrendingUp,
  CheckCircle,
  ArrowRight,
  Briefcase,
  Award,
  Rocket,
  Mail,
  Handshake
} from 'lucide-react';

const About = () => {
  const stats = [
    { label: 'Aktif <PERSON>', value: '10K+' },
    { label: 'Ülke', value: '150+' },
    { label: 'Başarılı İşlem', value: '25K+' },
    { label: 'Memnuniyet Oranı', value: '%98' }
  ];

  const features = [
    {
      icon: Globe2,
      title: "Küresel Erişim",
      description: "İstanbul'dan tüm dünyaya açılmak isteyen şirketler için sanal ticaretin kalbini oluşturuyoruz."
    },
    {
      icon: TrendingUp,
      title: "Verimli Ticaret",
      description: "Geleneksel fuarların yüksek maliyetleri ve sınırlı erişiminden uzak, işletmelerin daha hızlı ve düşük maliyetli ticaret yapmalarını sağlıyoruz."
    },
    {
      icon: Shield,
      title: "Güvenli Platform",
      description: "Çok dilli destek, veri güvenliği ve yenilikçi ödeme çözümleriyle küresel ticaretin önündeki tüm engelleri kaldırıyoruz."
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <div className="relative bg-gradient-to-r from-primary to-[#169693] overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1557804506-669a67965ba0?auto=format&fit=crop&q=80"
              alt="About Hero"
              className="w-full h-full object-cover opacity-10"
            />
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-white mb-6">
                Hakkımızda
              </h1>
              <p className="text-xl text-white/90 max-w-3xl mx-auto">
                E-exportcity, ticaretin dijital dönüşümünü sağlayarak işletmelerin küresel pazarlara kolayca ulaşabilmesini sağlayan bir yeni nesil dijital ticaret platformudur.
              </p>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" style={{ marginTop: '-4rem' }}>
          <div className="bg-white rounded-xl shadow-xl p-8">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-3xl font-bold text-primary mb-2">
                    {stat.value}
                  </div>
                  <div className="text-gray-600">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                Dijital Ticaretin Geleceği
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Ticaretin geleceği dijitalde şekilleniyor. E-exportcity olarak amacımız, her büyüklükteki işletmenin en uygun koşullarda en geniş müşteri ve tedarikçi ağına erişmesini sağlamak. Sadece bir ticaret platformu değil, aynı zamanda bir global ekosistem sunuyoruz.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {features.map((feature, index) => (
                  <div key={index} className="bg-gray-50 p-6 rounded-xl">
                    <feature.icon className="w-8 h-8 text-primary mb-4" />
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {feature.description}
                    </p>
                  </div>
                ))}
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80"
                alt="Digital Trade"
                className="rounded-2xl shadow-2xl"
              />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-tr from-primary/20 to-transparent"></div>
            </div>
          </div>
        </div>

        {/* Mission Section */}
        <div className="bg-gray-50 py-16 mt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Misyonumuz
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                E-exportcity olarak misyonumuz, ticaretin önündeki engelleri ortadan kaldırarak, firmaların daha hızlı, verimli ve sürdürülebilir bir ticaret deneyimi yaşamasını sağlamaktır.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white p-8 rounded-xl shadow-sm">
                <ul className="space-y-4">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                    <span className="text-gray-600">
                      İşletmelere küresel pazarlara ulaşma fırsatı sunarak, büyümelerini hızlandırıyoruz.
                    </span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                    <span className="text-gray-600">
                      Sektöre özel akıllı eşleştirme sistemleri ile firmaların doğru alıcılarla ve tedarikçilerle buluşmalarını sağlıyoruz.
                    </span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                    <span className="text-gray-600">
                      Çok dilli destek, veri güvenliği ve yenilikçi ödeme çözümleriyle küresel ticaretin önündeki tüm engelleri kaldırıyoruz.
                    </span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-8 rounded-xl shadow-sm">
                <ul className="space-y-4">
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                    <span className="text-gray-600">
                      Geleneksel fuarların yüksek maliyetlerini ortadan kaldırarak, ticareti daha erişilebilir ve daha hızlı hale getiriyoruz.
                    </span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                    <span className="text-gray-600">
                      Yapay zeka destekli eşleştirme algoritmaları ile ticareti daha hızlı ve verimli hale getiriyoruz.
                    </span>
                  </li>
                  <li className="flex items-start space-x-3">
                    <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-1" />
                    <span className="text-gray-600">
                      Her ölçekten işletmeye, her sektörden bireysel brokerlara fırsatlar sunarak, rekabetçi bir ticaret ortamı oluşturuyoruz.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Vision Section */}
        <div className="bg-white py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Vizyonumuz
              </h2>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                E-exportcity olarak vizyonumuz, dijital ticaretin sınırlarını kaldırarak, tüm dünyadaki işletmelere eşit fırsatlar sunmak. İstanbul'dan başlayarak global ticaretin merkezi olmak, veri odaklı çözümlerle ticaretin geleceğini şekillendirmek.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="bg-gray-50 p-6 rounded-xl">
                <Target className="w-8 h-8 text-primary mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Global Merkez
                </h3>
                <p className="text-gray-600">
                  Dünyanın en kapsamlı dijital ticaret platformu olarak alıcıları ve satıcıları en akıllı şekilde bir araya getiren köprü olmak.
                </p>
              </div>

              <div className="bg-gray-50 p-6 rounded-xl">
                <Users className="w-8 h-8 text-primary mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Eşit Fırsatlar
                </h3>
                <p className="text-gray-600">
                  Her ölçekten işletmeye, her sektörden bireysel brokerlara fırsatlar sunarak, rekabetçi bir ticaret ortamı oluşturmak.
                </p>
              </div>

              <div className="bg-gray-50 p-6 rounded-xl">
                <Building2 className="w-8 h-8 text-primary mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Sürdürülebilir Büyüme
                </h3>
                <p className="text-gray-600">
                  Yapay zeka destekli eşleştirme algoritmaları ile ticareti daha hızlı ve verimli hale getirmek.
                </p>
              </div>

              <div className="bg-gray-50 p-6 rounded-xl">
                <Shield className="w-8 h-8 text-primary mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Güvenli Altyapı
                </h3>
                <p className="text-gray-600">
                  Güvenli ödeme altyapıları ve sürekli gelişen çözümlerle, dijital ticaretin geleceğini inşa etmek.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-primary py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold text-white mb-4">
                Geleceğin Ticaretine Katılın
              </h2>
              <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                E-exportcity ile global ticarette yerinizi alın, işinizi büyütün.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <button className="px-8 py-4 bg-white text-primary rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center">
                  <Handshake className="w-5 h-5 mr-2" />
                  Ücretsiz Üye Ol
                </button>
                <button className="px-8 py-4 bg-white/10 text-white rounded-lg font-semibold hover:bg-white/20 transition-colors flex items-center">
                  <Mail className="w-5 h-5 mr-2" />
                  Bize Ulaşın
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default About;