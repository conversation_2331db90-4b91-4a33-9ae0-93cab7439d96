import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Flex,
  Heading,
  Text,
  VStack,
  HStack,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  useToast,
  Image,
  Grid,
  GridItem,
  IconButton,
} from "@chakra-ui/react";
import {
  FaSave,
  FaTrash,
  FaUpload,
  FaShoppingCart,
  FaShoppingBag,
  FaClipboardList,
  FaUserTie
} from 'react-icons/fa';
import axios from "axios";
import { createItem, getItemById, updateItem } from "../api/itemApi";
import { getCategoriesByType, checkCreateRequest } from "../api";
import { ICategory } from "../types/category";
import { useAuth } from "../context/AuthContext";

interface ItemFormData {
  name: string;
  description: string;
  category?: string; // Should store MongoDB ObjectId string, not the category.id field
  type: "product" | "service";
  listingType: "sale" | "demand";
  images: File[];
  existingImages?: string[];
  id?: string;
}

const ProductAdd: React.FC = () => {
  const { t } = useTranslation("itemListing");
  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { id } = useParams<{ id: string }>();
  const { user } = useAuth();

  // Parse query parameters
  const queryParams = new URLSearchParams(location.search);
  const typeParam = queryParams.get('type');
  const isEditMode = Boolean(id);

  const [formData, setFormData] = useState<ItemFormData>({
    name: "",
    description: "",
    category: undefined,
    type: typeParam === 'service' ? 'service' : 'product',
    listingType: "sale",
    images: [],
    existingImages: [],
  });

  const [categories, setCategories] = useState<ICategory[]>([]);
  const [categoryLevels, setCategoryLevels] = useState<ICategory[][]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);

  // Load item data if in edit mode
  useEffect(() => {
    const fetchItemData = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const itemData = await getItemById(id);

        setFormData({
          id: itemData._id,
          name: itemData.name || "",
          description: itemData.description || "",
          category: typeof itemData.category === 'string' ? itemData.category : itemData.category?._id,
          type: itemData.type || "product",
          listingType: itemData.listingType || "sale",
          images: [],
          existingImages: itemData.images || [],
        });

        // If category exists, find all parent categories and set them in the selected categories array
        if (itemData.category) {
          // We need to use the id field for UI display
          // The backend expects _id for MongoDB operations
          // Since we need the categories array, set this up after loading categories in the next useEffect
          const categoryId = typeof itemData.category === 'string'
            ? itemData.category
            : (itemData.category as ICategory)._id;

          // We'll set up the selected category for UI in the next useEffect when categories are loaded
          // For now, just store the MongoDB _id
          setSelectedCategories([categoryId]);
        }

      } catch (error) {
        console.error("Failed to load item data:", error);
        toast({
          title: t("errors.itemLoadFailed"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchItemData();
  }, [id, t, toast]);

  // Load categories based on selected type
  useEffect(() => {
    const loadCategories = async () => {
      try {
        setIsLoading(true);
        // Get all categories for this type
        const allTypeCategories = await getCategoriesByType(
          formData.type.toLowerCase()
        );

        // Store all categories for relationship lookups
        setCategories(allTypeCategories);

        if (allTypeCategories && allTypeCategories.length > 0) {
          // Filter for level 1 categories (no parent_id)
          const firstLevelCategories = allTypeCategories.filter(
            (cat: ICategory) => !cat.parent_id || cat.parent_id === "" || cat.parent_id === null
          );

          // Sort categories alphabetically
          firstLevelCategories.sort((a: ICategory, b: ICategory) => a.name.localeCompare(b.name));

          // Set just the top level categories
          setCategoryLevels([firstLevelCategories]);

          // If in edit mode and we have a category ID, select the right categories
          if (isEditMode && formData.category) {
            // Find the category and its parent categories
            const categoryChain: string[] = [];
            let currentCatId = typeof formData.category === 'string'
              ? formData.category
              : (formData.category as ICategory).id;

            // Start with the current category
            const currentCategory = allTypeCategories.find((cat: ICategory) => cat.id === currentCatId);
            if (currentCategory) {
              categoryChain.unshift(currentCatId);

              // Find each parent in the chain
              let parentId = currentCategory.parent_id;
              while (parentId) {
                const parentCategory = allTypeCategories.find((cat: ICategory) => cat.id === parentId);
                if (parentCategory) {
                  categoryChain.unshift(parentCategory.id);
                  parentId = parentCategory.parent_id;
                } else {
                  break;
                }
              }

              // Update selected categories with the full chain
              setSelectedCategories(categoryChain);
            }
          }
        }
      } catch (error) {
        console.error("Failed to load categories:", error);
        toast({
          title: t("errors.categoryLoad"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadCategories();
  }, [formData.type, t, toast, isEditMode, formData.category]);

  const handleTypeChange = (newType: "product" | "service") => {
    setFormData({ ...formData, type: newType });
    setSelectedCategories([]);
    setCategoryLevels([]);

    // Update URL with the new type parameter
    const newParams = new URLSearchParams(location.search);
    newParams.set('type', newType.toLowerCase());
    navigate(`${location.pathname}?${newParams.toString()}`, { replace: true });
  };

  const handleCategoryChange = async (level: number, categoryId: string) => {
    try {
      // Important: Don't set isLoading before calling API -
      // it can cause render issues with async functions

      // If empty selection, clear this level and all subsequent levels
      if (!categoryId) {
        setSelectedCategories(selectedCategories.slice(0, level));
        setCategoryLevels(categoryLevels.slice(0, level + 1));
        setFormData({ ...formData, category: undefined });
        return;
      }

      // Find the selected category to get its string ID (needed for finding children)
      const selectedCategory = categories.find(cat => cat.id === categoryId);
      if (!selectedCategory) {
        console.error('Selected category not found:', categoryId);
        return;
      }

      // Update selected categories up to this level
      const newSelectedCategories = [
        ...selectedCategories.slice(0, level),
        categoryId,
      ];
      setSelectedCategories(newSelectedCategories);

      // Set the category in the form data (always use the most specific category)
      // Ensure we're passing the full category object with _id instead of just string ID
      const categoryToSet = categories.find(cat => cat.id === categoryId);
      setFormData({ ...formData, category: categoryToSet?._id || categoryId });

      // Get the authentication token for API call
      const token = localStorage.getItem('userToken');

      // Now set loading to true for UI feedback
      setIsLoading(true);

      try {
        // Fetch subcategories from the API endpoint
        const response = await axios.get(`${import.meta.env.VITE_API_BASE_URL}/public/categories/${categoryId}/subcategories`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          withCredentials: true,
        });

        // Handle the subcategory response
        if (response.status === 200 && response.data) {
          console.log("Loaded subcategories:", response.data);
          // Extract subcategories from the response - API returns { success: true, data: [...categories] }
          const childCategories = response.data.success && response.data.data ? response.data.data : [];

          // Sort child categories alphabetically
          childCategories.sort((a: ICategory, b: ICategory) => a.name.localeCompare(b.name));

          // Update category levels after API response is received
          if (childCategories.length > 0) {
            // Create a copy of the current category levels
            let newCategoryLevels = [...categoryLevels];

            // Add new subcategories at the correct level + 1
            newCategoryLevels[level + 1] = childCategories;

            // Trim any levels deeper than the current one + 1 if we're not at the 3rd level
            if (level < 2) {  // If we're at level 0 or 1
              newCategoryLevels = newCategoryLevels.slice(0, level + 2);
            }

            setCategoryLevels(newCategoryLevels);
            console.log("Updated category levels:", newCategoryLevels);
          } else {
            // No children found, so remove any deeper levels
            setCategoryLevels(categoryLevels.slice(0, level + 1));
          }
        }
      } catch (error) {
        console.error("Failed to fetch subcategories:", error);
        // Keep existing category levels (don't modify them on error)

        toast({
          title: t("errors.subcategoryLoad"),
          description: t("errors.tryAgain.description"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } finally {
        // Important: Set loading to false only after API calls complete
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Failed to handle category change:", error);
      toast({
        title: t("errors.loadSubcategories"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      setIsLoading(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files) {
      const fileArray = Array.from(files);
      if (fileArray.length > 5) {
        toast({
          title: t("errors.tooManyFiles"),
          description: t("errors.maxFiveFiles"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        return;
      }
      setFormData({ ...formData, images: fileArray });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);

      if (!formData.name || !formData.description || !formData.category) {
        toast({
          title: t("errors.validation.title"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      // For create mode, check if user has remaining create requests
      if (!isEditMode) {
        const checkResponse = await checkCreateRequest();
        if (!checkResponse.hasRemaining) {
          toast({
            title: checkResponse.message || t("errors.noCreateRequests.title"),
            description: t("errors.upgradeRequired.description"),
            status: "error",
            duration: 5000,
            isClosable: true,
          });
          return;
        }
      }

      // Create form data with image files directly
      const submitFormData = new FormData();
      submitFormData.append("name", formData.name);
      submitFormData.append("description", formData.description);
      if (formData.category) {
        // Use _id from the category object if available, otherwise use the stored category value
        // This ensures we're sending the MongoDB ObjectId to the backend
        const categoryId = formData.category;
        submitFormData.append("category", categoryId);
      }
      submitFormData.append("type", formData.type);
      submitFormData.append("listingType", formData.listingType);

      // Add information about existing images that should be kept
      if (formData.existingImages && formData.existingImages.length > 0) {
        submitFormData.append("existingImages", JSON.stringify(formData.existingImages));
      }

      // Attach new image files directly to FormData
      if (formData.images.length > 0) {
        formData.images.forEach((file) => {
          submitFormData.append("images", file);
        });
      }

      if (isEditMode && formData.id) {
        // Update existing item
        await updateItem(formData.id, submitFormData);
        toast({
          title: t("success.itemUpdated"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        // Create new item with status "INACTIVE" so it needs admin approval
        submitFormData.append("status", "INACTIVE");

        // Add store location information if available from user's store
        if (user && (user as any)?.store?.location) {
          if ((user as any).store.location.country) {
            submitFormData.append("country", (user as any).store.location.country);
          }
          if ((user as any).store.location.city) {
            submitFormData.append("city", (user as any).store.location.city);
          }
        }

        await createItem(submitFormData);
        toast({
          title: t("success.itemCreated"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }

      // Navigate back to items page
      navigate('/items');
    } catch (error: any) {
      console.error("Error saving item:", error);

      // Extract error message from API response if available
      let errorMessage = t("errors.itemSaveFailed");

      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.error) {
          errorMessage = error.response.data.error;
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast({
        title: errorMessage,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box as="form" onSubmit={handleSubmit}>
      <Flex direction="column" gap={8}>
        {/* Header */}
        <Box bg="white" p={6} borderRadius="xl" shadow="sm">
          <Flex justifyContent="space-between" alignItems="center" flexWrap="wrap" gap={4}>
            <Box>
              <Heading as="h1" size="lg" mb={2}>
                {isEditMode ? t("editItem") :
                  (formData.listingType === 'sale'
                    ? (formData.type === 'product' ? t("addProduct") : t("addService"))
                    : (formData.type === 'product' ? t("requestProduct") : t("requestService")))
                }
              </Heading>
              <Text color="gray.600">
                {formData.listingType === 'sale'
                  ? (formData.type === 'product' ? t("addProductDesc") : t("addServiceDesc"))
                  : (formData.type === 'product' ? t("requestProductDesc") : t("requestServiceDesc"))}
              </Text>
            </Box>
          </Flex>
        </Box>

        {/* Type and Mode Selectors */}
        <Box bg="white" p={6} borderRadius="xl" shadow="sm">
          <Heading as="h2" size="md" mb={4}>
            {t("selectTypeAndMode")}
          </Heading>

          <VStack spacing={6} align="stretch">
            {/* Type Selector */}
            <FormControl>
              <FormLabel>{t("type")}</FormLabel>
              <HStack spacing={4}>
                <Button
                  leftIcon={<FaShoppingBag />}
                  colorScheme={formData.type === 'product' ? 'teal' : 'gray'}
                  variant={formData.type === 'product' ? 'solid' : 'outline'}
                  onClick={() => handleTypeChange('product')}
                  flex="1"
                >
                  {t("product")}
                </Button>
                <Button
                  leftIcon={<FaUserTie />}
                  colorScheme={formData.type === 'service' ? 'teal' : 'gray'}
                  variant={formData.type === 'service' ? 'solid' : 'outline'}
                  onClick={() => handleTypeChange('service')}
                  flex="1"
                >
                  {t("service")}
                </Button>
              </HStack>
            </FormControl>

            {/* Listing Type Selector */}
            <FormControl>
              <FormLabel>{t("listingType")}</FormLabel>
              <HStack spacing={4}>
                <Button
                  leftIcon={<FaShoppingCart />}
                  colorScheme={formData.listingType === 'sale' ? 'teal' : 'gray'}
                  variant={formData.listingType === 'sale' ? 'solid' : 'outline'}
                  onClick={() => setFormData({ ...formData, listingType: 'sale' })}
                  flex="1"
                >
                  {t("sale")}
                </Button>
                <Button
                  leftIcon={<FaClipboardList />}
                  colorScheme={formData.listingType === 'demand' ? 'teal' : 'gray'}
                  variant={formData.listingType === 'demand' ? 'solid' : 'outline'}
                  onClick={() => setFormData({ ...formData, listingType: 'demand' })}
                  flex="1"
                >
                  {t("demand")}
                </Button>
              </HStack>
            </FormControl>
          </VStack>
        </Box>

        {/* Basic Information */}
        <Box bg="white" p={6} borderRadius="xl" shadow="sm">
          <Heading as="h2" size="md" mb={4}>
            {t("basicInfo")}
          </Heading>

          <VStack spacing={6} align="stretch">
            {/* Name */}
            <FormControl isRequired>
              <FormLabel>
                {formData.listingType === 'sale'
                  ? (formData.type === 'product' ? t("fields.productName") : t("fields.serviceName"))
                  : t("fields.requestTitle")}
              </FormLabel>
              <Input
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder={formData.listingType === 'sale'
                  ? (formData.type === 'product' ? t("placeholders.productName") : t("placeholders.serviceName"))
                  : t("placeholders.requestTitle")}
              />
            </FormControl>

            {/* Description */}
            <FormControl isRequired>
              <FormLabel>
                {formData.listingType === 'sale' ? t("fields.description") : t("fields.requestDetails")}
              </FormLabel>
              <Textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder={formData.listingType === 'sale'
                  ? (formData.type === 'product'
                    ? t("placeholders.productDescription")
                    : t("placeholders.serviceDescription"))
                  : (formData.type === 'product'
                    ? t("placeholders.productRequestDescription")
                    : t("placeholders.serviceRequestDescription"))}
                minH="200px"
              />
            </FormControl>

            {/* Categories */}
            {categoryLevels.map((categories, levelIndex) => (
              <FormControl
                key={levelIndex}
                isRequired={levelIndex === 0}
              >
                <FormLabel>
                  {levelIndex === 0
                    ? t("categoryLevels.main")
                    : t("categoryLevels.level", { level: levelIndex + 1 })}
                </FormLabel>
                <Select
                  value={selectedCategories[levelIndex] || ""}
                  onChange={(e) => handleCategoryChange(levelIndex, e.target.value)}
                  placeholder={t("categoryLevels.select", { level: levelIndex + 1 })}
                  isDisabled={isLoading}
                >
                  {categories.map((category) => (
                    <option key={category._id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Select>
              </FormControl>
            ))}
          </VStack>
        </Box>

        {/* Images */}
        <Box bg="white" p={6} borderRadius="xl" shadow="sm">
          <Heading as="h2" size="md" mb={4}>
            {t("fields.images")}
          </Heading>

          <VStack spacing={4} align="stretch">
            <FormControl>
              <Input
                type="file"
                accept="image/*"
                multiple
                name="images"
                ref={fileInputRef}
                onChange={handleFileSelect}
                display="none"
              />
              <Button
                leftIcon={<FaUpload />}
                onClick={() => fileInputRef.current?.click()}
                colorScheme="gray"
                variant="outline"
                w="full"
              >
                {t("buttons.uploadImages")}
              </Button>
              <Text fontSize="sm" color="gray.500" mt={1}>
                {t("fields.imageHelp")}
              </Text>
            </FormControl>

            {formData.images.length > 0 && (
              <Grid templateColumns="repeat(5, 1fr)" gap={2}>
                {Array.from(formData.images).map((file, index) => (
                  <GridItem key={index} position="relative">
                    <Box position="relative">
                      <Image
                        src={URL.createObjectURL(file)}
                        alt={`Preview ${index + 1}`}
                        boxSize="100px"
                        objectFit="cover"
                        borderRadius="md"
                      />
                      <IconButton
                        aria-label="Remove image"
                        icon={<FaTrash />}
                        size="sm"
                        colorScheme="red"
                        position="absolute"
                        top={1}
                        right={1}
                        onClick={() => {
                          const newImages = Array.from(formData.images);
                          newImages.splice(index, 1);
                          setFormData({ ...formData, images: newImages });
                        }}
                      />
                    </Box>
                  </GridItem>
                ))}
              </Grid>
            )}
          </VStack>
        </Box>

        {/* Submit Buttons */}
        <Flex justifyContent="flex-end" gap={4} mt={4}>
          <Button
            variant="ghost"
            onClick={() => navigate('/items')}
          >
            {t("buttons.cancel")}
          </Button>
          <Button
            type="submit"
            colorScheme="teal"
            leftIcon={<FaSave />}
            isLoading={isSubmitting}
          >
            {isEditMode
              ? t("buttons.updateItem")
              : (formData.listingType === 'sale'
                ? (formData.type === 'product' ? t("buttons.publishProduct") : t("buttons.publishService"))
                : t("buttons.publishRequest"))}
          </Button>
        </Flex>
      </Flex>
    </Box>
  );
};

export default ProductAdd;