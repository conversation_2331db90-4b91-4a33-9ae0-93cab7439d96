import React, { useEffect, useState } from "react";
import { useSocket } from "../context/SocketContext";
import { getChatHistory } from "../api";
import { IMessage, IMessageSocketData } from "../types/message";

interface ChatProps {
  roomId: string;
  userId: string;
}

const Chat: React.FC<ChatProps> = ({ roomId, userId }) => {
  const socket = useSocket();
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [input, setInput] = useState("");

  useEffect(() => {
    if (!socket) return;

    (socket as any)?.emit("joinRoom", roomId);

    // Fetch existing chat history
    getChatHistory(roomId).then((history) => {
      setMessages(history);
    });

    // Listen for incoming messages
    (socket as any).on("message", (message: IMessage) => {
      setMessages((prev) => [...prev, message]);
    });

    return () => {
      (socket as any).off("message");
      (socket as any).emit("leaveRoom", roomId);
    };
  }, [socket, roomId]);

  const handleSend = () => {
    if (input.trim() !== "" && socket) {
      const messageData: IMessageSocketData = {
        content: input,
        senderId: userId,
        recipientId: "", // Add recipient ID if needed
        roomId,
        productId: "", // Add product ID if needed
      };

      (socket as any).emit("chatMessage", messageData);
      setInput("");
    }
  };

  return (
    <div className="chat-container">
      <div className="messages">
        {messages.map((msg: any, idx) => (
          <div
            key={idx}
            className={`message ${
              typeof msg.senderId === "string"
                ? msg.senderId === userId
                  ? "sent"
                  : "received"
                : msg.senderId._id === userId
                  ? "sent"
                  : "received"
            }`}
          >
            <span>{msg.content}</span>
            <span className="timestamp">
              {new Date(msg.timestamp).toLocaleTimeString()}
            </span>
          </div>
        ))}
      </div>
      <div className="input-container">
        <input
          type="text"
          placeholder="Type your message..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyPress={(e) => e.key === "Enter" && handleSend()}
        />

        <button onClick={handleSend}>Send</button>
      </div>
    </div>
  );
};

export default Chat;
