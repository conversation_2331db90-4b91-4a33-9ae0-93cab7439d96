import React, { useState, useEffect } from "react";
import { useNavigate, Link, useLocation } from "react-router-dom";
import { useToast } from "@chakra-ui/react";
import { Eye, EyeOff, MessageCircle } from "lucide-react";
import {
  registerUser,
  getCities,
  getCountries,
  api
} from "../api";
import { useTranslation } from "react-i18next";
import { ICity, ICountry } from "@/types/user";
import { ICategory } from "@/types/category";

// New UI Components
import OfflineHeader from "../components/OfflineHeader";
import Footer from "../components/common/Footer";
import NewTermsModal from "../components/register/NewTermsModal";
import PrivacyPolicyModal from "../components/register/PrivacyPolicyModal";

const Register: React.FC = () => {
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [countryCode, setCountryCode] = useState("+90");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [address, setAddress] = useState("");
  const [city, setCity] = useState<ICity | null>(null);
  const [country, setCountry] = useState<ICountry | null>(null);
  const [birthDate, setBirthDate] = useState<any>("");
  const [zipCode, setZipCode] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [cities, setCities] = useState<ICity[]>([]);
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [category, setCategory] = useState<ICategory | null>(null);
  const [level2Categories, setLevel2Categories] = useState<ICategory[]>([]);
  const [level2Category, setLevel2Category] = useState<ICategory | null>(null);
  const [level3Categories, setLevel3Categories] = useState<ICategory[]>([]);
  const [level3Category, setLevel3Category] = useState<ICategory | null>(null);
  const [referralCode, setReferralCode] = useState("");
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);
  const [privacyAccepted, setPrivacyAccepted] = useState(false);
  const [isPrivacyModalOpen, setIsPrivacyModalOpen] = useState(false);
  const [accountType, setAccountType] = useState('company');
  const [error, setError] = useState<string>("");

  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation(["register", "common"]);
  const location = useLocation();

  // Account type options
  const accountTypes = [
    { id: 'company', name: t('register:form.accountType.company') },
    { id: 'individual', name: t('register:form.accountType.individual') },
    { id: 'broker', name: t('register:form.accountType.broker') }
  ];

  // Define all fetch functions first
  const fetchCitiesData = React.useCallback(async (selectedCountry: string) => {
    try {
      const data = await getCities(selectedCountry);
      setCities(data);
      setCity(null);
    } catch (error: any) {
      console.error("Failed to fetch cities:", error);
      toast({
        title: t("register:toasts.fetchCitiesFailed.title"),
        description: t("register:toasts.fetchCitiesFailed.description"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  }, [t, toast]);

  const fetchSubcategories = React.useCallback(async (categoryId: string) => {
    try {
      console.log("Fetching subcategories for category ID:", categoryId);
      // Use id instead of _id for the API call
      const response = await api.get(`/public/categories/${categoryId}/subcategories`);

      if (response.status === 200 && response.data.data) {
        console.log("Loaded subcategories:", response.data.data);
        setLevel2Categories(response.data.data);
      } else {
        setLevel2Categories([]);
      }
    } catch (error: any) {
      console.error("Failed to fetch subcategories:", error);
      toast({
        title: t("register:toasts.fetchCategoriesFailed.title") || "Error",
        description: t("register:toasts.fetchCategoriesFailed.description") || "Failed to load subcategories",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      // Reset subcategories on error
      setLevel2Categories([]);
    }
  }, [t, toast]);

  const fetchLevel3Categories = React.useCallback(async (categoryId: string) => {
    try {
      console.log("Fetching level 3 categories for category ID:", categoryId);
      // Use id instead of _id for the API call
      const response = await api.get(`/public/categories/${categoryId}/subcategories`);

      if (response.status === 200 && response.data.data) {
        console.log("Loaded level 3 categories:", response.data.data);
        setLevel3Categories(response.data.data);
      } else {
        setLevel3Categories([]);
      }
    } catch (error: any) {
      console.error("Failed to fetch level 3 categories:", error);
      toast({
        title: t("register:toasts.fetchCategoriesFailed.title") || "Error",
        description: t("register:toasts.fetchCategoriesFailed.description") || "Failed to load subcategories",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      // Reset level 3 categories on error
      setLevel3Categories([]);
    }
  }, [t, toast]);

  const fetchInitialData = React.useCallback(async () => {
    try {
      // Fetch both countries and categories in parallel using public endpoints
      const [countriesData, categoriesResponse] = await Promise.all([
        getCountries(),
        api.get("/public/categories")
      ]);

      setCountries(countriesData);

      if (categoriesResponse.status === 200 && categoriesResponse.data.data) {
        setCategories(categoriesResponse.data.data);
        console.log("Loaded categories:", categoriesResponse.data.data);
      }
    } catch (error: any) {
      console.error("Failed to fetch initial data:", error);
      toast({
        title: t("register:toasts.fetchDataFailed.title") || "Error",
        description: t("register:toasts.fetchDataFailed.description") || "Failed to load initial data",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  }, [t, toast]);

  // Load initial data on component mount
  useEffect(() => {
    document.title = t("register:title") + " | " + t("common:appName");
    fetchInitialData();
  }, [fetchInitialData, t]);

  // Load cities when country changes
  useEffect(() => {
    if (country) {
      fetchCitiesData(country.code);
    }
  }, [country, fetchCitiesData]);

  // Add effect to fetch subcategories when a category is selected
  useEffect(() => {
    if (category) {
      fetchSubcategories(category.id);
    } else {
      setLevel2Categories([]);
      setLevel2Category(null);
    }
  }, [category, fetchSubcategories]);

  // Add effect to fetch level 3 categories when a level 2 category is selected
  useEffect(() => {
    if (level2Category) {
      fetchLevel3Categories(level2Category.id);
    } else {
      setLevel3Categories([]);
      setLevel3Category(null);
    }
  }, [level2Category, fetchLevel3Categories]);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    if (!termsAccepted) {
      setError(t("register:form.terms.error"));
      return;
    }

    if (!privacyAccepted) {
      setError(t("common:register.privacyPolicyRequired"));
      return;
    }

    if (!email || !password || !firstName || !lastName || !birthDate ||
      !category || !phoneNumber) {
      setError(t("register:form.validation.requiredFields"));
      return;
    }

    setIsLoading(true);
    try {
      const payload: any = {
        firstName,
        lastName,
        phoneNumber: countryCode + phoneNumber,
        email,
        password,
        address,
        city: city?.name,
        country: country?.name,
        zipCode,
        birthDate,
        categoryLevel1Id: category?.id,
        categoryLevel2Id: level2Category?.id,
        categoryLevel3Id: level3Category?.id,
        referralCode: referralCode || undefined,
        accountType
      };

      const result = await registerUser(payload);
      if (result.success) {
        toast({
          title: t("register:toasts.registerSuccess.title"),
          description: t("register:toasts.registerSuccess.description"),
          status: "success",
          duration: 2000,
          isClosable: true,
        });
        navigate("/");
      } else {
        setError(result.message || t("register:toasts.registerFailed.description"));
      }
    } catch (error: any) {
      setError(error.response?.data?.message || t("register:toasts.errorMessage.description"));
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => setShowPassword(!showPassword);

  useEffect(() => {
    // Get referral code from URL if exists
    const params = new URLSearchParams(location.search);
    const refCode = params.get("ref");
    if (refCode) {
      setReferralCode(refCode);
    }
  }, [location.search, setReferralCode]);

  return (
    <div className="relative w-full min-h-screen flex flex-col overflow-y-auto">
      <OfflineHeader />

      <div className="relative w-full bg-cover bg-center pt-8 pb-16" style={{ backgroundImage: `url("/bg.jpg")` }}>
        {/* Blur overlay */}
        <div className="absolute inset-0 bg-black/30 backdrop-blur-md"></div>

        {/* Main Content */}
        <div className="flex justify-center py-6 md:py-12 px-3 md:px-6 lg:px-8 relative">
          <div className="w-full max-w-sm md:max-w-4xl relative z-10">
            <div className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl p-4 md:p-8">
              <div className="text-center mb-4 md:mb-6">
                <h1 className="text-xl md:text-2xl font-bold text-gray-900">{t("register:title")}</h1>
                <p className="text-sm md:text-base text-gray-600 mt-2">{t("register:subtitle")}</p>
              </div>

              {error && (
                <div className="bg-red-50 text-red-600 p-4 rounded-lg mb-6">
                  {error}
                </div>
              )}

              <form onSubmit={handleRegister} className="space-y-4 md:space-y-6">
                {/* Account Type Selection */}
                <div>
                  <h2 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">{t("register:form.accountType.title")}</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-4">
                    {accountTypes.map((type) => (
                      <button
                        key={type.id}
                        type="button"
                        onClick={() => setAccountType(type.id)}
                        className={`p-3 md:p-4 rounded-lg border transition-all ${accountType === type.id
                          ? 'border-primary bg-primary/10 text-primary'
                          : 'border-gray-300 hover:border-primary/50'
                          }`}
                      >
                        <span className="text-xs md:text-sm font-medium">{type.name}</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Category Selection */}
                <div>
                  <h2 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">{t("register:form.sector.title")}</h2>
                  <div className="grid grid-cols-1 gap-3 md:grid-cols-3 md:gap-4">
                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.sector.level1")} *
                      </label>
                      <select
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={category?.id || ""}
                        onChange={(e) => {
                          const selected = categories.find(
                            (cat) => cat.id === e.target.value
                          );
                          setCategory(selected || null);
                        }}
                        required
                      >
                        <option value="">{t("register:form.sector.selectPlaceholder")}</option>
                        {categories && categories.length > 0 ? (
                          categories.map((cat) => (
                            <option key={cat.id} value={cat.id}>
                              {cat.name}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {t("register:toasts.fetchCategoriesFailed.description")}
                          </option>
                        )}
                      </select>
                    </div>

                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.sector.level2")}
                      </label>
                      <select
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={level2Category?.id || ""}
                        onChange={(e) => {
                          const selected = level2Categories.find(
                            (cat) => cat.id === e.target.value
                          );
                          setLevel2Category(selected || null);
                        }}
                        disabled={!category || level2Categories.length === 0}
                      >
                        <option value="">{t("register:form.sector.selectPlaceholder")}</option>
                        {level2Categories && level2Categories.length > 0 ? (
                          level2Categories.map((cat) => (
                            <option key={cat.id} value={cat.id}>
                              {cat.name}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {category ? "No subcategories available" : "Select a main category first"}
                          </option>
                        )}
                      </select>
                    </div>

                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.sector.level3")}
                      </label>
                      <select
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={level3Category?.id || ""}
                        onChange={(e) => {
                          const selected = level3Categories.find(
                            (cat) => cat.id === e.target.value
                          );
                          setLevel3Category(selected || null);
                        }}
                        disabled={!level2Category || level3Categories.length === 0}
                      >
                        <option value="">{t("register:form.sector.selectPlaceholder")}</option>
                        {level3Categories && level3Categories.length > 0 ? (
                          level3Categories.map((cat) => (
                            <option key={cat.id} value={cat.id}>
                              {cat.name}
                            </option>
                          ))
                        ) : (
                          <option value="" disabled>
                            {level2Category ? "No subcategories available" : "Select a subcategory first"}
                          </option>
                        )}
                      </select>
                    </div>
                  </div>
                </div>

                {/* Personal Information */}
                <div>
                  <h2 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">{t("register:form.personal.title")}</h2>
                  <div className="grid grid-cols-1 gap-3 md:grid-cols-4 md:gap-4">
                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.personal.firstName")} *
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        placeholder={t("register:form.personal.firstNamePlaceholder")}
                        required
                      />
                    </div>

                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.personal.lastName")} *
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        placeholder={t("register:form.personal.lastNamePlaceholder")}
                        required
                      />
                    </div>

                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.personal.birthDate")} *
                      </label>
                      <div className="relative">
                        <input
                          type="date"
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                          value={birthDate}
                          onChange={(e) => setBirthDate(e.target.value)}
                          placeholder="dd/mm/yyyy"
                          required
                        />
                      </div>
                    </div>

                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.personal.password")} *
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? "text" : "password"}
                          className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary pr-10"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          placeholder={t("register:form.personal.passwordPlaceholder")}
                          required
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400"
                          onClick={togglePasswordVisibility}
                        >
                          {showPassword ? (
                            <EyeOff className="h-5 w-5" />
                          ) : (
                            <Eye className="h-5 w-5" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.personal.email")} *
                      </label>
                      <input
                        type="email"
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder={t("register:form.personal.emailPlaceholder")}
                        required
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.personal.phone")} *
                      </label>
                      <div className="flex">
                        <select
                          className="w-20 p-2 border border-gray-300 rounded-l-md focus:ring-primary focus:border-primary"
                          value={countryCode}
                          onChange={(e) => setCountryCode(e.target.value)}
                        >
                          <option value="+90">+90</option>
                          <option value="+1">+1</option>
                          <option value="+44">+44</option>
                          <option value="+49">+49</option>
                          <option value="+33">+33</option>
                          <option value="+39">+39</option>
                          <option value="+7">+7</option>
                        </select>
                        <input
                          type="tel"
                          className="flex-1 p-2 border border-gray-300 rounded-r-md focus:ring-primary focus:border-primary"
                          value={phoneNumber}
                          onChange={(e) => setPhoneNumber(e.target.value)}
                          placeholder="5XX XXX XX XX"
                          required
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Location Information */}
                <div>
                  <h2 className="text-base md:text-lg font-semibold text-gray-900 mb-3 md:mb-4">{t("register:form.location.title")}</h2>
                  <div className="grid grid-cols-1 gap-3 md:grid-cols-4 md:gap-4">
                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.location.country")}
                      </label>
                      <select
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={country?.code || ""}
                        onChange={(e) => {
                          const selectedCountry = countries.find(
                            (c) => c.code === e.target.value
                          );
                          setCountry(selectedCountry || null);
                        }}
                      >
                        <option value="">{t("register:form.location.selectCountry")}</option>
                        {countries.map((country) => (
                          <option key={country.code} value={country.code}>
                            {country.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.location.city")}
                      </label>
                      <select
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={city?.name || ""}
                        onChange={(e) => {
                          const selectedCity = cities.find(
                            (c) => c.name === e.target.value
                          );
                          setCity(selectedCity || null);
                        }}
                        disabled={!country}
                      >
                        <option value="">{t("register:form.location.selectCity")}</option>
                        {cities.map((city) => (
                          <option key={city.name} value={city.name}>
                            {city.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.location.zipCode")}
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={zipCode}
                        onChange={(e) => setZipCode(e.target.value)}
                        placeholder={t("register:form.location.zipCodePlaceholder")}
                      />
                    </div>

                    <div className="md:col-span-1">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.referralCode")}
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={referralCode}
                        onChange={(e) => setReferralCode(e.target.value)}
                        placeholder={t("register:form.referralCodePlaceholder")}
                      />
                    </div>

                    <div className="md:col-span-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t("register:form.location.address")}
                      </label>
                      <input
                        type="text"
                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-primary focus:border-primary"
                        value={address}
                        onChange={(e) => setAddress(e.target.value)}
                        placeholder={t("register:form.location.addressPlaceholder")}
                      />
                    </div>
                  </div>
                </div>

                {/* Terms and Conditions */}
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="terms"
                        type="checkbox"
                        className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                        checked={termsAccepted}
                        onChange={(e) => setTermsAccepted(e.target.checked)}
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="terms" className="text-gray-700">
                        {t("register:form.terms.accept")}{" "}
                        <button
                          type="button"
                          className="text-primary hover:text-primary-hover font-medium"
                          onClick={() => setIsTermsModalOpen(true)}
                        >
                          {t("register:form.terms.readTerms")}
                        </button>
                      </label>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="privacy"
                        type="checkbox"
                        className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                        checked={privacyAccepted}
                        onChange={(e) => setPrivacyAccepted(e.target.checked)}
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="privacy" className="text-gray-700">
                        {t("common:register.privacyPolicyLabel", { 
                          privacyPolicy: ''
                        }).split('{privacyPolicy}')[0]}
                        <button
                          type="button"
                          className="text-primary hover:text-primary-hover font-medium"
                          onClick={() => setIsPrivacyModalOpen(true)}
                        >
                          {t("common:register.privacyPolicyLink")}
                        </button>
                        {t("common:register.privacyPolicyLabel", { 
                          privacyPolicy: ''
                        }).split('{privacyPolicy}')[1]}
                      </label>
                    </div>
                  </div>
                </div>

                <button
                  type="submit"
                  className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-primary-hover transition-colors"
                  disabled={isLoading}
                >
                  {isLoading ? t("register:form.submitting") : t("register:form.submit")}
                </button>

                <div className="text-center mt-4">
                  <p className="text-sm text-gray-600">
                    {t("register:form.alreadyHaveAccount")}{" "}
                    <Link to="/login" className="font-medium text-primary hover:text-primary-hover">
                      {t("register:form.login")}
                    </Link>
                  </p>
                </div>
              </form>
            </div>
          </div>
        </div>

        <NewTermsModal
          isOpen={isTermsModalOpen}
          onClose={() => setIsTermsModalOpen(false)}
          onAccept={() => {
            setTermsAccepted(true);
            setIsTermsModalOpen(false);
          }}
        />

        <PrivacyPolicyModal
          isOpen={isPrivacyModalOpen}
          onClose={() => setIsPrivacyModalOpen(false)}
          onAccept={() => {
            setPrivacyAccepted(true);
            setIsPrivacyModalOpen(false);
          }}
        />
      </div>

      {/* Chat Button */}
      <div className="fixed bottom-6 right-6 z-40">
        <button
          onClick={() => window.open('https://wa.me/' + import.meta.env.VITE_WHATSAPP_NUMBER)}
          className="bg-primary hover:bg-primary-hover text-white p-4 rounded-full shadow-lg transition-colors"
        >
          <MessageCircle className="h-6 w-6" />
        </button>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Register;