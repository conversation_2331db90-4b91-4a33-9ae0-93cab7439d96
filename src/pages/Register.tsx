import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { Mail, Lock, Eye, EyeOff } from 'lucide-react';

const Register = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: {
      code: '+90',
      number: ''
    },
    birthDate: '',
    password: '',
    sector: '',
    subsector: '',
    type: 'company' // 'company', 'individual', 'broker'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');

  const sectors = [
    {
      name: "Bilgi Teknolojileri ve Yazılım",
      subsectors: ["Bulut Teknolojileri", "Telekomünikasyon", "Veri Analitiği", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Geliştirme"]
    },
    {
      name: "<PERSON><PERSON><PERSON> ve Atık Yönetimi",
      subsectors: ["Atık Yönetimi", "Çevre Koruma"]
    },
    {
      name: "Eğitim ve Danışmanlık",
      subsectors: ["Danışmanlık Hizmetleri", "Eğitim Kurumları", "Profesyonel Eğitim"]
    }
  ];

  const accountTypes = [
    { id: 'company', name: 'Kurumsal Firma' },
    { id: 'individual', name: 'Şahıs Firması' },
    { id: 'broker', name: 'Broker' }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.email || !formData.password) {
      setError('Lütfen tüm alanları doldurun.');
      return;
    }

    try {
      console.log('Registration attempt with:', formData);
      localStorage.setItem('isLoggedIn', 'true');
      navigate('/');
    } catch (err) {
      setError('Kayıt olurken bir hata oluştu. Lütfen tekrar deneyin.');
    }
  };

  return (
    <Layout>
      <div 
        className="min-h-[calc(100vh-80px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative"
        style={{
          backgroundImage: 'url("https://images.unsplash.com/photo-*************-c3d57bc86b40?auto=format&fit=crop&q=80")',
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        }}
      >
        <div className="absolute inset-0 backdrop-blur-md bg-black/30" />

        <div className="w-full max-w-md relative z-10">
          <div className="bg-white/90 backdrop-blur-lg rounded-2xl shadow-xl p-8">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900">
                Hesap Oluşturun
              </h1>
              <p className="mt-2 text-gray-600">
                E-exportcity'ye hoş geldiniz
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <div className="bg-red-50 text-red-600 p-4 rounded-lg text-sm">
                  {error}
                </div>
              )}

              {/* Account Type Selection */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Hesap Türü</h2>
                <div className="grid grid-cols-3 gap-4">
                  {accountTypes.map((type) => (
                    <button
                      key={type.id}
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, type: type.id }))}
                      className={`p-4 rounded-lg border-2 transition-all ${
                        formData.type === type.id
                          ? 'border-primary bg-primary/10'
                          : 'border-gray-200 hover:border-primary/50'
                      }`}
                    >
                      <span className="text-sm font-medium">{type.name}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Sector Selection */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Sektör Bilgileri</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sektör*
                    </label>
                    <select
                      value={formData.sector}
                      onChange={(e) => {
                        setFormData(prev => ({
                          ...prev,
                          sector: e.target.value,
                          subsector: ''
                        }));
                      }}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    >
                      <option value="">Sektör seçin</option>
                      {sectors.map((sector) => (
                        <option key={sector.name} value={sector.name}>
                          {sector.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Alt Sektör*
                    </label>
                    <select
                      value={formData.subsector}
                      onChange={(e) => setFormData(prev => ({ ...prev, subsector: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                      disabled={!formData.sector}
                    >
                      <option value="">Alt sektör seçin</option>
                      {formData.sector && sectors.find(s => s.name === formData.sector)?.subsectors.map((subsector) => (
                        <option key={subsector} value={subsector}>
                          {subsector}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {/* Personal Information */}
              <div>
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Kişisel Bilgiler</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Ad*
                    </label>
                    <input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Soyad*
                    </label>
                    <input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      E-posta*
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    />
                  </div>

                  <div className="w-full">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Telefon*
                    </label>
                    <div className="flex items-center gap-2">
                      <select
                        value={formData.phone.code}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          phone: { ...prev.phone, code: e.target.value }
                        }))}
                        className="w-[90px] px-2 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      >
                        <option value="+90">+90</option>
                        <option value="+1">+1</option>
                        <option value="+44">+44</option>
                        <option value="+49">+49</option>
                      </select>
                      <input
                        type="tel"
                        value={formData.phone.number}
                        onChange={(e) => setFormData(prev => ({ 
                          ...prev, 
                          phone: { ...prev.phone, number: e.target.value }
                        }))}
                        className="flex-1 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="5XX XXX XX XX"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Doğum Tarihi*
                    </label>
                    <input
                      type="date"
                      value={formData.birthDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, birthDate: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Şifre*
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        value={formData.password}
                        onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                        className="w-full pl-4 pr-12 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                      >
                        {showPassword ? (
                          <EyeOff className="h-5 w-5" />
                        ) : (
                          <Eye className="h-5 w-5" />
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                className="w-full bg-primary text-white py-3 rounded-lg font-medium hover:bg-[#0A9996] transition-colors"
              >
                Kayıt Ol
              </button>

              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Zaten hesabınız var mı?{' '}
                  <button
                    type="button"
                    onClick={() => navigate('/login')}
                    className="font-medium text-primary hover:text-[#0A9996]"
                  >
                    Giriş Yapın
                  </button>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Register;