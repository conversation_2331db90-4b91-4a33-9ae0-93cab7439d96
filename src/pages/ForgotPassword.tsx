import React, { useState, useEffect } from "react";
import { useNavigate, Link } from "react-router-dom";
import {
  Box,
  Button,
  Input,
  FormControl,
  FormLabel,
  Heading,
  VStack,
  useToast,
  Text,
  Flex,
  Container,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Image,
} from "@chakra-ui/react";
import { forgotPassword } from "../api";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";

const MotionContainer = motion(Container);

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const navigate = useNavigate();
  const toast = useToast();
  const { t, i18n } = useTranslation(["forgotPassword", "common"]);
  const cardBgColor = useColorModeValue("white", "gray.800");

  useEffect(() => {
    document.title = t("forgotPassword:title") + " | " + t("common:appName");
  }, [t]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await forgotPassword(email);
      toast({
        title: t("forgotPassword:toasts.resetSuccess.title"),
        description: t("forgotPassword:toasts.resetSuccess.description"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      setEmailSent(true);
    } catch (error: any) {
      toast({
        title: t("forgotPassword:toasts.resetFailed.title"),
        description: t("forgotPassword:toasts.resetFailed.description"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLanguageChange = (language: string) => {
    i18n.changeLanguage(language);
  };

  return (
    <Box
      position="relative"
      minH="100vh"
      width="100%"
      overflow="auto"
      bg="gray.50"
      sx={{
        backgroundImage: `
      radial-gradient(circle, #CBD5E0 1px, transparent 1px)
    `,
        backgroundSize: "24px 24px",
        backgroundPosition: "0 0",
      }}
    >
      <Flex
        position="fixed"
        top={0}
        left={0}
        right={0}
        zIndex={10}
        height={{ base: "60px", md: "70px" }}
        borderBottom="1px"
        borderColor="gray.100"
        px={{ base: 3, md: 6 }}
        py={{ base: 2, md: 3 }}
        align="center"
        justify="space-between"
      >
        <Image
          src="/logo.png"
          alt="Logo"
          width={{ base: "100px", sm: "120px", md: "180px" }}
          height="auto"
          transition="transform 0.3s ease"
          _hover={{ transform: "scale(1.05)" }}
        />

        <Box zIndex={2}>
          <Menu>
            <MenuButton
              as={Button}
              size={{ base: "xs", sm: "sm" }}
              variant="solid"
              colorScheme="teal"
            >
              {i18n.language.toUpperCase().split("-")[0]}
            </MenuButton>
            <MenuList>
              <MenuItem onClick={() => handleLanguageChange("en")}>EN</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("tr")}>TR</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("ar")}>AR</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("ru")}>RU</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("fr")}>FR</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("es")}>ES</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("de")}>DE</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("it")}>IT</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("zh")}>ZH</MenuItem>
            </MenuList>
          </Menu>
        </Box>
      </Flex>

      <Flex
        minH="100vh"
        w="full"
        align="center"
        justify="center"
        position="relative"
        pt={{ base: "70px", md: "90px" }}
        pb={{ base: "20px", md: "30px" }}
        px={{ base: "3", sm: "4", md: "6" }}
      >
        <MotionContainer
          maxW="lg"
          py={{ base: "6", sm: "8", md: "12" }}
          px={{ base: "3", sm: "6", md: "8" }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          width="100%"
        >
          <Box
            py={{ base: "6", sm: "8", md: "12" }}
            px={{ base: "4", sm: "6", md: "10" }}
            shadow="2xl"
            rounded={{ base: "md", md: "xl" }}
            bg={cardBgColor}
            borderWidth="1px"
            borderColor="teal.100"
            backdropFilter="blur(10px)"
          >
            <VStack
              spacing={{ base: 4, md: 8 }}
              align="center"
              mb={{ base: 4, md: 6 }}
            >
              <Heading
                as="h1"
                size={{ base: "lg", md: "xl" }}
                color="teal.500"
                textAlign="center"
              >
                {t("forgotPassword:title")}
              </Heading>
              <Text
                color="gray.600"
                fontSize={{ base: "sm", md: "md" }}
                textAlign="center"
                maxW="md"
              >
                {t("forgotPassword:description")}
              </Text>
            </VStack>

            {!emailSent ? (
              <form onSubmit={handleSubmit}>
                <VStack spacing={{ base: 4, md: 6 }}>
                  <FormControl isRequired>
                    <FormLabel fontSize={{ base: "sm", md: "md" }}>
                      {t("forgotPassword:form.email.label")}
                    </FormLabel>
                    <Input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder={t("forgotPassword:form.email.placeholder")}
                      focusBorderColor="teal.400"
                      size={{ base: "md", md: "lg" }}
                      bg={useColorModeValue("white", "gray.700")}
                      _hover={{ borderColor: "teal.300" }}
                    />
                  </FormControl>

                  <Button
                    type="submit"
                    colorScheme="teal"
                    size={{ base: "md", md: "lg" }}
                    width="full"
                    isLoading={isLoading}
                    loadingText={t("forgotPassword:form.submitLoading")}
                    _hover={{ transform: "translateY(-2px)", boxShadow: "lg" }}
                    transition="all 0.2s"
                  >
                    {t("forgotPassword:form.submit")}
                  </Button>
                </VStack>
              </form>
            ) : (
              <VStack spacing={{ base: 4, md: 6 }}>
                <Text
                  color="gray.600"
                  fontSize={{ base: "sm", md: "md" }}
                  textAlign="center"
                  maxW="md"
                >
                  {t("forgotPassword:emailSentMessage")}
                </Text>
                <Button
                  colorScheme="teal"
                  size={{ base: "md", md: "lg" }}
                  width="full"
                  onClick={() => navigate("/login")}
                >
                  {t("forgotPassword:goToLogin")}
                </Button>
              </VStack>
            )}
            {!emailSent && (
              <Flex justify="center" mt={{ base: 4, md: 6 }}>
                <Link to="/login">
                  <Text
                    color="teal.500"
                    fontSize={{ base: "xs", sm: "sm" }}
                    _hover={{ color: "teal.600", textDecoration: "underline" }}
                    transition="all 0.2s"
                  >
                    {t("forgotPassword:links.backToLogin")}
                  </Text>
                </Link>
              </Flex>
            )}
          </Box>
        </MotionContainer>
      </Flex>
    </Box>
  );
};

export default ForgotPassword;
