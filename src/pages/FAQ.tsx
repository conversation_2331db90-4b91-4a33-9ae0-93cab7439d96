import React, { useState } from 'react';
import Layout from '../components/Layout';
import { 
  ChevronDown,
  ChevronUp,
  Search,
  Users,
  Globe,
  Shield,
  DollarSign,
  FileText,
  HelpCircle,
  Mail
} from 'lucide-react';

const FAQ = () => {
  const [activeCategory, setActiveCategory] = useState('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [openQuestions, setOpenQuestions] = useState<string[]>([]);

  const categories = [
    { id: 'general', name: 'Gene<PERSON>', icon: HelpCircle },
    { id: 'account', name: 'He<PERSON><PERSON>', icon: Users },
    { id: 'platform', name: 'Platform', icon: Globe },
    { id: 'security', name: '<PERSON><PERSON><PERSON><PERSON>', icon: Shield },
    { id: 'payment', name: 'Ödeme', icon: DollarSign },
    { id: 'documents', name: '<PERSON>gel<PERSON>', icon: FileText }
  ];

  const faqs = {
    general: [
      {
        id: 'general-1',
        question: 'E-exportcity nedir?',
        answer: 'E-exportcity, Türkiye\'nin önde gelen dijital ihracat platformudur. Ticaretin geleceği dijitalde şekilleniyor. E-exportcity olarak amacımız, her büyüklükteki işletmenin en uygun koşullarda en geniş müşteri ve tedarikçi ağına erişmesini sağlamak. Sadece bir ticaret platformu değil, aynı zamanda bir global ekosistem sunuyoruz.'
      },
      {
        id: 'general-2',
        question: 'E-exportcity hangi hizmetleri sunuyor?',
        answer: 'E-exportcity, dijital ticaretin merkezine yerleşerek işletmeleri ve bireysel brokerları küresel fırsatlarla buluşturuyor. Platformumuz üzerinden firma profili oluşturma, ürün/hizmet listeleme, potansiyel müşterilerle iletişim kurma, ihracat danışmanlığı ve daha birçok hizmetten yararlanabilirsiniz.'
      },
      {
        id: 'general-3',
        question: 'E-exportcity\'nin vizyonu nedir?',
        answer: 'E-exportcity olarak vizyonumuz, dijital ticaretin sınırlarını kaldırarak tüm dünyadaki işletmelere eşit fırsatlar sunmak. İstanbul\'dan başlayarak global ticaretin merkezi olmak ve veri odaklı çözümlerle ticaretin geleceğini şekillendirmek.'
      }
    ],
    account: [
      {
        id: 'account-1',
        question: 'Nasıl üye olabilirim?',
        answer: 'Platformumuza üye olmak için ana sayfadaki "Ücretsiz Üye Ol" butonuna tıklayarak kayıt formunu doldurmanız yeterlidir. Üyelik işleminiz tamamlandıktan sonra hesabınızı hemen kullanmaya başlayabilirsiniz.'
      },
      {
        id: 'account-2',
        question: 'Şirket profilimi nasıl oluşturabilirim?',
        answer: 'Üye olduktan sonra hesap ayarlarınızdan şirket bilgilerinizi ekleyebilir, logonuzu yükleyebilir ve iletişim bilgilerinizi güncelleyebilirsiniz.'
      }
    ],
    platform: [
      {
        id: 'platform-1',
        question: 'E-exportcity\'yi kimler kullanabilir?',
        answer: 'E-exportcity, her ölçekten işletme ve bireysel brokerlar için tasarlanmıştır. İhracat yapmak isteyen üreticiler, toptancılar, perakendeciler ve hizmet sağlayıcılar platformumuzu kullanabilir.'
      },
      {
        id: 'platform-2',
        question: 'Platformda nasıl öne çıkabilirim?',
        answer: 'Premium üyelik paketlerimizle firma profilinizi öne çıkarabilir, daha fazla görünürlük elde edebilir ve gelişmiş özelliklere erişebilirsiniz. Ayrıca düzenli içerik paylaşımı ve aktif platform kullanımı da görünürlüğünüzü artıracaktır.'
      }
    ],
    security: [
      {
        id: 'security-1',
        question: 'Verilerimiz nasıl korunuyor?',
        answer: 'En son güvenlik teknolojilerini kullanarak verilerinizi SSL şifreleme ve güvenlik duvarları ile koruyoruz. Düzenli güvenlik denetimleri gerçekleştiriyor ve veri güvenliği konusunda uluslararası standartlara uyuyoruz.'
      },
      {
        id: 'security-2',
        question: 'İşlemlerimizin güvenliği nasıl sağlanıyor?',
        answer: 'Platform üzerindeki tüm işlemler şifreli bağlantılar üzerinden gerçekleştirilir. Kullanıcı doğrulama sistemimiz ve gelişmiş güvenlik protokollerimiz ile işlemlerinizin güvenliğini garanti altına alıyoruz.'
      }
    ],
    payment: [
      {
        id: 'payment-1',
        question: 'Hangi ödeme yöntemlerini kabul ediyorsunuz?',
        answer: 'Kredi kartı, banka havalesi ve online ödeme sistemleri ile ödeme kabul ediyoruz. Tüm ödemeler güvenli ödeme altyapımız üzerinden gerçekleştirilmektedir.'
      },
      {
        id: 'payment-2',
        question: 'Üyelik ücretleri nasıl belirleniyor?',
        answer: 'Üyelik ücretlerimiz, sunulan hizmetlerin kapsamına ve kullanım süresine göre belirlenmektedir. Farklı ihtiyaçlara yönelik çeşitli paketlerimiz mevcuttur. Detaylı bilgi için "Paketler" sayfamızı ziyaret edebilirsiniz.'
      }
    ],
    documents: [
      {
        id: 'documents-1',
        question: 'İhracat için gerekli belgeler nelerdir?',
        answer: 'İhracat işlemleriniz için gerekli tüm belgelerin listesine ve şablonlarına platform üzerinden erişebilirsiniz. Uzman ekibimiz belge hazırlama sürecinde size destek olmaktadır.'
      },
      {
        id: 'documents-2',
        question: 'Yasal gereklilikler nelerdir?',
        answer: 'Platform üzerinden yapacağınız işlemlerde uymanız gereken yasal gereklilikleri detaylı olarak belgelendiriyoruz. İhracat mevzuatı ve uluslararası ticaret kuralları hakkında güncel bilgilere platformumuzdan ulaşabilirsiniz.'
      }
    ]
  };

  const toggleQuestion = (questionId: string) => {
    setOpenQuestions(prev => 
      prev.includes(questionId) 
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  const filteredFaqs = searchQuery
    ? Object.values(faqs).flat().filter(faq =>
        faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : faqs[activeCategory as keyof typeof faqs];

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50">
        {/* Hero Section */}
        <div className="bg-primary">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20">
            <div className="text-center">
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Size Nasıl Yardımcı Olabiliriz?
              </h1>
              <div className="max-w-2xl mx-auto relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Sorunuzu yazın..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 rounded-xl shadow-lg focus:outline-none focus:ring-2 focus:ring-white/20"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-8">
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => {
                    setActiveCategory(category.id);
                    setSearchQuery('');
                  }}
                  className={`flex flex-col items-center justify-center p-4 rounded-lg transition-all ${
                    activeCategory === category.id
                      ? 'bg-primary text-white'
                      : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <category.icon className="w-6 h-6 mb-2" />
                  <span className="text-sm font-medium">{category.name}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* FAQ List */}
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <div className="space-y-4">
            {filteredFaqs.map((faq) => (
              <div
                key={faq.id}
                className="bg-white rounded-xl shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md"
              >
                <button
                  onClick={() => toggleQuestion(faq.id)}
                  className="w-full flex items-center justify-between p-6"
                >
                  <span className="text-lg font-medium text-gray-900">
                    {faq.question}
                  </span>
                  {openQuestions.includes(faq.id) ? (
                    <ChevronUp className="w-5 h-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-gray-500" />
                  )}
                </button>
                {openQuestions.includes(faq.id) && (
                  <div className="px-6 pb-6">
                    <div className="prose max-w-none">
                      <p className="text-gray-600">{faq.answer}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Contact CTA */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Sorunuza yanıt bulamadınız mı?
            </h2>
            <p className="text-gray-600 mb-8">
              Uzman ekibimiz size yardımcı olmak için hazır.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors"
              >
                <Mail className="w-5 h-5 mr-2" />
                E-posta Gönder
              </a>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default FAQ;