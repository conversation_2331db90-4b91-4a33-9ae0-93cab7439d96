import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  ChevronDown,
  ChevronUp,
  Search,
  Users,
  Globe,
  Shield,
  DollarSign,
  FileText,
  HelpCircle,
  Mail
} from 'lucide-react';

const FAQ = () => {
  const { t } = useTranslation(['faq']);
  const [activeCategory, setActiveCategory] = useState('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [openQuestions, setOpenQuestions] = useState<string[]>([]);

  const categories = [
    { id: 'general', name: t('categories.general'), icon: HelpCircle },
    { id: 'account', name: t('categories.account'), icon: Users },
    { id: 'platform', name: t('categories.platform'), icon: Globe },
    { id: 'security', name: t('categories.security'), icon: Shield },
    { id: 'payment', name: t('categories.payment'), icon: DollarSign },
    { id: 'documents', name: t('categories.documents'), icon: FileText }
  ];

  const faqs = {
    general: [
      {
        id: 'general-1',
        question: t('questions.general.0.question'),
        answer: t('questions.general.0.answer')
      },
      {
        id: 'general-2',
        question: t('questions.general.1.question'),
        answer: t('questions.general.1.answer')
      },
      {
        id: 'general-3',
        question: t('questions.general.2.question'),
        answer: t('questions.general.2.answer')
      }
    ],
    account: [
      {
        id: 'account-1',
        question: t('questions.account.0.question'),
        answer: t('questions.account.0.answer')
      },
      {
        id: 'account-2',
        question: t('questions.account.1.question'),
        answer: t('questions.account.1.answer')
      }
    ],
    platform: [
      {
        id: 'platform-1',
        question: t('questions.platform.0.question'),
        answer: t('questions.platform.0.answer')
      },
      {
        id: 'platform-2',
        question: t('questions.platform.1.question'),
        answer: t('questions.platform.1.answer')
      }
    ],
    security: [
      {
        id: 'security-1',
        question: t('questions.security.0.question'),
        answer: t('questions.security.0.answer')
      },
      {
        id: 'security-2',
        question: t('questions.security.1.question'),
        answer: t('questions.security.1.answer')
      }
    ],
    payment: [
      {
        id: 'payment-1',
        question: t('questions.payment.0.question'),
        answer: t('questions.payment.0.answer')
      },
      {
        id: 'payment-2',
        question: t('questions.payment.1.question'),
        answer: t('questions.payment.1.answer')
      }
    ],
    documents: [
      {
        id: 'documents-1',
        question: t('questions.documents.0.question'),
        answer: t('questions.documents.0.answer')
      },
      {
        id: 'documents-2',
        question: t('questions.documents.1.question'),
        answer: t('questions.documents.1.answer')
      }
    ]
  };

  const toggleQuestion = (questionId: string) => {
    setOpenQuestions(prev =>
      prev.includes(questionId)
        ? prev.filter(id => id !== questionId)
        : [...prev, questionId]
    );
  };

  const filteredFaqs = searchQuery
    ? Object.values(faqs).flat().filter(faq =>
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase())
    )
    : faqs[activeCategory as keyof typeof faqs];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 md:py-20">
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-6">
              {t('heroTitle')}
            </h1>
            <div className="max-w-2xl mx-auto relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder={t('searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 rounded-xl shadow-lg focus:outline-none focus:ring-2 focus:ring-white/20"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-8">
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => {
                  setActiveCategory(category.id);
                  setSearchQuery('');
                }}
                className={`flex flex-col items-center justify-center p-4 rounded-lg transition-all ${activeCategory === category.id
                    ? 'bg-primary text-white'
                    : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
                  }`}
              >
                <category.icon className="w-6 h-6 mb-2" />
                <span className="text-sm font-medium">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* FAQ List */}
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="space-y-4">
          {filteredFaqs.map((faq) => (
            <div
              key={faq.id}
              className="bg-white rounded-xl shadow-sm overflow-hidden transition-all duration-200 hover:shadow-md"
            >
              <button
                onClick={() => toggleQuestion(faq.id)}
                className="w-full flex items-center justify-between p-6"
              >
                <span className="text-lg font-medium text-gray-900">
                  {faq.question}
                </span>
                {openQuestions.includes(faq.id) ? (
                  <ChevronUp className="w-5 h-5 text-gray-500" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                )}
              </button>
              {openQuestions.includes(faq.id) && (
                <div className="px-6 pb-6">
                  <div className="prose max-w-none">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Contact CTA */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="bg-white rounded-xl shadow-lg p-8 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {t('contactCta.title')}
          </h2>
          <p className="text-gray-600 mb-8">
            {t('contactCta.description')}
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors"
            >
              <Mail className="w-5 h-5 mr-2" />
              {t('contactCta.emailButton')}
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FAQ;