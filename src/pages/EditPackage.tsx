import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Input,
  Select,
  Checkbox,
  useToast,
  Heading,
  Textarea,
  NumberInput,
  NumberInputField,
  Grid,
  GridItem,
  Text,
  useColorModeValue,
  Flex,
  Icon,
  Badge,
  Card,
  CardHeader,
  CardBody,
  Stack,
  Spacer,
} from "@chakra-ui/react";
import { FiPackage, FiSettings, FiDollarSign, FiList, FiSliders, FiX, FiSave } from 'react-icons/fi';
import { getAdminPackageById, updatePackage } from "@/api/packageApi";
import { IPackage } from "@/types/package";

const EditPackage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const toast = useToast();
  const { t } = useTranslation("admin");

  const [packageData, setPackageData] = useState<IPackage>({
    _id: "",
    name: "",
    nameEn: "",
    description: "",
    price: 0,
    type: "standard",
    viewRequestLimit: 0,
    createRequestLimit: 0,
    emailNotification: false,
    smsNotification: false,
    languageIntroRights: 0,
    messagingAllowed: false,
    homepageAd: false,
    yearEndSectorReport: false,
    isActive: true,
    features: [],
    maxMessages: 0,
    duration: 1,
    order: 0,
  });

  useEffect(() => {
    const fetchPackage = async () => {
      try {
        if (!id) return;
        const foundPackage = await getAdminPackageById(id);
        setPackageData(foundPackage);
      } catch (error: any) {
        console.error("Failed to fetch package:", error);
        toast({
          title: t("common:fetchFailed"),
          description: t("packages.fetchFailed"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
        navigate("/admin/packages");
      }
    };

    fetchPackage();
  }, [id, navigate, toast, t]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >,
  ) => {
    const { name, value } = e.target;
    setPackageData((prev) => ({ ...prev, [name]: value }));
  };

  const handleNumberInputChange = (name: string, value: string) => {
    setPackageData((prev) => ({ ...prev, [name]: Number(value) }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setPackageData((prev) => ({ ...prev, [name]: checked }));
  };

  const validateForm = () => {
    const errors = [];
    if (!packageData.name.trim()) errors.push("Name is required");
    if (!packageData.nameEn.trim()) errors.push("English name is required");
    if (!packageData.description.trim()) errors.push("Description is required");
    if (packageData.price < 0) errors.push("Price must be non-negative");
    if (packageData.viewRequestLimit < 0)
      errors.push("View request limit must be non-negative");
    if (packageData.createRequestLimit < 0)
      errors.push("Create request limit must be non-negative");
    if (packageData.duration < 1)
      errors.push("Duration must be at least 1 month");
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const errors = validateForm();

    if (errors.length > 0) {
      errors.forEach((error) => {
        toast({
          title: "Validation Error",
          description: error,
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      });
      return;
    }

    try {
      await updatePackage(id!, packageData);
      toast({
        title: t("common:updateSuccess.title"),
        description: t("packages.form.updateSuccess"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      navigate("/admin/packages");
    } catch (error: any) {
      console.error("Failed to update package:", error);
      toast({
        title: t("common:updateFailed.title"),
        description: t("packages.form.updateFailed"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const bgColor = useColorModeValue("white", "gray.800");
  const headerBgColor = useColorModeValue("blue.50", "blue.900");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Container maxW="container.xl" py={8}>
      <Box mb={8}>
        <Flex align="center" mb={2}>
          <Icon as={FiPackage} mr={2} fontSize="xl" color="blue.500" />
          <Heading as="h1" size="xl">
            {t("packages.editPackage")}
          </Heading>
          <Spacer />
          <Badge colorScheme={packageData.isActive ? "green" : "red"} fontSize="md" px={3} py={1} borderRadius="md">
            {packageData.isActive ? t("common:active") : t("common:inactive")}
          </Badge>
        </Flex>
        <Text color="gray.500">{t("packages.form.editDescription")}</Text>
      </Box>

      <form onSubmit={handleSubmit}>
        <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={6}>
          {/* Basic Information */}
          <GridItem colSpan={{ base: 1, md: 2 }}>
            <Card bg={bgColor} boxShadow="md" mb={6} borderColor={borderColor} borderWidth="1px">
              <CardHeader bg={headerBgColor} py={4}>
                <Flex align="center">
                  <Icon as={FiSliders} mr={2} />
                  <Heading size="md">{t("packages.form.basicInfo")}</Heading>
                </Flex>
              </CardHeader>
              <CardBody>
                <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={4}>
                  <GridItem>
                    <FormControl isRequired>
                      <FormLabel>{t("packages.form.name")} (TR)</FormLabel>
                      <Input
                        name="name"
                        value={packageData.name}
                        onChange={handleInputChange}
                      />
                    </FormControl>
                  </GridItem>
                  <GridItem>
                    <FormControl isRequired>
                      <FormLabel>{t("packages.form.nameEn")} (EN)</FormLabel>
                      <Input
                        name="nameEn"
                        value={packageData.nameEn}
                        onChange={handleInputChange}
                      />
                    </FormControl>
                  </GridItem>
                  <GridItem colSpan={{ base: 1, md: 2 }}>
                    <FormControl isRequired>
                      <FormLabel>{t("packages.form.description")}</FormLabel>
                      <Textarea
                        name="description"
                        value={packageData.description}
                        onChange={handleInputChange}
                        rows={4}
                      />
                    </FormControl>
                  </GridItem>
                </Grid>
              </CardBody>
            </Card>
          </GridItem>

          {/* Pricing and Duration */}
          <GridItem colSpan={1}>
            <Card bg={bgColor} boxShadow="md" mb={6} borderColor={borderColor} borderWidth="1px">
              <CardHeader bg={headerBgColor} py={4}>
                <Flex align="center">
                  <Icon as={FiDollarSign} mr={2} />
                  <Heading size="md">{t("packages.form.pricingSection")}</Heading>
                </Flex>
              </CardHeader>
              <CardBody>
                <Stack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel>{t("packages.form.price")}</FormLabel>
                    <NumberInput
                      min={0}
                      value={packageData.price}
                      onChange={(value) => handleNumberInputChange("price", value)}
                    >
                      <NumberInputField name="price" />
                    </NumberInput>
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>{t("packages.form.type")}</FormLabel>
                    <Select
                      name="type"
                      value={packageData.type}
                      onChange={handleInputChange}
                    >
                      <option value="standard">{t("packages.type.standard")}</option>
                      <option value="addon">{t("packages.type.addon")}</option>
                    </Select>
                  </FormControl>
                  <FormControl>
                    <FormLabel>{t("packages.form.duration")} ({t("packages.form.months")})</FormLabel>
                    <NumberInput
                      min={1}
                      value={packageData.duration}
                      onChange={(value) => handleNumberInputChange("duration", value)}
                    >
                      <NumberInputField name="duration" />
                    </NumberInput>
                  </FormControl>
                  <FormControl>
                    <FormLabel>{t("packages.form.order")}</FormLabel>
                    <NumberInput
                      min={0}
                      value={packageData.order}
                      onChange={(value) => handleNumberInputChange("order", value)}
                    >
                      <NumberInputField name="order" />
                    </NumberInput>
                  </FormControl>
                </Stack>
              </CardBody>
            </Card>
          </GridItem>

          {/* Limits and Restrictions */}
          <GridItem colSpan={1}>
            <Card bg={bgColor} boxShadow="md" mb={6} borderColor={borderColor} borderWidth="1px">
              <CardHeader bg={headerBgColor} py={4}>
                <Flex align="center">
                  <Icon as={FiList} mr={2} />
                  <Heading size="md">{t("packages.form.limitsSection")}</Heading>
                </Flex>
              </CardHeader>
              <CardBody>
                <Stack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel>{t("packages.form.viewRequestLimit")}</FormLabel>
                    <NumberInput
                      min={0}
                      value={packageData.viewRequestLimit}
                      onChange={(value) =>
                        handleNumberInputChange("viewRequestLimit", value)
                      }
                    >
                      <NumberInputField name="viewRequestLimit" />
                    </NumberInput>
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>{t("packages.form.createRequestLimit")}</FormLabel>
                    <NumberInput
                      min={0}
                      value={packageData.createRequestLimit}
                      onChange={(value) =>
                        handleNumberInputChange("createRequestLimit", value)
                      }
                    >
                      <NumberInputField name="createRequestLimit" />
                    </NumberInput>
                  </FormControl>
                  <FormControl>
                    <FormLabel>{t("packages.form.languageIntroRights")}</FormLabel>
                    <NumberInput
                      min={0}
                      value={packageData.languageIntroRights}
                      onChange={(value) =>
                        handleNumberInputChange("languageIntroRights", value)
                      }
                    >
                      <NumberInputField name="languageIntroRights" />
                    </NumberInput>
                  </FormControl>
                  <FormControl>
                    <FormLabel>{t("packages.form.maxMessages")}</FormLabel>
                    <NumberInput
                      min={0}
                      value={packageData.maxMessages}
                      onChange={(value) =>
                        handleNumberInputChange("maxMessages", value)
                      }
                    >
                      <NumberInputField name="maxMessages" />
                    </NumberInput>
                  </FormControl>
                </Stack>
              </CardBody>
            </Card>
          </GridItem>

          {/* Features */}
          <GridItem colSpan={{ base: 1, md: 2 }}>
            <Card bg={bgColor} boxShadow="md" mb={6} borderColor={borderColor} borderWidth="1px">
              <CardHeader bg={headerBgColor} py={4}>
                <Flex align="center">
                  <Icon as={FiSettings} mr={2} />
                  <Heading size="md">{t("packages.form.featuresSection")}</Heading>
                </Flex>
              </CardHeader>
              <CardBody>
                <Grid templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }} gap={4}>
                  <GridItem>
                    <Stack spacing={3}>
                      <FormControl>
                        <Checkbox
                          name="emailNotification"
                          isChecked={packageData.emailNotification}
                          onChange={handleCheckboxChange}
                          colorScheme="blue"
                          size="lg"
                        >
                          {t("packages.form.emailNotification")}
                        </Checkbox>
                      </FormControl>
                      <FormControl>
                        <Checkbox
                          name="smsNotification"
                          isChecked={packageData.smsNotification}
                          onChange={handleCheckboxChange}
                          colorScheme="blue"
                          size="lg"
                        >
                          {t("packages.form.smsNotification")}
                        </Checkbox>
                      </FormControl>
                      <FormControl>
                        <Checkbox
                          name="messagingAllowed"
                          isChecked={packageData.messagingAllowed}
                          onChange={handleCheckboxChange}
                          colorScheme="blue"
                          size="lg"
                        >
                          {t("packages.form.messagingAllowed")}
                        </Checkbox>
                      </FormControl>
                    </Stack>
                  </GridItem>
                  <GridItem>
                    <Stack spacing={3}>
                      <FormControl>
                        <Checkbox
                          name="homepageAd"
                          isChecked={packageData.homepageAd}
                          onChange={handleCheckboxChange}
                          colorScheme="blue"
                          size="lg"
                        >
                          {t("packages.form.homepageAd")}
                        </Checkbox>
                      </FormControl>
                      <FormControl>
                        <Checkbox
                          name="yearEndSectorReport"
                          isChecked={packageData.yearEndSectorReport}
                          onChange={handleCheckboxChange}
                          colorScheme="blue"
                          size="lg"
                        >
                          {t("packages.form.yearEndSectorReport")}
                        </Checkbox>
                      </FormControl>
                      <FormControl>
                        <Checkbox
                          name="isActive"
                          isChecked={packageData.isActive}
                          onChange={handleCheckboxChange}
                          colorScheme="green"
                          size="lg"
                        >
                          {t("packages.form.isActive")}
                        </Checkbox>
                      </FormControl>
                    </Stack>
                  </GridItem>
                </Grid>
              </CardBody>
            </Card>
          </GridItem>
        </Grid>

        <Flex justify="space-between" mt={6}>
          <Button
            onClick={() => navigate("/admin/packages")}
            variant="outline"
            leftIcon={<Icon as={FiX} />}
            colorScheme="red"
            size="lg"
          >
            {t("common:cancel")}
          </Button>
          <Button
            type="submit"
            colorScheme="blue"
            leftIcon={<Icon as={FiSave} />}
            size="lg"
          >
            {t("common:update")}
          </Button>
        </Flex>
      </form>
    </Container>
  );
};

export default EditPackage;
