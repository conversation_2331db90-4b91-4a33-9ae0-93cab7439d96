import React from 'react';
import Layout from '../components/Layout';
import { 
  Handshake,
  Globe,
  Users,
  TrendingUp,
  Building,
  BadgeCheck,
  ArrowRight,
  Briefcase,
  Target,
  Award,
  Rocket,
  CheckCircle,
  Mail
} from 'lucide-react';

const Partnership = () => {
  const benefits = [
    {
      icon: Globe,
      title: "Global Erişim",
      description: "150+ ülkede aktif iş ağına erişim imkanı"
    },
    {
      icon: Users,
      title: "Geniş Network",
      description: "10.000+ aktif üye ile networking fırsatı"
    },
    {
      icon: TrendingUp,
      title: "Büyüme Fırsatı",
      description: "İhracat potansiyelinizi maksimize edin"
    },
    {
      icon: Building,
      title: "Kurumsal İş Birlikleri",
      description: "Güvenilir iş ortaklarıyla çalışma imkanı"
    }
  ];

  const partnershipTypes = [
    {
      title: "Tedarikçi Ortaklığı",
      description: "Ürün ve hizmetlerinizi global pazara sunun",
      features: [
        "<PERSON>rün listelem önceliği",
        "<PERSON>zel firma profili",
        "Analitik raporlar",
        "7/24 destek"
      ],
      icon: Briefcase
    },
    {
      title: "Stratejik Ortaklık",
      description: "Uzun vadeli iş birliği fırsatları yakalayın",
      features: [
        "Özel iş fırsatları",
        "Pazar araştırmaları",
        "Networking etkinlikleri",
        "Mentorluk desteği"
      ],
      icon: Target
    },
    {
      title: "Teknoloji Ortaklığı",
      description: "İnovatif çözümlerle rekabet avantajı elde edin",
      features: [
        "API entegrasyonu",
        "Teknik destek",
        "Özel geliştirmeler",
        "Öncelikli destek"
      ],
      icon: Rocket
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <div className="relative bg-gradient-to-r from-primary to-[#169693] overflow-hidden">
          <div className="absolute inset-0">
            <img
              src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?auto=format&fit=crop&q=80"
              alt="Partnership"
              className="w-full h-full object-cover opacity-10"
            />
          </div>
          <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-white mb-6">
                E-exportcity İş Birliği Programı
              </h1>
              <p className="text-xl text-white/90 max-w-2xl mx-auto mb-8">
                Global ticarette güçlerimizi birleştirerek büyüyelim. İş birliği programımızla işinizi bir üst seviyeye taşıyın.
              </p>
              <button className="inline-flex items-center px-8 py-4 bg-white text-primary rounded-lg text-lg font-semibold transition-all duration-200 hover:shadow-lg group">
                <span>Hemen Başvur</span>
                <ArrowRight className="ml-2 h-5 w-5 transform group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="py-16 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                İş Birliği Avantajları
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                E-exportcity iş ortağı olarak elde edeceğiniz avantajlarla işinizi büyütün
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {benefits.map((benefit, index) => (
                <div 
                  key={index}
                  className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <benefit.icon className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600">
                    {benefit.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Partnership Types */}
        <div className="py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                İş Birliği Modelleri
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                İhtiyacınıza en uygun iş birliği modelini seçin
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {partnershipTypes.map((type, index) => (
                <div 
                  key={index}
                  className="bg-white rounded-xl border border-gray-200 p-8 hover:shadow-lg transition-shadow"
                >
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
                    <type.icon className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {type.title}
                  </h3>
                  <p className="text-gray-600 mb-6">
                    {type.description}
                  </p>
                  <ul className="space-y-3">
                    {type.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-primary flex-shrink-0" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-primary rounded-2xl overflow-hidden">
              <div className="px-8 py-12 md:p-12">
                <div className="md:flex items-center justify-between">
                  <div className="mb-8 md:mb-0 md:mr-8">
                    <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
                      İş Birliği için İlk Adımı Atın!
                    </h2>
                    <p className="text-white/90 text-lg max-w-xl">
                      Detaylı bilgi almak ve iş birliği fırsatlarını değerlendirmek için bizimle iletişime geçin.
                    </p>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <button className="inline-flex items-center justify-center px-6 py-3 bg-white text-primary rounded-lg font-medium hover:bg-opacity-90 transition-colors">
                      <Mail className="w-5 h-5 mr-2" />
                      Bize Ulaşın
                    </button>
                    <button className="inline-flex items-center justify-center px-6 py-3 bg-white/10 text-white rounded-lg font-medium hover:bg-white/20 transition-colors">
                      <Award className="w-5 h-5 mr-2" />
                      Broşürü İndir
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Partnership;