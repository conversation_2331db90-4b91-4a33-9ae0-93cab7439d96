import React, { useState, useEffect, useRef, useCallback } from "react";
import { useSocket } from "../context/SocketContext";
import { getMessages, sendMessage } from "../api";
import { format } from "date-fns";
import { IProductConversation, IConversation } from "@/types/message";
import { useAuthCheck } from "@/hooks/useAuthCheck";
import { useTranslation } from "react-i18next";
import {
  Search,
  Send,
  Package,
  ArrowLeft,
  MoreVertical,
  Check,
  CheckCircle,
  Archive,
  Trash2,
  Pin
} from "lucide-react";


const MessageCenter: React.FC = () => {
  const { t } = useTranslation("messageCenter");
  const { socket, typingUsers, clearNotifications } = useSocket();
  const [productConversations, setProductConversations] = useState<
    IProductConversation[]
  >([]);
  const [selectedProduct, setSelectedProduct] =
    useState<IProductConversation | null>(null);
  const [selectedConversation, setSelectedConversation] =
    useState<IConversation | null>(null);
  const [message, setMessage] = useState("");
  const { user } = useAuthCheck();
  const [searchTerm, setSearchTerm] = useState("");
  const messageContainerRef = useRef<any>(null);
  const conversationListRef = useRef<any>(null);
  const typingTimeoutRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showOptions, setShowOptions] = useState<string | null>(null);
  const [filter, setFilter] = useState('all'); // 'all', 'unread', 'archived'

  const scrollToBottom = (ref: React.RefObject<HTMLDivElement>) => {
    if (ref.current) {
      ref.current.scrollTop = ref.current.scrollHeight;
    }
  };

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        const response = await getMessages();
        if (Array.isArray(response)) {
          setProductConversations(
            response as unknown as IProductConversation[],
          );
        }
      } catch (error: any) {
        console.error("Error fetching messages:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessages();
  }, []);

  useEffect(() => {
    clearNotifications();
  }, [clearNotifications]);

  useEffect(() => {
    if (socket) {
      socket.on("message:received", (newMessage) => {
        console.log("Received message in MessageCenter:", newMessage);
        console.log("Current user ID:", user?._id);
        console.log("Message sender ID:", typeof newMessage.senderId === 'object' ? newMessage.senderId._id : newMessage.senderId);
        console.log("Selected conversation:", selectedConversation ? selectedConversation.roomId : 'none');

        // Add message to conversations
        setProductConversations((prev) => {
          const newConversations = [...prev];
          const productIndex = newConversations.findIndex(
            (prod) =>
              prod.productId ===
              (newMessage.productId?._id || newMessage.productId),
          );

          if (productIndex > -1) {
            const conversationIndex = newConversations[
              productIndex
            ].conversations.findIndex(
              (conv) => conv.roomId === newMessage.roomId,
            );

            if (conversationIndex > -1) {
              const messageExists = newConversations[
                productIndex
              ].conversations[conversationIndex].messages.some(
                (msg) => msg._id === newMessage._id,
              );

              if (!messageExists) {
                console.log("Adding new message to conversation:", newMessage);
                newConversations[productIndex].conversations[
                  conversationIndex
                ].messages.push(newMessage);
                newConversations[productIndex].conversations[
                  conversationIndex
                ].lastMessage = newMessage;

                // Update unread count if message is from other user
                const newMessageSenderId = typeof newMessage.senderId === 'object'
                  ? newMessage.senderId._id
                  : newMessage.senderId;

                if (newMessageSenderId !== user?._id) {
                  newConversations[productIndex].conversations[
                    conversationIndex
                  ].unreadCount += 1;
                }

                // Move conversation to top if not selected
                if (
                  !selectedConversation ||
                  selectedConversation.roomId !== newMessage.roomId
                ) {
                  const conv =
                    newConversations[productIndex].conversations[
                    conversationIndex
                    ];

                  newConversations[productIndex].conversations.splice(
                    conversationIndex,
                    1,
                  );
                  newConversations[productIndex].conversations.unshift(conv);
                }
              } else {
                console.log("Message already exists in conversation");
              }
            } else {
              // Create new conversation
              console.log("Creating new conversation for message:", newMessage);
              const otherUser =
                (typeof newMessage.senderId === 'object' ? newMessage.senderId._id : newMessage.senderId) === user?._id
                  ? newMessage.recipientId
                  : newMessage.senderId;
              newConversations[productIndex].conversations.unshift({
                roomId: newMessage.roomId,
                messages: [newMessage],
                lastMessage: newMessage,
                user: otherUser,
                unreadCount: (typeof newMessage.senderId === 'object' ? newMessage.senderId._id : newMessage.senderId) !== user?._id ? 1 : 0,
              });
            }
          } else if (newMessage.productId) {
            // Create new product conversation
            console.log("Creating new product conversation:", newMessage);
            newConversations.unshift({
              productId: newMessage.productId._id,
              productName: newMessage.productId.name,
              productPrice: newMessage.productId.price,
              conversations: [
                {
                  roomId: newMessage.roomId,
                  messages: [newMessage],
                  lastMessage: newMessage,
                  user:
                    (typeof newMessage.senderId === 'object' ? newMessage.senderId._id : newMessage.senderId) === user?._id
                      ? newMessage.recipientId
                      : newMessage.senderId,
                  unreadCount: (typeof newMessage.senderId === 'object' ? newMessage.senderId._id : newMessage.senderId) !== user?._id ? 1 : 0,
                },
              ],
            } as any);
          }

          return newConversations;
        });

        // Scroll to bottom if viewing the conversation
        if (selectedConversation?.roomId === newMessage.roomId) {
          setTimeout(() => scrollToBottom(messageContainerRef), 100);
        }
      });

      return () => {
        socket.off("message:received");
      };
    }
  }, [socket, user?._id, selectedConversation?.roomId]);

  useEffect(() => {
    if (socket) {
      socket.on("new_notification", (notification) => {
        console.log("Received notification:", notification);
        if (notification.type === "message") {
          const [firstName, ...lastNameParts] = notification.sender.split(" ");
          const lastName = lastNameParts.join(" ");

          // Check if this is from the current user or to the current user
          const isSentByCurrentUser = user?._id === notification.data.senderId;
          const userIdToCheck = isSentByCurrentUser ? notification.data.recipientId : notification.data.senderId;

          const messageData = {
            _id: new Date().toISOString(),
            content: notification.preview,
            senderId: {
              _id: userIdToCheck,
              firstName,
              lastName,
            },
            recipientId: {
              _id: user?._id,
            },
            productId: notification.data.productId,
            timestamp: notification.timestamp,
            read: false,
            roomId: notification.data.roomId,
          };

          console.log("Created message data from notification:", messageData);
          updateConversations(messageData);

          setTimeout(() => {
            scrollToBottom(conversationListRef);

            // Fix comparison to work with both string and object senderIds
            const selectedConvUserId = selectedConversation?.user?._id;
            const messageSenderId = messageData.senderId._id;

            if (selectedConvUserId && messageSenderId && selectedConvUserId === messageSenderId) {
              scrollToBottom(messageContainerRef);
            }
          }, 100);
        }
      });

      return () => {
        socket.off("new_notification");
      };
    }
  }, [socket, selectedConversation, user]);

  useEffect(() => {
    scrollToBottom(messageContainerRef);
  }, [selectedConversation]);

  const updateConversations = (newMessage: any) => {
    console.log("Updating conversations with message:", newMessage);
    setProductConversations((prev) => {
      const newConversations = [...prev];
      const productIndex = newConversations.findIndex(
        (prod) => prod.productId === newMessage.productId,
      );

      if (productIndex > -1) {
        const conversationIndex = newConversations[
          productIndex
        ].conversations.findIndex((conv) => {
          const senderId =
            typeof newMessage.senderId === "string"
              ? newMessage.senderId
              : newMessage.senderId._id;
          const recipientId =
            typeof newMessage.recipientId === "string"
              ? newMessage.recipientId
              : newMessage.recipientId?._id;

          const convUserId = conv.user._id;
          const isUserSender = user?._id === senderId;

          return convUserId === (isUserSender ? recipientId : senderId);
        });

        if (conversationIndex > -1) {
          // Check if message already exists
          const messageExists = newConversations[productIndex].conversations[
            conversationIndex
          ].messages.some((msg) => msg._id === newMessage._id);

          if (!messageExists) {
            console.log("Adding new message to conversation:", newMessage);
            newConversations[productIndex].conversations[
              conversationIndex
            ].messages.push(newMessage);
            newConversations[productIndex].conversations[
              conversationIndex
            ].lastMessage = newMessage;

            // Update unread count if message is from other user
            if (newMessage.senderId._id !== user?._id) {
              newConversations[productIndex].conversations[
                conversationIndex
              ].unreadCount += 1;
            }

            // Move the conversation to top if it's not already selected
            if (
              !selectedConversation ||
              selectedConversation.user._id !==
              newConversations[productIndex].conversations[conversationIndex]
                .user._id
            ) {
              const conv =
                newConversations[productIndex].conversations[conversationIndex];
              newConversations[productIndex].conversations.splice(
                conversationIndex,
                1,
              );
              newConversations[productIndex].conversations.unshift(conv);
            }
          }
        } else {
          // Create new conversation if it doesn't exist
          console.log("Creating new conversation for message:", newMessage);
          const otherUser =
            newMessage.senderId._id === user?._id
              ? newMessage.recipientId
              : newMessage.senderId;
          const newConv: IConversation = {
            user: {
              _id: otherUser._id,
              firstName: otherUser.firstName,
              lastName: otherUser.lastName,
              email: otherUser.email || "",
              phoneNumber: "",
            },
            messages: [newMessage],
            unreadCount: newMessage.senderId._id !== user?._id ? 1 : 0,
            lastMessage: newMessage,
            roomId: newMessage.roomId,
          };
          newConversations[productIndex].conversations.unshift(newConv);
        }
      }
      return newConversations;
    });
  };

  const createRoomId = useCallback((userId1: string, userId2: string) => {
    // Sort IDs to ensure same room ID regardless of order
    return [userId1, userId2].sort().join("-");
  }, []);

  const emitTyping = useCallback(
    (isTyping: boolean) => {
      if (socket && selectedConversation && user) {
        const roomId = createRoomId(
          (user as any)._id,
          selectedConversation.user._id,
        );
        const typingData = {
          userId: (user as any)._id,
          recipientId: selectedConversation.user._id,
          roomId,
          userName: `${user.firstName} ${user.lastName}`,
        };

        if (isTyping && typingTimeoutRef.current) {
          // If we're already typing, don't emit another start event
          return;
        }

        console.log(
          "Emitting typing event:",
          isTyping ? "typing:start" : "typing:stop",
          typingData,
        );
        socket.emit(isTyping ? "typing:start" : "typing:stop", typingData);
      }
    },
    [socket, selectedConversation, user, createRoomId],
  );

  useEffect(() => {
    if (socket && selectedConversation && user) {
      // Join the shared room when conversation is selected
      const roomId = createRoomId(
        (user as any)._id,
        selectedConversation.user._id,
      );
      console.log("Joining shared room:", roomId);
      socket.emit("join:room", roomId);

      return () => {
        // Leave the room when conversation changes or component unmounts
        console.log("Leaving shared room:", roomId);
        socket.emit("leave:room", roomId);
      };
    }
  }, [socket, selectedConversation, user, createRoomId]);

  const getOtherUserName = (conversation: IConversation) => {
    if (!user) return "";

    // Get the last message to determine the other user
    const lastMessage = conversation.lastMessage;
    if (!lastMessage)
      return conversation.user.firstName + " " + conversation.user.lastName;

    // If current user is the sender, show recipient's name
    if (lastMessage.senderId._id === user._id) {
      return `${lastMessage.recipientId.firstName} ${lastMessage.recipientId.lastName}`;
    }
    // If current user is the recipient, show sender's name
    return `${lastMessage.senderId.firstName} ${lastMessage.senderId.lastName}`;
  };

  const handleMessageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newMessage = e.target.value;
    setMessage(newMessage);

    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    if (newMessage.length > 0) {
      // Only emit typing:start if we're not already typing
      if (!typingTimeoutRef.current) {
        emitTyping(true);
      }

      typingTimeoutRef.current = setTimeout(() => {
        console.log("Typing timeout triggered");
        emitTyping(false);
        typingTimeoutRef.current = undefined;
      }, 2000);
    } else {
      emitTyping(false);
      typingTimeoutRef.current = undefined;
    }
  };

  const handleSendMessage = async () => {
    if (!message.trim() || !selectedProduct || !selectedConversation || !user)
      return;
    try {
      const messageData = {
        content: message,
        senderId: user!._id,
        recipientId: selectedConversation.user._id!,
        productId: selectedProduct.productId,
        roomId: selectedConversation.roomId,
      };

      // Send message to API first
      const response = await sendMessage(messageData);
      console.log("Message sent:", response);

      // Create a complete message object
      const newMessage = {
        _id: response._id,
        content: response.content,
        senderId: {
          _id: user._id,
          firstName: user.firstName,
          lastName: user.lastName,
        },
        recipientId: {
          _id: selectedConversation.user._id,
          firstName: selectedConversation.user.firstName,
          lastName: selectedConversation.user.lastName,
        },
        productId: selectedProduct.productId,
        roomId: selectedConversation.roomId,
        createdAt: response.createdAt || new Date().toISOString(),
        read: false,
      };

      // Update local state with the complete message object
      setProductConversations((prev) => {
        const newConversations = [...prev];
        const productIndex = newConversations.findIndex(
          (prod) => prod.productId === selectedProduct.productId,
        );

        if (productIndex > -1) {
          const conversationIndex = newConversations[
            productIndex
          ].conversations.findIndex(
            (conv) => conv.roomId === selectedConversation.roomId,
          );

          if (conversationIndex > -1) {
            // Check if message already exists
            const messageExists = newConversations[productIndex].conversations[
              conversationIndex
            ].messages.some((msg) => msg._id === newMessage._id);

            if (!messageExists) {
              newConversations[productIndex].conversations[
                conversationIndex
              ].messages.push(newMessage);
              newConversations[productIndex].conversations[
                conversationIndex
              ].lastMessage = newMessage;
            }
          }
        }
        return newConversations;
      });

      // Clear message input
      setMessage("");

      // Emit socket event with the complete message object
      if (socket) {
        socket.emit("message:send", {
          ...newMessage,
          source: "api",
        });
      }

      // Scroll to bottom
      setTimeout(() => scrollToBottom(messageContainerRef), 100);
    } catch (error: any) {
      console.error("Error sending message:", error);
    }
  };

  const filteredProductConversations = productConversations.filter((prod) => {
    return prod.conversations.some((conv: any) => {
      const userName =
        `${conv.user.firstName} ${conv.user.lastName}`.toLowerCase();
      const matchesSearch = userName.includes(searchTerm.toLowerCase());

      // Handle filter logic
      const matchesFilter =
        filter === 'all' ? true :
          filter === 'unread' ? conv.unreadCount > 0 :
            filter === 'archived' ? conv.status === 'archived' : true;

      return matchesSearch && matchesFilter;
    });
  });


  // Determine message status for display
  const getMessageStatus = (status: string) => {
    switch (status) {
      case 'sent':
        return <Check className="h-4 w-4 text-gray-400" />;
      case 'delivered':
        return <Check className="h-4 w-4 text-teal-500" />;
      case 'read':
        return <CheckCircle className="h-4 w-4 text-teal-500" />;
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-teal-500"></div>
        <p className="ml-4">{t("conversations.loading")}</p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-3 min-h-[calc(100vh-80px)]">
          {/* Messages List */}
          <div className={`bg-white border-r ${selectedConversation ? 'hidden md:block' : ''}`}>
            {/* Header */}
            <div className="p-6 border-b">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">{t("conversations.title")}</h1>
              <p className="text-gray-600 mb-6">{t("manageConversations")}</p>

              {/* Search and Filter */}
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder={t("search.placeholder")}
                    className="w-full pl-12 pr-4 py-3 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => setFilter('all')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${filter === 'all'
                      ? 'bg-teal-500 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                  >
                    {t("all")}
                  </button>
                  <button
                    onClick={() => setFilter('unread')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${filter === 'unread'
                      ? 'bg-teal-500 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                  >
                    {t("unread")}
                  </button>
                  <button
                    onClick={() => setFilter('archived')}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${filter === 'archived'
                      ? 'bg-teal-500 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                  >
                    {t("archived")}
                  </button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="divide-y overflow-y-auto" ref={conversationListRef}>
              {filteredProductConversations.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  {t("conversations.noConversations")}
                </div>
              ) : (
                filteredProductConversations.map((product) => (
                  <div key={product.productId} className="mb-4">
                    <div className="px-4 py-2 flex items-center space-x-2 bg-gray-50">
                      <Package className="h-5 w-5 text-gray-600" />
                      <span className="font-medium text-gray-700">{product.productName}</span>
                    </div>

                    {product.conversations.map((conversation: any) => {
                      const otherUserName = getOtherUserName(conversation);
                      const lastMessage = conversation.lastMessage;
                      const isPinned = conversation.pinned || false;
                      const isTyping = Array.from(typingUsers.values()).some(
                        (data) => data.roomId === conversation.roomId
                      );

                      return (
                        <div
                          key={conversation.roomId}
                          className={`relative p-4 hover:bg-gray-50 transition-colors cursor-pointer ${selectedConversation?.roomId === conversation.roomId ? 'bg-gray-50' : ''
                            }`}
                        >
                          <div
                            className="flex items-start space-x-4"
                            onClick={() => {
                              setSelectedProduct(product);
                              setSelectedConversation(conversation);
                            }}
                          >
                            <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-white font-bold">
                              {otherUserName.charAt(0)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                  <h3 className={`font-semibold ${conversation.unreadCount > 0 ? 'text-gray-900' : 'text-gray-700'}`}>
                                    {otherUserName}
                                  </h3>
                                  {isPinned && (
                                    <Pin className="h-4 w-4 text-teal-500" />
                                  )}
                                </div>
                                <span className="text-sm text-gray-500">
                                  {lastMessage?.createdAt && format(new Date(lastMessage.createdAt), "HH:mm")}
                                </span>
                              </div>
                              <p className="text-sm text-gray-600">{product.productName}</p>
                              <p className={`mt-1 text-sm truncate ${conversation.unreadCount > 0 ? 'font-medium text-gray-900' : 'text-gray-600'}`}>
                                {isTyping ? (
                                  <span className="text-teal-500 italic">{t("conversations.typing")}</span>
                                ) : (
                                  lastMessage?.content
                                )}
                              </p>
                            </div>
                          </div>

                          {/* Unread badge */}
                          {conversation.unreadCount > 0 && (
                            <div className="absolute top-4 right-10 bg-teal-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                              {conversation.unreadCount}
                            </div>
                          )}

                          {/* Message Options Button */}
                          <button
                            onClick={() => setShowOptions(showOptions === conversation.roomId ? null : conversation.roomId)}
                            className="absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100"
                          >
                            <MoreVertical className="h-5 w-5" />
                          </button>

                          {/* Options Menu */}
                          {showOptions === conversation.roomId && (
                            <div className="absolute top-12 right-4 w-48 bg-white rounded-lg shadow-lg border border-gray-100 py-1 z-10">
                              <button
                                onClick={() => {
                                  // Handle pin/unpin
                                  setShowOptions(null);
                                }}
                                className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50"
                              >
                                <Pin className="h-4 w-4" />
                                <span>{isPinned ? t("unpinConversation") : t("pinConversation")}</span>
                              </button>
                              <button
                                onClick={() => {
                                  // Handle archive/unarchive
                                  setShowOptions(null);
                                }}
                                className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50"
                              >
                                <Archive className="h-4 w-4" />
                                <span>{conversation.status === 'archived' ? t("unarchiveConversation") : t("archiveConversation")}</span>
                              </button>
                              <button
                                onClick={() => {
                                  // Handle delete
                                  setShowOptions(null);
                                }}
                                className="w-full flex items-center space-x-2 px-4 py-2 text-red-600 hover:bg-gray-50"
                              >
                                <Trash2 className="h-4 w-4" />
                                <span>{t("deleteConversation")}</span>
                              </button>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Chat View */}
          {selectedConversation ? (
            <div className="md:col-span-2 bg-white flex flex-col h-[calc(100vh-80px)]">
              {/* Chat Header */}
              <div className="p-6 border-b flex items-center justify-between bg-white sticky top-0 z-10">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setSelectedConversation(null)}
                    className="md:hidden text-gray-400 hover:text-gray-600"
                  >
                    <ArrowLeft className="h-6 w-6" />
                  </button>
                  <div className="w-12 h-12 rounded-full bg-gray-300 flex items-center justify-center text-white font-bold">
                    {getOtherUserName(selectedConversation).charAt(0)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">{getOtherUserName(selectedConversation)}</h3>
                    <p className="text-sm text-gray-600">{selectedProduct?.productName}</p>
                  </div>
                </div>
              </div>

              {/* Chat Messages */}
              <div className="flex-1 overflow-y-auto p-6 space-y-6" ref={messageContainerRef}>
                {selectedConversation.messages.map((msg: any, index: number) => {
                  // Fix the isCurrentUser check to handle both string IDs and object IDs
                  const senderId = typeof msg.senderId === 'object' ? msg.senderId._id : msg.senderId;
                  const isCurrentUser = senderId === user?._id;

                  // Fix showAvatar calculation to handle different ID formats
                  let showAvatar: any = false;
                  if (index > 0) {
                    const prevSenderId = typeof selectedConversation.messages[index - 1].senderId === 'object'
                      ? selectedConversation.messages[index - 1].senderId._id
                      : selectedConversation.messages[index - 1].senderId;

                    const currentSenderId = typeof msg.senderId === 'object'
                      ? msg.senderId._id
                      : msg.senderId;

                    if (prevSenderId !== currentSenderId) {
                      showAvatar = true;
                    }
                  }

                  console.log(showAvatar);

                  return (
                    <div
                      key={msg._id}
                      className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className={`max-w-[70%] ${isCurrentUser
                        ? 'bg-teal-500 text-white rounded-l-xl rounded-tr-xl'
                        : 'bg-gray-100 text-gray-900 rounded-r-xl rounded-tl-xl'
                        } p-4`}>
                        <p>{msg.content}</p>
                        <div className={`flex items-center justify-end space-x-2 mt-2 ${isCurrentUser ? 'text-white/70' : 'text-gray-500'
                          }`}>
                          <span className="text-xs">
                            {msg.createdAt && format(new Date(msg.createdAt), "HH:mm")}
                          </span>
                          {isCurrentUser && getMessageStatus(msg.status || 'sent')}
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Typing Indicator */}
                {Array.from(typingUsers).map(([userId, data]) => (
                  typingUsers.size > 0 ? (
                    <div
                      key={userId}
                      className="flex justify-start"
                    >
                      <div className="max-w-[70%] bg-gray-50 text-gray-500 rounded-r-xl rounded-tl-xl p-4">
                        <p className="italic">{data.userName} {t("isTyping")}</p>
                      </div>
                    </div>
                  ) : null
                ))}
              </div>

              {/* Message Input */}
              <div className="p-6 border-t bg-white sticky bottom-0">
                <form onSubmit={(e) => { e.preventDefault(); handleSendMessage(); }} className="flex items-center space-x-4">
                  <input
                    type="text"
                    value={message}
                    onChange={handleMessageChange}
                    placeholder={t("messages.inputPlaceholder")}
                    className="flex-1 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                  />
                  <button
                    type="submit"
                    disabled={!message.trim()}
                    className="p-2 bg-teal-500 text-white rounded-lg hover:bg-teal-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <Send className="h-5 w-5" />
                  </button>
                </form>
              </div>
            </div>
          ) : (
            <div className="hidden md:flex md:col-span-2 items-center justify-center bg-white">
              <div className="text-center">
                <div className="h-16 w-16 text-gray-300 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                  <Search className="h-8 w-8" />
                </div>
                <h3 className="text-lg font-medium text-gray-900">{t("messages.noMessages")}</h3>
                <p className="text-gray-600 mt-2">{t("selectConversationToStartMessaging")}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MessageCenter;