import React from 'react';
import { useSearchParams } from 'react-router-dom';
import Layout from '../components/Layout';
import { Search as SearchIcon } from 'lucide-react';

const Search = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q') || '';

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-2 mb-8">
            <SearchIcon className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold text-gray-900">
              "{query}" için arama sonuçları
            </h1>
          </div>

          {/* Placeholder for search results */}
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <p className="text-gray-600">
              Bu özellik yakında aktif olacaktır. Aradığınız terim: {query}
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Search;