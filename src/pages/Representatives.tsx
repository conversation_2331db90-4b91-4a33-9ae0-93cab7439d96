import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import {
  Search,
  BadgeCheck,
  MapPin,
  Mail,
  Phone,
  Languages,
  Award,
  Calendar,
  Briefcase,
  X,
  Filter,
  ChevronDown,
  Globe2
} from 'lucide-react';

const Representatives = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showPhoneMap, setShowPhoneMap] = useState<{ [key: string]: boolean }>({});

  const languageOptions = [
    { id: 'all', name: '<PERSON><PERSON><PERSON>' },
    { id: 'tr', name: '<PERSON><PERSON>rk<PERSON><PERSON>' },
    { id: 'en', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 'ar', name: '<PERSON><PERSON><PERSON>' },
    { id: 'ru', name: '<PERSON><PERSON><PERSON>' },
    { id: 'fr', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 'es', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
    { id: 'it', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 'zh', name: '<PERSON><PERSON><PERSON>' }
  ];

  const representatives = [
    {
      name: "Ahmet Yılmaz",
      title: "Kıdemli İhracat Uzmanı",
      company: "TechGlobal Solutions",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80",
      languages: ["Türkçe", "İngilizce", "Almanca", "Fransızca"],
      experience: "12 yıl",
      expertise: ["Teknoloji İhracatı", "Pazar Analizi"],
      phone: "+90 532 123 45 67",
      email: "<EMAIL>",
      country: "Türkiye",
      region: "Avrupa",
      sector: "Teknoloji",
      verified: true
    },
    {
      name: "Ayşe Demir",
      title: "Uluslararası Ticaret Danışmanı",
      company: "EcoTrade International",
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80",
      languages: ["Türkçe", "İngilizce", "Fransızca", "İspanyolca"],
      experience: "8 yıl",
      expertise: ["Sürdürülebilir İhracat", "Yeşil Ticaret"],
      phone: "+90 533 234 56 78",
      email: "<EMAIL>",
      country: "Almanya",
      region: "Avrupa",
      sector: "Sürdürülebilir Ticaret",
      verified: true
    },
    {
      name: "Mehmet Kaya",
      title: "İhracat Geliştirme Müdürü",
      company: "MediCare Exports",
      image: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?auto=format&fit=crop&q=80",
      languages: ["Türkçe", "İngilizce", "Arapça", "İtalyanca"],
      experience: "15 yıl",
      expertise: ["Medikal İhracat", "Regülasyon"],
      phone: "+90 534 345 67 89",
      email: "<EMAIL>",
      country: "Birleşik Arap Emirlikleri",
      region: "Orta Doğu",
      sector: "Sağlık",
      verified: false
    },
    {
      name: "Zeynep Aydın",
      title: "Uluslararası İş Geliştirme",
      company: "BuildPro Construction",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&q=80",
      languages: ["Türkçe", "İngilizce", "Rusça", "Çince"],
      experience: "10 yıl",
      expertise: ["İnşaat İhracatı", "Proje Yönetimi"],
      phone: "+90 535 456 78 90",
      email: "<EMAIL>",
      country: "Rusya",
      region: "Avrasya",
      sector: "İnşaat",
      verified: true
    }
  ];

  const togglePhone = (repName: string) => {
    setShowPhoneMap(prev => ({
      ...prev,
      [repName]: !prev[repName]
    }));
  };

  const filteredRepresentatives = representatives.filter(rep => {
    const matchesSearch =
      rep.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rep.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rep.company.toLowerCase().includes(searchQuery.toLowerCase()) ||
      rep.country.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h1 className="text-2xl font-bold text-gray-900">İhracat Temsilcileri</h1>
            <p className="mt-2 text-gray-600">Global ticarette deneyimli uzmanlarımız</p>
          </div>
        </div>

        {/* Filters */}
        <div className="border-b bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Temsilci veya ülke ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Language Filter */}
              <div className="relative">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="appearance-none pl-4 pr-10 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                >
                  {languageOptions.map(option => (
                    <option key={option.id} value={option.id}>
                      {option.name}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>
        </div>

        {/* Representatives Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredRepresentatives.map((rep, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
                <div className="p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <img
                      src={rep.image}
                      alt={rep.name}
                      className="w-16 h-16 rounded-xl object-cover"
                    />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{rep.name}</h3>
                      <p className="text-primary">{rep.title}</p>
                      <p className="text-sm text-gray-600">{rep.company}</p>
                    </div>
                  </div>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-2">
                      <Globe2 className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{rep.country} ({rep.region})</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Languages className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{rep.languages.join(", ")}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Briefcase className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{rep.experience} deneyim</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Award className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{rep.expertise.join(", ")}</span>
                    </div>
                    {showPhoneMap[rep.name] && (
                      <div className="flex items-center space-x-2 text-primary">
                        <Phone className="h-4 w-4" />
                        <span className="text-sm">{rep.phone}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <a
                      href={`mailto:${rep.email}`}
                      className="flex-1 py-2 px-3 bg-primary/10 text-primary rounded-lg text-sm font-medium hover:bg-primary hover:text-white transition-colors duration-200 flex items-center justify-center space-x-2"
                    >
                      <Mail className="h-4 w-4" />
                      <span>E-posta Gönder</span>
                    </a>
                    <button
                      onClick={() => togglePhone(rep.name)}
                      className="flex-1 py-2 px-3 bg-primary/10 text-primary rounded-lg text-sm font-medium hover:bg-primary hover:text-white transition-colors duration-200 flex items-center justify-center space-x-2"
                    >
                      <Phone className="h-4 w-4" />
                      <span>Telefon</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Representatives;