import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Layout from '../components/layout/Layout';
import {
  Search,
  BadgeCheck,
  Mail,
  Phone,
  Languages,
  Award,
  Briefcase,
  ChevronDown,
  Globe2
} from 'lucide-react';
import { getActiveRepresentatives } from "../api/representativeApi";

interface Representative {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  countryName?: string;
  cityName?: string;
  profilePicture?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  company?: string;
  title?: string;
  languages?: string[];
  expertise?: string[];
  experience?: string;
  verified?: boolean;
  region?: string;
}

const Representatives = () => {
  const { t } = useTranslation('representatives');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('all');
  const [showPhoneMap, setShowPhoneMap] = useState<{ [key: string]: boolean }>({});
  const [representatives, setRepresentatives] = useState<Representative[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const languageOptions = [
    { id: 'all', name: t('filters.allLanguages') },
    { id: 'tr', name: t('filters.turkish') },
    { id: 'en', name: t('filters.english') },
    { id: 'ar', name: t('filters.arabic') },
    { id: 'ru', name: t('filters.russian') },
    { id: 'fr', name: t('filters.french') },
    { id: 'es', name: t('filters.spanish') },
    { id: 'it', name: t('filters.italian') },
    { id: 'zh', name: t('filters.chinese') }
  ];

  useEffect(() => {
    const fetchRepresentatives = async () => {
      try {
        setIsLoading(true);
        const response: any = await getActiveRepresentatives();
        if (response.success && response.data) {
          // We want to use the full names, no need to process/revert to codes
          setRepresentatives(response.data);
        } else {
          console.error('Failed to fetch representatives:', response.error);
        }
      } catch (error) {
        console.error('Error fetching representatives:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRepresentatives();
  }, []);

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      // setMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const togglePhone = (repId: string) => {
    setShowPhoneMap(prev => ({
      ...prev,
      [repId]: !prev[repId]
    }));
  };

  const getFullName = (rep: Representative) => {
    return `${rep.firstName} ${rep.lastName}`;
  };

  const filteredRepresentatives = representatives.filter(rep => {
    const matchesSearch =
      getFullName(rep).toLowerCase().includes(searchQuery.toLowerCase()) ||
      (rep.title && rep.title.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (rep.company && rep.company.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (rep.cityName && rep.cityName.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (rep.city && rep.city.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (rep.countryName && rep.countryName.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (rep.country && rep.country.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesLanguage = selectedLanguage === 'all' ||
      (rep.languages && rep.languages.some(lang =>
        lang.toLowerCase().includes(selectedLanguage.toLowerCase())
      ));

    return matchesSearch && matchesLanguage;
  });

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-8">
            <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
            <p className="mt-2 text-gray-600">{t('subtitle')}</p>
          </div>
        </div>

        {/* Filters */}
        <div className="border-b bg-white">
          <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-4">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder={t('filters.searchPlaceholder')}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Language Filter */}
              <div className="relative">
                <select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="appearance-none pl-4 pr-10 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                >
                  {languageOptions.map(option => (
                    <option key={option.id} value={option.id}>
                      {option.name}
                    </option>
                  ))}
                </select>
                <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>
        </div>

        {/* Representatives Grid */}
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-8">
          {isLoading ? (
            <div className="flex justify-center items-center py-12">
              <div className="spinner-border animate-spin inline-block w-8 h-8 border-4 rounded-full text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          ) : filteredRepresentatives.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">{t('noRepresentatives')}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRepresentatives.map((rep) => (
                <div key={rep._id} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="p-6">
                    <div className="flex items-center space-x-4 mb-4">
                      {rep.profilePicture ? (
                        <img
                          src={
                            rep.profilePicture.startsWith("data:") || rep.profilePicture.startsWith("http")
                              ? rep.profilePicture
                              : `${import.meta.env.VITE_SOCKET_URL}/uploads/${rep.profilePicture.replace(/^\/+/, '')}`
                          }
                          alt={getFullName(rep)}
                          className="w-16 h-16 rounded-xl object-cover"
                          onError={(e) => {
                            e.currentTarget.onerror = null;
                            // Create a container for the initials
                            const initialsDiv = document.createElement('div');
                            initialsDiv.className = "w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center text-primary font-bold";
                            initialsDiv.innerText = `${rep.firstName.charAt(0)}${rep.lastName.charAt(0)}`;

                            // Replace the image with the initials div
                            const parent = e.currentTarget.parentElement;
                            if (parent) {
                              parent.replaceChild(initialsDiv, e.currentTarget);
                            }
                          }}
                        />
                      ) : (
                        <div className="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center text-primary font-bold">
                          {rep.firstName.charAt(0)}{rep.lastName.charAt(0)}
                        </div>
                      )}
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-semibold text-gray-900">{getFullName(rep)}</h3>
                          {rep.verified && <BadgeCheck className="h-5 w-5 text-primary" />}
                        </div>
                        {rep.title && <p className="text-primary">{rep.title}</p>}
                        {rep.company && <p className="text-sm text-gray-600">{rep.company}</p>}
                      </div>
                    </div>

                    <div className="space-y-3 mb-6">
                      <div className="flex items-center space-x-2">
                        <Globe2 className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {rep.cityName && rep.countryName ? 
                            `${rep.cityName}, ${rep.countryName}` : 
                            (rep.city && rep.country ? `${rep.city}, ${rep.country}` : '')
                          } 
                          {rep.region ? `(${rep.region})` : ''}
                        </span>
                      </div>
                      {rep.languages && rep.languages.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <Languages className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{rep.languages.join(", ")}</span>
                        </div>
                      )}
                      {rep.experience && (
                        <div className="flex items-center space-x-2">
                          <Briefcase className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{rep.experience} {t('experience')}</span>
                        </div>
                      )}
                      {rep.expertise && rep.expertise.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <Award className="h-4 w-4 text-gray-400" />
                          <span className="text-sm text-gray-600">{rep.expertise.join(", ")}</span>
                        </div>
                      )}
                      {showPhoneMap[rep._id] && (
                        <div className="flex items-center space-x-2 text-primary">
                          <Phone className="h-4 w-4" />
                          <span className="text-sm">{rep.phoneNumber}</span>
                        </div>
                      )}
                    </div>

                    <div className="flex gap-2">
                      <a
                        href={`mailto:${rep.email}`}
                        className="flex-1 py-2 px-3 bg-primary/10 text-primary rounded-lg text-sm font-medium hover:bg-primary hover:text-white transition-colors duration-200 flex items-center justify-center space-x-2"
                      >
                        <Mail className="h-4 w-4" />
                        <span>{t('emailButton')}</span>
                      </a>
                      <button
                        onClick={() => togglePhone(rep._id)}
                        className="flex-1 py-2 px-3 bg-primary/10 text-primary rounded-lg text-sm font-medium hover:bg-primary hover:text-white transition-colors duration-200 flex items-center justify-center space-x-2"
                      >
                        <Phone className="h-4 w-4" />
                        <span>{t('phoneButton')}</span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Representatives;
