import React, { useEffect, useState, useRef } from "react";
import ProductCard from "../components/common/ProductCard";
import {
  Box,
  Heading,
  SimpleGrid,
  Button,
  Select,
  VStack,
  Spinner,
  Alert,
  AlertIcon,
  Container,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  HStack,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Text,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { IItem } from "../types/item";
import { ICategory } from "../types/category";
import ItemRequestModal from "../components/profile/ItemRequestModal";
import {
  getOwnedItemRequests,
  getOwnedItemsByType,
  getCategories,
} from "@/api";
import { deleteItemRequest, deleteItem } from "../api";
import { ChevronDownIcon } from "@chakra-ui/icons";
import { useNavigate } from "react-router-dom";
import { ItemCard } from "@/components/profile/ItemCard";



const UserItems: React.FC = () => {
  const { t } = useTranslation("itemListing");
  const navigate = useNavigate();
  const [items, setItems] = useState<IItem[]>([]);
  const [itemRequests, setItemRequests] = useState<any[]>([]);
  const [selectedType, setSelectedType] = useState<"product" | "service">(
    "product",
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedItemRequest, setSelectedItemRequest] = useState<
    any | undefined
  >(undefined);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const cancelRef = useRef<any>(null);
  const toast = useToast();

  const fetchCategories = async () => {
    try {
      const categoriesData = await getCategories();
      setCategories(categoriesData);
    } catch (err) {
      console.error("Error fetching categories:", err);
    }
  };

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [itemsData, requestsData] = await Promise.all([
        getOwnedItemsByType(selectedType),
        getOwnedItemRequests(),
      ]);
      setItems(itemsData);
      setItemRequests(requestsData);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError("errorFetchingItems");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchData();
  }, [selectedType]);

  const handleEdit = (item: IItem | any) => {
    if ("status" in item) {
      // It's an item request
      setSelectedItemRequest(item);
      setIsModalOpen(true);
    } else {
      // It's an approved item
      toast({
        title: t("cannotEditApprovedItem"),
        status: "warning",
        duration: 3000,
      });
    }
  };

  const handleDelete = (item: IItem | any) => {
    if ("status" in item) {
      // It's an item request
      setDeleteItemId(item._id);
      setIsDeleteDialogOpen(true);
    } else {
      // It's an approved item
      setDeleteItemId(item._id);
      setIsDeleteDialogOpen(true);
    }
  };

  const confirmDelete = async () => {
    if (!deleteItemId) return;

    try {
      const itemToDelete = [...itemRequests, ...items].find(
        (item) => item._id === deleteItemId,
      );
      if (!itemToDelete) return;

      if ("status" in itemToDelete) {
        // It's an item request
        await deleteItemRequest(deleteItemId);
      } else {
        // It's an approved item
        await deleteItem(deleteItemId);
      }

      await fetchData(); // Refresh the data
      toast({
        title:
          "status" in itemToDelete ? t("itemRequestDeleted") : t("itemDeleted"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: t("errorDeletingItemRequest"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setDeleteItemId(null);
    }
  };

  const handleCreateNew = () => {
    setSelectedItemRequest(undefined);
    setIsModalOpen(true);
  };

  const isItemInCategory = (item: any, categoryId: string) => {
    if (!categoryId) return true; // If no category selected, show all items

    // Direct category match
    if (item.category && item.category._id === categoryId) return true;

    // Find the selected category
    const selectedCat = categories.find((cat) => cat._id === categoryId);
    if (!selectedCat) return false;

    // If item's category level is deeper than selected category
    if (item.category && item.category.level > selectedCat.level) {
      // Find all parent categories of this item
      let currentCat: any = item.category;
      while (currentCat && currentCat.parent_id) {
        // Check if any parent matches our selected category
        if (currentCat.parent_id === categoryId) {
          return true;
        }
        // Get the parent category object
        currentCat = categories.find(
          (cat) => cat._id === currentCat.parent_id,
        ) as ICategory;
      }
    }

    return false;
  };

  const filteredItems = selectedCategory
    ? items.filter((item) => isItemInCategory(item, selectedCategory))
    : items;

  const filteredRequests = selectedCategory
    ? itemRequests.filter(
      (request) =>
        request.type === selectedType &&
        request.status === "PENDING" &&
        isItemInCategory(request, selectedCategory),
    )
    : itemRequests.filter(
      (request) =>
        request.type === selectedType && request.status === "PENDING",
    );

  const parentCategories = categories.filter(
    (category) =>
      category.level === 0 && category.type.toLowerCase() === selectedType,
  );

  const getSubcategories = (parentId: string) => {
    return categories.filter((category) => category.parent_id === parentId);
  };

  if (isLoading) {
    return (
      <Box textAlign="center" py={10}>
        <Spinner />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        {t(error)}
      </Alert>
    );
  }

  return (
    <Container maxW="container.xl" py={8}>
      <VStack spacing={6} align="stretch">
        <HStack justify="space-between" align="center">
          <Box>
            <Heading size="lg" mb={4}>
              {t("myItemsTitle")}
            </Heading>
            <Select
              value={selectedType}
              onChange={(e) =>
                setSelectedType(e.target.value as "product" | "service")
              }
              mb={4}
            >
              <option value="product">{t("products")}</option>
              <option value="service">{t("services")}</option>
            </Select>

            <Menu>
              <MenuButton
                as={Button}
                rightIcon={<ChevronDownIcon />}
                mb={4}
                width="220px"
              >
                {selectedCategory
                  ? categories.find((cat) => cat._id === selectedCategory)
                    ?.name || t("selectCategory")
                  : t("selectCategory")}
              </MenuButton>
              <MenuList maxH="300px" overflowY="auto">
                <MenuItem onClick={() => setSelectedCategory(null)}>
                  {t("allCategories")}
                </MenuItem>
                {parentCategories.map((parent) => (
                  <React.Fragment key={parent._id}>
                    <MenuItem
                      fontWeight="bold"
                      onClick={() => setSelectedCategory(parent._id)}
                    >
                      {parent.name}
                    </MenuItem>
                    {getSubcategories(parent._id).map((subCat) => (
                      <MenuItem
                        key={subCat._id}
                        pl={6}
                        onClick={() => setSelectedCategory(subCat._id)}
                      >
                        {subCat.name}
                      </MenuItem>
                    ))}
                  </React.Fragment>
                ))}
              </MenuList>
            </Menu>
          </Box>
          <Button colorScheme="blue" onClick={handleCreateNew}>
            {t("createNewRequest")}
          </Button>
        </HStack>

        <Box>
          <Heading size="md" mb={4}>
            {t("pendingRequests")}
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
            {filteredRequests.map((request) => (
              <ItemCard
                key={request._id}
                item={request}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            ))}
            {filteredRequests.length === 0 && (
              <Text color="gray.500">{t("noItemsFound")}</Text>
            )}
          </SimpleGrid>
        </Box>

        <Box>
          <Heading size="md" mb={4}>
            {t("approvedItems")}
          </Heading>
          <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
            {filteredItems.map((item) => (
              <ProductCard
                key={item._id}
                item={item}
                showActions={true}
                onView={(item: any) => navigate(`/items/${item._id}`)}
                onEdit={handleEdit}
                onDelete={handleDelete}
                isManagement={true}
              />
            ))}
            {filteredItems.length === 0 && (
              <Text color="gray.500">{t("noItemsFound")}</Text>
            )}
          </SimpleGrid>
        </Box>

        <ItemRequestModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedItemRequest(undefined);
          }}
          itemRequest={selectedItemRequest}
          onSuccess={fetchData}
        />

        <AlertDialog
          isOpen={isDeleteDialogOpen}
          leastDestructiveRef={cancelRef}
          onClose={() => setIsDeleteDialogOpen(false)}
        >
          <AlertDialogOverlay>
            <AlertDialogContent>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                {t("deleteItemRequest")}
              </AlertDialogHeader>

              <AlertDialogBody>
                {t("deleteItemRequestConfirmation")}
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button
                  ref={cancelRef}
                  onClick={() => setIsDeleteDialogOpen(false)}
                >
                  {t("cancel")}
                </Button>
                <Button colorScheme="red" onClick={confirmDelete} ml={3}>
                  {t("delete")}
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </VStack>
    </Container>
  );
};

export default UserItems;
