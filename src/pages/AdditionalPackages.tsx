import React from 'react';
import { useParams } from 'react-router-dom';
import Layout from '../components/Layout';
import { 
  FileText,
  ShoppingCart,
  TrendingUp,
  DollarSign,
  Check,
  ArrowRight
} from 'lucide-react';

const AdditionalPackages = () => {
  const { id } = useParams();

  const packages = [
    {
      name: "50 Talep Oluşturma",
      price: 25,
      icon: FileText,
      description: "İşletmeniz için aylık 50 adet talep oluşturma hakkı",
      features: [
        "50 adet talep oluşturma",
        "30 gün geçerlilik",
        "Detaylı talep istatistikleri",
        "Öncelikli destek",
        "E-posta bildirimleri"
      ],
      isPopular: false
    },
    {
      name: "50 Satış Oluşturma",
      price: 25,
      icon: ShoppingCart,
      description: "İşletmeniz için aylık 50 adet satış oluşturma hakkı",
      features: [
        "50 adet satış oluşturma",
        "30 gün geçerlilik",
        "<PERSON><PERSON><PERSON><PERSON> istatistikleri",
        "Öncelikli destek",
        "E-posta bildirimleri"
      ],
      isPopular: false
    },
    {
      name: "Aylık Ana Sayfa Reklamı",
      price: 100,
      icon: TrendingUp,
      description: "Ana sayfada 30 gün boyunca premium reklam gösterimi",
      features: [
        "30 gün reklam gösterimi",
        "Premium konum",
        "Özelleştirilebilir içerik",
        "Detaylı analitik",
        "Reklam performans raporu"
      ],
      isPopular: true
    }
  ];

  return (
    <Layout>
      <div className="min-h-screen bg-white">
        {/* Header Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <span className="inline-block px-4 py-2 rounded-lg bg-primary/10 text-primary text-sm font-medium mb-4">
              Hizmetlerinizi Genişletin
            </span>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              İhtiyacınıza Uygun Ek Paketler
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              E-exportcity'nin esnek ve hedef odaklı ek paketleriyle işletmenizi bir adım öne taşıyın.
            </p>
          </div>
        </div>

        {/* Packages Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {packages.map((pkg, index) => (
              <div 
                key={index}
                className={`relative rounded-2xl ${
                  pkg.isPopular 
                    ? 'border-2 border-primary scale-105 shadow-xl' 
                    : 'border border-gray-200 shadow-sm'
                } bg-white p-8 transform transition-all duration-200 hover:shadow-lg`}
              >
                {pkg.isPopular && (
                  <div className="absolute -top-5 left-1/2 transform -translate-x-1/2">
                    <span className="inline-block px-4 py-2 rounded-full bg-primary text-white text-sm font-medium">
                      En Çok Tercih Edilen
                    </span>
                  </div>
                )}

                <div className="flex items-center space-x-3 mb-4">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    pkg.isPopular ? 'bg-primary text-white' : 'bg-primary/10 text-primary'
                  }`}>
                    <pkg.icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">{pkg.name}</h3>
                </div>

                <p className="text-gray-600 mb-6">
                  {pkg.description}
                </p>

                <div className="mb-8">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">${pkg.price}</span>
                    <span className="ml-2 text-gray-500">/aylık</span>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {pkg.features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-3">
                      <Check className="h-5 w-5 text-primary flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button 
                  className={`w-full py-4 rounded-lg flex items-center justify-center space-x-2 text-base font-medium transition-all duration-200 ${
                    pkg.isPopular
                      ? 'bg-primary text-white hover:bg-primary/90'
                      : 'bg-primary/10 text-primary hover:bg-primary hover:text-white'
                  } group`}
                >
                  <ShoppingCart className="w-5 h-5" />
                  <span>Satın Al</span>
                  <ArrowRight className="w-5 h-5 transform group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Bottom Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 border-t">
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              E-exportcity ile işletmenizi büyütün!
            </h2>
            <p className="text-gray-600 max-w-3xl mx-auto">
              E-exportcity'nin sunduğu ek paketlerle işletmenizin potansiyelini maksimuma çıkarın. İhtiyacınıza uygun paketlerle daha fazla müşteriye ulaşın, satışlarınızı artırın ve global pazarda öne çıkın.
            </p>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AdditionalPackages;