import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Container,
  FormControl,
  FormLabel,
  Input,
  Select,
  Textarea,
  VStack,
  useToast,
  FormErrorMessage,
  NumberInput,
  NumberInputField,
  HStack,
  Image,
  IconButton,
  useColorModeValue,
} from "@chakra-ui/react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { DeleteIcon } from "@chakra-ui/icons";
import { ICreateTicket } from "@/types/ticket";
import { ticketApi } from "@/api/ticketApi";
import {
  getCategories,
  getCategoriesByParentId,
  getCountries,
  getCities,
} from "@/api";
import { ICategory } from "@/types/category";
import { ICity, ICountry } from "@/types/user";

const CreateTicket: React.FC = () => {
  const { t } = useTranslation(["tickets", "common"]);
  const toast = useToast();
  const navigate = useNavigate();
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [subcategories, setSubcategories] = useState<ICategory[]>([]);
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [cities, setCities] = useState<ICity[]>([]);
  const bgColor = useColorModeValue("white", "gray.700");

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<ICreateTicket>();

  const selectedCategory = watch("category");
  const selectedCountry = watch("country");

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (selectedCategory) {
      fetchSubcategories(selectedCategory);
    }
  }, [selectedCategory]);

  useEffect(() => {
    if (selectedCountry) {
      const country = countries.find((c) => c.name === selectedCountry);
      if (country) {
        fetchCities(country.code);
      }
    }
  }, [selectedCountry, countries]);

  const fetchInitialData = async () => {
    try {
      const [categoriesData, countriesData] = await Promise.all([
        getCategories("0"),
        getCountries(),
      ]);
      setCategories(categoriesData);
      setCountries(countriesData);
    } catch (error: any) {
      toast({
        title: t("tickets:messages.fetch_error.title"),
        description: t("tickets:messages.fetch_error.description"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const fetchSubcategories = async (parentId: string) => {
    try {
      const data = await getCategoriesByParentId(parentId);
      setSubcategories(data);
    } catch (error: any) {
      toast({
        title: t("tickets:messages.fetch_error.title"),
        description: t("tickets:messages.fetch_error.description"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const fetchCities = async (countryCode: string) => {
    try {
      const data = await getCities(countryCode);
      setCities(data);
      setValue("city", ""); // Reset city when country changes
    } catch (error: any) {
      toast({
        title: t("tickets:messages.fetch_error.title"),
        description: t("tickets:messages.fetch_error.description"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      const newImages = Array.from(files);
      setSelectedImages((prev) => [...prev, ...newImages]);

      // Create preview URLs
      newImages.forEach((file) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          setImagePreviewUrls((prev) => [...prev, reader.result as string]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const removeImage = (index: number) => {
    setSelectedImages((prev) => prev.filter((_, i) => i !== index));
    setImagePreviewUrls((prev) => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: ICreateTicket) => {
    try {
      const formData = new FormData();
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      selectedImages.forEach((file) => {
        formData.append("images", file);
      });

      const response = await ticketApi.create(formData);

      if (response.success) {
        toast({
          title: t("tickets:messages.create_success.title"),
          description: t("tickets:messages.create_success.description"),
          status: "success",
          duration: 3000,
        });
        navigate("/tickets");
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: t("tickets:messages.create_error.title"),
        description: t("tickets:messages.create_error.description"),
        status: "error",
        duration: 5000,
      });
    }
  };

  return (
    <Container maxW="container.md" py={8}>
      <Box bg={bgColor} p={6} borderRadius="lg" shadow="md">
        <form onSubmit={handleSubmit(onSubmit)}>
          <VStack spacing={4} align="stretch">
            <FormControl isInvalid={!!errors.title}>
              <FormLabel>{t("tickets:form.title")}</FormLabel>
              <Input
                {...register("title", {
                  required: t("tickets:validation.title_required"),
                })}
                placeholder={t("tickets:form.title")}
              />

              <FormErrorMessage>{errors.title?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.type}>
              <FormLabel>{t("tickets:form.type")}</FormLabel>
              <Select
                {...register("type", {
                  required: t("tickets:validation.type_required"),
                })}
                placeholder={t("tickets:form.select_type")}
              >
                <option value="product">
                  {t("tickets:form.type_product")}
                </option>
                <option value="service">
                  {t("tickets:form.type_service")}
                </option>
              </Select>
              <FormErrorMessage>{errors.type?.message}</FormErrorMessage>
            </FormControl>

            <HStack spacing={4}>
              <FormControl isInvalid={!!errors.category}>
                <FormLabel>{t("tickets:form.category")}</FormLabel>
                <Select
                  {...register("category", {
                    required: t("tickets:validation.category_required"),
                  })}
                  placeholder={t("tickets:form.select_type")}
                >
                  {categories.map((category: any) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage>{errors.category?.message}</FormErrorMessage>
              </FormControl>

              <FormControl>
                <FormLabel>{t("tickets:form.subcategory")}</FormLabel>
                <Select
                  {...register("subcategory")}
                  placeholder={t("tickets:form.subcategory")}
                  isDisabled={!selectedCategory}
                >
                  {subcategories.map((subcategory: any) => (
                    <option key={subcategory.id} value={subcategory.id}>
                      {subcategory.name}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </HStack>

            <HStack spacing={4}>
              <FormControl isInvalid={!!errors.country}>
                <FormLabel>{t("tickets:form.country")}</FormLabel>
                <Select
                  {...register("country", {
                    required: t("tickets:validation.country_required"),
                  })}
                  placeholder={t("tickets:form.country")}
                >
                  {countries.map((country) => (
                    <option key={country.code} value={country.name}>
                      {country.name}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage>{errors.country?.message}</FormErrorMessage>
              </FormControl>

              <FormControl isInvalid={!!errors.city}>
                <FormLabel>{t("tickets:form.city")}</FormLabel>
                <Select
                  {...register("city", {
                    required: t("tickets:validation.city_required"),
                  })}
                  placeholder={t("tickets:form.city")}
                  isDisabled={!selectedCountry}
                >
                  {cities.map((city) => (
                    <option key={city.name} value={city.name}>
                      {city.name}
                    </option>
                  ))}
                </Select>
                <FormErrorMessage>{errors.city?.message}</FormErrorMessage>
              </FormControl>
            </HStack>

            <FormControl isInvalid={!!errors.amount}>
              <FormLabel>{t("tickets:form.amount")}</FormLabel>
              <NumberInput min={0}>
                <NumberInputField
                  {...register("amount", {
                    required: t("tickets:validation.amount_required"),
                    min: {
                      value: 0,
                      message: t("tickets:validation.amount_min"),
                    },
                  })}
                  placeholder={t("tickets:form.amount")}
                />
              </NumberInput>
              <FormErrorMessage>{errors.amount?.message}</FormErrorMessage>
            </FormControl>

            <FormControl isInvalid={!!errors.description}>
              <FormLabel>{t("tickets:form.description")}</FormLabel>
              <Textarea
                {...register("description", {
                  required: t("tickets:validation.description_required"),
                  minLength: {
                    value: 20,
                    message: t("tickets:validation.description_min_length"),
                  },
                })}
                placeholder={t("tickets:form.description")}
                rows={5}
              />

              <FormErrorMessage>{errors.description?.message}</FormErrorMessage>
            </FormControl>

            <FormControl>
              <FormLabel>{t("tickets:form.images")}</FormLabel>
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageChange}
                display="none"
                id="image-upload"
              />

              <Button
                as="label"
                htmlFor="image-upload"
                colorScheme="blue"
                width="full"
              >
                {t("tickets:form.upload_images")}
              </Button>

              {imagePreviewUrls.length > 0 && (
                <HStack mt={4} flexWrap="wrap" spacing={4}>
                  {imagePreviewUrls.map((url, index) => (
                    <Box key={index} position="relative">
                      <Image
                        src={url}
                        alt={`Preview ${index + 1}`}
                        boxSize="100px"
                        objectFit="cover"
                        borderRadius="md"
                      />

                      <IconButton
                        aria-label="Remove image"
                        icon={<DeleteIcon />}
                        size="sm"
                        position="absolute"
                        top={1}
                        right={1}
                        colorScheme="red"
                        onClick={() => removeImage(index)}
                      />
                    </Box>
                  ))}
                </HStack>
              )}
            </FormControl>

            <Button
              type="submit"
              colorScheme="blue"
              isLoading={isSubmitting}
              loadingText={t("tickets:form.submitting")}
            >
              {t("common:create")}
            </Button>
          </VStack>
        </form>
      </Box>
    </Container>
  );
};

export default CreateTicket;
