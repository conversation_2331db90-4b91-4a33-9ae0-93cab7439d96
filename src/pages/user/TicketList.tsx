import React, { useEffect, useState } from "react";
import {
  Box,
  Container,
  Heading,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  useColorModeValue,
  useToast,
  HStack,
  Text,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  VStack,
  Image,
  Textarea,
  IconButton,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { AddIcon, ChatIcon } from "@chakra-ui/icons";
import { ITicket } from "@/types/ticket";
import { ticketApi } from "@/api/ticketApi";

const TicketList: React.FC = () => {
  const { t } = useTranslation(["tickets", "common"]);
  const toast = useToast();
  const navigate = useNavigate();
  const [tickets, setTickets] = useState<any[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<any>(null);
  const [newResponse, setNewResponse] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const bgColor = useColorModeValue("white", "gray.700");

  const fetchTickets = async () => {
    try {
      const response = await ticketApi.getUserTickets();
      if (response.success && response.data) {
        setTickets(
          Array.isArray(response.data) ? response.data : [response.data],
        );
      }
    } catch (error) {
      toast({
        title: t("tickets:messages.fetch_error.title"),
        description: t("tickets:messages.fetch_error.description"),
        status: "error",
        duration: 3000,
      });
    }
  };

  useEffect(() => {
    fetchTickets();
  }, []);

  const handleViewTicket = async (ticket: ITicket) => {
    setSelectedTicket(ticket);
    onOpen();
  };

  const handleAddResponse = async () => {
    if (!selectedTicket || !newResponse.trim()) return;

    setIsLoading(true);
    try {
      const response = await ticketApi.addResponse(
        selectedTicket.id,
        newResponse,
      );
      if (response.success) {
        toast({
          title: t("tickets:messages.response_added.title"),
          description: t("tickets:messages.response_added.description"),
          status: "success",
          duration: 3000,
        });
        setNewResponse("");
        // Refresh ticket data
        const updatedTicket = await ticketApi.getById(selectedTicket.id);
        if (updatedTicket.success && updatedTicket.data) {
          setSelectedTicket(updatedTicket.data as ITicket);
          await fetchTickets(); // Refresh the list
        }
      }
    } catch (error) {
      toast({
        title: t("tickets:messages.response_error.title"),
        description: t("tickets:messages.response_error.description"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const statusColors = {
      pending: "yellow",
      in_progress: "blue",
      resolved: "green",
      closed: "gray",
    };
    return statusColors[status as keyof typeof statusColors] || "gray";
  };

  return (
    <Container maxW="container.xl" py={8}>
      <HStack justify="space-between" mb={6}>
        <Heading size="lg">{t("tickets:title")}</Heading>
        <Button
          leftIcon={<AddIcon />}
          colorScheme="blue"
          onClick={() => navigate("/tickets/create")}
        >
          {t("tickets:create_ticket")}
        </Button>
      </HStack>

      <Box bg={bgColor} borderRadius="lg" shadow="md" overflow="hidden">
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>{t("tickets:table.title")}</Th>
              <Th>{t("tickets:table.type")}</Th>
              <Th>{t("tickets:table.category")}</Th>
              <Th>{t("tickets:table.status")}</Th>
              <Th>{t("tickets:table.created_at")}</Th>
              <Th>{t("tickets:table.actions")}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {tickets.map((ticket: any) => (
              <Tr key={ticket.id}>
                <Td>{ticket.title}</Td>
                <Td>{t(`tickets:types.${ticket.type}`)}</Td>
                <Td>{ticket.category}</Td>
                <Td>
                  <Badge colorScheme={getStatusColor(ticket.status)}>
                    {t(`tickets:status.${ticket.status}`)}
                  </Badge>
                </Td>
                <Td>{new Date(ticket.createdAt).toLocaleDateString()}</Td>
                <Td>
                  <IconButton
                    aria-label="View ticket"
                    icon={<ChatIcon />}
                    size="sm"
                    colorScheme="blue"
                    onClick={() => handleViewTicket(ticket)}
                  />
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>

      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{selectedTicket?.title}</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              <Box>
                <Text fontWeight="bold">{t("tickets:form.description")}</Text>
                <Text>{selectedTicket?.description}</Text>
              </Box>

              {selectedTicket?.images && selectedTicket.images.length > 0 && (
                <Box>
                  <Text fontWeight="bold" mb={2}>
                    {t("tickets:form.images")}
                  </Text>
                  <HStack spacing={4} overflowX="auto" py={2}>
                    {selectedTicket.images.map(
                      (image: string, index: number) => (
                        <Image
                          key={index}
                          src={image}
                          alt={`Ticket image ${index + 1}`}
                          boxSize="100px"
                          objectFit="cover"
                          borderRadius="md"
                        />
                      ),
                    )}
                  </HStack>
                </Box>
              )}

              <Box>
                <Text fontWeight="bold" mb={2}>
                  {t("tickets:responses")}
                </Text>
                <VStack spacing={3} align="stretch">
                  {selectedTicket?.responses?.map((response: any) => (
                    <Box
                      key={response.id}
                      p={3}
                      bg={response.isAdmin ? "blue.50" : "gray.50"}
                      borderRadius="md"
                    >
                      <Text fontSize="sm" color="gray.500" mb={1}>
                        {response.isAdmin
                          ? t("tickets:admin")
                          : t("tickets:you")}{" "}
                        - {new Date(response.createdAt).toLocaleString()}
                      </Text>
                      <Text>{response.message}</Text>
                    </Box>
                  ))}
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2}>
                  {t("tickets:add_response")}
                </Text>
                <Textarea
                  value={newResponse}
                  onChange={(e) => setNewResponse(e.target.value)}
                  placeholder={t("tickets:response_placeholder")}
                  rows={3}
                />

                <Button
                  mt={2}
                  colorScheme="blue"
                  isLoading={isLoading}
                  onClick={handleAddResponse}
                  isDisabled={!newResponse.trim()}
                >
                  {t("tickets:send")}
                </Button>
              </Box>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Container>
  );
};

export default TicketList;
