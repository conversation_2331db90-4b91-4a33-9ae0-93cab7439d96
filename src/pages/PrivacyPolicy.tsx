import React from 'react';
import { useTranslation } from 'react-i18next';
import Header from '@/components/Header';
import Footer from '@/components/common/Footer';

const PrivacyPolicy: React.FC = () => {
  const { t } = useTranslation('privacyPolicy');

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1 container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-3xl font-bold mb-8 text-center">{t('title')}</h1>

        <div className="prose prose-lg max-w-none">
          <p className="mb-6">{t('intro')}</p>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">{t('sections.collectedData.title')}</h2>
            <p className="mb-4">{t('sections.collectedData.description')}</p>
            <ul className="list-disc pl-6 space-y-2">
              {(t('sections.collectedData.items', { returnObjects: true }) as any).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">{t('sections.dataUsage.title')}</h2>
            <p className="mb-4">{t('sections.dataUsage.description')}</p>
            <ul className="list-disc pl-6 space-y-2">
              {(t('sections.dataUsage.items', { returnObjects: true }) as any).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">{t('sections.dataSharing.title')}</h2>
            <p className="mb-4">{t('sections.dataSharing.description')}</p>
            <ul className="list-disc pl-6 space-y-2">
              {(t('sections.dataSharing.items', { returnObjects: true }) as any).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">{t('sections.cookies.title')}</h2>
            <p>{t('sections.cookies.description')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">{t('sections.dataSecurity.title')}</h2>
            <p className="mb-4">{t('sections.dataSecurity.description')}</p>
            <ul className="list-disc pl-6 space-y-2">
              {(t('sections.dataSecurity.items', { returnObjects: true }) as any).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
            <p className="mt-4">{t('sections.dataSecurity.payment')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">{t('sections.yourRights.title')}</h2>
            <p className="mb-4">{t('sections.yourRights.description')}</p>
            <ul className="list-disc pl-6 space-y-2">
              {(t('sections.yourRights.items', { returnObjects: true }) as any).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
            <p className="mt-4">{t('sections.yourRights.contact')}</p>
          </section>

          <section className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">{t('sections.changes.title')}</h2>
            <p>{t('sections.changes.description')}</p>
          </section>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default PrivacyPolicy;