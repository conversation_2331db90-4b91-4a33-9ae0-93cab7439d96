import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import ImageUpload from '../components/ImageUpload';
import { 
  User,
  Building2,
  Mail,
  Phone,
  Globe,
  MapPin,
  Lock,
  Bell,
  Languages,
  Save,
  Trash2,
  Image as ImageIcon,
  Upload,
  FileText,
  DollarSign,
  CreditCard,
  Briefcase,
  Users,
  Settings as SettingsIcon,
  Package,
  Star,
  Crown,
  HelpCircle,
  Plus,
  ExternalLink,
  MessageSquare,
  Clock,
  CheckCircle,
  AlertCircle,
  ShoppingCart,
  Copy,
  UserCheck,
  Calendar,
  Award,
  Volume2,
  Monitor,
  Sun,
  Moon,
  BellRing,
  Shield,
  Eye,
  EyeOff,
  Key,
  Zap,
  TrendingUp,
  ArrowRight,
  Link,
  Pencil,
  X
} from 'lucide-react';

const Settings = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('profile');
  const [showCopiedMessage, setShowCopiedMessage] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [referenceCode] = useState('TGS-2024-001');
  const [activeMembers] = useState(12850);
  const [lastActive] = useState('2 saat önce');
  const [joinDate] = useState('Ocak 2024');

  // Profile Data
  const [profileData, setProfileData] = useState({
    firstName: 'Ahmet',
    lastName: 'Yılmaz',
    email: '<EMAIL>',
    phone: '+90 532 123 45 67',
    company: 'TechGlobal Solutions',
    position: 'CEO',
    website: 'www.techglobal.com',
    location: 'İstanbul, Türkiye',
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80",
    coverImage: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&q=80",
    about: {
      description: "Endüstriyel yazılım çözümleri ve teknoloji danışmanlığı alanında öncü firma",
      mission: "Teknoloji ile işletmelerin dijital dönüşümüne öncülük etmek",
      vision: "Global ölçekte teknoloji çözümleri sunan lider firma olmak",
      values: [
        "Müşteri Odaklılık",
        "İnovasyon",
        "Sürdürülebilirlik",
        "Kalite"
      ]
    },
    companyInfo: {
      type: 'company', // 'company', 'individual', 'broker'
      foundedYear: '2015',
      employeeCount: '50-100',
      taxNumber: '1234567890',
      taxOffice: 'İstanbul',
      sector: 'Teknoloji',
      subsector: 'Yazılım'
    },
    certificates: [
      {
        id: 1,
        name: "ISO 9001:2015",
        issuer: "Bureau Veritas",
        issueDate: "2023-01-15",
        expiryDate: "2026-01-14",
        file: "iso9001.pdf"
      },
      {
        id: 2,
        name: "ISO 27001",
        issuer: "TÜV SÜD",
        issueDate: "2023-03-20",
        expiryDate: "2026-03-19",
        file: "iso27001.pdf"
      }
    ],
    socialMedia: {
      linkedin: "https://linkedin.com/company/techglobal",
      twitter: "https://twitter.com/techglobal",
      facebook: "https://facebook.com/techglobal",
      instagram: "https://instagram.com/techglobal"
    }
  });

  // Password Change Data
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  // Products and Services
  const [products] = useState([
    {
      id: 1,
      name: "Endüstriyel Otomasyon Yazılımı",
      type: "Hizmet",
      status: "active",
      views: 1250,
      created: "2024-01-15"
    },
    {
      id: 2,
      name: "IoT Platform",
      type: "Ürün",
      status: "active",
      views: 980,
      created: "2024-02-01"
    }
  ]);

  const handleProfileImageChange = (file: File) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      setProfileData(prev => ({
        ...prev,
        avatar: reader.result as string
      }));
    };
    reader.readAsDataURL(file);
  };

  const handleCoverImageChange = (file: File) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      setProfileData(prev => ({
        ...prev,
        coverImage: reader.result as string
      }));
    };
    reader.readAsDataURL(file);
  };

  const handleCertificateUpload = (file: File) => {
    // Handle certificate upload
    console.log('Certificate upload:', file);
  };

  const handleRemoveCertificate = (id: number) => {
    setProfileData(prev => ({
      ...prev,
      certificates: prev.certificates.filter(cert => cert.id !== id)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', profileData);
  };

  return (
    <Layout>
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Tabs */}
          <div className="flex space-x-4 mb-8">
            <button
              onClick={() => setActiveTab('profile')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                activeTab === 'profile'
                  ? 'bg-primary text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Profil Ayarları
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                activeTab === 'settings'
                  ? 'bg-primary text-white'
                  : 'bg-white text-gray-600 hover:bg-gray-50'
              }`}
            >
              Ayarlar
            </button>
          </div>

          {activeTab === 'profile' ? (
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Profile Images */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Profil Görselleri</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Kapak Fotoğrafı
                    </label>
                    <ImageUpload
                      currentImage={profileData.coverImage}
                      onChange={handleCoverImageChange}
                      aspectRatio="aspect-[21/9]"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Profil Fotoğrafı
                    </label>
                    <ImageUpload
                      currentImage={profileData.avatar}
                      onChange={handleProfileImageChange}
                      className="w-32 h-32"
                    />
                  </div>
                </div>
              </div>

              {/* Company Information */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Firma Bilgileri</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Firma Adı
                    </label>
                    <input
                      type="text"
                      value={profileData.company}
                      onChange={(e) => setProfileData(prev => ({ ...prev, company: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Firma Türü
                    </label>
                    <select
                      value={profileData.companyInfo.type}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, type: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      <option value="company">Kurumsal Firma</option>
                      <option value="individual">Şahıs Firması</option>
                      <option value="broker">Broker</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Kuruluş Yılı
                    </label>
                    <input
                      type="text"
                      value={profileData.companyInfo.foundedYear}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, foundedYear: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Çalışan Sayısı
                    </label>
                    <select
                      value={profileData.companyInfo.employeeCount}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, employeeCount: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      <option value="1-10">1-10</option>
                      <option value="11-50">11-50</option>
                      <option value="50-100">50-100</option>
                      <option value="101-250">101-250</option>
                      <option value="251-500">251-500</option>
                      <option value="500+">500+</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Vergi Numarası
                    </label>
                    <input
                      type="text"
                      value={profileData.companyInfo.taxNumber}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, taxNumber: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Vergi Dairesi
                    </label>
                    <input
                      type="text"
                      value={profileData.companyInfo.taxOffice}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, taxOffice: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sektör
                    </label>
                    <input
                      type="text"
                      value={profileData.companyInfo.sector}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, sector: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Alt Sektör
                    </label>
                    <input
                      type="text"
                      value={profileData.companyInfo.subsector}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        companyInfo: { ...prev.companyInfo, subsector: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                </div>
              </div>

              {/* About Information */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Hakkında</h2>
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Firma Açıklaması
                    </label>
                    <textarea
                      value={profileData.about.description}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        about: { ...prev.about, description: e.target.value }
                      }))}
                      rows={4}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Misyon
                    </label>
                    <textarea
                      value={profileData.about.mission}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        about: { ...prev.about, mission: e.target.value }
                      }))}
                      rows={3}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Vizyon
                    </label>
                    <textarea
                      value={profileData.about.vision}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        about: { ...prev.about, vision: e.target.value }
                      }))}
                      rows={3}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Değerler
                    </label>
                    <div className="space-y-2">
                      {profileData.about.values.map((value, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <input
                            type="text"
                            value={value}
                            onChange={(e) => {
                              const newValues = [...profileData.about.values];
                              newValues[index] = e.target.value;
                              setProfileData(prev => ({
                                ...prev,
                                about: { ...prev.about, values: newValues }
                              }));
                            }}
                            className="flex-1 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              const newValues = profileData.about.values.filter((_, i) => i !== index);
                              setProfileData(prev => ({
                                ...prev,
                                about: { ...prev.about, values: newValues }
                              }));
                            }}
                            className="p-2 text-red-500 hover:text-red-600"
                          >
                            <X className="h-5 w-5" />
                          </button>
                        </div>
                      ))}
                      <button
                        type="button"
                        onClick={() => {
                          setProfileData(prev => ({
                            ...prev,
                            about: {
                              ...prev.about,
                              values: [...prev.about.values, '']
                            }
                          }));
                        }}
                        className="flex items-center space-x-2 text-primary hover:text-[#0A9996]"
                      >
                        <Plus className="h-5 w-5" />
                        <span>Değer Ekle</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Certificates */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Sertifikalar</h2>
                <div className="space-y-6">
                  {profileData.certificates.map((cert) => (
                    <div key={cert.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h3 className="font-medium text-gray-900">{cert.name}</h3>
                        <p className="text-sm text-gray-600">{cert.issuer}</p>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <span>Veriliş: {cert.issueDate}</span>
                          <span>Bitiş: {cert.expiryDate}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          type="button"
                          onClick={() => window.open(cert.file)}
                          className="p-2 text-primary hover:text-[#0A9996]"
                        >
                          <FileText className="h-5 w-5" />
                        </button>
                        <button
                          type="button"
                          onClick={() => handleRemoveCertificate(cert.id)}
                          className="p-2 text-red-500 hover:text-red-600"
                        >
                          <Trash2 className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  ))}

                  <div className="relative border-2 border-dashed border-gray-300 rounded-lg p-6">
                    <input
                      type="file"
                      onChange={(e) => {
                        if (e.target.files?.[0]) {
                          handleCertificateUpload(e.target.files[0]);
                        }
                      }}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      accept=".pdf,.jpg,.jpeg,.png"
                    />
                    <div className="text-center">
                      <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">
                        Sertifika yüklemek için tıklayın veya sürükleyin
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        PDF, JPG veya PNG (max. 5MB)
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Products and Services */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-lg font-semibold text-gray-900">Ürün ve Hizmetler</h2>
                  <button
                    type="button"
                    onClick={() => navigate('/products/add')}
                    className="flex items-center space-x-2 text-primary hover:text-[#0A9996]"
                  >
                    <Plus className="h-5 w-5" />
                    <span>Yeni Ekle</span>
                  </button>
                </div>

                <div className="space-y-4">
                  {products.map((product) => (
                    <div key={product.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h3 className="font-medium text-gray-900">{product.name}</h3>
                        <div className="flex items-center space-x-4 mt-2">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            product.type === 'Hizmet'
                              ? 'bg-primary/10 text-primary'
                              : 'bg-[#10B981]/10 text-[#10B981]'
                          }`}>
                            {product.type}
                          </span>
                          <div className="flex items-center space-x-1 text-gray-500">
                            <Eye className="h-4 w-4" />
                            <span className="text-sm">{product.views}</span>
                          </div>
                          <span className="text-sm text-gray-500">{product.created}</span>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => navigate(`/products/${product.id}`)}
                        className="text-primary hover:text-[#0A9996]"
                      >
                        <ExternalLink className="h-5 w-5" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Social Media */}
              <div className="bg-white rounded-xl shadow-sm p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">Sosyal Medya</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      LinkedIn
                    </label>
                    <input
                      type="url"
                      value={profileData.socialMedia.linkedin}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        socialMedia: { ...prev.socialMedia, linkedin: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Twitter
                    </label>
                    <input
                      type="url"
                      value={profileData.socialMedia.twitter}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        socialMedia: { ...prev.socialMedia, twitter: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Facebook
                    </label>
                    <input
                      type="url"
                      value={profileData.socialMedia.facebook}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        socialMedia: { ...prev.socialMedia, facebook: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Instagram
                    </label>
                    <input
                      type="url"
                      value={profileData.socialMedia.instagram}
                      onChange={(e) => setProfileData(prev => ({
                        ...prev,
                        socialMedia: { ...prev.socialMedia, instagram: e.target.value }
                      }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => navigate('/settings')}
                  className="px-6 py-3 text-gray-600 hover:text-gray-800"
                >
                  İptal
                </button>
                <button
                  type="submit"
                  className="px-6 py-3 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors flex items-center space-x-2"
                >
                  <Save className="h-5 w-5" />
                  <span>Değişiklikleri Kaydet</span>
                </button>
              </div>
            </form>
          ) : (
            // Settings Tab content remains unchanged
            <div className="space-y-8">
              {/* Previous settings content */}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Settings;