import React, { useState, useEffect, useRef, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  Zap,
  Star,
  Check,
  Building2,
  ShoppingCart,
  Plus,
  FileText,
  TrendingUp,
  ArrowRight,
  LogIn
} from 'lucide-react';
import { getActiveSubscription, getPackages } from "../api";
import { addStoredCard, getStoredCards } from "@/api/cardApi";
import { initiatePackagePurchase } from "@/api/packageApi";
import { useAuth } from "@/context/AuthContext";

interface PaymentResultEvent {
  type: "PAYMENT_RESULT";
  status: "success" | "failed";
  messageKey?: string;
}

declare global {
  interface WindowEventMap {
    "payment-completed": CustomEvent;
  }
}

interface IPackage {
  _id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  type: "standard" | "addon";
  viewRequestLimit: number;
  createRequestLimit: number;
  emailNotification: boolean;
  smsNotification: boolean;
  languageIntroRights: number;
  messagingAllowed: boolean;
  homepageAd: boolean;
  yearEndSectorReport: boolean;
  isActive: boolean;
  features: string[];
  maxMessages: number;
  duration: number;
  order: number;
  isCurrentPackage?: boolean;
  isUpgradeable?: boolean;
  upgradePrice?: number;
  isDisabled?: boolean;
}

interface StoredCard {
  _id: string;
  cardAlias: string;
  lastFourDigits: string;
  cardType: string;
  cardAssociation: string;
  isDefault?: boolean;
}

const PackageSelection: React.FC = () => {
  const [packages, setPackages] = useState<IPackage[]>([]);
  const [addOnPackages, setAddOnPackages] = useState<IPackage[]>([]);
  const [standardPackages, setStandardPackages] = useState<IPackage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPackage, setSelectedPackage] = useState<any>(null);
  const [currentPackage, setCurrentPackage] = useState<any>(null);
  const [remainingDays, setRemainingDays] = useState(0);
  const [paymentCard, setPaymentCard] = useState({
    cardHolderName: "",
    cardNumber: "",
    expireMonth: "",
    expireYear: "",
    cvc: "",
    saveCard: false,
    cardAlias: "",
  });

  const [storedCards, setStoredCards] = useState<StoredCard[]>([]);
  const [selectedCardId, setSelectedCardId] = useState<string>("");
  const [useNewCard, setUseNewCard] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [threeDSContent, setThreeDSContent] = useState<string | null>(null);

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const cancelRef = useRef<HTMLButtonElement>(null);

  const { t, i18n } = useTranslation("packages");
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();

  const getCardType = (number: string) => {
    const cleanNumber = number.replace(/\s/g, "");
    if (cleanNumber.startsWith("34") || cleanNumber.startsWith("37"))
      return "amex";
    if (cleanNumber.startsWith("4")) return "visa";
    if (cleanNumber.startsWith("5")) return "mastercard";
    return "unknown";
  };

  const isAmexCard = () => getCardType(paymentCard.cardNumber) === "amex";

  const calculateProRatedPrice = useCallback(
    (pkg: IPackage) => {
      if (!currentPackage || !pkg) return pkg?.price || 0;

      // For standard packages, return the upgrade price if applicable
      if (pkg.type === "standard") {
        return pkg.upgradePrice || pkg.price;
      }

      // For addon packages, calculate pro-rated price based on remaining days
      const remainingDays = currentPackage.remainingDays || 30; // Default to package duration if no remaining days
      const packageDuration = pkg.duration || 30; // Default to 30 if duration is not set

      // Calculate pro-rated price based on remaining days
      const proRatedPrice = Math.ceil(
        (pkg.price * remainingDays) / packageDuration,
      );
      return Math.max(0, proRatedPrice); // Ensure price is not negative
    },
    [currentPackage],
  );


  useEffect(() => {
    // Fetch packages data on component mount
    fetchPackagesData();
  }, [isAuthenticated]);

  // Fetch stored cards when user is authenticated
  useEffect(() => {
    if (isAuthenticated) {
      fetchStoredCards();
    }
  }, [isAuthenticated]);

  const fetchStoredCards = async () => {
    try {
      const cards = await getStoredCards();
      setStoredCards(cards);
      // If user has cards, default to using saved card
      if (cards.length > 0) {
        setUseNewCard(false);
        // Select the default card or first card
        const defaultCard = cards.find(card => card.isDefault) || cards[0];
        if (defaultCard) {
          setSelectedCardId(defaultCard._id);
        }
      }
    } catch (error) {
      console.error('Error fetching stored cards:', error);
    }
  };

  const handlePackageSelect = async (pkg: IPackage) => {
    try {
      // Check if user is authenticated, redirect to login if not
      if (!isAuthenticated) {
        // Navigate to login but save the current URL to redirect back after login
        navigate("/login", { state: { returnUrl: "/packages" } });
        return;
      }

      // Don't proceed if package is marked as disabled
      if (pkg.isDisabled) {
        alert(t("upgradeDetails.package.cannotDowngrade"));
        return;
      }

      // Get latest subscription status
      const subscriptionData = await getActiveSubscription().catch(() => null);
      console.log('Active subscription data:', subscriptionData);
      // Handle both possible response formats
      const hasActiveSubscription = !!subscriptionData?.subscription || !!subscriptionData;

      // For first-time users, only allow standard packages
      if (!hasActiveSubscription && pkg.type === "addon") {
        alert(t("package.action.requiresStandard"));
        return;
      }

      if (hasActiveSubscription) {
        // Account for different API response structures
        const activePackageId = subscriptionData.subscription?.packageId || subscriptionData?.packageId;
        console.log('Active package ID:', activePackageId);
        const activePackageIdStr =
          typeof activePackageId === "object" ? activePackageId._id : activePackageId;

        // Check if user is trying to select their current package
        if (activePackageIdStr === pkg._id) {
          alert(t("upgradeDetails.package.alreadyPurchased"));
          return;
        }

        // If user has active subscription and trying to buy standard package,
        // ensure it's an upgrade (higher price)
        if (pkg.type === "standard") {
          const currentPkg = packages.find((p) => p._id === activePackageIdStr);
          if (currentPkg?.type === "standard" && pkg.price <= currentPkg.price) {
            alert(t("upgradeDetails.package.cannotDowngrade"));
            return;
          }
        }

        // If buying an addon with homepage ads, verify restrictions
        if (pkg.type === "addon" && (pkg.homepageAd || pkg.features.includes("homepage_ad"))) {
          const currentPkg = packages.find((p) => p._id === activePackageIdStr);

          // Check if current package already has homepage ads
          if (currentPkg && (currentPkg.homepageAd || currentPkg.features.includes("homepage_ad"))) {
            alert(t("upgradeDetails.package.featureAlreadyIncluded"));
            return;
          }

          // Check if user already has a homepage ad addon
          const hasHomepageAdAddon = subscriptionData.subscription?.addons?.some(
            (addon: any) => {
              const addonId = typeof addon === 'object' ? addon._id : addon;
              const addonObj = packages.find(p => p._id === addonId);
              return addonObj && (addonObj.homepageAd || addonObj.features.includes("homepage_ad"));
            }
          );

          if (hasHomepageAdAddon) {
            alert(t("upgradeDetails.package.addonAlreadyPurchased"));
            return;
          }
        }
      }

      setSelectedPackage(pkg);
      setShowPaymentModal(true);
    } catch (error) {
      console.error("Error checking subscription status:", error);
    }
  };

  // Function to fetch packages data
  const fetchPackagesData = async () => {
    setIsLoading(true);
    try {
      // For non-authenticated users, we just want to show packages without subscription info
      const response = await getPackages();
      const { packages: packagesData, currentSubscription } = response;

      // If user is not authenticated, we won't have subscription info

      // Sort packages by type and price
      const sortedPackages = packagesData.sort((a: any, b: any) => {
        if (a.type === "standard" && b.type === "addon") return -1;
        if (a.type === "addon" && b.type === "standard") return 1;
        return a.order - b.order;
      });

      // Filter standard packages and mark lower ones as disabled if user has a higher one
      const standardPkgs = sortedPackages
        .filter((pkg: IPackage) => pkg.type === "standard")
        .map((pkg: IPackage) => {
          if (currentSubscription && currentSubscription.packageId &&
            currentSubscription.type === "standard") {
            // Find current package
            const currentPkgId = typeof currentSubscription.packageId === 'object'
              ? currentSubscription.packageId._id
              : currentSubscription.packageId;

            const currentPkgObj = sortedPackages.find((p: any) => p._id === currentPkgId);

            // Mark as current package
            if (pkg._id === currentPkgId) {
              return { ...pkg, isCurrentPackage: true };
            }

            // Mark packages with lower or equal price as disabled (can't downgrade)
            if (currentPkgObj && pkg.price <= currentPkgObj.price) {
              return { ...pkg, isDisabled: true };
            }
          } else {
            // For users with no current subscription, all standard packages should be available
            // No need to check for "activePackage" as there's no current subscription
          }
          return pkg;
        });

      // Process addon packages and mark them as disabled if needed
      const addonPkgs = sortedPackages
        .filter((pkg: IPackage) => pkg.type === "addon")
        .map((pkg: IPackage) => {
          // Check if user has ANY subscription first
          if (!currentSubscription) {
            // First-time users can't buy addons
            return { ...pkg, isDisabled: true };
          }

          // Get current package details
          const currentPkgId = typeof currentSubscription.packageId === 'object'
            ? currentSubscription.packageId._id
            : currentSubscription.packageId;
          const currentPkgObj = sortedPackages.find((p: any) => p._id === currentPkgId);

          // Mark as current addon if applicable
          if (pkg._id === currentPkgId) {
            return { ...pkg, isCurrentPackage: true };
          }

          // Handle homepage ad addon specifically
          if (pkg.homepageAd || pkg.features.includes("homepage_ad")) {
            // Check if current package already has homepage ads
            if (currentPkgObj && (currentPkgObj.homepageAd || currentPkgObj.features.includes("homepage_ad"))) {
              return { ...pkg, isDisabled: true };
            } else {
              return { ...pkg, isDisabled: false };
            }
          }

          return pkg;
        });

      if (currentSubscription) {
        const currentPkg = sortedPackages.find(
          (p: any) => p._id === currentSubscription.packageId._id,
        );
        setCurrentPackage(currentPkg);
        setRemainingDays(currentSubscription.remainingDays);
      } else {
        setCurrentPackage(null);
        setRemainingDays(0);
      }

      setPackages(sortedPackages);
      setStandardPackages(standardPkgs);
      setAddOnPackages(addonPkgs);
    } catch (error: any) {
      console.error("Failed to fetch packages:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handlePaymentComplete = (
    status: "success" | "failed",
    messageKey?: string,
  ) => {
    if (status === "success") {
      setShowPaymentModal(false);
      alert(t("payment.messages.success.description"));
      // Refresh packages and subscription data without full page reload
      fetchPackagesData();
    } else {
      setShowPaymentModal(false);
      alert(messageKey
        ? t(`payment.messages.${messageKey}.description`)
        : t("payment.messages.failed.description"));
    }
  };

  // Use effect to set the iframe srcdoc
  useEffect(() => {
    if (threeDSContent && iframeRef.current) {
      // Just set srcdoc, no direct DOM access
      iframeRef.current.srcdoc = threeDSContent;
    }
  }, [threeDSContent]);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      const data = event.data as PaymentResultEvent;
      if (data.type === "PAYMENT_RESULT") {
        handlePaymentComplete(data.status, data.messageKey);
      } else if (data.type === "CLOSE_PAYMENT_MODAL") {
        // Handle close message from iframe
        setShowPaymentModal(false);
      }
    };

    const handlePaymentCallback = (event: CustomEvent) => {
      handlePaymentComplete(event.detail.status, event.detail.messageKey);
    };

    window.addEventListener("message", handleMessage);
    window.addEventListener("payment-completed", handlePaymentCallback);

    return () => {
      window.removeEventListener("message", handleMessage);
      window.removeEventListener("payment-completed", handlePaymentCallback);
    };
  }, [selectedPackage, navigate, t]);

  // Effect to prevent body scrolling when modal is open and clear 3DS content when modal closes
  useEffect(() => {
    if (showPaymentModal) {
      // Save the current body overflow style
      const originalStyle = window.getComputedStyle(document.body).overflow;
      // Prevent scrolling on the body
      document.body.style.overflow = 'hidden';

      // Restore original style when modal closes and clear 3DS content
      return () => {
        document.body.style.overflow = originalStyle;
        // Clear the 3DS content when modal is closed
        setThreeDSContent(null);
      };
    }
  }, [showPaymentModal]);

  const saveCardFirst = async () => {
    try {
      const result: any = await addStoredCard({
        cardHolderName: paymentCard.cardHolderName,
        cardNumber: paymentCard.cardNumber.replace(/\s/g, ""),
        expireMonth: paymentCard.expireMonth,
        expireYear: paymentCard.expireYear,
        cvc: paymentCard.cvc,
        cardAlias:
          paymentCard.cardAlias ||
          `Card ending in ${paymentCard.cardNumber.slice(-4)}`,
      });

      if (result) {
        // Refresh stored cards
        const cards = await getStoredCards();
        setStoredCards(cards);
        setSelectedCardId(result.cardId);
        setUseNewCard(false);
        return result.cardId;
      } else {
        throw new Error(result.error || "Failed to save card");
      }
    } catch (error: any) {
      alert(error.message || t("payment.form.errors.cardSaveError.description"));
      throw error;
    }
  };

  // State to track validation errors
  const [validationError, setValidationError] = useState<string | null>(null);

  const validateCardForm = (): boolean => {
    // Clear previous errors
    setValidationError(null);

    // If using saved card, just validate selection
    if (!useNewCard) {
      if (!selectedCardId) {
        setValidationError(t("payment.form.errors.selectCard"));
        return false;
      }
      return true;
    }

    // Validate new card information
    if (!paymentCard.cardHolderName.trim()) {
      setValidationError(t("payment.form.errors.cardHolderNameRequired"));
      return false;
    }

    const cleanCardNumber = paymentCard.cardNumber.replace(/\s/g, "");
    if (cleanCardNumber.length < 15 || cleanCardNumber.length > 16) {
      setValidationError(t("payment.form.errors.invalidCardNumber"));
      return false;
    }

    if (!paymentCard.expireMonth || !paymentCard.expireYear) {
      setValidationError(t("payment.form.errors.expiryDateRequired"));
      return false;
    }

    // Validate expiry date is not in the past
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;
    const expYear = parseInt(paymentCard.expireYear);
    const expMonth = parseInt(paymentCard.expireMonth);

    if (expYear < currentYear || (expYear === currentYear && expMonth < currentMonth)) {
      setValidationError(t("payment.form.errors.cardExpired"));
      return false;
    }

    if (!paymentCard.cvc || (paymentCard.cvc.length < 3)) {
      setValidationError(t("payment.form.errors.invalidCVC"));
      return false;
    }

    if (paymentCard.saveCard && !paymentCard.cardAlias.trim()) {
      setValidationError(t("payment.form.errors.cardAliasRequired"));
      return false;
    }

    return true;
  };

  const handlePaymentSubmit = async () => {
    if (!selectedPackage) return;

    // Validate form data before proceeding
    if (!validateCardForm()) {
      return;
    }

    setIsProcessing(true);

    try {
      const paymentData = {
        packageId: selectedPackage._id,
        isUpgrade: !!currentPackage && selectedPackage.type === "standard",
        callbackUrl: `${window.location.origin}/payment-callback`,
      };

      let response: any;

      if (!useNewCard && selectedCardId) {
        // Use existing saved card
        response = await initiatePackagePurchase(selectedPackage._id, {
          storedCardId: selectedCardId,
          ...paymentData,
        });
      } else {
        // Use new card
        let cardId = "";

        // If user wants to save the card
        if (paymentCard.saveCard) {
          try {
            cardId = await saveCardFirst();
          } catch (error) {
            setIsProcessing(false);
            return;
          }
        }

        if (paymentCard.saveCard && cardId) {
          // Use the card that was just saved
          response = await initiatePackagePurchase(selectedPackage._id, {
            storedCardId: cardId,
            ...paymentData,
          });
        } else {
          // Use new card without saving
          response = await initiatePackagePurchase(selectedPackage._id, {
            cardInfo: {
              cardHolderName: paymentCard.cardHolderName,
              cardNumber: paymentCard.cardNumber.replace(/\s/g, ""),
              expireMonth: paymentCard.expireMonth,
              expireYear: paymentCard.expireYear,
              cvc: paymentCard.cvc,
            },
            saveCard: paymentCard.saveCard,
            ...paymentData,
          });
        }
      }

      if (response?.html3DS) {
        decodeAndRenderPaymentForm(response.html3DS);
      } else if (response?.token) {
        // Handle non-3D secure payment
        handlePaymentComplete("success");
      }
    } catch (error: any) {
      alert(error.message || t("payment.messages.error.description"));
    } finally {
      setIsProcessing(false);
    }
  };

  const decodeAndRenderPaymentForm = (html3DS: string) => {
    try {
      const decodedHtml = atob(html3DS);
      setThreeDSContent(decodedHtml);
    } catch (error) {
      console.error("Error handling 3DS form:", error);
      alert(t("payment.messages.3dSecureError.description"));
      setIsProcessing(false);
    }
  };

  const getPackageIcon = (pkg: IPackage) => {
    if (pkg.price <= 15) return Zap;
    if (pkg.price >= 100) return Star;
    return Building2;
  };

  const getPackageFeatures = (pkg: IPackage) => {
    const features = [];

    if (pkg.viewRequestLimit > 0) {
      features.push(`${pkg.viewRequestLimit} Görüntüleme Hakkı`);
    }

    if (pkg.createRequestLimit > 0) {
      features.push(`${pkg.createRequestLimit} Talep/Satış Oluşturma`);
    }

    if (pkg.features.includes("email_notifications") || pkg.emailNotification) {
      features.push("E-posta Bildirimleri");
    }

    if (pkg.features.includes("sms_notifications") || pkg.smsNotification) {
      features.push("SMS Bildirimleri");
    }

    if (pkg.messagingAllowed) {
      features.push("Mesajlaşma");
    }

    if (pkg.features.includes("sector_report") || pkg.yearEndSectorReport) {
      features.push("Yıl Sonu Sektör Raporu");
    }

    if (pkg.homepageAd) {
      features.push("Anasayfa Reklam Alanı");
    }

    if (pkg.features.includes("language_intro") || pkg.languageIntroRights > 0) {
      features.push("Çoklu Dil Desteği");
    }

    return features;
  };

  const isPopular = (pkg: IPackage) => {
    // Determine the highest priced standard package
    return standardPackages.every(p => p.price <= pkg.price) && pkg.type === "standard";
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <span className="inline-block px-4 py-2 rounded-lg bg-primary/10 text-primary text-sm font-medium mb-4">
            {t("packages.headerTag")}
          </span>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {t("packages.title")}
          </h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("packages.description")}
          </p>
        </div>
      </div>

      {/* Main Packages Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {standardPackages.map((pkg) => {
            const PackageIcon = getPackageIcon(pkg);
            const features = getPackageFeatures(pkg);
            const popular = isPopular(pkg);
            const isCurrentPkg = pkg.isCurrentPackage;

            return (
              <div
                key={pkg._id}
                className={`relative rounded-2xl ${popular
                  ? 'border-2 border-primary scale-105 shadow-xl'
                  : pkg.isDisabled
                    ? 'border border-gray-300 bg-gray-50'
                    : 'border border-gray-200 shadow-sm'
                  } bg-white p-8 transform transition-all duration-200 ${pkg.isDisabled
                    ? 'cursor-not-allowed opacity-70'
                    : isCurrentPkg
                      ? 'cursor-not-allowed opacity-85'
                      : 'cursor-pointer hover:shadow-lg'
                  }`}
                onClick={() => !isCurrentPkg && !pkg.isDisabled && handlePackageSelect(pkg)}
              >
                {popular && !pkg.isDisabled && !isCurrentPkg && (
                  <div className="absolute -top-5 left-1/2 transform -translate-x-1/2">
                    <span className="inline-block px-4 py-2 rounded-full bg-primary text-white text-sm font-medium">
                      {t("packages.mostPopular")}
                    </span>
                  </div>
                )}

                {isCurrentPkg && (
                  <div className="absolute -top-5 left-1/2 transform -translate-x-1/2">
                    <span className="inline-block px-4 py-2 rounded-full bg-gray-500 text-white text-sm font-medium">
                      {t("packages.current")}
                    </span>
                  </div>
                )}

                {pkg.isDisabled && !isCurrentPkg && (
                  <div className="absolute -top-5 left-1/2 transform -translate-x-1/2">
                    <span className="inline-block px-4 py-2 rounded-full bg-gray-400 text-white text-sm font-medium">
                      {t("upgradeDetails.package.notAvailable")}
                    </span>
                  </div>
                )}

                <div className="flex items-center space-x-3 mb-4">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${popular ? 'bg-primary text-white' : 'bg-primary/10 text-primary'
                    }`}>
                    <PackageIcon className="w-6 h-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">
                    {i18n.language === "en" ? pkg.nameEn : pkg.name}
                  </h3>
                </div>

                <p className="text-gray-600 mb-6">
                  {i18n.language === "en" ? pkg.descriptionEn : pkg.description}
                </p>

                <div className="mb-8">
                  <div className="flex items-baseline">
                    <span className="text-4xl font-bold text-gray-900">${pkg.price}</span>
                    <span className="ml-2 text-gray-500">{t("package.perMonth")}</span>
                  </div>
                </div>

                <ul className="space-y-4 mb-8">
                  {features.map((feature, idx) => (
                    <li key={idx} className="flex items-center space-x-3">
                      <Check className="h-5 w-5 text-primary flex-shrink-0" />
                      <span className="text-gray-600">{feature}</span>
                    </li>
                  ))}
                </ul>

                <button
                  className={`w-full py-4 rounded-lg flex items-center justify-center space-x-2 text-base font-medium transition-all duration-200
                    ${isCurrentPkg
                      ? 'bg-gray-300 text-gray-700 cursor-not-allowed'
                      : pkg.isDisabled
                        ? 'bg-gray-200 text-gray-500 cursor-not-allowed border border-gray-300'
                        : popular
                          ? 'bg-primary text-white hover:bg-primary/90'
                          : 'bg-primary/10 text-primary hover:bg-primary hover:text-white'
                    }`}
                  onClick={(e) => {
                    e.stopPropagation();
                    if (!isCurrentPkg && !pkg.isDisabled) {
                      handlePackageSelect(pkg);
                    }
                  }}
                  disabled={isCurrentPkg || pkg.isDisabled}
                >
                  {isCurrentPkg ? (
                    <span>{t("packages.current")}</span>
                  ) : pkg.isDisabled ? (
                    <div className="flex items-center space-x-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                      </svg>
                      <span>{t("upgradeDetails.package.notAvailable")}</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <ShoppingCart className="w-5 h-5" />
                      <span>{t("packages.buyNow")}</span>
                    </div>
                  )}
                </button>
              </div>
            );
          })}
        </div>
      </div>

      {/* Additional Packages Section - Only shown for authenticated users */}
      {addOnPackages.length > 0 && isAuthenticated && (
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <span className="inline-block px-4 py-2 rounded-lg bg-primary/10 text-primary text-sm font-medium mb-4">
                {t("packages.addonHeader")}
              </span>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                {t("packages.addonTitle")}
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                {t("packages.addonDescription")}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {addOnPackages.map((pkg) => {
                const features = getPackageFeatures(pkg);
                const isCurrentAddon = pkg.isCurrentPackage;

                // Determine the icon based on package features
                let AddonIcon = Plus; // Default icon
                if (pkg.features.includes("homepage_ad")) {
                  AddonIcon = TrendingUp;
                } else if (pkg.features.includes("create_request")) {
                  AddonIcon = FileText;
                }

                return (
                  <div
                    key={pkg._id}
                    className={`bg-white rounded-xl p-6 ${pkg.isDisabled
                      ? 'border border-gray-300 bg-gray-50'
                      : isCurrentAddon
                        ? 'border border-primary/20 shadow-sm'
                        : !currentPackage
                          ? 'border border-gray-300 bg-gray-50'
                          : 'shadow-sm hover:shadow-md border border-gray-200'}
                      transition-all duration-200 ${(isCurrentAddon || !currentPackage || pkg.isDisabled)
                        ? 'cursor-not-allowed opacity-70'
                        : 'cursor-pointer'}`}
                    onClick={() => !isCurrentAddon && currentPackage && !pkg.isDisabled && handlePackageSelect(pkg)}
                  >
                    {isCurrentAddon && (
                      <div className="absolute transform translate-y-[-20px] translate-x-[-10px]">
                        <span className="inline-block px-4 py-1 rounded-full bg-gray-500 text-white text-xs font-medium">
                          {t("packages.current")}
                        </span>
                      </div>
                    )}

                    {pkg.isDisabled && !isCurrentAddon && (
                      <div className="absolute transform translate-y-[-20px] translate-x-[-10px]">
                        <span className="inline-block px-4 py-1 rounded-full bg-gray-400 text-white text-xs font-medium">
                          {t("upgradeDetails.package.notAvailable")}
                        </span>
                      </div>
                    )}

                    {!currentPackage && !isCurrentAddon && !pkg.isDisabled && (
                      <div className="absolute transform translate-y-[-20px] translate-x-[-10px]">
                        <span className="inline-block px-4 py-1 rounded-full bg-amber-500 text-white text-xs font-medium">
                          {t("package.action.requiresStandard")}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-12 h-12 rounded-lg bg-primary/10 text-primary flex items-center justify-center">
                        <AddonIcon className="w-6 h-6" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900">
                        {i18n.language === "en" ? pkg.nameEn : pkg.name}
                      </h3>
                    </div>

                    <p className="text-gray-600 mb-6">
                      {i18n.language === "en" ? pkg.descriptionEn : pkg.description}
                    </p>

                    <div className="mb-6">
                      <div className="flex items-baseline">
                        <span className="text-3xl font-bold text-gray-900">${pkg.price}</span>
                        <span className="ml-2 text-gray-500">{t("package.perMonth")}</span>
                      </div>
                    </div>

                    <ul className="space-y-3 mb-6">
                      {features.map((feature, idx) => (
                        <li key={idx} className="flex items-center space-x-3">
                          <Check className="h-5 w-5 text-primary flex-shrink-0" />
                          <span className="text-gray-600">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <button
                      className={`w-full py-3 rounded-lg font-medium flex items-center justify-center space-x-2 group
                        ${isCurrentAddon
                          ? 'bg-gray-300 text-gray-700 cursor-not-allowed'
                          : pkg.isDisabled
                            ? 'bg-gray-200 text-gray-500 cursor-not-allowed border border-gray-300'
                            : !currentPackage
                              ? 'bg-amber-100 text-amber-700 cursor-not-allowed border border-amber-200'
                              : 'bg-primary/10 text-primary hover:bg-primary hover:text-white transition-colors'}`}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (!isCurrentAddon && currentPackage && !pkg.isDisabled) {
                          handlePackageSelect(pkg);
                        }
                      }}
                      disabled={isCurrentAddon || !currentPackage || pkg.isDisabled}
                    >
                      {isCurrentAddon ? (
                        <span>{t("packages.current")}</span>
                      ) : pkg.isDisabled ? (
                        <div className="flex items-center space-x-2">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                          </svg>
                          <span>{t("upgradeDetails.package.notAvailable")}</span>
                        </div>
                      ) : !currentPackage ? (
                        <div className="flex items-center space-x-2">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                          </svg>
                          <span>{t("package.action.requiresStandard")}</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-2">
                          <span>{t("payment.details.title")}</span>
                          <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                        </div>
                      )}
                    </button>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Login encouragement for non-authenticated users */}
      {!isAuthenticated && (
        <div className="bg-gray-50 py-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center p-8 bg-white rounded-xl shadow-sm border border-gray-100">
              <span className="inline-block px-4 py-2 rounded-lg bg-primary/10 text-primary text-sm font-medium mb-4">
                {t("common:login.prompt")}
              </span>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {t("packages.loginToSeeMore")}
              </h2>
              <p className="text-gray-600 max-w-2xl mx-auto mb-6">
                {t("packages.additionalPackagesDescription")}
              </p>
              <button
                onClick={() => navigate("/login", { state: { returnUrl: "/packages" } })}
                className="inline-flex items-center px-6 py-3 bg-primary text-white font-medium rounded-lg hover:bg-primary-hover transition-colors shadow-md"
              >
                <LogIn className="w-5 h-5 mr-2" />
                {t("buttons.login")}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Bottom Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            {t("packages.footer.title")}
          </h2>
          <p className="text-gray-600 max-w-3xl mx-auto">
            {t("packages.footer.description")}
          </p>
          <div className="mt-8 flex flex-wrap gap-4 justify-center">
            <div className="flex items-center space-x-2 bg-primary/5 px-4 py-2 rounded-lg">
              <Check className="h-5 w-5 text-primary" />
              <span className="text-gray-700">{t("packages.footer.features.global")}</span>
            </div>
            <div className="flex items-center space-x-2 bg-primary/5 px-4 py-2 rounded-lg">
              <Check className="h-5 w-5 text-primary" />
              <span className="text-gray-700">{t("packages.footer.features.experts")}</span>
            </div>
            <div className="flex items-center space-x-2 bg-primary/5 px-4 py-2 rounded-lg">
              <Check className="h-5 w-5 text-primary" />
              <span className="text-gray-700">{t("packages.footer.features.support")}</span>
            </div>
            <div className="flex items-center space-x-2 bg-primary/5 px-4 py-2 rounded-lg">
              <Check className="h-5 w-5 text-primary" />
              <span className="text-gray-700">{t("packages.footer.features.technology")}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && selectedPackage && (
        <div
          className="fixed inset-0 z-50 overflow-hidden bg-black bg-opacity-50"
          onClick={() => setShowPaymentModal(false)}
          style={{ overflow: 'hidden' }}
        >
          <div className="flex flex-col items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div
              className="inline-block align-bottom bg-white rounded-xl text-left overflow-y-auto shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-xl sm:w-full max-h-[90vh]"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="absolute top-0 right-0 pt-4 pr-4">
                <button
                  type="button"
                  className="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary"
                  onClick={() => setShowPaymentModal(false)}
                >
                  <span className="sr-only">{t("payment.modal.close")}</span>
                  <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                    <div className="flex items-center space-x-2 mb-4">
                      <div className="w-10 h-10 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                        <ShoppingCart className="w-5 h-5" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {t("payment.modal.title")}
                      </h3>
                    </div>
                    <div className="mb-6 p-4 rounded-lg bg-primary/5 border border-primary/10">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-primary">
                          {i18n.language === "en" ? selectedPackage.nameEn : selectedPackage.name}
                        </span>
                        <span className="text-lg font-bold text-gray-900">
                          ${calculateProRatedPrice(selectedPackage)}
                        </span>
                      </div>
                    </div>

                    {threeDSContent ? (
                      <div className="mt-4 border border-gray-300 rounded-md overflow-hidden">
                        <iframe
                          ref={iframeRef}
                          title="3D Secure Payment"
                          className="w-full h-[500px] border-none"
                        />
                      </div>
                    ) : (
                      <div className="mt-4 space-y-4">
                        {/* Card Selection */}
                        {storedCards.length > 0 && (
                          <div className="space-y-3">
                            <label className="block text-sm font-medium text-gray-700">
                              {t("payment.form.paymentMethod")}
                            </label>
                            <div className="space-y-2">
                              {/* Saved Cards Option */}
                              <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input
                                  type="radio"
                                  checked={!useNewCard}
                                  onChange={() => setUseNewCard(false)}
                                  className="h-4 w-4 text-primary focus:ring-primary"
                                />
                                <span className="ml-3 text-sm font-medium text-gray-700">
                                  {t("payment.options.useSavedCard")}
                                </span>
                              </label>

                              {!useNewCard && (
                                <div className="ml-7 mt-2">
                                  <select
                                    value={selectedCardId}
                                    onChange={(e) => setSelectedCardId(e.target.value)}
                                    className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
                                  >
                                    {storedCards.map((card) => (
                                      <option key={card._id} value={card._id}>
                                        {card.cardAlias} - {card.cardType} **** {card.lastFourDigits}
                                        {card.isDefault && ` (${t("payment.form.defaultCard")})`}
                                      </option>
                                    ))}
                                  </select>
                                </div>
                              )}

                              {/* New Card Option */}
                              <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                                <input
                                  type="radio"
                                  checked={useNewCard}
                                  onChange={() => setUseNewCard(true)}
                                  className="h-4 w-4 text-primary focus:ring-primary"
                                />
                                <span className="ml-3 text-sm font-medium text-gray-700">
                                  {t("payment.options.useNewCard")}
                                </span>
                              </label>
                            </div>
                          </div>
                        )}

                        <div>
                          {/* New card form */}
                          {useNewCard && (
                            <div className="space-y-4">
                              <div className="rounded-lg border border-gray-200 p-5 bg-white shadow-sm">
                                <div className="mb-4">
                                  <label htmlFor="cardHolderName" className="block text-sm font-medium text-gray-700">
                                    {t("payment.form.cardHolderName")}
                                  </label>
                                  <div className="mt-1 relative rounded-md shadow-sm">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                    <input
                                      type="text"
                                      id="cardHolderName"
                                      value={paymentCard.cardHolderName}
                                      onChange={(e) =>
                                        setPaymentCard({
                                          ...paymentCard,
                                          cardHolderName: e.target.value,
                                        })
                                      }
                                      placeholder={t("payment.form.cardHolderNamePlaceholder")}
                                      className="block w-full pl-10 border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                    />
                                  </div>
                                </div>

                                <div className="mb-4">
                                  <label htmlFor="cardNumber" className="block text-sm font-medium text-gray-700">
                                    {t("payment.form.cardNumber")}
                                  </label>
                                  <div className="mt-1 relative rounded-md shadow-sm">
                                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z" />
                                        <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd" />
                                      </svg>
                                    </div>
                                    <input
                                      type="text"
                                      id="cardNumber"
                                      value={paymentCard.cardNumber}
                                      onChange={(e) => {
                                        const value = e.target.value.replace(/\D/g, "");
                                        const formatted =
                                          value.match(/.{1,4}/g)?.join(" ") || value;
                                        setPaymentCard({
                                          ...paymentCard,
                                          cardNumber: formatted,
                                        });
                                      }}
                                      placeholder={t("payment.form.cardNumberPlaceholder")}
                                      maxLength={19}
                                      className="block w-full pl-10 border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                    />
                                    <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                                      {getCardType(paymentCard.cardNumber) === "visa" && (
                                        <svg className="h-5 w-8" viewBox="0 0 48 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M17.4 15.2H14L16.1 0.8H19.5L17.4 15.2Z" fill="#00579F" />
                                          <path d="M31.3 1C30.5 0.7 29.3 0.4 27.8 0.4C24.4 0.4 22 2.2 22 4.8C22 6.8 23.8 7.8 25.2 8.5C26.5 9.1 27 9.6 27 10.2C27 11.1 26 11.6 25 11.6C23.5 11.6 22.8 11.3 21.5 10.7L21 10.5L20.4 13.4C21.3 13.8 22.9 14.2 24.6 14.2C28.3 14.2 30.7 12.5 30.7 9.7C30.7 8.1 29.8 6.9 27.7 5.9C26.5 5.2 25.8 4.8 25.8 4.2C25.8 3.6 26.5 3 27.8 3C29 3 29.8 3.2 30.5 3.5L30.9 3.7L31.3 1Z" fill="#00579F" />
                                          <path d="M35.9 10.3C36.3 9.3 37.4 6.4 37.4 6.4L37.8 5.3C37.9 5.6 38.4 6.9 38.6 7.5L39.2 10.3H35.9ZM42.1 0.8H39.5C38.6 0.8 38 1.1 37.6 2L32 15.2H35.7C35.7 15.2 36.3 13.5 36.5 13.1H41.1C41.2 13.7 41.6 15.2 41.6 15.2H45L42.1 0.8Z" fill="#00579F" />
                                          <path d="M12.8 0.8L9.4 10.7L9 9C8.3 7 6.6 4.9 4.7 3.8L7.8 15.2H11.6L16.7 0.8H12.8Z" fill="#00579F" />
                                          <path d="M5.9 0.8H0.1L0 1.1C4.5 2.2 7.5 5.2 8.7 8.8L7.4 2C7.2 1.1 6.6 0.8 5.9 0.8Z" fill="#FAA61A" />
                                        </svg>
                                      )}
                                      {getCardType(paymentCard.cardNumber) === "mastercard" && (
                                        <svg className="h-5 w-8" viewBox="0 0 48 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M17.5 3.8H30.5V24.2H17.5V3.8Z" fill="#FF5F00" />
                                          <path d="M18.4 14C18.4 9.6 20.5 5.6 23.9 3.8C21.5 1.9 18.6 0.8 15.3 0.8C7.8 0.8 1.8 6.7 1.8 14C1.8 21.3 7.8 27.2 15.3 27.2C18.6 27.2 21.5 26.1 23.9 24.2C20.5 22.4 18.4 18.4 18.4 14Z" fill="#EB001B" />
                                          <path d="M46.2 14C46.2 21.3 40.2 27.2 32.7 27.2C29.4 27.2 26.5 26.1 24.1 24.2C27.6 22.4 29.6 18.4 29.6 14C29.6 9.6 27.5 5.6 24.1 3.8C26.5 1.9 29.4 0.8 32.7 0.8C40.2 0.8 46.2 6.7 46.2 14Z" fill="#F79E1B" />
                                        </svg>
                                      )}
                                      {getCardType(paymentCard.cardNumber) === "amex" && (
                                        <svg className="h-5 w-8" viewBox="0 0 48 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                                          <path d="M45.3 0H2.7C1.2 0 0 1.2 0 2.7V29.3C0 30.8 1.2 32 2.7 32H45.3C46.8 32 48 30.8 48 29.3V2.7C48 1.2 46.8 0 45.3 0Z" fill="#006FCF" />
                                          <path d="M24 16L26.5 11.3H31.3L22.6 26.6H17.8L14.1 13.4L10.4 26.6H5.7L9.7 5.4H14.5L18 18.2L23.5 5.4H28.2L35.8 19.9L38.5 5.4H43.1L38.1 26.6H33.5L24 16Z" fill="white" />
                                        </svg>
                                      )}
                                    </div>
                                  </div>
                                </div>

                                <div className="grid grid-cols-3 gap-4">
                                  <div>
                                    <label htmlFor="expireMonth" className="block text-sm font-medium text-gray-700">
                                      {t("payment.form.expiryMonth")}
                                    </label>
                                    <div className="relative mt-1 rounded-md shadow-sm">
                                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                        </svg>
                                      </div>
                                      <select
                                        id="expireMonth"
                                        value={paymentCard.expireMonth}
                                        onChange={(e) =>
                                          setPaymentCard({
                                            ...paymentCard,
                                            expireMonth: e.target.value,
                                          })
                                        }
                                        className="pl-10 block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                      >
                                        <option value="">{t("payment.form.select")}</option>
                                        {Array.from({ length: 12 }, (_, i) => {
                                          const month = (i + 1).toString().padStart(2, "0");
                                          return (
                                            <option key={month} value={month}>
                                              {month}
                                            </option>
                                          );
                                        })}
                                      </select>
                                    </div>
                                  </div>

                                  <div>
                                    <label htmlFor="expireYear" className="block text-sm font-medium text-gray-700">
                                      {t("payment.form.expiryYear")}
                                    </label>
                                    <div className="relative mt-1 rounded-md shadow-sm">
                                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                                        </svg>
                                      </div>
                                      <select
                                        id="expireYear"
                                        value={paymentCard.expireYear}
                                        onChange={(e) =>
                                          setPaymentCard({
                                            ...paymentCard,
                                            expireYear: e.target.value,
                                          })
                                        }
                                        className="pl-10 block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                      >
                                        <option value="">{t("payment.form.select")}</option>
                                        {Array.from({ length: 10 }, (_, i) => {
                                          const year = (
                                            new Date().getFullYear() + i
                                          ).toString();
                                          return (
                                            <option key={year} value={year.slice(-2)}>
                                              {year}
                                            </option>
                                          );
                                        })}
                                      </select>
                                    </div>
                                  </div>

                                  <div>
                                    <label htmlFor="cvc" className="block text-sm font-medium text-gray-700">
                                      {t("payment.form.cvv")}
                                    </label>
                                    <div className="relative mt-1 rounded-md shadow-sm">
                                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                        <svg className="h-4 w-4 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                          <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                                        </svg>
                                      </div>
                                      <input
                                        type="text"
                                        id="cvc"
                                        value={paymentCard.cvc}
                                        onChange={(e) => {
                                          const value = e.target.value.replace(/\D/g, "");
                                          setPaymentCard({ ...paymentCard, cvc: value });
                                        }}
                                        placeholder={t("payment.form.cvvPlaceholder")}
                                        maxLength={isAmexCard() ? 4 : 3}
                                        className="pl-10 block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                      />
                                    </div>
                                  </div>
                                </div>

                                {/* Save card checkbox */}
                                <div className="mt-4">
                                  <label className="flex items-center">
                                    <input
                                      type="checkbox"
                                      checked={paymentCard.saveCard}
                                      onChange={(e) => setPaymentCard({ ...paymentCard, saveCard: e.target.checked })}
                                      className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                    />
                                    <span className="ml-2 text-sm text-gray-700">
                                      {t("payment.form.saveCard")}
                                    </span>
                                  </label>
                                </div>

                                {/* Card alias input (shown when save card is checked) */}
                                {paymentCard.saveCard && (
                                  <div className="mt-3">
                                    <label htmlFor="cardAlias" className="block text-sm font-medium text-gray-700">
                                      {t("payment.form.cardAlias")}
                                    </label>
                                    <input
                                      type="text"
                                      id="cardAlias"
                                      value={paymentCard.cardAlias}
                                      onChange={(e) => setPaymentCard({ ...paymentCard, cardAlias: e.target.value })}
                                      placeholder={t("payment.form.cardAliasPlaceholder")}
                                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                                    />
                                  </div>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Package Details Section */}
                          {currentPackage && selectedPackage && (
                            <div className="mt-4 p-4 bg-gray-50 rounded-md">
                              <h4 className="font-medium text-gray-700 mb-2">
                                {t("payment.details.title")}
                              </h4>
                              <div className="space-y-1 text-sm text-gray-600">
                                <p>{t("subscription.currentPackage")}: {i18n.language === "en" ? currentPackage.nameEn : currentPackage.name}</p>
                                <p>{t("subscription.remainingDays")}: {remainingDays} {t("subscription.days")}</p>

                                {selectedPackage.type === "standard" ? (
                                  <>
                                    <p>{t("subscription.currentValue")}: ${currentPackage.price.toFixed(2)}</p>
                                    <p>{t("subscription.newPackagePrice")}: ${selectedPackage.price}</p>
                                    <p className="font-semibold text-primary">
                                      {t("subscription.upgradePrice")}: ${selectedPackage.price - currentPackage.price}
                                    </p>
                                  </>
                                ) : (
                                  <>
                                    <p>{t("subscription.originalPrice")}: ${selectedPackage.price}</p>
                                    <p className="font-semibold text-primary">
                                      {t("subscription.proRatedPrice")}: ${calculateProRatedPrice(selectedPackage)}
                                    </p>
                                  </>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Validation Error Message */}
                          {validationError && (
                            <div className="mt-4 px-4 py-3 rounded-md bg-red-50 border border-red-200">
                              <div className="flex">
                                <div className="flex-shrink-0">
                                  <svg className="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                  </svg>
                                </div>
                                <div className="ml-3">
                                  <p className="text-sm text-red-700">{validationError}</p>
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Pay Button Inside Card Section */}
                          <div className="mt-6">
                            <button
                              type="button"
                              className="w-full py-3 px-6 bg-primary rounded-lg text-white font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors shadow-md flex justify-center items-center"
                              onClick={handlePaymentSubmit}
                              disabled={isProcessing}
                            >
                              {isProcessing ? (
                                <>
                                  <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                  </svg>
                                  {t("payment.processing")}
                                </>
                              ) : (
                                <>
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                                  </svg>
                                  {t("payment.form.payButton")}
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="border-t border-gray-200"></div>
            <div className="px-6 py-4 bg-gray-50 rounded-b-xl">
              <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                <button
                  type="button"
                  className="py-3 px-6 bg-white rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors shadow-sm"
                  onClick={() => setShowPaymentModal(false)}
                  ref={cancelRef}
                >
                  {t("payment.modal.cancel")}
                </button>
                {!threeDSContent && (
                  <button
                    type="button"
                    className="py-3 px-6 bg-primary rounded-lg text-white font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors shadow-md flex-1 flex justify-center items-center"
                    onClick={handlePaymentSubmit}
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {t("payment.processing")}
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        {t("payment.payButton")}
                      </>
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div >
  );
};

export default PackageSelection;