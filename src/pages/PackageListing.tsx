import React, { useState, useEffect } from "react";
import {
  Box,
  Heading,
  SimpleGrid,
  VStack,
  Text,
  Button,
  Spinner,
  useToast,
  Badge,
  Checkbox,
  HStack,
  ButtonGroup,
  Divider,
  List,
  ListItem,
  ListIcon,
  Tooltip,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { initiatePackagePurchase } from "../api/packageApi";
import { getPackages, getActiveSubscription, getUserProfile } from "../api";

// Augment window interface to allow for our global state
declare global {
  interface Window {
    hasActivePackage?: boolean;
  }
}
import { IPackage } from "@/types/package";
import { CheckIcon, InfoIcon } from "@chakra-ui/icons";

const PackageListing: React.FC = () => {
  const [packages, setPackages] = useState<IPackage[]>([]);
  const [filteredPackages, setFilteredPackages] = useState<IPackage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPackages, setSelectedPackages] = useState<string[]>([]);
  const [activeFilter, setActiveFilter] = useState<
    "all" | "standard" | "addon"
  >("all");
  const [activeSubscription, setActiveSubscription] = useState<any>(null);
  const [currentPackageId, setCurrentPackageId] = useState<string | null>(null);
  const toast = useToast();
  const { t } = useTranslation("packages");

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Check user profile first to see if they have an active package
        try {
          const userProfile = await getUserProfile().catch(() => null);
          if (userProfile && userProfile.hasPackage === true) {
            console.log('User has active package according to user profile');
            // Attach to window for global access in component
            window.hasActivePackage = true;
          } else {
            window.hasActivePackage = false;
          }
        } catch (profileError) {
          console.error('Error checking user profile for package status:', profileError);
          window.hasActivePackage = false;
        }

        // Fetch packages data
        const packagesData = await getPackages();

        // Log all packages received from the server for debugging
        console.log('All packages received:', packagesData);

        // Determine if this is an object with packages property or direct array
        const packages = Array.isArray(packagesData) ? packagesData : packagesData.packages || [];

        // Sort packages by order to ensure consistent display
        const sortedPackages = [...packages].sort((a, b) => a.order - b.order);
        console.log('Sorted packages:', sortedPackages);

        // Find the current package based on isCurrentPackage flag
        const currentPkg = sortedPackages.find(pkg => pkg.isCurrentPackage === true);
        if (currentPkg) {
          console.log('Found current package from backend flag:', currentPkg.name, currentPkg._id);
          setCurrentPackageId(currentPkg._id);
        }

        setPackages(sortedPackages);
        setFilteredPackages(sortedPackages);

        // Set subscription data if available
        if (!Array.isArray(packagesData) && packagesData.currentSubscription) {
          console.log('Current subscription from packagesData:', packagesData.currentSubscription);
          setActiveSubscription(packagesData.currentSubscription);
        } else {
          // If currentSubscription is not in packagesData, try fetching separately
          try {
            const subscriptionData = await getActiveSubscription();
            if (subscriptionData && subscriptionData.subscription) {
              console.log('Active subscription fetched separately:', subscriptionData.subscription);
              setActiveSubscription(subscriptionData.subscription);
            }
          } catch (error) {
            console.error('Failed to fetch active subscription:', error);
            setActiveSubscription(null);
          }
        }
      } catch (error: any) {
        console.error("Failed to fetch data:", error);
        toast({
          title: t("payment.messages.error.title"),
          description: t("payment.messages.error.description"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [t, toast]);

  useEffect(() => {
    const filtered = packages.filter((pkg) => {
      if (activeFilter === "all") return true;
      return pkg.type === activeFilter;
    });
    setFilteredPackages(filtered);
  }, [activeFilter, packages]);

  const handleCompare = (packageId: string) => {
    setSelectedPackages((prev) =>
      prev.includes(packageId)
        ? prev.filter((id) => id !== packageId)
        : prev.length < 3
          ? [...prev, packageId]
          : prev,
    );
  };

  const handlePurchase = async (packageId: string) => {
    try {
      // Direct to payment page - the new PackageSelection page will handle card selection
      const response = await initiatePackagePurchase(packageId, {});

      // If response contains a payment page URL, redirect to it
      if (response.paymentPageUrl) {
        window.location.href = response.paymentPageUrl;
      } else {
        // Handle success
        toast({
          title: t("payment.messages.success.title"),
          description: t("payment.messages.success.description"),
          status: "success",
          duration: 5000,
          isClosable: true,
        });
      }
    } catch (error: any) {
      console.error("Error purchasing package:", error);
      toast({
        title: t("payment.messages.error.title"),
        description: error.response?.data?.message || t("payment.messages.error.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        height="100vh"
      >
        <Spinner size="xl" />
        <Text ml={4}>{t("package.loading")}</Text>
      </Box>
    );
  }

  return (
    <Box p={{ base: 3, md: 8 }}>
      <VStack spacing={{ base: 3, md: 8 }}>
        <Heading as="h1" size={{ base: "md", md: "xl" }} textAlign="center" px={{ base: 2, md: 0 }}>
          {t("titles.packageSelection")}
        </Heading>

        <ButtonGroup
          spacing={{ base: 1, md: 4 }}
          flexWrap="wrap"
          justifyContent="center"
          w="full"
        >
          <Button
            colorScheme={activeFilter === "all" ? "teal" : "gray"}
            onClick={() => setActiveFilter("all")}
            size={{ base: "sm", md: "md" }}
          >
            {t("filters.allItems")}
          </Button>
          <Button
            colorScheme={activeFilter === "standard" ? "teal" : "gray"}
            onClick={() => setActiveFilter("standard")}
            size={{ base: "sm", md: "md" }}
          >
            {t("filters.standard")}
          </Button>
          <Button
            colorScheme={activeFilter === "addon" ? "teal" : "gray"}
            onClick={() => setActiveFilter("addon")}
            size={{ base: "sm", md: "md" }}
          >
            {t("filters.addon")}
          </Button>
        </ButtonGroup>

        <SimpleGrid
          columns={{ base: 1, lg: 2, xl: 3 }}
          spacing={{ base: 3, md: 6 }}
          w="full"
        >
          {filteredPackages.map((pkg) => (
            <Box
              key={pkg._id}
              borderWidth={pkg.isCurrentPackage ? "2px" : "1px"}
              borderRadius="lg"
              p={{ base: 3, md: 6 }}
              position="relative"
              borderColor={pkg.isCurrentPackage ? "green.500" : "gray.200"}
              bg="white"
              shadow="sm"
            >
              {/* Show "Active Package" badge at the top if this is the current package */}
              {pkg.isCurrentPackage && (
                <Badge
                  position="absolute"
                  top="-12px"
                  left="50%"
                  transform="translateX(-50%)"
                  colorScheme="green"
                  fontSize="md"
                  px={4}
                  py={1}
                >
                  {t("package.activePackage") || "Active Package"}
                </Badge>
              )}
              
              <VStack align="stretch" spacing={{ base: 2, md: 4 }}>
                <VStack align="stretch" spacing={2}>
                  <HStack justify="space-between" align="start">
                    <Heading size={{ base: "sm", md: "lg" }} flex="1" noOfLines={2}>{pkg.name}</Heading>
                    <Badge
                      colorScheme={pkg.type === "standard" ? "blue" : "purple"}
                      fontSize={{ base: "2xs", md: "xs" }}
                    >
                      {t(`package.type.${pkg.type}`)}
                    </Badge>
                  </HStack>
                  
                  <Text fontSize={{ base: "lg", md: "xl" }} fontWeight="bold" color="teal.600">
                    ${pkg.price}
                    <Text as="span" fontSize={{ base: "sm", md: "md" }} fontWeight="normal" color="gray.600">
                      {t("package.perMonth")}
                    </Text>
                  </Text>
                </VStack>

                <Divider />

                <List spacing={{ base: 1, md: 3 }}>
                  {pkg.features.map((feature, index) => (
                    <ListItem key={index} fontSize={{ base: "sm", md: "md" }}>
                      <ListIcon as={CheckIcon} color="green.500" />

                      {feature}
                    </ListItem>
                  ))}
                </List>

                <VStack spacing={{ base: 2, md: 3 }} mt={{ base: 3, md: 4 }}>
                  {/* Button for purchasing package */}
                  {(() => {
                    // Log debug info for this package
                    const packageInfo = {
                      name: pkg.name,
                      id: pkg._id,
                      type: pkg.type,
                      price: pkg.price,
                      order: pkg.order,
                      isActive: pkg.isActive,
                      isCurrentPackage: pkg.isCurrentPackage,
                      activeSubscriptionExists: activeSubscription !== null
                    };
                    console.log('Package button logic for:', packageInfo);

                    // CASE 1: If this is the exact same package the user already has
                    if (pkg.isCurrentPackage) {
                      console.log(`Marking ${pkg.name} as current package`);
                      return (
                        <Button
                          colorScheme="green"
                          isDisabled={true}
                          size={{ base: "sm", md: "md" }}
                          w="full"
                        >
                          {t("package.status.current") || "Current Package"}
                        </Button>
                      );
                    }

                    // CASE 2: User has an active standard package
                    const hasActiveStandardPackage = packages.some(p => p.isCurrentPackage && p.type === 'standard');
                    const currentPackage = packages.find(p => p.isCurrentPackage);
                    
                    if (hasActiveStandardPackage && currentPackage) {
                      // For standard packages
                      if (pkg.type === 'standard') {
                        // Use backend-provided isUpgradeable flag
                        if (pkg.isUpgradeable === false && pkg.price !== currentPackage.price) {
                          console.log(`Disabling ${pkg.name} - not upgradeable according to backend`);
                          return (
                            <Tooltip label={t('upgradeDetails.package.notAvailable') || t('package.cannotDowngrade') || 'Cannot downgrade to a lower tier'} hasArrow>
                              <Button
                                colorScheme="gray"
                                isDisabled={true}
                                size={{ base: "sm", md: "md" }}
                                w="full"
                              >
                                {t("upgradeDetails.package.notAvailable") || t("package.lowerTier") || "Not Available"}
                              </Button>
                            </Tooltip>
                          );
                        }
                        
                        // Higher priced packages - show upgrade button
                        if (pkg.isUpgradeable === true || pkg.price > currentPackage.price) {
                          console.log(`Showing upgrade button for ${pkg.name} - upgradeable or higher price than current`);
                          return (
                            <Button
                              colorScheme="blue"
                              onClick={() => handlePurchase(pkg._id)}
                              size={{ base: "sm", md: "md" }}
                              w="full"
                            >
                              {t("package.action.upgrade") || "Upgrade"}
                            </Button>
                          );
                        }
                      }
                      
                      // For addon packages when user has a standard package
                      if (pkg.type === 'addon' && pkg.isActive) {
                        console.log(`Enabling add-on purchase for ${pkg.name}`);
                        return (
                          <Button
                            colorScheme="purple"
                            onClick={() => handlePurchase(pkg._id)}
                            size={{ base: "sm", md: "md" }}
                            w="full"
                          >
                            {t("package.action.addToPackage") || "Add to Package"}
                          </Button>
                        );
                      }
                    }

                    // CASE 3: No active subscription - show purchase for all active packages
                    if (!hasActiveStandardPackage && !activeSubscription) {
                      if (pkg.isActive) {
                        console.log(`Enabling purchase for ${pkg.name} - user has no active subscription`);
                        return (
                          <Button
                            colorScheme={pkg.type === 'standard' ? "teal" : "purple"}
                            onClick={() => handlePurchase(pkg._id)}
                            size={{ base: "sm", md: "md" }}
                            w="full"
                          >
                            {t("package.purchase") || "Purchase"}
                          </Button>
                        );
                      }
                    }

                    // CASE 4: Active subscription exists but we couldn't detect which package
                    // This is a safety fallback - should rarely happen
                    const hasSubscription = activeSubscription !== null || window.hasActivePackage === true;
                    if (!hasActiveStandardPackage && hasSubscription) {
                      console.log(`User has subscription but package not detected`);
                      // For standard packages, disable them
                      if (pkg.type === 'standard') {
                        return (
                          <Tooltip label={t('package.alreadyHavePackage') || 'You already have an active package'} hasArrow>
                            <Button
                              colorScheme="gray"
                              isDisabled={true}
                              size={{ base: "sm", md: "md" }}
                              w="full"
                            >
                              {t("package.status.unavailable") || "Unavailable"}
                            </Button>
                          </Tooltip>
                        );
                      }
                      // Allow addon packages
                      if (pkg.type === 'addon' && pkg.isActive) {
                        return (
                          <Button
                            colorScheme="purple"
                            onClick={() => handlePurchase(pkg._id)}
                            size={{ base: "sm", md: "md" }}
                            w="full"
                          >
                            {t("package.action.addToPackage") || "Add to Package"}
                          </Button>
                        );
                      }
                    }

                    // Fallback: Inactive packages
                    if (!pkg.isActive) {
                      return (
                        <Tooltip label={t('upgradeDetails.package.notAvailable') || 'This package is not available'} hasArrow>
                          <Button
                            colorScheme="gray"
                            isDisabled={true}
                            size={{ base: "sm", md: "md" }}
                            w="full"
                          >
                            {t("upgradeDetails.package.notAvailable") || "Not Available"}
                          </Button>
                        </Tooltip>
                      );
                    }

                    // Final fallback - should not reach here
                    console.warn(`Unhandled case for package ${pkg.name}`);
                    return (
                      <Button
                        colorScheme="gray"
                        isDisabled={true}
                        size={{ base: "sm", md: "md" }}
                        flex="1"
                      >
                        {t("package.status.unavailable") || "Unavailable"}
                      </Button>
                    );
                  })()}
                  <Checkbox
                    isChecked={selectedPackages.includes(pkg._id)}
                    onChange={() => handleCompare(pkg._id)}
                    size={{ base: "sm", md: "md" }}
                    w="full"
                    justifyContent="center"
                  >
                    {t("package.compare")}
                  </Checkbox>
                </VStack>
              </VStack>
            </Box>
          ))}
        </SimpleGrid>
      </VStack>
    </Box>
  );
};

export default PackageListing;
