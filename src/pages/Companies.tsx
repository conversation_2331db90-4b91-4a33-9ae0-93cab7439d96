import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { 
  Search,
  BadgeCheck,
  MapPin,
  ExternalLink,
  Filter,
  ChevronDown,
  Building2,
  Users,
  Calendar,
  X,
  Globe2,
  Star,
  TrendingUp,
  Briefcase,
  Clock,
  Eye,
  Building,
  CheckCircle,
  AlertCircle,
  Sliders,
  ArrowUpDown,
  SlidersHorizontal
} from 'lucide-react';

const Companies = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState({
    sector: 'all',
    location: 'all',
    employeeCount: 'all',
    foundedYear: 'all',
    verificationStatus: 'all',
    sortBy: 'newest'
  });

  const companies = [
    {
      id: 'techglobal-solutions',
      name: "TechGlobal Solutions",
      sector: "Teknoloji",
      location: "İstanbul, Türkiye",
      image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&q=80",
      logo: "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?auto=format&fit=crop&q=80",
      description: "Endüstriyel yazılım çözümleri ve teknoloji danışmanlığı alanında öncü firma",
      verified: true,
      employeeCount: "50-100",
      foundedYear: "2015",
      views: 1250
    },
    {
      id: 'ecotrade-international',
      name: "EcoTrade International",
      sector: "Sürdürülebilir Ticaret",
      location: "İzmir, Türkiye",
      image: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?auto=format&fit=crop&q=80",
      logo: "https://images.unsplash.com/photo-1542744094-24638eff58bb?auto=format&fit=crop&q=80",
      description: "Sürdürülebilir ve organik ürünlerin global ticaretinde uzman firma",
      verified: true,
      employeeCount: "100-250",
      foundedYear: "2010",
      views: 980
    },
    {
      id: 'medicare-exports',
      name: "MediCare Exports",
      sector: "Sağlık",
      location: "Ankara, Türkiye",
      image: "https://images.unsplash.com/photo-1538108149393-fbbd81895907?auto=format&fit=crop&q=80",
      logo: "https://images.unsplash.com/photo-1505751172876-fa1923c5c528?auto=format&fit=crop&q=80",
      description: "Medikal ekipman ve sağlık ürünleri ihracatında uzmanlaşmış firma",
      verified: false,
      employeeCount: "25-50",
      foundedYear: "2018",
      views: 750
    }
  ];

  const sectors = [
    { id: 'all', name: 'Tüm Sektörler' },
    { id: 'technology', name: 'Teknoloji' },
    { id: 'trade', name: 'Ticaret' },
    { id: 'health', name: 'Sağlık' },
    { id: 'construction', name: 'İnşaat' },
    { id: 'agriculture', name: 'Tarım' }
  ];

  const locations = [
    { id: 'all', name: 'Tüm Lokasyonlar' },
    { id: 'istanbul', name: 'İstanbul' },
    { id: 'izmir', name: 'İzmir' },
    { id: 'ankara', name: 'Ankara' },
    { id: 'bursa', name: 'Bursa' },
    { id: 'antalya', name: 'Antalya' }
  ];

  const employeeRanges = [
    { id: 'all', name: 'Tüm Çalışan Sayıları' },
    { id: '1-10', name: '1-10 Çalışan' },
    { id: '11-50', name: '11-50 Çalışan' },
    { id: '51-200', name: '51-200 Çalışan' },
    { id: '201-500', name: '201-500 Çalışan' },
    { id: '501+', name: '501+ Çalışan' }
  ];

  const foundedYears = [
    { id: 'all', name: 'Tüm Yıllar' },
    { id: '2020+', name: '2020 ve Sonrası' },
    { id: '2015-2019', name: '2015-2019' },
    { id: '2010-2014', name: '2010-2014' },
    { id: '2000-2009', name: '2000-2009' },
    { id: '-2000', name: '2000 Öncesi' }
  ];

  const sortOptions = [
    { id: 'newest', name: 'En Yeni' },
    { id: 'oldest', name: 'En Eski' },
    { id: 'mostViewed', name: 'En Çok Görüntülenen' },
    { id: 'leastViewed', name: 'En Az Görüntülenen' }
  ];

  const filteredCompanies = companies.filter(company => {
    const matchesSearch = 
      company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.sector.toLowerCase().includes(searchQuery.toLowerCase()) ||
      company.location.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesSector = filters.sector === 'all' || company.sector.toLowerCase() === filters.sector;
    const matchesLocation = filters.location === 'all' || company.location.includes(filters.location);
    const matchesEmployeeCount = filters.employeeCount === 'all' || company.employeeCount === filters.employeeCount;
    const matchesFoundedYear = filters.foundedYear === 'all' || company.foundedYear === filters.foundedYear;
    const matchesVerification = filters.verificationStatus === 'all' || 
      (filters.verificationStatus === 'verified' && company.verified) ||
      (filters.verificationStatus === 'unverified' && !company.verified);

    return matchesSearch && matchesSector && matchesLocation && matchesEmployeeCount && 
           matchesFoundedYear && matchesVerification;
  }).sort((a, b) => {
    switch (filters.sortBy) {
      case 'oldest':
        return parseInt(a.foundedYear) - parseInt(b.foundedYear);
      case 'mostViewed':
        return b.views - a.views;
      case 'leastViewed':
        return a.views - b.views;
      default: // newest
        return parseInt(b.foundedYear) - parseInt(a.foundedYear);
    }
  });

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Header */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <h1 className="text-2xl font-bold text-gray-900">Firmalar</h1>
            <p className="mt-2 text-gray-600">Global ticarette güvenilir iş ortaklarınız</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="sticky top-0 z-10 bg-white border-b shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Firma ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              {/* Sort Dropdown */}
              <select
                value={filters.sortBy}
                onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                className="px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
              >
                {sortOptions.map(option => (
                  <option key={option.id} value={option.id}>
                    {option.name}
                  </option>
                ))}
              </select>

              {/* Filters Toggle */}
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors ${
                  showFilters ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Sliders className="h-5 w-5" />
                <span>Filtreler</span>
              </button>
            </div>

            {/* Extended Filters */}
            {showFilters && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200 animate-fadeIn">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Sector Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Sektör
                    </label>
                    <select
                      value={filters.sector}
                      onChange={(e) => setFilters(prev => ({ ...prev, sector: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                    >
                      {sectors.map(sector => (
                        <option key={sector.id} value={sector.id}>
                          {sector.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Location Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Lokasyon
                    </label>
                    <select
                      value={filters.location}
                      onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                    >
                      {locations.map(location => (
                        <option key={location.id} value={location.id}>
                          {location.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Employee Count Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Çalışan Sayısı
                    </label>
                    <select
                      value={filters.employeeCount}
                      onChange={(e) => setFilters(prev => ({ ...prev, employeeCount: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                    >
                      {employeeRanges.map(range => (
                        <option key={range.id} value={range.id}>
                          {range.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Founded Year Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Kuruluş Yılı
                    </label>
                    <select
                      value={filters.foundedYear}
                      onChange={(e) => setFilters(prev => ({ ...prev, foundedYear: e.target.value }))}
                      className="w-full px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white"
                    >
                      {foundedYears.map(year => (
                        <option key={year.id} value={year.id}>
                          {year.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Reset Filters */}
                <div className="mt-4 flex justify-end">
                  <button
                    onClick={() => {
                      setFilters({
                        sector: 'all',
                        location: 'all',
                        employeeCount: 'all',
                        foundedYear: 'all',
                        verificationStatus: 'all',
                        sortBy: 'newest'
                      });
                      setSearchQuery('');
                    }}
                    className="px-4 py-2 text-primary hover:text-[#0A9996] transition-colors flex items-center space-x-2"
                  >
                    <X className="h-5 w-5" />
                    <span>Filtreleri Temizle</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Companies Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredCompanies.map((company) => (
              <div 
                key={company.id}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300"
              >
                {/* Cover Image */}
                <div className="aspect-[16/9] relative rounded-t-xl overflow-hidden">
                  <img 
                    src={company.image}
                    alt={company.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                  
                  {/* Company Logo */}
                  <div className="absolute bottom-4 left-4 flex items-center space-x-2">
                    <div className="w-12 h-12 rounded-lg overflow-hidden border-2 border-white">
                      <img 
                        src={company.logo}
                        alt={`${company.name} logo`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    {company.verified && (
                      <BadgeCheck className="h-6 w-6 text-primary bg-white rounded-full p-1" />
                    )}
                  </div>

                  {/* Views */}
                  <div className="absolute top-4 right-4">
                    <div className="flex items-center space-x-1 bg-white/90 rounded-full px-2 py-1">
                      <Eye className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-600">{company.views}</span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  {/* Company Info */}
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">{company.name}</h3>
                    
                    {/* Category */}
                    <div className="flex items-center space-x-2 mb-2">
                      <Building2 className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{company.sector}</span>
                    </div>

                    {/* Location */}
                    <div className="flex items-center space-x-2 mb-2">
                      <MapPin className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{company.location}</span>
                    </div>

                    {/* Employee Count */}
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">{company.employeeCount} çalışan</span>
                    </div>
                  </div>

                  {/* View Profile Button */}
                  <button 
                    onClick={() => navigate(`/companies/${company.id}`)}
                    className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
                  >
                    <span>Firma Profilini Görüntüle</span>
                    <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Companies;