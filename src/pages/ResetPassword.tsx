import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Box,
  Button,
  Input,
  FormControl,
  FormLabel,
  Heading,
  VStack,
  useToast,
  Text,
  Flex,
  InputGroup,
  InputRightElement,
  IconButton,
  Container,
  useColorModeValue,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  Image,
  FormErrorMessage,
} from "@chakra-ui/react";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { resetPassword } from "../api";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";

const MotionContainer = motion(Container);

const ResetPassword: React.FC = () => {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [passwordError, setPasswordError] = useState("");
  const [confirmPasswordError, setConfirmPasswordError] = useState("");

  const navigate = useNavigate();
  const location = useLocation();
  const toast = useToast();
  const { t, i18n } = useTranslation(["resetPassword", "common"]);
  const cardBgColor = useColorModeValue("white", "gray.800");

  const token = new URLSearchParams(location.search).get("token");

  useEffect(() => {
    document.title = t("resetPassword:title") + " | " + t("common:appName");
    if (!token) {
      toast({
        title: t("resetPassword:toasts.invalidToken.title"),
        description: t("resetPassword:toasts.invalidToken.description"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      setTimeout(() => navigate("/login"), 3000);
    }
  }, [t, token, navigate, toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (password !== confirmPassword) {
      setPasswordError(t("resetPassword:form.passwordMismatch"));
      setConfirmPasswordError(t("resetPassword:form.passwordMismatch"));
      setIsSubmitting(false);
      return;
    }

    try {
      await resetPassword(token!, password);
      toast({
        title: t("resetPassword:toasts.resetSuccess.title"),
        description: t("resetPassword:toasts.resetSuccess.description"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
      setTimeout(() => navigate("/login"), 3000);
    } catch (error: any) {
      toast({
        title: t("resetPassword:toasts.resetFailed.title"),
        description:
          error instanceof Error
            ? error.message
            : t("resetPassword:toasts.unknownError.description"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const togglePasswordVisibility = () => setShowPassword(!showPassword);
  const toggleConfirmPasswordVisibility = () =>
    setShowConfirmPassword(!showConfirmPassword);

  const handleLanguageChange = (language: string) => {
    i18n.changeLanguage(language);
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPassword(e.target.value);
    setPasswordError("");
  };

  const handleConfirmPasswordChange = (
    e: React.ChangeEvent<HTMLInputElement>,
  ) => {
    setConfirmPassword(e.target.value);
    setConfirmPasswordError("");
  };

  return (
    <Box
      position="relative"
      minH="100vh"
      width="100%"
      overflow="auto"
      bg="gray.50"
      sx={{
        backgroundImage: `
      radial-gradient(circle, #CBD5E0 1px, transparent 1px)
    `,
        backgroundSize: "24px 24px",
        backgroundPosition: "0 0",
      }}
    >
      <Flex
        position="fixed"
        top={0}
        left={0}
        right={0}
        zIndex={10}
        height={{ base: "60px", md: "70px" }}
        borderBottom="1px"
        borderColor="gray.100"
        px={{ base: 3, md: 6 }}
        py={{ base: 2, md: 3 }}
        align="center"
        justify="space-between"
      >
        <Image
          src="/logo.png"
          alt="Logo"
          width={{ base: "100px", sm: "120px", md: "180px" }}
          height="auto"
          transition="transform 0.3s ease"
          _hover={{ transform: "scale(1.05)" }}
        />

        <Box zIndex={2}>
          <Menu>
            <MenuButton
              as={Button}
              size={{ base: "xs", sm: "sm" }}
              variant="solid"
              colorScheme="teal"
            >
              {i18n.language.toUpperCase().split("-")[0]}
            </MenuButton>
            <MenuList>
              <MenuItem onClick={() => handleLanguageChange("en")}>EN</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("tr")}>TR</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("ar")}>AR</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("ru")}>RU</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("fr")}>FR</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("es")}>ES</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("de")}>DE</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("it")}>IT</MenuItem>
              <MenuItem onClick={() => handleLanguageChange("zh")}>ZH</MenuItem>
            </MenuList>
          </Menu>
        </Box>
      </Flex>

      <Flex
        minH="100vh"
        w="full"
        align="center"
        justify="center"
        position="relative"
        pt={{ base: "70px", md: "90px" }}
        pb={{ base: "20px", md: "30px" }}
        px={{ base: "3", sm: "4", md: "6" }}
      >
        <MotionContainer
          maxW="lg"
          py={{ base: "6", sm: "8", md: "12" }}
          px={{ base: "3", sm: "6", md: "8" }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          width="100%"
        >
          <Box
            py={{ base: "6", sm: "8", md: "12" }}
            px={{ base: "4", sm: "6", md: "10" }}
            shadow="2xl"
            rounded={{ base: "md", md: "xl" }}
            bg={cardBgColor}
            borderWidth="1px"
            borderColor="teal.100"
            backdropFilter="blur(10px)"
          >
            <VStack
              spacing={{ base: 4, md: 8 }}
              align="center"
              mb={{ base: 4, md: 6 }}
            >
              <Heading
                as="h1"
                size={{ base: "lg", md: "xl" }}
                color="teal.500"
                textAlign="center"
              >
                {t("resetPassword:title")}
              </Heading>
            </VStack>

            <form onSubmit={handleSubmit}>
              <VStack spacing={{ base: 4, md: 6 }}>
                <FormControl isRequired isInvalid={!!passwordError}>
                  <FormLabel fontSize={{ base: "sm", md: "md" }}>
                    {t("resetPassword:form.password.label")}
                  </FormLabel>
                  <InputGroup size={{ base: "md", md: "lg" }}>
                    <Input
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={handlePasswordChange}
                      placeholder={t("resetPassword:form.password.placeholder")}
                      focusBorderColor="teal.400"
                      bg={useColorModeValue("white", "gray.700")}
                      _hover={{ borderColor: "teal.300" }}
                    />

                    <InputRightElement width="4.5rem">
                      <IconButton
                        h="1.75rem"
                        size="sm"
                        aria-label={
                          showPassword
                            ? t("resetPassword:form.password.hide")
                            : t("resetPassword:form.password.show")
                        }
                        icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                        onClick={togglePasswordVisibility}
                        variant="ghost"
                      />
                    </InputRightElement>
                  </InputGroup>
                  {passwordError && (
                    <FormErrorMessage>{passwordError}</FormErrorMessage>
                  )}
                </FormControl>
                <FormControl isRequired isInvalid={!!confirmPasswordError}>
                  <FormLabel fontSize={{ base: "sm", md: "md" }}>
                    {t("resetPassword:form.confirmPassword.label")}
                  </FormLabel>
                  <InputGroup size={{ base: "md", md: "lg" }}>
                    <Input
                      type={showConfirmPassword ? "text" : "password"}
                      value={confirmPassword}
                      onChange={handleConfirmPasswordChange}
                      placeholder={t(
                        "resetPassword:form.confirmPassword.placeholder",
                      )}
                      focusBorderColor="teal.400"
                      bg={useColorModeValue("white", "gray.700")}
                      _hover={{ borderColor: "teal.300" }}
                    />

                    <InputRightElement width="4.5rem">
                      <IconButton
                        h="1.75rem"
                        size="sm"
                        aria-label={
                          showConfirmPassword
                            ? t("resetPassword:form.password.hide")
                            : t("resetPassword:form.password.show")
                        }
                        icon={showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
                        onClick={toggleConfirmPasswordVisibility}
                        variant="ghost"
                      />
                    </InputRightElement>
                  </InputGroup>
                  {confirmPasswordError && (
                    <FormErrorMessage>{confirmPasswordError}</FormErrorMessage>
                  )}
                </FormControl>
                <Button
                  type="submit"
                  colorScheme="teal"
                  size={{ base: "md", md: "lg" }}
                  width="full"
                  isLoading={isSubmitting}
                  loadingText={t("resetPassword:form.submitLoading")}
                  _hover={{ transform: "translateY(-2px)", boxShadow: "lg" }}
                  transition="all 0.2s"
                >
                  {t("resetPassword:form.submit")}
                </Button>
              </VStack>
            </form>
          </Box>
        </MotionContainer>
      </Flex>
    </Box>
  );
};

export default ResetPassword;
