// socketHandler.ts
import { Server, Socket } from 'socket.io';
import { createClient } from 'redis';
import { Message } from './models/Message';
import { verifyToken } from './utils/auth';
import { User } from './models/User';
import { setupMessageHandlers } from './socket/messageHandler';
import { setupLiveChatHandlers } from './socket/liveChatHandler';
import mongoose from 'mongoose';

// Check if Redis is enabled
const redisEnabled = process.env.REDIS_ENABLED === 'true';

// Create Redis client only if enabled
const redis = redisEnabled 
  ? createClient({
      url: process.env.REDIS_URL || `redis://${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || '6379'}`
    })
  : null;

// Connect to Redis only if enabled
if (redisEnabled && redis) {
  redis.on('error', (err: Error) => console.error('Redis Client Error', err));
  redis.connect().catch(console.error);
} else {
  console.log('Redis is disabled. Skipping connection setup.');
}

const connectedUsers = new Map<string, Set<string>>();

export const handleSocketConnection = (io: Server) => {
  // Middleware for authentication (optional for live chat)
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      console.log('Socket auth middleware', { 
        socketId: socket.id, 
        hasToken: !!token
      });

      // Allow connections without token for anonymous live chat
      if (!token) {
        // Mark as anonymous user
        socket.data.anonymous = true;
        return next();
      }

      // Verify token
      const decoded = verifyToken(token);
      if (!decoded) {
        console.log('Invalid token');
        return next(new Error('Invalid token'));
      }

      console.log('Token decoded successfully', { 
        id: decoded.id, 
        role: decoded.role 
      });

      const user = await User.findById(decoded.id);
      if (!user) {
        console.log('User not found', { id: decoded.id });
        return next(new Error('User not found'));
      }

      // Important: Attach role information from token
      socket.data.user = {
        ...user.toObject(),
        role: decoded.role || user.role,
        isAdmin: decoded.role === 'admin' || user.role === 'admin'
      };

      console.log('Socket authenticated', { 
        userId: socket.data.user.id || socket.data.user._id,
        role: socket.data.user.role,
        isAdmin: socket.data.user.isAdmin
      });
      
      next();
    } catch (error:any) {
      console.error('Socket authentication error', error);
      // Allow anonymous connections
      socket.data.anonymous = true;
      next();
    }
  });

  io.on('connection', async (socket: Socket) => {
    const userId = socket.data.user?.id || socket.data.user?._id;
    const isAdmin = socket.data.user?.isAdmin || socket.data.user?.role === 'admin';

    console.log('Socket connected', { 
      socketId: socket.id, 
      userId, 
      isAdmin,
      userRole: socket.data.user?.role,
      authToken: socket.handshake?.auth?.token ? 'Present' : 'None'
    });

    if (userId) {
      // Add socket to user's connected sockets
      if (!connectedUsers.has(userId)) {
        connectedUsers.set(userId, new Set());
      }
      connectedUsers.get(userId)?.add(socket.id);

      // Join user to their personal room
      socket.join(userId);

      // Join admin room if user is admin
      if (isAdmin) {
        console.log(`Admin user ${userId} joining admin room`);
        socket.join('admin');
      }

      // Get initial notification count if Redis is enabled
      if (redisEnabled && redis) {
        const count = await redis.get(`notifications:${userId}`);
        socket.emit('notifications:count', parseInt(count || '0', 10));
      } else {
        socket.emit('notifications:count', 0);
      }

      // Set up message handlers
      setupMessageHandlers(io, socket);
    }

    // Set up live chat handlers for all users (including anonymous)
    setupLiveChatHandlers(io, socket);

    socket.on('disconnect', () => {
      if (userId) {
        // Remove socket from connected users
        const userSockets = connectedUsers.get(userId);
        if (userSockets) {
          userSockets.delete(socket.id);
          if (userSockets.size === 0) {
            connectedUsers.delete(userId);
          }
        }

        // Notify other users about offline status
        socket.broadcast.emit('user:offline', userId);
      }
    });
  });
};