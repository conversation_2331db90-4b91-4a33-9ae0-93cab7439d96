/* Import Google Fonts first */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Then include Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Poppins', sans-serif;
  background-color: #f7fafc;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  overflow-x: hidden;
  width: 100%;
}

/* Ensure no margins or padding on root elements */
#root {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}

/* Full-width utility class */
.full-screen-width {
  width: 100vw;
  position: relative;
  left: 50%;
  right: 50%;
  margin-left: -50vw;
  margin-right: -50vw;
}

/* Remove all margins and padding from the root container */
html, body, #root, div#root, div#root > div {
  padding: 0 !important;
  margin: 0 !important;
  box-sizing: border-box !important;
  overflow-x: hidden !important;
}

html, body, #root {
  height: 100%;
}

.profile-button {
  position: relative;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 24px;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -10px;
  background-color: red;
  color: white;
  border-radius: 50%;
  padding: 4px 7px;
  font-size: 12px;
}