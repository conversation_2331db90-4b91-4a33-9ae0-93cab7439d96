@tailwind base;
@tailwind components;
@tailwind utilities;

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

html, body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  font-family: 'Poppins', sans-serif;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 104px; /* Height of fixed header */
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.min-h-screen {
  min-height: 100vh;
}

.flex-grow {
  flex-grow: 1;
}

/* Mobile-first responsive text sizes */
@layer base {
  h1 {
    @apply text-2xl md:text-3xl lg:text-4xl font-semibold;
  }
  h2 {
    @apply text-xl md:text-2xl lg:text-3xl font-semibold;
  }
  h3 {
    @apply text-lg md:text-xl font-semibold;
  }
  p {
    @apply text-sm md:text-base;
  }
}

/* Mobile-first padding and margins */
@layer utilities {
  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .section-spacing {
    @apply py-8 md:py-12 lg:py-16;
  }
  
  .card-padding {
    @apply p-4 md:p-6;
  }
}

/* Mobile-first grid layouts */
@layer components {
  .responsive-grid {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6;
  }
  
  .responsive-grid-tight {
    @apply grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 md:gap-4;
  }
}

/* Touch-friendly interactive elements */
@layer components {
  .touch-button {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }
  
  .touch-input {
    @apply min-h-[44px] px-4;
  }
}

/* Responsive images */
img {
  @apply max-w-full h-auto;
}

/* Mobile navigation overlay */
.mobile-nav-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-40;
}

/* Mobile menu transitions */
.mobile-menu-enter {
  opacity: 0;
  transform: translateX(-100%);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: opacity 300ms, transform 300ms;
}

.mobile-menu-exit {
  opacity: 1;
  transform: translateX(0);
}

.mobile-menu-exit-active {
  opacity: 0;
  transform: translateX(-100%);
  transition: opacity 300ms, transform 300ms;
}

/* Safe area insets for modern mobile browsers */
@supports(padding: max(0px)) {
  .safe-bottom {
    padding-bottom: max(env(safe-area-inset-bottom), 1rem);
  }
  
  .safe-top {
    padding-top: max(env(safe-area-inset-top), 1rem);
  }
}

/* Responsive tables */
.table-responsive {
  @apply w-full overflow-x-auto -mx-4 px-4;
}

/* Mobile-first modal positioning */
.modal-container {
  @apply fixed inset-0 px-4 py-6 md:py-12 overflow-y-auto;
}

.modal-content {
  @apply w-full max-w-md mx-auto bg-white rounded-xl shadow-xl transform transition-all;
}

/* Better touch targets for mobile */
.mobile-touch-target {
  @apply p-3;
}