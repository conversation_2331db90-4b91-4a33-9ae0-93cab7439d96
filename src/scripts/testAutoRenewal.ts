import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { User } from '../models/User';
import { Subscription } from '../models/Subscription';
import { Payment } from '../models/Payment';
import { Package } from '../models/Package';
import { StoredCard } from '../models/StoredCard';
// We need to import these functions from the service
import { checkExpiringSubscriptions, processSubscriptionRenewal } from '../services/autoRenewalService';
import connectDB from '../config/database';

// Initialize environment variables
dotenv.config();

// Custom logger for this test script
const testLogger = {
  info: (message: string) => console.log(`[INFO] ${message}`),
  warn: (message: string) => console.log(`[WARN] ${message}`),
  error: (message: string, error?: any) => {
    console.log(`[ERROR] ${message}`);
    if (error) {
      console.log(error);
    }
  }
};

// The main test function
async function runTest() {
  testLogger.info('Starting auto-renewal test script');

  try {
    // Connect to the database
    await connectDB();
    testLogger.info('Connected to database');

    // Find the test user
    const testEmail = '<EMAIL>';
    const user = await User.findOne({ email: testEmail });

    if (!user) {
      testLogger.error(`Test user with email ${testEmail} not found`);
      process.exit(1);
    }

    testLogger.info(`Found test user: ${user.firstName} ${user.lastName} (${user.email})`);

    // Clean up existing subscriptions and payments
    // First, make sure we remove any subscription references in the user record
    await User.findByIdAndUpdate(user._id, {
      $unset: { subscription: 1 },
      hasPackage: false
    });
    testLogger.info(`Reset user's subscription references`);
    
    // Delete all payments associated with this user
    const deletedPayments = await Payment.deleteMany({ userId: user._id });
    testLogger.info(`Deleted ${deletedPayments.deletedCount} existing payments for the user`);
    
    // Delete all subscriptions for this user
    const deletedSubscriptions = await Subscription.deleteMany({ userId: user._id });
    testLogger.info(`Deleted ${deletedSubscriptions.deletedCount} existing subscriptions for the user`);

    // Find a premium package to use for the test - simulate a premium user
    // Instead of using just "isActive: true" which might get any package
    let packageToUse = await Package.findOne({ isActive: true, name: { $regex: /premium/i } });

    if (!packageToUse) {
      // Fall back to any active package if premium not found
      packageToUse = await Package.findOne({ isActive: true });
      
      if (!packageToUse) {
        testLogger.error('No active packages found in the database');
        process.exit(1);
      }
      
      testLogger.info(`Using fallback package: ${packageToUse.name} (${packageToUse._id})`);
    } else {
      testLogger.info(`Using premium package: ${packageToUse.name} (${packageToUse._id})`);
    }

    // Check if user has a stored card
    const storedCard = await StoredCard.findOne({ userId: user._id, isActive: true });

    if (!storedCard) {
      testLogger.warn('Test user has no stored card. Creating a fake one for testing purposes');

      // Create a fake stored card for testing
      const newCard = new StoredCard({
        userId: user._id,
        cardUserKey: 'test-card-user-key',
        cardToken: 'test-card-token',
        cardAlias: 'Test Card',
        lastFourDigits: '1234',
        cardType: 'CREDIT_CARD',
        cardAssociation: 'VISA',
        cardFamily: 'Bonus',
        isDefault: true,
        isActive: true
      });

      await newCard.save();
      testLogger.info(`Created test card: ${newCard._id}`);
    } else {
      testLogger.info(`Found existing card: ${storedCard.cardAlias} (${storedCard._id})`);
    }

    // Create a new subscription that expires today
    const today = new Date();

    const newSubscription = new Subscription({
      userId: user._id,
      packageId: packageToUse._id,
      startDate: new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
      endDate: today,
      renewalDate: today,
      remainingViewRequests: packageToUse.viewRequestLimit,
      remainingCreateRequests: packageToUse.createRequestLimit,
      status: 'ACTIVE',
      paymentStatus: 'paid',
      autoRenewal: true,
      features: {
        emailNotification: packageToUse.emailNotification,
        smsNotification: packageToUse.smsNotification,
        messagingAllowed: packageToUse.messagingAllowed,
        homepageAd: packageToUse.homepageAd,
        languageIntroRights: packageToUse.languageIntroRights
      }
    });

    await newSubscription.save();
    testLogger.info(`Created test subscription: ${newSubscription._id}`);
    testLogger.info(`Subscription expires: ${newSubscription.endDate.toISOString()}`);

    // Update user to reference this subscription
    await User.findByIdAndUpdate(user._id, {
      subscription: newSubscription._id,
      hasPackage: true
    });
    testLogger.info('Updated user with new subscription reference');

    // Wait a moment to ensure all DB operations are complete
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Manually run the check for expiring subscriptions
    testLogger.info('Running check for expiring subscriptions');

    // First, let's fetch the subscription with populated data
    const populatedSubscription = await Subscription.findById(newSubscription._id)
      .populate('userId')
      .populate('packageId');

    if (!populatedSubscription) {
      testLogger.error('Failed to find the populated subscription');
      process.exit(1);
    }

    testLogger.info('Running subscription renewal process');
    await processSubscriptionRenewal(populatedSubscription);

    // Verify the results
    const updatedSubscription = await Subscription.findById(newSubscription._id);

    if (!updatedSubscription) {
      testLogger.error('Failed to find the updated subscription');
      process.exit(1);
    }

    testLogger.info(`Renewal test complete. Subscription status: ${updatedSubscription.status}`);
    testLogger.info(`Payment status: ${updatedSubscription.paymentStatus}`);
    testLogger.info(`New end date: ${updatedSubscription.endDate.toISOString()}`);
    testLogger.info(`Auto-renewal flag: ${updatedSubscription.autoRenewal}`);

    // Check for payment records created during renewal
    const paymentRecords = await Payment.find({
      userId: user._id,
      createdAt: { $gte: new Date(Date.now() - 10 * 60 * 1000) } // Last 10 minutes
    });

    testLogger.info(`Found ${paymentRecords.length} recent payment records`);

    for (const payment of paymentRecords) {
      testLogger.info(`Payment ID: ${payment._id}`);
      testLogger.info(`Payment status: ${payment.status}`);
      testLogger.info(`Payment amount: ${payment.amount}`);
      testLogger.info(`Payment date: ${payment.paymentDate}`);

      if (payment.iyzico) {
        testLogger.info(`Iyzico conversation ID: ${payment.iyzico.conversationId}`);
        testLogger.info(`Iyzico error: ${(payment.iyzico as any).error || 'None'}`);
      }
    }

    testLogger.info('Test script completed successfully');
  } catch (error) {
    testLogger.error('Test script failed', error);
  } finally {
    // Disconnect from the database
    await mongoose.disconnect();
    testLogger.info('Disconnected from database');
  }
}

// This import is already at the top of the file, removing duplicate

// Run the test
runTest().catch(err => {
  console.error('Unhandled error in test script:', err);
  process.exit(1);
});