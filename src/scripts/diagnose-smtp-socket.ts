/**
 * Low-Level SMTP Socket Diagnosis Tool
 *
 * This script tests SMTP connectivity at the socket level to diagnose issues
 * with your mail server connection.
 *
 * Run with: npx ts-node src/scripts/diagnose-smtp-socket.ts
 */

import * as net from 'net';
import * as tls from 'tls';
import { config } from '../config';
import dotenv from 'dotenv';

dotenv.config();

// SMTP Configuration from environment
const host = config.smtp.host;
const port = config.smtp.port;
const secure = config.smtp.secure;
const user = config.smtp.user;
const pass = config.smtp.pass;

// Optional: Try alternative configuration
const useAlternativePort = process.argv.includes('--alt');
const actualPort = useAlternativePort ? 587 : port;
const actualSecure = useAlternativePort ? false : secure;

console.log('SMTP Socket Diagnosis Tool');
console.log('=========================');
console.log(`Testing connection to: ${host}:${actualPort} (${actualSecure ? 'Secure' : 'Plain'})`);
console.log(`Using credentials: ${user}`);

// Create socket object based on secure flag
const createConnection = () => {
  if (actualSecure) {
    console.log('Creating secure TLS socket...');
    return tls.connect(
      {
        host,
        port: actualPort,
        rejectUnauthorized: false, // Accept self-signed certificates
        timeout: 60000, // 60 seconds timeout
      },
      () => handleConnection(socket, true)
    );
  } else {
    console.log('Creating plain TCP socket...');
    return net.createConnection(
      {
        host,
        port: actualPort,
        timeout: 60000, // 60 seconds timeout
      },
      () => handleConnection(socket, false)
    );
  }
};

// Socket handlers
const socket = createConnection();

socket.on('error', (err) => {
  console.error('Socket error:', err);
  socket.end();
  process.exit(1);
});

socket.on('timeout', () => {
  console.error('Connection timed out');
  socket.end();
  process.exit(1);
});

socket.on('end', () => {
  console.log('Connection closed by server');
});

socket.on('close', () => {
  console.log('Connection closed');
  process.exit(0);
});

// Handle SMTP conversation
let authenticated = false;
let stage = 0;

function handleConnection(socket: net.Socket | tls.TLSSocket, isSecure: boolean) {
  console.log('Connection established!');

  // Set up data handler for SMTP responses
  socket.on('data', (data) => {
    const response = data.toString();
    console.log(`<< ${response.trim()}`);

    // Simple SMTP state machine
    switch (stage) {
      case 0: // Server greeting received
        if (response.startsWith('220')) {
          console.log('>> EHLO example.com');
          socket.write('EHLO example.com\r\n');
          stage = 1;
        } else {
          console.error('Unexpected server greeting');
          socket.end();
        }
        break;

      case 1: // EHLO response received
        if (response.includes('250')) {
          if (!isSecure) {
            console.log('>> STARTTLS');
            socket.write('STARTTLS\r\n');
            stage = 2;
          } else {
            // Already secure, proceed to auth
            console.log('>> AUTH LOGIN');
            socket.write('AUTH LOGIN\r\n');
            stage = 3;
          }
        } else {
          console.error('EHLO command failed');
          socket.end();
        }
        break;

      case 2: // STARTTLS response (only for non-secure connections)
        if (response.startsWith('220')) {
          console.log('STARTTLS command successful, upgrading connection...');
          // This is where you would upgrade the connection to TLS
          // Socket is plain, need to advise manual TLS upgrade
          console.log('Cannot upgrade socket in this script. For production use nodemailer.');
          console.log('>> AUTH LOGIN');
          socket.write('AUTH LOGIN\r\n');
          stage = 3;
        } else {
          console.error('STARTTLS command failed');
          socket.end();
        }
        break;

      case 3: // AUTH LOGIN response
        if (response.startsWith('334')) {
          const usernameBase64 = Buffer.from(user).toString('base64');
          console.log(`>> ${usernameBase64} (username in base64)`);
          socket.write(`${usernameBase64}\r\n`);
          stage = 4;
        } else {
          console.error('AUTH LOGIN command failed');
          socket.end();
        }
        break;

      case 4: // Username response
        if (response.startsWith('334')) {
          const passwordBase64 = Buffer.from(pass).toString('base64');
          console.log('>> ******** (password in base64)');
          socket.write(`${passwordBase64}\r\n`);
          stage = 5;
        } else {
          console.error('Username rejected');
          socket.end();
        }
        break;

      case 5: // Auth result
        if (response.startsWith('235')) {
          console.log('Authentication successful!');
          authenticated = true;

          // Send a simple test email command - MAIL FROM
          console.log(`>> MAIL FROM:<${user}>`);
          socket.write(`MAIL FROM:<${user}>\r\n`);
          stage = 6;
        } else {
          console.error('Authentication failed');
          socket.end();
        }
        break;

      case 6: // MAIL FROM response
        if (response.startsWith('250')) {
          // Set recipient
          const recipient = '<EMAIL>';
          console.log(`>> RCPT TO:<${recipient}>`);
          socket.write(`RCPT TO:<${recipient}>\r\n`);
          stage = 7;
        } else {
          console.error('MAIL FROM command failed');
          socket.end();
        }
        break;

      case 7: // RCPT TO response
        if (response.startsWith('250')) {
          // Start message data
          console.log('>> DATA');
          socket.write('DATA\r\n');
          stage = 8;
        } else {
          console.log('RCPT TO command completed (may be rejected in test mode)');
          // Quit anyway
          console.log('>> QUIT');
          socket.write('QUIT\r\n');
          stage = 9;
        }
        break;

      case 8: // DATA response
        if (response.startsWith('354')) {
          // Don't actually send email content in test mode
          console.log('DATA command accepted, but not sending actual content for test');
          console.log('>> .\r\n');
          socket.write('.\r\n'); // End message
          stage = 9;
        } else {
          console.error('DATA command failed');
          socket.end();
        }
        break;

      case 9: // After data or QUIT command
        console.log('Test completed. SMTP session successful!');
        console.log('>> QUIT');
        socket.write('QUIT\r\n');
        socket.end();
        break;

      default:
        console.log('Unknown stage: ' + stage);
        socket.end();
    }
  });
}

// Print useful info
console.log('\nInstructions:');
console.log('1. Watch the SMTP conversation for errors');
console.log('2. Look for specific error messages from your mail server');
console.log('3. If connection is established but authentication fails, check credentials');
console.log('4. If using port 465, ensure TLS/SSL is properly configured');
console.log('5. Try alternative ports with: npx ts-node src/scripts/diagnose-smtp-socket.ts --alt');
console.log('\nStarting connection test...\n');