import mongoose from 'mongoose';
import { Item } from '../models/Item';
import { Category } from '../models/Category';
import { User } from '../models/User';
import dotenv from 'dotenv';

dotenv.config();

const seedData = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/b2b-platform');
    console.log('Connected to MongoDB');

    // Create test user if not exists
    const testUser = await User.findOne({ email: '<EMAIL>' });
    const userId = testUser ? testUser._id : (await User.create({
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'password123',
      isActive: true
    }))._id;

    // Create test categories if not exist
    const categories = await Promise.all([
      Category.findOneAndUpdate(
        { name: 'Electronics' },
        {
          name: 'Electronics',
          nameEn: 'Electronics',
          type: 'Product',
          level: 0
        },
        { upsert: true, new: true }
      ),
      Category.findOneAndUpdate(
        { name: 'Software Development' },
        {
          name: 'Software Development',
          nameEn: 'Software Development',
          type: 'Service',
          level: 0
        },
        { upsert: true, new: true }
      )
    ]);

    // Create test items
    const testItems = [
      {
        name: 'Test Product 1',
        description: '<p>This is a test product</p>',
        price: 99.99,
        category: categories[0]._id,
        type: 'product',
        isApproved: true,
        owner: userId,
        isActive: true,
        images: ['https://example.com/image1.jpg']
      },
      {
        name: 'Test Service 1',
        description: '<p>This is a test service</p>',
        price: 199.99,
        category: categories[1]._id,
        type: 'service',
        isApproved: true,
        owner: userId,
        isActive: true,
        images: ['https://example.com/image2.jpg']
      }
    ];

    // Insert test items
    await Item.insertMany(testItems);

    console.log('Test data seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding test data:', error);
    process.exit(1);
  }
};

seedData();
