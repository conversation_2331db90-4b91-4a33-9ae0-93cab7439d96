import mongoose from 'mongoose';
import { StoredCard } from '../models/StoredCard';
import { isEncrypted } from '../utils/encryption';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function checkCardEncryption() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/e-exportcity';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Check if encryption key is set
    if (!process.env.CARD_ENCRYPTION_KEY) {
      console.error('\n❌ ERROR: CARD_ENCRYPTION_KEY is not set in environment variables!');
      console.error('Run: npm run generate:encryption-key to create a new key');
      console.error('Then add it to your .env file\n');
      process.exit(1);
    }

    // Find all stored cards
    const cards = await StoredCard.find({});
    console.log(`\nFound ${cards.length} stored cards\n`);

    let encryptedCount = 0;
    let unencryptedCount = 0;
    let invalidCount = 0;
    const problematicCards: any[] = [];

    for (const card of cards) {
      try {
        // Check if cardToken is encrypted
        const cardTokenDoc = card.toObject({ getters: false });
        const isTokenEncrypted = isEncrypted(cardTokenDoc.cardToken);
        const isUserKeyEncrypted = isEncrypted(cardTokenDoc.cardUserKey);

        if (isTokenEncrypted && isUserKeyEncrypted) {
          // Try to access the decrypted values (this will trigger decryption)
          try {
            const decryptedToken = card.cardToken;
            const decryptedUserKey = card.cardUserKey;
            if (decryptedToken && decryptedUserKey) {
              encryptedCount++;
            }
          } catch (error) {
            invalidCount++;
            problematicCards.push({
              id: card._id,
              userId: card.userId,
              cardAlias: card.cardAlias,
              lastFour: card.lastFourDigits,
              error: 'Decryption failed - wrong key'
            });
          }
        } else {
          unencryptedCount++;
          problematicCards.push({
            id: card._id,
            userId: card.userId,
            cardAlias: card.cardAlias,
            lastFour: card.lastFourDigits,
            error: 'Not encrypted'
          });
        }
      } catch (error: any) {
        invalidCount++;
        problematicCards.push({
          id: card._id,
          userId: card.userId,
          cardAlias: card.cardAlias,
          error: error.message
        });
      }
    }

    // Report results
    console.log('=== Encryption Status Report ===');
    console.log(`✅ Properly encrypted cards: ${encryptedCount}`);
    console.log(`⚠️  Unencrypted cards: ${unencryptedCount}`);
    console.log(`❌ Cards with decryption errors: ${invalidCount}`);
    console.log(`Total cards: ${cards.length}\n`);

    if (problematicCards.length > 0) {
      console.log('=== Problematic Cards ===');
      problematicCards.forEach(card => {
        console.log(`Card ID: ${card.id}`);
        console.log(`User ID: ${card.userId}`);
        console.log(`Alias: ${card.cardAlias || 'N/A'}`);
        console.log(`Last 4: ${card.lastFour || 'N/A'}`);
        console.log(`Error: ${card.error}`);
        console.log('---');
      });

      console.log('\n=== Recommended Actions ===');
      if (unencryptedCount > 0) {
        console.log('1. For unencrypted cards: These cards need to be re-saved to trigger encryption');
      }
      if (invalidCount > 0) {
        console.log('2. For cards with decryption errors:');
        console.log('   - If you lost the original encryption key, these cards cannot be recovered');
        console.log('   - You should mark them as inactive and ask users to add their cards again');
        console.log('   - Run: npm run cards:cleanup to mark invalid cards as inactive');
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the script
checkCardEncryption();