/**
 * Test Email Using Ethereal
 *
 * This script tests email sending using Ethereal, a fake SMTP service
 * that captures emails for testing without actually sending them.
 *
 * Run with: ts-node src/scripts/test-ethereal.ts
 */

import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

// Initialize environment variables
dotenv.config();

const TEST_RECIPIENT = '<EMAIL>';

async function testWithEthereal() {
  console.log('Creating test account with Ethereal...');

  // Create a test account at Ethereal
  const testAccount = await nodemailer.createTestAccount();
  console.log('Test account created:', testAccount.user);

  // Create a transporter using the test account
  const transporter = nodemailer.createTransport({
    host: 'smtp.ethereal.email',
    port: 587,
    secure: false,
    auth: {
      user: testAccount.user,
      pass: testAccount.pass
    }
  });

  // Email content
  const subject = 'Test Email from eExportCity Platform';
  const text = 'This is a test email sent from the eExportCity platform to verify email sending functionality.';
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>eExportCity Platform</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email sent from the eExportCity platform to verify that our email sending functionality is working correctly.</p>
            <p>If you've received this email, it means our email service is configured properly.</p>
            <p>No action is required on your part.</p>
            <p>Thank you!</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} eExportCity. All rights reserved.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  // Send the email
  try {
    console.log('Sending test email...');
    const info = await transporter.sendMail({
      from: `"eExportCity" <${testAccount.user}>`,
      to: TEST_RECIPIENT,
      subject,
      text,
      html
    });

    console.log('✅ Email sent successfully!');
    console.log('Message ID:', info.messageId);

    // Preview URL (only works with Ethereal)
    console.log('Preview URL:', nodemailer.getTestMessageUrl(info));

    return info;
  } catch (error) {
    console.error('❌ Failed to send email:', error);
    throw error;
  }
}

// Execute the test function
testWithEthereal()
  .then(() => {
    console.log('Test completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Test failed with error:', error);
    process.exit(1);
  });