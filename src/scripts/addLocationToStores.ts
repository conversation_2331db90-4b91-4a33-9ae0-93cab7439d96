import mongoose from 'mongoose';
import dotenv from 'dotenv';
import { Store } from '../models/Store';

// Load environment variables
dotenv.config();

// Sample location data for testing
const sampleLocations = [
  { city: 'Istanbul', country: 'Turkey' },
  { city: 'Ankara', country: 'Turkey' },
  { city: 'Izmir', country: 'Turkey' },
  { city: 'Bursa', country: 'Turkey' },
  { city: 'New York', country: 'USA' },
  { city: 'London', country: 'UK' },
  { city: 'Paris', country: 'France' },
  { city: 'Berlin', country: 'Germany' }
];

const updateStoresWithLocation = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI as string);
    console.log('Connected to MongoDB');

    // Get all stores without location data
    const stores = await Store.find({ $or: [{ location: { $exists: false } }, { location: null }] });
    console.log(`Found ${stores.length} stores without location data`);

    // Update each store with random location data
    for (const store of stores) {
      const randomLocation = sampleLocations[Math.floor(Math.random() * sampleLocations.length)];
      
      await Store.updateOne(
        { _id: store._id },
        { $set: { location: randomLocation } }
      );
      
      console.log(`Updated store ${store.name} with location: ${randomLocation.city}, ${randomLocation.country}`);
    }

    console.log('All updates completed');
    process.exit(0);
  } catch (error) {
    console.error('Error updating stores:', error);
    process.exit(1);
  }
};

// Run the update function
updateStoresWithLocation();
