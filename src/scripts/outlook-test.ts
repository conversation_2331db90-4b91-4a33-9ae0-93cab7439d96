/**
 * Outlook/Office 365 SMTP Email Test
 *
 * This script provides a straightforward way to test email sending
 * using Outlook or Office 365 SMTP server.
 *
 * Run with: npx ts-node src/scripts/outlook-test.ts
 */

import nodemailer from 'nodemailer';

// ======= CONFIGURE THESE VALUES DIRECTLY =======
// Recipient email address
const RECIPIENT = '<EMAIL>';

// Outlook account details
const OUTLOOK_USER = '<EMAIL>';  // Your Outlook email address
const OUTLOOK_PASSWORD = 'your-password-here';  // Your Outlook password

// "From" details
const FROM_NAME = 'eExportCity Test';
const FROM_EMAIL = OUTLOOK_USER;

// ======= END OF CONFIGURATION =======

// Outlook server configuration
const MAIL_CONFIG = {
  host: 'smtp-mail.outlook.com',
  port: 587,
  secure: false,
  requireTLS: true,
  auth: {
    user: OUTLOOK_USER,
    pass: OUTLOOK_PASSWORD
  },
  connectionTimeout: 60000,
  greetingTimeout: 60000,
  socketTimeout: 90000
};

async function sendOutlookTest() {
  console.log('Outlook SMTP Email Test');
  console.log('=======================');
  console.log(`\nMail Configuration:`);
  console.log(`- Service: Outlook/Office 365`);
  console.log(`- Username: ${MAIL_CONFIG.auth.user}`);
  console.log(`- Password: ${MAIL_CONFIG.auth.pass.length > 0 ? '******' : 'NOT SET (PLEASE CONFIGURE PASSWORD)'}`);
  console.log(`\nSending to: ${RECIPIENT}`);

  // Check for placeholder values
  if (OUTLOOK_USER === '<EMAIL>' || OUTLOOK_PASSWORD === 'your-password-here') {
    console.error('\n❌ ERROR: You need to set your actual Outlook address and password in the script!');
    console.log('   Open outlook-test.ts and replace the placeholder values with your actual Outlook details.');
    return false;
  }

  // Create a transporter with Outlook configuration
  const transporter = nodemailer.createTransport(MAIL_CONFIG);

  console.log('\nVerifying connection to Outlook...');
  try {
    await transporter.verify();
    console.log('✅ Outlook connection verified successfully!');
  } catch (error) {
    console.error('❌ Outlook connection verification failed:', error);
    console.log('Continuing with email send attempt anyway...');
  }

  // Create a simple email
  const timestamp = new Date().toISOString();
  const subject = `Outlook Test Email from eExportCity (${timestamp})`;
  const text = `This is a test email sent via Outlook SMTP at ${timestamp}.`;
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Outlook Test Email</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email sent from the eExportCity platform via Outlook/Office 365.</p>
            <p>If you've received this email, it means our Outlook configuration is working correctly.</p>
            <p>Test timestamp: ${timestamp}</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  try {
    console.log('\nSending test email via Outlook...');
    const info = await transporter.sendMail({
      from: {
        name: FROM_NAME,
        address: FROM_EMAIL
      },
      to: RECIPIENT,
      subject,
      text,
      html
    });

    console.log('\n✅ Email sent successfully via Outlook!');
    console.log('Message ID:', info.messageId);
    return true;
  } catch (error) {
    console.error('\n❌ Failed to send email via Outlook:', error);
    return false;
  }
}

// Execute the function
sendOutlookTest()
  .then(success => {
    if (success) {
      console.log('\n✅ Outlook email test completed successfully!');
      console.log('Check your inbox for the test email.');
    } else {
      console.log('\n❌ Outlook email test failed.');
      console.log('\nTroubleshooting Tips:');
      console.log('1. Verify that your Outlook email and password are correct');
      console.log('2. Check if your Outlook account has any security restrictions');
      console.log('3. For Office 365 accounts, you might need to allow "less secure apps"');
      console.log('4. If you have 2FA enabled, you might need to use an app password');
      console.log('5. Try the env-config-test.ts script to use your .env configuration');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });