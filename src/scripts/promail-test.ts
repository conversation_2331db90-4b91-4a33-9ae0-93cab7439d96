/**
 * ProMail SMTP Email Test Script
 *
 * This script is specifically designed to test the mega.promail.com.tr mail server
 * with precise configuration options to handle its specific requirements.
 *
 * Run with: npx ts-node src/scripts/promail-test.ts
 */

import nodemailer from 'nodemailer';

// ======= CONFIGURE THESE VALUES DIRECTLY =======
// Recipient email address
const RECIPIENT = '<EMAIL>';

// Credentials
const USERNAME = '<EMAIL>';
const PASSWORD = 'Info13579!';  // Be sure to replace this with your actual password!

// "From" details
const FROM_NAME = 'eExportCity Test';
const FROM_EMAIL = `${FROM_NAME} <${USERNAME}>`;

// ======= END OF CONFIGURATION =======

async function testProMailServer() {
  console.log('ProMail SMTP Test');
  console.log('================');
  console.log(`\nMail Configuration:`);
  console.log(`- Host: mega.promail.com.tr`);
  console.log(`- Port: 587`);
  console.log(`- Username: ${USERNAME}`);
  console.log(`- Password: ${PASSWORD ? '******' : 'NOT SET'}`);
  console.log(`\nSending to: ${RECIPIENT}`);
  console.log(`From: ${FROM_EMAIL}`);

  // Create a transporter with inline configuration
  const transporter = nodemailer.createTransport({
    host: 'mega.promail.com.tr',
    port: 587,
    secure: false,
    requireTLS: true,
    auth: {
      user: USERNAME,
      pass: PASSWORD
    },
    // Connection settings - longer timeouts for stability
    connectionTimeout: 60000,
    greetingTimeout: 60000,
    socketTimeout: 90000,
    // TLS settings optimized for Turkish hosting providers
    tls: {
      rejectUnauthorized: false, // Accept self-signed certificates
      minVersion: 'TLSv1'        // Use older TLS protocol for compatibility
    }
  });

  console.log('\nVerifying connection to mail server...');
  let connectionVerified = false;

  try {
    await transporter.verify();
    console.log('✅ Mail server connection verified successfully!');
    connectionVerified = true;
  } catch (error) {
    console.error('❌ Mail server connection verification failed:', error);
    console.log('Attempting to send email anyway...');
  }

  // Create a simple email
  const timestamp = new Date().toISOString();
  const subject = `ProMail Test Email (${timestamp})`;
  const text = `This is a test email sent at ${timestamp} using mega.promail.com.tr.`;
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>ProMail Test Email</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email sent using the ProMail mail server (mega.promail.com.tr).</p>
            <p>If you've received this email, it means our ProMail configuration is working correctly.</p>
            <p>Test timestamp: ${timestamp}</p>
            <p>Connection verified: ${connectionVerified ? 'Yes' : 'No'}</p>
          </div>
          <div class="footer">
            <p>This is an automated message from eExportCity.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  try {
    console.log('\nSending test email...');
    const info = await transporter.sendMail({
      from: FROM_EMAIL,
      to: RECIPIENT,
      subject,
      text,
      html
    });

    console.log('\n✅ Email sent successfully!');
    console.log('Message ID:', info.messageId);
    return true;
  } catch (error: any) {
    console.error('\n❌ Failed to send email:', error);

    // Show detailed error information
    if (error.code) {
      console.log(`Error code: ${error.code}`);
    }

    if (error.command) {
      console.log(`Failed SMTP command: ${error.command}`);
    }

    // Try alternative settings if original failed
    if (!connectionVerified) {
      console.log('\n🔄 Trying alternative settings...');

      // Try alternative configuration (port 465 with secure:true)
      const altTransporter = nodemailer.createTransport({
        host: 'mega.promail.com.tr',
        port: 465,
        secure: true,
        requireTLS: false,
        auth: {
          user: USERNAME,
          pass: PASSWORD
        },
        connectionTimeout: 60000,
        greetingTimeout: 60000,
        socketTimeout: 90000,
        tls: {
          rejectUnauthorized: false,
          minVersion: 'TLSv1'
        }
      });

      console.log('Alternative settings:');
      console.log(`- Port: 465 (instead of 587)`);
      console.log(`- Secure: true (instead of false)`);
      console.log(`- RequireTLS: false (instead of true)`);

      try {
        console.log('\nVerifying alternative connection...');
        await altTransporter.verify();
        console.log('✅ Alternative connection verified successfully!');

        console.log('\nSending test email with alternative settings...');
        const altInfo = await altTransporter.sendMail({
          from: FROM_EMAIL,
          to: RECIPIENT,
          subject: `${subject} (Alternative Settings)`,
          text,
          html: html.replace('ProMail Test Email', 'ProMail Test Email (Alternative Settings)')
        });

        console.log('\n✅ Email sent successfully with alternative settings!');
        console.log('Message ID:', altInfo.messageId);
        return true;
      } catch (altError) {
        console.error('\n❌ Alternative configuration also failed:', altError);
      }
    }

    return false;
  }
}

// Execute the function
testProMailServer()
  .then(success => {
    if (success) {
      console.log('\n✅ ProMail test completed successfully!');
      console.log('Check your inbox for the test email.');
    } else {
      console.log('\n❌ ProMail test failed.');
      console.log('\nTroubleshooting Tips for Turkish Hosting Providers:');
      console.log('1. Verify your hosting account has email services activated');
      console.log('2. Contact your hosting provider to confirm the correct mail settings');
      console.log('3. Make sure your domain\'s MX records are correctly configured');
      console.log('4. Try these specific settings for Turkish hosting providers:');
      console.log('   - Port 587 with secure:false and requireTLS:true');
      console.log('   - Port 465 with secure:true and requireTLS:false');
      console.log('   - Port 25 (sometimes works if other ports are blocked)');
      console.log('5. As a last resort, consider using Gmail or Outlook for more reliable delivery');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });