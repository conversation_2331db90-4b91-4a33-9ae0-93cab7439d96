/**
 * Test Email Sending with Custom Mail Configuration
 *
 * This script tests email sending using custom SMTP settings that can be
 * provided via environment variables or command line arguments.
 *
 * Usage:
 *   npx ts-node src/scripts/test-custom-mail.ts
 *
 * With environment variables:
 *   TEST_MAIL_HOST=mail.example.com TEST_MAIL_PORT=587 TEST_MAIL_USER=<EMAIL> TEST_MAIL_PASS=password npx ts-node src/scripts/test-custom-mail.ts
 *
 * Or with command line args:
 *   npx ts-node src/scripts/test-custom-mail.ts --host=mail.example.com --port=587 --user=<EMAIL> --pass=password
 */

import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { config } from '../config';

// Initialize environment variables
dotenv.config();

// Parse command line arguments
const args = process.argv.slice(2).reduce((acc, arg) => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.substring(2).split('=');
    acc[key] = value;
  }
  return acc;
}, {} as Record<string, string>);

// Configuration for the test
const TEST_RECIPIENT = process.env.TEST_RECIPIENT || '<EMAIL>';
const TEST_MAIL_HOST = args.host || process.env.TEST_MAIL_HOST || config.smtp.host;
const TEST_MAIL_PORT = parseInt(args.port || process.env.TEST_MAIL_PORT || config.smtp.port.toString());
const TEST_MAIL_SECURE = args.secure ? args.secure === 'true' : process.env.TEST_MAIL_SECURE === 'true' || config.smtp.secure;
const TEST_MAIL_USER = args.user || process.env.TEST_MAIL_USER || config.smtp.user;
const TEST_MAIL_PASS = args.pass || process.env.TEST_MAIL_PASS || config.smtp.pass;
const TEST_MAIL_TLS = args.tls ? args.tls === 'true' : process.env.TEST_MAIL_TLS === 'true' || true;

/**
 * Tests email sending with the specified mail settings
 */
async function testCustomMailSettings() {
  console.log('Starting email test with custom mail settings...');
  console.log(`Sending test email to: ${TEST_RECIPIENT}`);

  console.log('\nMail server configuration:');
  console.log(`- Host: ${TEST_MAIL_HOST}`);
  console.log(`- Port: ${TEST_MAIL_PORT}`);
  console.log(`- Secure: ${TEST_MAIL_SECURE}`);
  console.log(`- User: ${TEST_MAIL_USER}`);
  console.log(`- Auth: ${TEST_MAIL_PASS ? 'Password provided' : 'No password provided'}`);
  console.log(`- TLS: ${TEST_MAIL_TLS}`);

  // Create a transporter using the custom configuration
  const transporter = nodemailer.createTransport({
    host: TEST_MAIL_HOST,
    port: TEST_MAIL_PORT,
    secure: TEST_MAIL_SECURE,
    requireTLS: TEST_MAIL_TLS && !TEST_MAIL_SECURE,
    auth: {
      user: TEST_MAIL_USER,
      pass: TEST_MAIL_PASS
    },
    connectionTimeout: 60000, // 60 seconds
    greetingTimeout: 60000,
    socketTimeout: 90000,
    tls: {
      rejectUnauthorized: false
    },
    debug: true, // Enable debug output
    logger: true // Log information to console
  });

  // First, verify the connection
  console.log('\nVerifying connection to mail server...');
  try {
    await transporter.verify();
    console.log('✅ Mail server connection verified successfully!');
  } catch (error) {
    console.error('❌ Mail server connection verification failed:', error);
    console.log('Continuing with email send attempt anyway...');
  }

  // Email content
  const timestamp = new Date().toISOString();
  const subject = `Test Email from eExportCity Platform (${timestamp})`;
  const text = `This is a test email sent at ${timestamp} from the eExportCity platform to verify email sending functionality.`;
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
          .timestamp { background-color: #ffffcc; padding: 5px; border-radius: 3px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>eExportCity Platform</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email sent from the eExportCity platform to verify our email sending functionality.</p>
            <p>If you've received this email, it means our email service is configured properly.</p>
            <div class="timestamp">
              <p><strong>Test Information:</strong></p>
              <p>Timestamp: ${timestamp}</p>
              <p>Mail Server: ${TEST_MAIL_HOST}:${TEST_MAIL_PORT}</p>
              <p>Secure: ${TEST_MAIL_SECURE ? 'Yes' : 'No'}</p>
            </div>
            <p>Thank you!</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} eExportCity. All rights reserved.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  try {
    // Send the email
    console.log('\nSending test email...');
    const info = await transporter.sendMail({
      from: `"eExportCity Test" <${TEST_MAIL_USER}>`,
      to: TEST_RECIPIENT,
      subject,
      text,
      html
    });

    console.log('\n✅ Email sent successfully!');
    console.log('Message ID:', info.messageId);
    return info;
  } catch (error) {
    console.error('\n❌ Failed to send email:', error);
    throw error;
  }
}

// Execute the test function
console.log('Custom Mail Testing Tool');
console.log('======================');
console.log('This tool tests email sending with custom SMTP settings.');

testCustomMailSettings()
  .then((info) => {
    console.log('\nTest completed successfully!');
    console.log('If the email was sent successfully, it should arrive in your inbox shortly.');
    console.log('Check your inbox or spam folder for the test email.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\nTest failed with error:', error);
    console.log('\nTroubleshooting tips:');
    console.log('1. Check that your mail server host and port are correct');
    console.log('2. Verify your username and password');
    console.log('3. Check if your mail server requires TLS or SSL');
    console.log('4. Some mail servers might block connections from certain IP addresses');
    console.log('5. Try another port (common ports: 25, 465, 587, 2525)');
    console.log('6. Check your mail server logs if possible');
    process.exit(1);
  });