import { sendEmail } from '../services/emailService';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function sendTestEmail() {
  const toEmail = '<EMAIL>';
  const subject = 'Test Email from E-Export City';
  const text = 'This is a test email to verify the email functionality.';
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #2C7A7B; padding: 20px; text-align: center;">
        <h1 style="color: white; margin: 0;">E-Export City</h1>
      </div>
      <div style="padding: 20px; border: 1px solid #e5e5e5; border-top: none;">
        <h2 style="color: #2C7A7B;">Test Email</h2>
        <p>Hello,</p>
        <p>This is a test email sent from the E-Export City platform to verify email functionality.</p>
        <p>If you received this email, the email system is working correctly.</p>
        <p style="color: #718096; font-size: 0.9em;">
          Best regards,<br>
          E-Export City Team
        </p>
      </div>
    </div>
  `;
  
  try {
    console.log(`Attempting to send test email to ${toEmail}...`);
    const result = await sendEmail(toEmail, subject, text, html);
    console.log('Test email sent successfully!');
    console.log('Message ID:', result.messageId);
    return { success: true, message: 'Email sent successfully' };
  } catch (error) {
    console.error('Failed to send test email:', error);
    return { success: false, message: 'Failed to send email', error };
  }
}

// Execute the function
sendTestEmail()
  .then(result => {
    console.log('Result:', result);
    process.exit(result.success ? 0 : 1);
  })
  .catch(err => {
    console.error('Error in test script:', err);
    process.exit(1);
  });
