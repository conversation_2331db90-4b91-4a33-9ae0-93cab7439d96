import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { config } from '../config';

// Load environment variables
dotenv.config();

// Email configuration from config.ts
const MAIL_HOST = config.smtp.host;
const MAIL_PORT = config.smtp.port;
const MAIL_USER = config.smtp.user;
const MAIL_PASS = config.smtp.pass;
const EMAIL_FROM_NAME = process.env.EMAIL_FROM_NAME || 'Export City';

// Using a sample test email address
const TEST_EMAIL = '<EMAIL>'; // Replace with your actual Gmail if this isn't yours

async function sendTestEmail() {
  console.log(`Starting email test to Gmail: ${TEST_EMAIL}`);
  console.log(`Mail configuration: ${MAIL_HOST}:${MAIL_PORT}, User: ${MAIL_USER}`);
  
  try {
    // Create a transporter with minimal configuration
    const transporter = nodemailer.createTransport({
      host: MAIL_HOST,
      port: MAIL_PORT,
      secure: false,
      auth: {
        user: MAIL_USER,
        pass: MAIL_PASS
      }
    });
    
    console.log('Testing connection...');
    await transporter.verify();
    console.log('Connection verified');
    
    const mailOptions = {
      from: `${EMAIL_FROM_NAME} <${MAIL_USER}>`, // No quotes in name
      to: TEST_EMAIL,
      subject: 'Gmail Test Email from E-Export City',
      text: 'This is a test email sent to a Gmail address to check deliverability.',
      html: `
        <div style="font-family: Arial, sans-serif;">
          <h1 style="color: #2C7A7B;">E-Export City Test to Gmail</h1>
          <p>This is a test email sent to a Gmail address to check deliverability.</p>
          <p>Time sent: ${new Date().toLocaleString()}</p>
          <p>If you received this email, please let me know!</p>
        </div>
      `
    };
    
    console.log(`Sending email to ${TEST_EMAIL}...`);
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully!');
    console.log('Message ID:', info.messageId);
    
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error in email test:', error);
    return { success: false, error };
  }
}

// Run the test
sendTestEmail()
  .then(result => {
    console.log('Test result:', result);
    process.exit(result.success ? 0 : 1);
  })
  .catch(err => {
    console.error('Unexpected error:', err);
    process.exit(1);
  });
