/**
 * Test Script to verify the User Profile API returns only active packages
 * 
 * This script:
 * 1. Finds an existing user or creates a test user
 * 2. Creates multiple packages with different statuses: ACTIVE, CANCELED, EXPIRED
 * 3. Logs in as the user to get an auth token
 * 4. Fetches the user profile to check which packages are returned
 * 5. Verifies that only ACTIVE packages are included in the response
 */

import mongoose from 'mongoose';
import { User } from '../models/User';
import { Package } from '../models/Package';
import { Subscription } from '../models/Subscription';
import axios from 'axios';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const API_URL = process.env.API_URL || 'http://localhost:3001/api';

async function createTestUser() {
  const email = `test-user-${Date.now()}@example.com`;
  const password = 'Test@123';

  try {
    const user = new User({
      firstName: 'Test',
      lastName: 'User',
      email,
      password,
      phoneNumber: '+905551234567',
      address: 'Test Address',
      city: 'Test City',
      country: 'TR',
      zipCode: '34000',
      birthDate: new Date('1990-01-01')
    });

    await user.save();
    console.log('Test user created:', email);
    return { user, password };
  } catch (error) {
    console.error('Error creating test user:', error);
    throw error;
  }
}

interface SubscriptionData {
  _id: mongoose.Types.ObjectId;
  [key: string]: any;
}

async function createTestSubscriptions(userId: mongoose.Types.ObjectId) {
  try {
    // Find an active standard package
    const standardPackage = await Package.findOne({ 
      isActive: true, 
      type: 'standard' 
    });

    if (!standardPackage) {
      throw new Error('No active standard package found');
    }

    // Create dates for subscriptions
    const now = new Date();
    const oneMonthLater = new Date(now);
    oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);
    
    const oneMonthAgo = new Date(now);
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    // Create an ACTIVE subscription
    const activeSubscription = new Subscription({
      userId,
      packageId: standardPackage._id,
      startDate: now,
      endDate: oneMonthLater,
      status: 'ACTIVE',
      paymentStatus: 'paid',
      remainingViewRequests: standardPackage.viewRequestLimit,
      remainingCreateRequests: standardPackage.createRequestLimit,
      renewalDate: oneMonthLater,
      nextBillingDate: oneMonthLater,
      autoRenewal: true
    });

    // Create a CANCELED subscription
    const canceledSubscription = new Subscription({
      userId,
      packageId: standardPackage._id,
      startDate: oneMonthAgo,
      endDate: oneMonthLater,
      status: 'CANCELED',
      paymentStatus: 'paid',
      remainingViewRequests: standardPackage.viewRequestLimit,
      remainingCreateRequests: standardPackage.createRequestLimit,
      renewalDate: oneMonthLater,
      nextBillingDate: oneMonthLater,
      autoRenewal: false
    });

    // Create an EXPIRED subscription
    const expiredSubscription = new Subscription({
      userId,
      packageId: standardPackage._id,
      startDate: oneMonthAgo,
      endDate: now,
      status: 'EXPIRED',
      paymentStatus: 'expired',
      remainingViewRequests: 0,
      remainingCreateRequests: 0,
      renewalDate: now,
      nextBillingDate: now,
      autoRenewal: false
    });

    await activeSubscription.save();
    await canceledSubscription.save();
    await expiredSubscription.save();

    console.log('Test subscriptions created');
    
    // Convert to plain objects with typed _id fields
    const activeData = activeSubscription.toObject() as SubscriptionData;
    const canceledData = canceledSubscription.toObject() as SubscriptionData;
    const expiredData = expiredSubscription.toObject() as SubscriptionData;
    
    return { 
      activeSubscription: activeData,
      canceledSubscription: canceledData, 
      expiredSubscription: expiredData
    };
  } catch (error) {
    console.error('Error creating test subscriptions:', error);
    throw error;
  }
}

async function loginUser(email: string, password: string) {
  try {
    const response = await axios.post(`${API_URL}/auth/login`, {
      email,
      password
    });

    return response.data.token;
  } catch (error) {
    console.error('Error logging in:', error);
    throw error;
  }
}

async function getUserProfile(token: string) {
  try {
    const response = await axios.get(`${API_URL}/users/profile`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
}

async function cleanupTestData(
  userId: mongoose.Types.ObjectId, 
  subscriptions: { 
    activeSubscription: SubscriptionData, 
    canceledSubscription: SubscriptionData, 
    expiredSubscription: SubscriptionData 
  }
) {
  try {
    await Subscription.deleteOne({ _id: subscriptions.activeSubscription._id });
    await Subscription.deleteOne({ _id: subscriptions.canceledSubscription._id });
    await Subscription.deleteOne({ _id: subscriptions.expiredSubscription._id });
    await User.deleteOne({ _id: userId });
    console.log('Test data cleaned up');
  } catch (error) {
    console.error('Error cleaning up test data:', error);
  }
}

async function main() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/exportcity');
    console.log('Connected to MongoDB');

    // Create test user
    const { user, password } = await createTestUser();

    // Create test subscriptions
    const subscriptions = await createTestSubscriptions(user._id);

    // Login as the test user
    const token = await loginUser(user.email, password);

    // Get the user profile
    const profile = await getUserProfile(token);

    // Check if only active subscriptions are returned
    console.log('\n===== User Profile Subscription Details =====');
    console.log(JSON.stringify(profile.data.subscriptionDetails, null, 2));

    // Verify the results
    if (!profile.data.subscriptionDetails) {
      console.error('❌ No subscription details found in the profile');
    } else {
      if (profile.data.subscriptionDetails.standard) {
        console.log('✅ Standard subscription found in profile');
        
        // Verify it's the active one
        const profileSubId = profile.data.subscriptionDetails.standard._id.toString();
        const activeSubId = subscriptions.activeSubscription._id.toString();
        const canceledSubId = subscriptions.canceledSubscription._id.toString();
        const expiredSubId = subscriptions.expiredSubscription._id.toString();
        
        if (profileSubId === activeSubId) {
          console.log('✅ The standard subscription is the ACTIVE one');
        } else if (profileSubId === canceledSubId) {
          console.error('❌ The standard subscription is the CANCELED one - this is wrong!');
        } else if (profileSubId === expiredSubId) {
          console.error('❌ The standard subscription is the EXPIRED one - this is wrong!');
        }
      } else {
        console.error('❌ Standard subscription not found in profile');
      }
    }

    // Clean up
    await cleanupTestData(user._id, subscriptions);

    // Close connection
    await mongoose.connection.close();
    console.log('MongoDB connection closed');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
main().catch(console.error);