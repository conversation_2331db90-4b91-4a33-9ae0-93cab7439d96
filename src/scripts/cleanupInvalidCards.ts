import mongoose from 'mongoose';
import { StoredCard } from '../models/StoredCard';
import { isEncrypted } from '../utils/encryption';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function cleanupInvalidCards() {
  try {
    // Connect to MongoDB
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/e-exportcity';
    await mongoose.connect(mongoUri);
    console.log('Connected to MongoDB');

    // Check if encryption key is set
    if (!process.env.CARD_ENCRYPTION_KEY) {
      console.error('\n❌ ERROR: CARD_ENCRYPTION_KEY is not set in environment variables!');
      console.error('This script requires the encryption key to identify invalid cards\n');
      process.exit(1);
    }

    // Find all stored cards
    const cards = await StoredCard.find({ isActive: true });
    console.log(`\nFound ${cards.length} active stored cards\n`);

    let deactivatedCount = 0;
    const deactivatedCards: any[] = [];

    for (const card of cards) {
      try {
        // Try to access the decrypted values
        const decryptedToken = card.cardToken;
        const decryptedUserKey = card.cardUserKey;
        
        if (!decryptedToken || !decryptedUserKey) {
          throw new Error('Empty decrypted values');
        }
      } catch (error: any) {
        // If decryption fails, mark the card as inactive
        console.log(`Deactivating card ${card._id} due to: ${error.message}`);
        
        card.isActive = false;
        await card.save();
        
        deactivatedCount++;
        deactivatedCards.push({
          id: card._id,
          userId: card.userId,
          cardAlias: card.cardAlias,
          lastFour: card.lastFourDigits
        });
      }
    }

    // Report results
    console.log('\n=== Cleanup Results ===');
    console.log(`✅ Cards deactivated: ${deactivatedCount}`);
    console.log(`📊 Total cards processed: ${cards.length}\n`);

    if (deactivatedCards.length > 0) {
      console.log('=== Deactivated Cards ===');
      deactivatedCards.forEach(card => {
        console.log(`Card ID: ${card.id}`);
        console.log(`User ID: ${card.userId}`);
        console.log(`Alias: ${card.cardAlias || 'N/A'}`);
        console.log(`Last 4: ${card.lastFour || 'N/A'}`);
        console.log('---');
      });

      console.log('\n✅ These cards have been marked as inactive.');
      console.log('Users will need to add their cards again.');
    } else {
      console.log('✅ All cards are valid! No cleanup needed.');
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Add confirmation prompt
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

console.log('\n⚠️  WARNING: This script will deactivate all cards that cannot be decrypted.');
console.log('Users will need to add their cards again.\n');

rl.question('Are you sure you want to continue? (yes/no): ', (answer: string) => {
  if (answer.toLowerCase() === 'yes') {
    rl.close();
    cleanupInvalidCards();
  } else {
    console.log('Operation cancelled.');
    rl.close();
    process.exit(0);
  }
});