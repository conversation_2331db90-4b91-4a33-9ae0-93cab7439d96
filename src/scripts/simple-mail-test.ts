/**
 * Simple SMTP Email Test
 *
 * This script provides a straightforward way to test email sending
 * with direct configuration, without relying on environment variables.
 *
 * Run with: npx ts-node src/scripts/simple-mail-test.ts
 */

import nodemailer from 'nodemailer';

// ======= CONFIGURE THESE VALUES DIRECTLY =======
// Recipient email address
const RECIPIENT = '<EMAIL>';

// Mail server settings
const MAIL_CONFIG = {
  // Option 1: Your hosting.com.tr mail server
  host: 'mail.e-exportcity.com',  // Your mail server hostname
  port: 587,                       // Try port 587 (alternative to 465)
  secure: false,                   // false for 587, true for 465
  requireTLS: true,                // Use TLS

  // Authentication details
  auth: {
    user: '<EMAIL>', // Your email address
    pass: 'your-password-here'     // Your email password (REPLACE THIS!)
  },

  // Connection settings
  connectionTimeout: 60000,        // 60 seconds
  greetingTimeout: 60000,
  socketTimeout: 90000,

  // TLS settings (accept self-signed certificates)
  tls: {
    rejectUnauthorized: false
  }
};

// "From" details
const FROM_NAME = 'eExportCity Test';
const FROM_EMAIL = MAIL_CONFIG.auth.user; // Use the same as auth.user

// ======= END OF CONFIGURATION =======

async function sendSimpleTestEmail() {
  console.log('Simple SMTP Email Test');
  console.log('=====================');
  console.log(`\nMail Configuration:`);
  console.log(`- Host: ${MAIL_CONFIG.host}`);
  console.log(`- Port: ${MAIL_CONFIG.port}`);
  console.log(`- Secure: ${MAIL_CONFIG.secure}`);
  console.log(`- Username: ${MAIL_CONFIG.auth.user}`);
  console.log(`- Password: ${MAIL_CONFIG.auth.pass.length > 0 ? '******' : 'NOT SET (PLEASE CONFIGURE PASSWORD)'}`);
  console.log(`\nSending to: ${RECIPIENT}`);

  // Check for placeholder password
  if (MAIL_CONFIG.auth.pass === 'your-password-here') {
    console.error('\n❌ ERROR: You need to set your actual email password in the script!');
    console.log('   Open simple-mail-test.ts and replace "your-password-here" with your actual password.');
    return false;
  }

  // Create a transporter with the specified config
  const transporter = nodemailer.createTransport(MAIL_CONFIG);

  console.log('\nVerifying connection to mail server...');
  try {
    await transporter.verify();
    console.log('✅ Mail server connection verified successfully!');
  } catch (error) {
    console.error('❌ Mail server connection verification failed:', error);
    console.log('Continuing with email send attempt anyway...');
  }

  // Create a simple email
  const timestamp = new Date().toISOString();
  const subject = `Simple Test Email from eExportCity (${timestamp})`;
  const text = `This is a test email sent at ${timestamp}.`;
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Simple Test Email</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a simple test email sent from the eExportCity platform.</p>
            <p>If you've received this email, it means our basic email configuration is working correctly.</p>
            <p>Test timestamp: ${timestamp}</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  try {
    console.log('\nSending test email...');
    const info = await transporter.sendMail({
      from: {
        name: FROM_NAME,
        address: FROM_EMAIL
      },
      to: RECIPIENT,
      subject,
      text,
      html
    });

    console.log('\n✅ Email sent successfully!');
    console.log('Message ID:', info.messageId);
    return true;
  } catch (error) {
    console.error('\n❌ Failed to send email:', error);
    return false;
  }
}

// Execute the function
sendSimpleTestEmail()
  .then(success => {
    if (success) {
      console.log('\n✅ Email test completed successfully!');
      console.log('Check your inbox for the test email.');
    } else {
      console.log('\n❌ Email test failed.');
      console.log('\nTroubleshooting Tips:');
      console.log('1. Double-check the mail server hostname and port');
      console.log('2. Verify that your username and password are correct');
      console.log('3. Try different port options (common SMTP ports: 25, 465, 587, 2525)');
      console.log('4. Try a different mail service like Gmail for testing:');
      console.log('   - host: "smtp.gmail.com"');
      console.log('   - port: 587');
      console.log('   - secure: false');
      console.log('   - requireTLS: true');
      console.log('   - auth.user: "<EMAIL>"');
      console.log('   - auth.pass: "your-app-password" (create app password in Google Account)');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });