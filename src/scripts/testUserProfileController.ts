/**
 * Test script to validate the user profile controller logic
 * This test bypasses MongoDB connectivity by mocking the models and functions
 */

import { getUserProfile } from '../controllers/userController';
import { Request, Response } from 'express';
import mongoose from 'mongoose';

// Mock the User model
jest.mock('../models/User', () => ({
  User: {
    findById: jest.fn().mockImplementation(() => ({
      select: jest.fn().mockResolvedValue({
        _id: 'user123',
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        toObject: () => ({
          _id: 'user123',
          firstName: 'Test',
          lastName: 'User',
          email: '<EMAIL>'
        })
      })
    }))
  }
}));

// Mock the Subscription model
jest.mock('../models/Subscription', () => {
  const mockActiveSubscription = {
    _id: 'sub1',
    packageId: {
      _id: 'pkg1',
      name: 'Premium',
      type: 'standard',
      viewRequestLimit: 100,
      createRequestLimit: 50,
      price: 99.99,
      currency: 'USD',
      features: ['feature1', 'feature2']
    },
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    status: 'ACTIVE',
    paymentStatus: 'paid',
    remainingViewRequests: 100,
    remainingCreateRequests: 50
  };

  const mockCanceledSubscription = {
    _id: 'sub2',
    packageId: {
      _id: 'pkg2',
      name: 'Basic',
      type: 'standard',
      viewRequestLimit: 50,
      createRequestLimit: 25,
      price: 49.99,
      currency: 'USD',
      features: ['feature1']
    },
    startDate: new Date(),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    status: 'CANCELED',
    paymentStatus: 'paid',
    remainingViewRequests: 50,
    remainingCreateRequests: 25
  };

  return {
    Subscription: {
      find: jest.fn().mockImplementation((query) => {
        // Should only return the active subscription
        if (query.status === 'ACTIVE' && query.parentSubscriptionId === null) {
          return {
            populate: jest.fn().mockReturnValue({
              lean: jest.fn().mockResolvedValue([mockActiveSubscription])
            })
          };
        }
        // Should return empty array for addon subscriptions since we don't have any
        if (query.status === 'ACTIVE' && query.parentSubscriptionId && query.parentSubscriptionId.$ne === null) {
          return {
            populate: jest.fn().mockReturnValue({
              lean: jest.fn().mockResolvedValue([])
            })
          };
        }
        return {
          populate: jest.fn().mockReturnValue({
            lean: jest.fn().mockResolvedValue([])
          })
        };
      })
    }
  };
});

// Mock mongoose
jest.mock('mongoose', () => ({
  Types: {
    ObjectId: jest.fn().mockImplementation(id => id)
  }
}));

// Test the getUserProfile controller
async function testGetUserProfile() {
  console.log('Testing getUserProfile controller...');

  // Mock request object
  const req = {
    user: { _id: 'user123' },
    headers: { 'accept-language': 'en' }
  } as unknown as Request;

  // Mock response object
  const res:any = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn(data => {
      console.log('Response:', JSON.stringify(data, null, 2));
      return data;
    })
  } as unknown as Response;

  // Call the controller
  await getUserProfile(req, res);

  // Verify the response
  if (res.json.mock.calls.length > 0) {
    const responseData = res.json.mock.calls[0][0];

    console.log('\n===== VERIFICATION =====');

    // Check if we have subscription details
    if (responseData.data.subscriptionDetails) {
      console.log('✅ Subscription details included in response');

      // Check if we have a standard subscription
      if (responseData.data.subscriptionDetails.standard) {
        console.log('✅ Standard subscription found in profile');
        console.log(`✅ Status: ${responseData.data.subscriptionDetails.standard.status}`);

        // Verify it's the active one
        if (responseData.data.subscriptionDetails.standard.status === 'ACTIVE') {
          console.log('✅ The standard subscription is ACTIVE as expected');
        } else {
          console.error(`❌ The standard subscription has unexpected status: ${responseData.data.subscriptionDetails.standard.status}`);
        }
      } else {
        console.error('❌ Standard subscription not found in profile');
      }

      // No addon subscriptions should be returned (empty array)
      if (Array.isArray(responseData.data.subscriptionDetails.addons) &&
          responseData.data.subscriptionDetails.addons.length === 0) {
        console.log('✅ No addon subscriptions found as expected');
      } else {
        console.error('❌ Unexpected addon subscriptions returned');
      }
    } else {
      console.error('❌ No subscription details found in the profile');
    }
  } else {
    console.error('❌ No response returned from controller');
  }
}

// Run the test
testGetUserProfile().catch(console.error);