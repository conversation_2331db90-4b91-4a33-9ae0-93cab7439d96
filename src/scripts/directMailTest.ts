import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { config } from '../config';

// Load environment variables
dotenv.config();

async function sendTestEmail() {
  const toEmail = '<EMAIL>';
  const subject = 'Test Email from E-Export City';
  const text = 'This is a test email to verify the email functionality.';

  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <div style="background-color: #2C7A7B; padding: 20px; text-align: center;">
        <h1 style="color: white; margin: 0;">E-Export City</h1>
      </div>
      <div style="padding: 20px; border: 1px solid #e5e5e5; border-top: none;">
        <h2 style="color: #2C7A7B;">Test Email</h2>
        <p>Hello,</p>
        <p>This is a test email sent from the E-Export City platform to verify email functionality.</p>
        <p>If you received this email, the email system is working correctly.</p>
        <p style="color: #718096; font-size: 0.9em;">
          Best regards,<br>
          E-Export City Team
        </p>
      </div>
    </div>
  `;

  // Create a dedicated transporter for this test
  const transporter = nodemailer.createTransport({
    host: config.smtp.host,
    port: config.smtp.port,
    secure: config.smtp.port === 465,
    auth: {
      user: config.smtp.user,
      pass: config.smtp.pass
    },
    // Shorter timeouts for testing
    connectionTimeout: 30000,
    greetingTimeout: 30000,
    socketTimeout: 60000,
    tls: {
      rejectUnauthorized: false
    },
    // Simple format for logging
    logger: true,
    debug: true
  });

  try {
    console.log('Mail Configuration:');
    console.log(`- Host: ${config.smtp.host}`);
    console.log(`- Port: ${config.smtp.port}`);
    console.log(`- User: ${config.smtp.user}`);
    console.log(`- Secure: ${config.smtp.port === 465}`);

    // Test the connection first
    console.log('Verifying connection...');
    await transporter.verify();
    console.log('Connection verified successfully');

    // Send the email
    console.log(`Sending test email to ${toEmail}...`);
    const mailOptions = {
      from: `"E-Export City Test" <${config.smtp.user}>`,
      to: toEmail,
      subject,
      text,
      html
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Test email sent successfully!');
    console.log('Message ID:', info.messageId);

    // Close the transporter
    transporter.close();

    return { success: true, message: 'Email sent successfully', messageId: info.messageId };
  } catch (error) {
    console.error('Failed to send test email:', error);

    // Try to close the transporter
    try {
      transporter.close();
    } catch (e) {
      console.error('Error closing transporter:', e);
    }

    return { success: false, message: 'Failed to send email', error };
  }
}

// Execute the function
sendTestEmail()
  .then(result => {
    console.log('Result:', result);
    process.exit(result.success ? 0 : 1);
  })
  .catch(err => {
    console.error('Error in test script:', err);
    process.exit(1);
  });
