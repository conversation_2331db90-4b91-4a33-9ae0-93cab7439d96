import mongoose from 'mongoose';
import { Item } from '../models/Item';
import { Category } from '../models/Category';
import { User } from '../models/User';
import dotenv from 'dotenv';
import path from 'path';

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

const seedItems = async () => {
  try {
    // Connect to MongoDB with authentication
    const MONGO_URI = process.env.MONGO_URI || '****************************************************************';
    await mongoose.connect(MONGO_URI);
    console.log('Connected to MongoDB');

    // Create test user if not exists
    const testUser = await User.findOneAndUpdate(
      { email: '<EMAIL>' },
      {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'password123',
        isActive: true
      },
      { upsert: true, new: true }
    );

    // Get some categories
    const productCategory = await Category.findOne({ type: 'Product', level: 3 });
    const serviceCategory = await Category.findOne({ type: 'Service', level: 3 });

    if (!productCategory || !serviceCategory) {
      console.error('Categories not found. Please run seedCategories.ts first.');
      process.exit(1);
    }

    // Sample items data
    const items = [
      {
        name: 'Sample Product 1',
        description: '<p>This is a sample product description</p>',
        price: 99.99,
        category: productCategory._id,
        type: 'product',
        isApproved: true,
        isActive: true,
        owner: testUser._id,
        images: ['https://via.placeholder.com/300']
      },
      {
        name: 'Sample Product 2',
        description: '<p>Another sample product description</p>',
        price: 149.99,
        category: productCategory._id,
        type: 'product',
        isApproved: true,
        isActive: true,
        owner: testUser._id,
        images: ['https://via.placeholder.com/300']
      },
      {
        name: 'Sample Service 1',
        description: '<p>This is a sample service description</p>',
        price: 199.99,
        category: serviceCategory._id,
        type: 'service',
        isApproved: true,
        isActive: true,
        owner: testUser._id,
        images: ['https://via.placeholder.com/300']
      },
      {
        name: 'Sample Service 2',
        description: '<p>Another sample service description</p>',
        price: 299.99,
        category: serviceCategory._id,
        type: 'service',
        isApproved: true,
        isActive: true,
        owner: testUser._id,
        images: ['https://via.placeholder.com/300']
      }
    ];

    // Clear existing items
    await Item.deleteMany({});
    console.log('Cleared existing items');

    // Insert new items
    const insertedItems = await Item.insertMany(items);
    console.log(`Inserted ${insertedItems.length} test items`);

    console.log('Test items seeded successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding test items:', error);
    process.exit(1);
  }
};

seedItems();
