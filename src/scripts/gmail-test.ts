/**
 * Gmail SMTP Email Test
 *
 * This script provides a straightforward way to test email sending
 * using Gmail's SMTP server.
 *
 * IMPORTANT: You must use an "App Password" for Gmail, not your regular password!
 * To create an App Password:
 * 1. Enable 2-Step Verification in your Google Account
 * 2. Go to https://myaccount.google.com/apppasswords
 * 3. Select "Mail" and "Other (Custom name)"
 * 4. Generate and use the 16-character password provided
 *
 * Run with: npx ts-node src/scripts/gmail-test.ts
 */

import nodemailer from 'nodemailer';

// ======= CONFIGURE THESE VALUES DIRECTLY =======
// Recipient email address
const RECIPIENT = '<EMAIL>';

// Gmail account details
const GMAIL_USER = '<EMAIL>';  // Your Gmail address
const GMAIL_APP_PASSWORD = 'your-app-password-here'; // Your Gmail App Password

// "From" details
const FROM_NAME = 'eExportCity Test';
const FROM_EMAIL = GMAIL_USER;

// ======= END OF CONFIGURATION =======

// Gmail server configuration
const MAIL_CONFIG = {
  host: 'smtp.gmail.com',
  port: 587,
  secure: false,
  requireTLS: true,
  auth: {
    user: GMAIL_USER,
    pass: GMAIL_APP_PASSWORD
  },
  connectionTimeout: 60000,
  greetingTimeout: 60000,
  socketTimeout: 90000,
  tls: {
    rejectUnauthorized: true // Gmail requires proper SSL validation
  }
};

async function sendGmailTest() {
  console.log('Gmail SMTP Email Test');
  console.log('====================');
  console.log(`\nMail Configuration:`);
  console.log(`- Service: Gmail`);
  console.log(`- Username: ${MAIL_CONFIG.auth.user}`);
  console.log(`- App Password: ${MAIL_CONFIG.auth.pass.length > 0 ? '******' : 'NOT SET (PLEASE CONFIGURE PASSWORD)'}`);
  console.log(`\nSending to: ${RECIPIENT}`);

  // Check for placeholder values
  if (GMAIL_USER === '<EMAIL>' || GMAIL_APP_PASSWORD === 'your-app-password-here') {
    console.error('\n❌ ERROR: You need to set your actual Gmail address and App Password in the script!');
    console.log('   Open gmail-test.ts and replace the placeholder values with your actual Gmail details.');
    console.log('   Remember to use an App Password, not your regular Gmail password!');
    return false;
  }

  // Create a transporter with Gmail configuration
  const transporter = nodemailer.createTransport(MAIL_CONFIG);

  console.log('\nVerifying connection to Gmail...');
  try {
    await transporter.verify();
    console.log('✅ Gmail connection verified successfully!');
  } catch (error) {
    console.error('❌ Gmail connection verification failed:', error);
    console.log('Continuing with email send attempt anyway...');
  }

  // Create a simple email
  const timestamp = new Date().toISOString();
  const subject = `Gmail Test Email from eExportCity (${timestamp})`;
  const text = `This is a test email sent via Gmail SMTP at ${timestamp}.`;
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Gmail Test Email</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email sent from the eExportCity platform via Gmail.</p>
            <p>If you've received this email, it means our Gmail configuration is working correctly.</p>
            <p>Test timestamp: ${timestamp}</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  try {
    console.log('\nSending test email via Gmail...');
    const info = await transporter.sendMail({
      from: {
        name: FROM_NAME,
        address: FROM_EMAIL
      },
      to: RECIPIENT,
      subject,
      text,
      html
    });

    console.log('\n✅ Email sent successfully via Gmail!');
    console.log('Message ID:', info.messageId);
    return true;
  } catch (error) {
    console.error('\n❌ Failed to send email via Gmail:', error);
    return false;
  }
}

// Execute the function
sendGmailTest()
  .then(success => {
    if (success) {
      console.log('\n✅ Gmail email test completed successfully!');
      console.log('Check your inbox for the test email.');
    } else {
      console.log('\n❌ Gmail email test failed.');
      console.log('\nTroubleshooting Tips:');
      console.log('1. Make sure you\'re using an App Password, not your regular Gmail password');
      console.log('2. Verify that 2-Step Verification is enabled for your Google Account');
      console.log('3. Check that you\'ve entered your Gmail address correctly');
      console.log('4. Try generating a new App Password');
      console.log('5. Check if your Gmail account has any security restrictions');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });