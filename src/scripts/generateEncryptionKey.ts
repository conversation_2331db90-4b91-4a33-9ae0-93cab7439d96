import crypto from 'crypto';

console.log('🔐 Generating secure encryption key for card data...\n');

// Generate a 32-byte (256-bit) key
const key = crypto.randomBytes(32).toString('hex');

console.log('Generated encryption key (64 hex characters):');
console.log(`CARD_ENCRYPTION_KEY=${key}`);
console.log('\n⚠️  IMPORTANT: Add this to your .env file and keep it secure!');
console.log('⚠️  Never commit this key to version control!');
console.log('⚠️  Losing this key means you cannot decrypt stored card tokens!\n');

// Also generate a base64 version if needed
const keyBase64 = crypto.randomBytes(32).toString('base64');
console.log('Alternative base64 format (if preferred):');
console.log(`CARD_ENCRYPTION_KEY_BASE64=${keyBase64}`);
console.log('\n✅ Key generation complete!');