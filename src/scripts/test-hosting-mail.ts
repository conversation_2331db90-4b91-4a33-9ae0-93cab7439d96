/**
 * Test Email Sending with hosting.com.tr Mail Server
 *
 * This script tests the email sending functionality specifically using
 * your hosting.com.tr mail server configuration.
 *
 * Run with: npx ts-node src/scripts/test-hosting-mail.ts
 */

import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { config } from '../config';

// Initialize environment variables
dotenv.config();

const TEST_RECIPIENT = '<EMAIL>';

async function testHostingMailServer() {
  console.log('Starting email test with hosting.com.tr mail server...');
  console.log(`Sending test email to: ${TEST_RECIPIENT}`);

  console.log('Mail server configuration:');
  console.log(`- Host: ${config.smtp.host}`);
  console.log(`- Port: ${config.smtp.port}`);
  console.log(`- Secure: ${config.smtp.secure}`);
  console.log(`- User: ${config.smtp.user}`);
  console.log(`- Auth: ${config.smtp.pass ? 'Password provided' : 'No password provided'}`);

  // Create a transporter using the hosting.com.tr configuration
  const transporter = nodemailer.createTransport({
    host: config.smtp.host,
    port: config.smtp.port,
    secure: config.smtp.secure,
    auth: {
      user: config.smtp.user,
      pass: config.smtp.pass
    },
    connectionTimeout: 60000, // 60 seconds
    greetingTimeout: 60000,
    socketTimeout: 90000,
    tls: {
      rejectUnauthorized: false
    },
    debug: true, // Enable debug output
    logger: true // Log information to console
  });

  // First, verify the connection
  console.log('Verifying connection to mail server...');
  try {
    await transporter.verify();
    console.log('✅ Mail server connection verified successfully!');
  } catch (error) {
    console.error('❌ Mail server connection verification failed:', error);
    console.log('Continuing with email send attempt anyway...');
  }

  // Email content
  const subject = 'Test Email from eExportCity Platform (Hosting Server)';
  const text = 'This is a test email sent from the eExportCity platform to verify direct hosting.com.tr email sending functionality.';
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>eExportCity Platform</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email sent directly from the hosting.com.tr mail server to verify our email sending functionality.</p>
            <p>If you've received this email, it means our direct mail server configuration is working correctly.</p>
            <p>Test timestamp: ${new Date().toISOString()}</p>
            <p>Thank you!</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} eExportCity. All rights reserved.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  // Try both primary and alternative port configurations
  const configurations = [
    {
      name: "Primary configuration",
      config: {
        host: config.smtp.host,
        port: config.smtp.port,
        secure: config.smtp.secure
      }
    },
    {
      name: "Alternative port configuration",
      config: {
        host: config.smtp.host,
        port: 587,
        secure: false,
        requireTLS: true
      }
    }
  ];

  let success = false;

  for (const configuration of configurations) {
    console.log(`\nTrying ${configuration.name}...`);
    const altTransporter = nodemailer.createTransport({
      ...configuration.config,
      auth: {
        user: config.smtp.user,
        pass: config.smtp.pass
      },
      connectionTimeout: 60000,
      greetingTimeout: 60000,
      socketTimeout: 90000,
      tls: {
        rejectUnauthorized: false
      },
      debug: true,
      logger: true
    });

    try {
      // Send the email
      console.log('Sending test email...');
      const info = await altTransporter.sendMail({
        from: `"eExportCity" <${config.smtp.user}>`,
        to: TEST_RECIPIENT,
        subject,
        text,
        html
      });

      console.log(`✅ Email sent successfully with ${configuration.name}!`);
      console.log('Message ID:', info.messageId);
      success = true;
      break; // Exit the loop if successful
    } catch (error) {
      console.error(`❌ Failed to send email with ${configuration.name}:`, error);
      console.log('Trying next configuration if available...');
    }
  }

  return success;
}

// Execute the test function
testHostingMailServer()
  .then((success) => {
    if (success) {
      console.log('Test completed successfully. At least one configuration worked!');
      process.exit(0);
    } else {
      console.log('All configurations failed. Email could not be sent directly through hosting.com.tr mail server.');
      console.log('Please check your mail server settings, network configuration, and firewall rules.');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('Test failed with error:', error);
    process.exit(1);
  });