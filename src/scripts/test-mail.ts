/**
 * Test Email Sending Script (TypeScript Version)
 *
 * This script tests the email sending functionality by sending a test email
 * to the specified recipient using the emailService.
 *
 * Run with: ts-node src/scripts/test-mail.ts
 * Or compile and run: npm run build && node dist/scripts/test-mail.js
 */

import { sendEmail } from '../services/emailService';
import dotenv from 'dotenv';

// Initialize environment variables
dotenv.config();

const TEST_RECIPIENT = '<EMAIL>';

async function sendTestEmail() {
  console.log('Starting email test...');
  console.log(`Sending test email to: ${TEST_RECIPIENT}`);

  const subject = 'Test Email from eExportCity Platform';
  const text = 'This is a test email sent from the eExportCity platform to verify email sending functionality.';
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>eExportCity Platform</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email sent from the eExportCity platform to verify that our email sending functionality is working correctly.</p>
            <p>If you've received this email, it means our email service is configured properly.</p>
            <p>No action is required on your part.</p>
            <p>Thank you!</p>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
            <p>© ${new Date().getFullYear()} eExportCity. All rights reserved.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  try {
    const result = await sendEmail(TEST_RECIPIENT, subject, text, html);
    console.log('✅ Email sent successfully!');
    console.log('Message ID:', result.messageId);
    return result;
  } catch (error) {
    console.error('❌ Failed to send email:', error);
    throw error;
  }
}

// Execute the test function
sendTestEmail()
  .then(() => {
    console.log('Test completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Test failed with error:', error);
    process.exit(1);
  });