/**
 * Environment Config Email Test
 *
 * This script tests email sending using the exact credentials
 * from your .env file, with no need to manually configure anything.
 *
 * Run with: npx ts-node src/scripts/env-config-test.ts
 */

import nodemailer from 'nodemailer';
import SMTPTransport from 'nodemailer/lib/smtp-transport';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// ======= CONFIGURATION FROM .ENV FILE =======
// Recipient email address
const RECIPIENT = '<EMAIL>';

// Use exact values from .env file
const MAIL_CONFIG: SMTPTransport.Options = {
  host: process.env.EMAIL_SERVER_HOST,
  port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
  secure: false, // For port 587, secure should be false
  auth: process.env.EMAIL_SERVER_USER && process.env.EMAIL_SERVER_PASS ? {
    user: process.env.EMAIL_SERVER_USER,
    pass: process.env.EMAIL_SERVER_PASS
  } : undefined,
  // Connection settings
  connectionTimeout: 60000,
  greetingTimeout: 60000,
  socketTimeout: 90000,
  // TLS settings for port 587
  requireTLS: true, // Require TLS for port 587
  tls: {
    rejectUnauthorized: false, // Accept self-signed certs
    minVersion: 'TLSv1' // Try older TLS version to fix "wrong version number" error
  }
};

// "From" details from .env
const FROM_EMAIL = process.env.EMAIL_FROM ||
  `eExportCity <${process.env.EMAIL_SERVER_USER}>`;

// ======= END OF CONFIGURATION =======

async function sendEnvConfigTest() {
  console.log('Environment Config Email Test');
  console.log('============================');

  // Verify .env variables are loaded
  if (!process.env.EMAIL_SERVER_HOST || !process.env.EMAIL_SERVER_USER || !process.env.EMAIL_SERVER_PASS) {
    console.error('\n❌ ERROR: Missing required environment variables in .env file!');
    console.log('Required variables:');
    console.log('- EMAIL_SERVER_HOST:', process.env.EMAIL_SERVER_HOST ? '✓' : '✗ Missing');
    console.log('- EMAIL_SERVER_PORT:', process.env.EMAIL_SERVER_PORT ? '✓' : '✗ Missing (will default to 587)');
    console.log('- EMAIL_SERVER_USER:', process.env.EMAIL_SERVER_USER ? '✓' : '✗ Missing');
    console.log('- EMAIL_SERVER_PASS:', process.env.EMAIL_SERVER_PASS ? '✓' : '✗ Missing');
    console.log('- EMAIL_FROM:', process.env.EMAIL_FROM ? '✓' : '✗ Missing (will use default)');
    console.log('\nPlease check your .env file and make sure all required variables are set.');
    return false;
  }

  // Password display for logging
  const hasPassword = process.env.EMAIL_SERVER_PASS && process.env.EMAIL_SERVER_PASS.length > 0;

  console.log(`\nMail Configuration from .env:`);
  console.log(`- Host: ${MAIL_CONFIG.host}`);
  console.log(`- Port: ${MAIL_CONFIG.port}`);
  console.log(`- Secure: ${MAIL_CONFIG.secure}`);
  console.log(`- Username: ${process.env.EMAIL_SERVER_USER}`);
  console.log(`- Password: ${hasPassword ? '******' : 'NOT SET'}`);
  console.log(`- From: ${FROM_EMAIL}`);
  console.log(`\nSending to: ${RECIPIENT}`);

  // Create a transporter with the config from .env
  const transporter = nodemailer.createTransport(MAIL_CONFIG);

  console.log('\nVerifying connection to mail server...');
  try {
    await transporter.verify();
    console.log('✅ Mail server connection verified successfully!');
  } catch (error) {
    console.error('❌ Mail server connection verification failed:', error);
    console.log('Attempting to send email anyway...');
  }

  // Create a simple email
  const timestamp = new Date().toISOString();
  const subject = `ENV Config Test Email (${timestamp})`;
  const text = `This is a test email sent at ${timestamp} using .env configuration.`;
  const html = `
    <html>
      <head>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
          .header { background-color: #f5f5f5; padding: 10px; border-radius: 5px 5px 0 0; }
          .content { padding: 20px; }
          .footer { background-color: #f5f5f5; padding: 10px; border-radius: 0 0 5px 5px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>Environment Config Test Email</h2>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>This is a test email sent using configuration from your .env file.</p>
            <p>If you've received this email, it means your email configuration in .env is working correctly.</p>
            <p>Test timestamp: ${timestamp}</p>
            <p>Configuration:</p>
            <ul>
              <li>Host: ${MAIL_CONFIG.host}</li>
              <li>Port: ${MAIL_CONFIG.port}</li>
              <li>User: ${process.env.EMAIL_SERVER_USER}</li>
            </ul>
          </div>
          <div class="footer">
            <p>This is an automated message. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
    </html>
  `;

  try {
    console.log('\nSending test email...');
    const info = await transporter.sendMail({
      from: FROM_EMAIL,
      to: RECIPIENT,
      subject,
      text,
      html
    });

    console.log('\n✅ Email sent successfully!');
    console.log('Message ID:', info.messageId);
    return true;
  } catch (error: any) {
    console.error('\n❌ Failed to send email:', error);

    // Show detailed error information
    if (error.code) {
      console.log(`Error code: ${error.code}`);
    }

    // Try with debug mode if sending failed
    if (error) {
      console.log('\nAttempting with debug mode to get more information...');

      // Create a debug transporter with logger
      const debugConfig: SMTPTransport.Options = {
        ...MAIL_CONFIG,
        logger: true
      };

      const debugTransporter = nodemailer.createTransport(debugConfig);

      try {
        console.log('Verifying connection with debug enabled...');
        await debugTransporter.verify();
        console.log('Debug verification succeeded.');
      } catch (debugError) {
        console.log('Debug verification failed, but provided more information.');
      }
    }

    return false;
  }
}

// Execute the function
sendEnvConfigTest()
  .then(success => {
    if (success) {
      console.log('\n✅ Email test completed successfully!');
      console.log('Check your inbox for the test email.');
    } else {
      console.log('\n❌ Email test failed.');
      console.log('\nTroubleshooting Tips:');
      console.log('1. Double-check your .env file for correct configuration');
      console.log('2. Verify that your mail server is accessible');
      console.log('3. Try different STARTTLS settings:');
      console.log('   For port 587: secure=false, requireTLS=true');
      console.log('   For port 465: secure=true, requireTLS=false');
      console.log('4. Contact your hosting provider to confirm the correct mail settings');
      console.log('5. Try the gmail-test.ts or outlook-test.ts scripts as alternatives');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
    process.exit(1);
  });