import dotenv from 'dotenv';
dotenv.config();

import { 
  sendValidationEmail,
  sendPasswordChangeSuccessEmail,
  sendWelcomeEmail,
  sendPackagePurchaseEmail,
  sendPackageCancellationEmail,
  sendPackageRenewalEmail,
  sendPackageExpirationEmail,
  sendEmail
} from '../services/emailService';
import { baseEmailTemplate, emailButton, alertBox, infoBox } from '../services/emailTemplates';

const targetEmail = '<EMAIL>';

async function sendAllEmailExamples() {
  console.log('Starting to send email examples to:', targetEmail);
  
  try {
    // 1. Email Validation
    console.log('1. Sending Email Validation example...');
    await sendValidationEmail(
      targetEmail,
      'https://example.com/validate?token=example-token-123',
      'tr'
    );
    console.log('✓ Email Validation sent');
    
    // Wait a bit between emails
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 2. Password Change Success
    console.log('2. Sending Password Change Success example...');
    await sendPasswordChangeSuccessEmail(targetEmail, 'tr');
    console.log('✓ Password Change Success sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 3. Welcome Email
    console.log('3. Sending Welcome Email example...');
    await sendWelcomeEmail(
      targetEmail,
      'Umut',
      'Korkmaz',
      'tr'
    );
    console.log('✓ Welcome Email sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 4. Package Purchase
    console.log('4. Sending Package Purchase example...');
    await sendPackagePurchaseEmail(
      targetEmail,
      'Umut',
      'Korkmaz',
      'Premium Business',
      1999.99,
      'TRY',
      new Date(),
      new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days later
      'tr'
    );
    console.log('✓ Package Purchase sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 5. Package Cancellation
    console.log('5. Sending Package Cancellation example...');
    await sendPackageCancellationEmail(
      targetEmail,
      'Umut',
      'Korkmaz',
      'Premium Business',
      'tr'
    );
    console.log('✓ Package Cancellation sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 6. Package Renewal
    console.log('6. Sending Package Renewal example...');
    await sendPackageRenewalEmail(
      targetEmail,
      'Umut',
      'Korkmaz',
      'Premium Business',
      1999.99,
      'TRY',
      new Date(),
      new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      'tr'
    );
    console.log('✓ Package Renewal sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 7. Package Expiration (3 days)
    console.log('7. Sending Package Expiration (3 days) example...');
    await sendPackageExpirationEmail(
      targetEmail,
      'Umut',
      'Korkmaz',
      'Premium Business',
      new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      3,
      'tr'
    );
    console.log('✓ Package Expiration (3 days) sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 8. Package Expiration (7 days)
    console.log('8. Sending Package Expiration (7 days) example...');
    await sendPackageExpirationEmail(
      targetEmail,
      'Umut',
      'Korkmaz',
      'Starter Package',
      new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      7,
      'tr'
    );
    console.log('✓ Package Expiration (7 days) sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 9. New User Registration Notification (Store Owner)
    console.log('9. Sending Store Owner Notification example...');
    const storeNotificationContent = `
      <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #667EEA 0%, #764BA2 100%); border-radius: 50%; line-height: 100px;">
          <span style="font-size: 50px; color: white;">👥</span>
        </div>
      </div>
      
      <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Yeni İş Fırsatı!</h2>
      
      <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 20px 0;">
        Sayın Umut Korkmaz,
      </p>
      
      <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
        Ürünlerinizin bulunduğu kategorilerde yeni bir kullanıcı kayıt oldu. Bu potansiyel bir iş fırsatı olabilir!
      </p>
      
      ${alertBox('Bu kullanıcı sizin ürünlerinizle ilgileniyor olabilir. Hemen iletişime geçin!', 'success')}
      
      ${infoBox('Yeni Kullanıcı Bilgileri', [
        { label: 'İsim', value: 'Ahmet Yılmaz' },
        { label: 'E-posta', value: '<EMAIL>' },
        { label: 'Ülke', value: 'Türkiye' },
        { label: 'Şehir', value: 'İstanbul' },
        { label: 'Kategoriler', value: 'Tekstil, Hazır Giyim, Deri Ürünleri' }
      ])}
      
      ${emailButton('Profilimi Güncelle', 'https://example.com/profile')}
    `;
    
    await sendEmail(
      targetEmail,
      '🎉 Kategorinizde Yeni Kullanıcı - E-Export City',
      'Kategorinizde yeni kullanıcı kaydı',
      baseEmailTemplate(storeNotificationContent)
    );
    console.log('✓ Store Owner Notification sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 10. New Message Notification
    console.log('10. Sending Message Notification example...');
    const messageNotificationContent = `
      <h2 style="color: #2D3748; font-size: 24px; font-weight: 700; margin: 0 0 20px 0;">Yeni Mesaj</h2>
      
      <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 20px 0;">
        Mehmet Öz size bir mesaj gönderdi: Merhaba, ürününüz hakkında bilgi almak istiyorum...
      </p>
      
      ${emailButton('Detayları Görüntüle', 'https://example.com/messages/123')}
      
      ${alertBox('Bu bildirim önemli bir güncelleme içeriyor. Lütfen en kısa sürede kontrol edin.', 'warning')}
    `;
    
    await sendEmail(
      targetEmail,
      'Yeni Mesaj',
      'Mehmet Öz size bir mesaj gönderdi',
      baseEmailTemplate(messageNotificationContent)
    );
    console.log('✓ Message Notification sent');
    
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 11. Auto-renewal Failed (No Card)
    console.log('11. Sending Auto-renewal Failed (No Card) example...');
    const renewalFailedContent = `
      <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; width: 100px; height: 100px; background-color: #FED7D7; border-radius: 50%; line-height: 100px;">
          <span style="font-size: 50px; color: #E53E3E;">⚠️</span>
        </div>
      </div>
      
      <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Otomatik Yenileme Başarısız</h2>
      
      <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
        Sayın Umut Korkmaz,
      </p>
      
      ${alertBox('Otomatik yenileme için kayıtlı bir kartınız bulunmuyor!', 'error')}
      
      <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 20px 0;">
        <strong>Premium Business</strong> paketinizi otomatik yenileyemedik çünkü hesabınızda otomatik ödeme için tanımlı bir kart bulunmuyor.
      </p>
      
      ${infoBox('Adımlar', [
        { label: '1. Adım', value: 'Hesabınıza giriş yapın' },
        { label: '2. Adım', value: 'Otomatik ödeme için kart ekleyin' },
        { label: '3. Adım', value: 'Aboneliğinizi yeniden başlatın' }
      ])}
      
      ${emailButton('Kart Ekle', 'https://example.com/profile/cards')}
    `;
    
    await sendEmail(
      targetEmail,
      '⚠️ Abonelik Yenileme Başarısız - E-Export City',
      'Abonelik yenileme başarısız',
      baseEmailTemplate(renewalFailedContent)
    );
    console.log('✓ Auto-renewal Failed sent');
    
    console.log('\n✅ All email examples have been sent successfully!');
    console.log('Please check your inbox at:', targetEmail);
    
  } catch (error) {
    console.error('Error sending email examples:', error);
  }
  
  process.exit(0);
}

// Run the script
sendAllEmailExamples();