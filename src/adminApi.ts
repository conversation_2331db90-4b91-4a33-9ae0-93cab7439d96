import axios from 'axios';
import { IUser, IAuthResponse } from './types/user';
import { CategoryUpdateInput, ICategory } from './types/category';
import { IItem } from './types/item';

const adminApi = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  withCredentials: true
});

// Helper functions to manage admin token
export const setAdminToken = (token: string) => {
  localStorage.setItem('adminToken', token);
};

export const getAdminToken = () => {
  return localStorage.getItem('adminToken');
};

export const removeAdminToken = () => {
  localStorage.removeItem('adminToken');
};

// Add interceptor to inject the admin token and language
adminApi.interceptors.request.use((config: any) => {
  const language = localStorage.getItem('i18nextLng') || 'tr';
  config.headers['Accept-Language'] = language;

  if (!config.headers) {
    config.headers = {};
  }

  const token = getAdminToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  return config;
}, (error) => {
  return Promise.reject(error);
});

// Add response interceptor to handle 401 errors
adminApi.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      removeAdminToken(); // Just remove the token, let AuthContext handle the redirect
    }
    return Promise.reject(error);
  }
);

// Admin Authentication
export const loginAdmin = async (adminData: { email: string; password: string }): Promise<IAuthResponse> => {
  const response = await adminApi.post('/admin/login', adminData);
  if (response.data.token) {
    setAdminToken(response.data.token);
  }
  return response.data;
};

export const registerAdmin = async (adminData: { name: string; email: string; password: string }): Promise<IAuthResponse> => {
  const response = await adminApi.post('/auth/admin/register', adminData);
  return response.data;
};

// Admin Profile
export const getAdminProfile = async (): Promise<IUser> => {
  const response = await adminApi.get('/admin/profile');
  return response.data;
};

// Admin Dashboard
export const getDashboardStats = async (): Promise<{
  userCount: number;
  itemCount: number;
  productCount: number;
  serviceCount: number;
}> => {
  const response = await adminApi.get('/admin/dashboard/stats');
  return response.data;
};

// User Management
export const getUsers = async (): Promise<IUser[]> => {
  const response = await adminApi.get('/admin/users');
  return response.data;
};

export const manageUser = async (userId: string, action: 'activate' | 'deactivate' | 'delete'): Promise<IUser> => {
  const response = await adminApi.post(`/admin/users/${userId}/${action}`);
  return response.data;
};

// Package Management
export const getAdminPackages = async () => {
  const response = await adminApi.get('/admin/packages');
  return response.data;
};

export const getPackage = async (packageId: string) => {
  const response = await adminApi.get(`/admin/packages/${packageId}`);
  return response.data;
};

export const createPackage = async (packageData: any) => {
  const response = await adminApi.post('/admin/packages', packageData);
  return response.data;
};

export const updatePackage = async (packageId: string, packageData: any) => {
  const response = await adminApi.put(`/admin/packages/${packageId}`, packageData);
  return response.data;
};

export const deletePackage = async (packageId: string) => {
  const response = await adminApi.delete(`/admin/packages/${packageId}`);
  return response.data;
};

// HomeAds Management
export const getHomeAds = async () => {
  const response = await adminApi.get('/admin/home-ads');
  return response.data;
};

export const updateHomeAdStatus = async (id: string, status: string) => {
  const response = await adminApi.put(`/admin/home-ads/${id}`, { status });
  return response.data;
};

export const deleteHomeAd = async (id: string) => {
  const response = await adminApi.delete(`/admin/home-ads/${id}`);
  return response.data;
};

// Apply package to user
export const applyPackageToUser = async (userId: string, packageId: string, duration: number) => {
  console.log('Applying package to user:', { userId, packageId, duration });
  try {
    // Use PUT instead of POST to avoid conflicts with the :action route pattern
    const response = await adminApi.put(`/admin/users/${userId}/apply-package`, {
      packageId,
      duration
    });
    console.log('Apply package response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error applying package to user:', error.response?.data || error.message);
    throw error;
  }
};

// Category Management
export const getAdminCategories = async (): Promise<ICategory[]> => {
  const response = await adminApi.get('/admin/categories');
  return response.data;
};

export const createCategory = async (categoryData: Partial<ICategory>): Promise<ICategory> => {
  const response = await adminApi.post('/admin/categories', categoryData);
  return response.data;
};

export const updateCategory = async (id: string, categoryData: CategoryUpdateInput): Promise<ICategory> => {
  const response = await adminApi.put(`/admin/categories/${id}`, categoryData);
  return response.data;
};

export const deleteCategory = async (id: string): Promise<void> => {
  await adminApi.delete(`/admin/categories/${id}`);
};

// Content Management
export const getPendingItems = async (): Promise<IItem[]> => {
  const response = await adminApi.get('/admin/items/pending');
  return response.data;
};

export const approveItem = async (itemId: string): Promise<IItem> => {
  const response = await adminApi.post(`/admin/items/${itemId}/approve`);
  return response.data;
};

export const rejectItem = async (itemId: string): Promise<IItem> => {
  const response = await adminApi.post(`/admin/items/${itemId}/reject`);
  return response.data;
};

export const manageContent = async (contentId: string, action: 'approve' | 'reject' | 'delete', reason?: string): Promise<IItem> => {
  const response = await adminApi.post<IItem>(`/admin/content/${action}`, { contentId, action, reason });
  return response.data;
};

// Ticket Management
export const getAdminTickets = async () => {
  const response = await adminApi.get('/admin/tickets');
  return response.data;
};

export const getAdminTicketById = async (id: string) => {
  const response = await adminApi.get(`/admin/tickets/${id}`);
  return response.data;
};

export const updateAdminTicket = async (id: string, data: any) => {
  const response = await adminApi.put(`/admin/tickets/${id}`, data);
  return response.data;
};

export const respondToTicket = async (id: string, resp: string) => {
  const response = await adminApi.post(`/admin/tickets/${id}/respond`, { response: resp });
  return response.data;
};

// Representative Management
export const getRepresentatives = async () => {
  const response = await adminApi.get('/admin/representatives');
  return response.data;
};

export const getRepresentativeById = async (id: string) => {
  const response = await adminApi.get(`/admin/representatives/${id}`);
  return response.data;
};

export const createRepresentative = async (data: any) => {
  const response = await adminApi.post('/admin/representatives', data);
  return response.data;
};

export const updateRepresentative = async (id: string, data: any) => {
  console.log('Updating representative with ID:', id);
  console.log('Update data:', data);

  const response = await adminApi.put(`/admin/representatives/${id}`, data);
  return response.data;
};

export const deleteRepresentative = async (id: string) => {
  const response = await adminApi.delete(`/admin/representatives/${id}`);
  return response.data;
};

// Products and Services
export const getItems = async () => {
  const response = await adminApi.get('/admin/items');
  return response.data;
};

export const createItem = async (data: any) => {
  const response = await adminApi.post('/admin/items', data);
  return response.data;
};

// File upload
export const uploadAdminFile = async (files: File | File[]) => {
  const formData = new FormData();
  if (Array.isArray(files)) {
    files.forEach(file => formData.append('files', file));
  } else {
    formData.append('files', files);
  }

  const response = await adminApi.post('/admin/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  return response.data;
};

// Get User Subscriptions
export const getUserSubscriptions = async (userId: string) => {
  try {
    const response = await adminApi.get(`/admin/users/${userId}/subscriptions`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting user subscriptions:', error.response?.data || error.message);
    throw error;
  }
};

// Store Management
export const getAdminStores = async () => {
  try {
    const response = await adminApi.get('/admin/stores');
    return response.data;
  } catch (error: any) {
    console.error('Error getting admin stores:', error.response?.data || error.message);
    throw error;
  }
};

export const getAdminStoreById = async (storeId: string) => {
  try {
    const response = await adminApi.get(`/admin/stores/${storeId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting store by ID:', error.response?.data || error.message);
    throw error;
  }
};

export const approveStore = async (storeId: string) => {
  try {
    const response = await adminApi.put(`/admin/stores/${storeId}/approve`);
    return response.data.data;
  } catch (error: any) {
    console.error('Error approving store:', error.response?.data || error.message);
    throw error;
  }
};

export const rejectStore = async (storeId: string, reason?: string) => {
  try {
    const response = await adminApi.put(`/admin/stores/${storeId}/reject`, { reason });
    return response.data.data;
  } catch (error: any) {
    console.error('Error rejecting store:', error.response?.data || error.message);
    throw error;
  }
};

export const toggleStoreStatus = async (storeId: string) => {
  try {
    const response = await adminApi.put(`/admin/stores/${storeId}/toggle-status`);
    return response.data.data;
  } catch (error: any) {
    console.error('Error toggling store status:', error.response?.data || error.message);
    throw error;
  }
};

export const deleteStore = async (storeId: string) => {
  try {
    const response = await adminApi.delete(`/admin/stores/${storeId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error deleting store:', error.response?.data || error.message);
    throw error;
  }
};

// LiveChat Admin API
export const getAdminActiveLiveChats = async (): Promise<any> => {
  try {
    const response = await adminApi.get('/admin/live-chat/active');
    return response.data;
  } catch (error: any) {
    console.error('Error getting active live chats:', error.response?.data || error.message);
    throw error;
  }
};

export const getAdminClosedLiveChats = async (page = 1, limit = 10): Promise<any> => {
  try {
    const response = await adminApi.get(`/admin/live-chat/closed?page=${page}&limit=${limit}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting closed live chats:', error.response?.data || error.message);
    throw error;
  }
};

export const getAdminArchivedLiveChats = async (page = 1, limit = 10): Promise<any> => {
  try {
    const response = await adminApi.get(`/admin/live-chat/archived?page=${page}&limit=${limit}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting archived live chats:', error.response?.data || error.message);
    throw error;
  }
};

export const getAdminLiveChatStatistics = async (): Promise<any> => {
  try {
    const response = await adminApi.get('/admin/live-chat/statistics');
    return response.data;
  } catch (error: any) {
    console.error('Error getting live chat statistics:', error.response?.data || error.message);
    throw error;
  }
};

export const archiveAdminLiveChat = async (chatId: string): Promise<any> => {
  try {
    const response = await adminApi.post(`/admin/live-chat/${chatId}/archive`);
    return response.data;
  } catch (error: any) {
    console.error('Error archiving live chat:', error.response?.data || error.message);
    throw error;
  }
};

// Get chat history for admins
export const getAdminLiveChatHistory = async (chatId: string): Promise<any> => {
  try {
    const response = await adminApi.get(`/admin/live-chat/${chatId}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting live chat history for admin:', error.response?.data || error.message);
    throw error;
  }
};

// Send chat message as admin
export const sendAdminLiveChatMessage = async (chatId: string, data: { content: string }): Promise<any> => {
  try {
    const response = await adminApi.post(`/admin/live-chat/${chatId}/message`, data);
    return response.data;
  } catch (error: any) {
    console.error('Error sending admin chat message:', error.response?.data || error.message);
    throw error;
  }
};

// Mark messages as read as admin
export const markAdminLiveChatMessagesAsRead = async (chatId: string): Promise<any> => {
  try {
    const response = await adminApi.post(`/admin/live-chat/${chatId}/read`, {});
    return response.data;
  } catch (error: any) {
    console.error('Error marking messages as read for admin:', error.response?.data || error.message);
    throw error;
  }
};

// Design Package Management
export const getDesignPackages = async () => {
  try {
    const response = await adminApi.get('/design-packages');
    return response.data;
  } catch (error: any) {
    console.error('Error getting design packages:', error.response?.data || error.message);
    throw error;
  }
};

export const getDesignPackageById = async (id: string) => {
  try {
    const response = await adminApi.get(`/design-packages/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error getting design package by ID:', error.response?.data || error.message);
    throw error;
  }
};

export const createDesignPackage = async (data: any) => {
  try {
    const response = await adminApi.post('/admin/design-packages', data);
    return response.data;
  } catch (error: any) {
    console.error('Error creating design package:', error.response?.data || error.message);
    throw error;
  }
};

export const updateDesignPackage = async (id: string, data: any) => {
  try {
    const response = await adminApi.put(`/admin/design-packages/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error('Error updating design package:', error.response?.data || error.message);
    throw error;
  }
};

export const deleteDesignPackage = async (id: string) => {
  try {
    const response = await adminApi.delete(`/admin/design-packages/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error deleting design package:', error.response?.data || error.message);
    throw error;
  }
};

export const getDesignPackageOrders = async () => {
  try {
    const response = await adminApi.get('/admin/design-packages/orders');
    return response.data;
  } catch (error: any) {
    console.error('Error getting design package orders:', error.response?.data || error.message);
    throw error;
  }
};

export const updateDesignPackageOrderStatus = async (orderId: string, status: string) => {
  try {
    const response = await adminApi.put(`/admin/design-packages/orders/${orderId}/status`, { status });
    return response.data;
  } catch (error: any) {
    console.error('Error updating design package order status:', error.response?.data || error.message);
    throw error;
  }
};

export { adminApi };