import { Server as SocketServer } from 'socket.io';
import { Server } from 'http';
import { User } from '../models/User';
import { netgsmService } from './netgsmService';
import { canSendSMS, updateSMSTracking } from './smsTrackingService';
import { Message } from '../models/Message';
import mongoose from 'mongoose';

let io: SocketServer;
const connectedUsers = new Map<string, string>(); // userId -> socketId

export const initializeSocket = (server: Server) => {
  // Configure socket options with proper CORS
  const socketOptions = {
    cors: {
      origin: ['http://localhost:5173','http://localhost:5174', 'http://localhost:3000', 'https://e-exportcity.com', 'https://www.e-exportcity.com'], // Allow development and production origins including www subdomain
      methods: ['GET', 'POST', 'OPTIONS'],
      credentials: true,
      allowedHeaders: ["Authorization", "Content-Type"]
    },
    path: "/socket.io",
    pingTimeout: 120000,       // Longer ping timeout (2 minutes)
    pingInterval: 30000,       // More frequent pings (30 seconds)
    connectTimeout: 45000,     // Longer connection timeout (45 seconds)
    maxHttpBufferSize: 1e8,    // Larger buffer size (100MB)
    transports: ['websocket', 'polling'] as any, // Support both for compatibility
    allowEIO3: true           // Enable Engine.io 3 compatibility
  };

  console.log('Socket.io server initializing with options:', JSON.stringify(socketOptions, null, 2));

  io = new SocketServer(server, socketOptions);

  // Debug the actual configuration used
  console.log('Socket.io server initialized with transports:', io.engine.opts.transports);
  console.log('Socket.io server cors settings:', io.engine.opts.cors);

  // Add detailed connection logging
  io.engine.on("headers", (headers: any, req: any) => {
    console.log(`Socket handshake headers from ${req.headers.origin || 'Unknown'}`);
  });

  io.engine.on("connection_error", (err: any) => {
    console.error('Socket connection error:', {
      code: err.code,
      message: err.message,
      context: err.context,
      origin: err.req?.headers?.origin || 'Unknown'
    });
  });

  io.on('connection', (socket) => {
    console.log(`New client connected, ID: ${socket.id}, Transport: ${socket.conn.transport.name}`);

    // Log disconnect events
    socket.on('disconnect', (reason) => {
      console.log(`Client ${socket.id} disconnected. Reason: ${reason}`);
    });

    // Log error events
    socket.on('error', (error) => {
      console.error(`Socket ${socket.id} error:`, error);
    });

    // Log connection upgrade events
    socket.conn.on('upgrade', (transport) => {
      console.log(`Socket ${socket.id} transport upgraded to ${transport.name}`);
    });

    socket.on('authenticate', (userId: string) => {
      if (userId) {
        socket.join(`user_${userId}`);
        connectedUsers.set(userId, socket.id);
        console.log(`User ${userId} authenticated`);
      }
    });

    socket.on('joinProductRoom', ({ productId, roomId }) => {
      if (productId && roomId) {
        socket.join(roomId);
        console.log(`Socket joined room: ${roomId}`);
      }
    });

    socket.on('leaveProductRoom', ({ productId, roomId }) => {
      if (productId && roomId) {
        socket.leave(roomId);
        console.log(`Socket left room: ${roomId}`);
      }
    });

    socket.on('message:send', async (data) => {
      try {
        console.log('Received message data:', data);

        const { content, senderId, recipientId, productId, roomId } = data;

        if (!content || !senderId || !recipientId || !productId || !roomId) {
          throw new Error('Missing required message data');
        }

        // Create and save the message
        const message = new Message({
          content,
          senderId: new mongoose.Types.ObjectId(senderId),
          recipientId: new mongoose.Types.ObjectId(recipientId),
          productId: new mongoose.Types.ObjectId(productId),
          roomId,
          createdAt: new Date(),
          read: false
        });

        await message.save();

        // Emit message to the room
        io.to(roomId).emit('message:received', {
          ...message.toObject(),
          sender: senderId
        });

        // Send notification to offline recipient
        const isRecipientOnline = connectedUsers.has(recipientId);
        if (!isRecipientOnline) {
          await sendMessageNotification(senderId, recipientId, content, productId);
        }

      } catch (error: any) {
        console.error('Error sending message:', error);
        socket.emit('message:error', {
          message: error.message || 'Failed to send message'
        });
      }
    });

    socket.on('disconnect', () => {
      for (const [userId, socketId] of connectedUsers.entries()) {
        if (socketId === socket.id) {
          connectedUsers.delete(userId);
          break;
        }
      }
      console.log('Client disconnected');
    });
  });

  return io;
};

export const getIO = () => {
  if (!io) {
    console.warn('Socket.io not initialized, returning null');
    return null;
  }
  return io;
};

export const isUserOnline = (userId: string): boolean => {
  return connectedUsers.has(userId);
};

export const sendMessageNotification = async (
  senderId: string,
  receiverId: string,
  message: string,
  productId?: string
) => {
  try {
    const receiver:any = await User.findById(receiverId)
      .populate('subscription')
      .select('phoneNumber subscription');

    const isOnline = isUserOnline(receiverId);

    if (isOnline) {
      io.to(`user_${receiverId}`).emit('newMessage', {
        senderId,
        message,
        productId,
        timestamp: new Date()
      });
    } else if (
      receiver?.subscription?.features?.smsNotification &&
      receiver.phoneNumber
    ) {
      const canSend = await canSendSMS(receiverId, productId || 'general');

      if (canSend) {
        const sender = await User.findById(senderId).select('firstName lastName');
        const smsContent = `${sender?.firstName} ${sender?.lastName} sent you a message: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`;
        const smsSent = await netgsmService.sendSMS(receiver.phoneNumber, smsContent);

        if (smsSent) {
          await updateSMSTracking(receiverId, productId || 'general');
        }
      }
    }
  } catch (error: any) {
    console.error('Error sending notification:', error);
  }
};
