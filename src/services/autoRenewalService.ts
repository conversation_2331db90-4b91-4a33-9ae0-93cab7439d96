import cron from 'node-cron';
import { Subscription } from '../models/Subscription';
import { StoredCard } from '../models/StoredCard';
import { Package } from '../models/Package';
import { User } from '../models/User';
import { makePaymentWithStoredCard } from './iyzicoPaymentService';
import { addMonths, format, startOfDay, endOfDay, subDays, isSameDay } from 'date-fns';
import { sendEmail, sendPackageRenewalEmail } from './emailService';
import { baseEmailTemplate, emailButton, alertBox, infoBox } from './emailTemplates';
import mongoose from 'mongoose';
import logger from '../utils/logger';

// Add more verbose logging for the test
const isTestMode = process.env.NODE_ENV === 'test' || process.argv.includes('--test-mode');
if (isTestMode) {
  logger.level = 'debug';
}
import { config } from '../config';

// Track already processed subscriptions to prevent duplicate charges
const processedSubscriptions = new Set<string>();
// Track already warned subscriptions to prevent duplicate warning emails
const warnedSubscriptions = {
  sevenDays: new Set<string>(),
  threeDays: new Set<string>(),
  oneDay: new Set<string>()
};

/**
 * Initializes the auto-renewal cron job
 * This job runs daily at 3 AM and attempts to auto-renew subscriptions using stored cards
 * We run it at 3 AM to avoid high traffic periods and to ensure we have time to process
 * all renewals before the business day starts
 */
export const initAutoRenewalJob = () => {
  // Run once daily at 3 AM
  cron.schedule('0 3 * * *', async () => {
    try {
      logger.info('Running daily auto-renewal check job');
      await checkExpiringSubscriptions();
      await checkUpcomingExpirations();
      logger.info('Daily auto-renewal check job completed');

      // Clean up processed subscriptions set to prevent memory leaks
      processedSubscriptions.clear();
      // Clean up warned subscriptions periodically (keep warnings for 30 days)
      if (new Date().getDate() === 1) {
        warnedSubscriptions.sevenDays.clear();
        warnedSubscriptions.threeDays.clear();
        warnedSubscriptions.oneDay.clear();
        logger.info('Cleared warned subscriptions tracking sets');
      }
      logger.info('Cleared processed subscriptions tracking set');
    } catch (error) {
      logger.error('Error in auto-renewal check job:', error);
    }
  });
};

/**
 * Checks for subscriptions that are expiring today and attempts to renew them
 * using the user's stored primary card. If a user doesn't have a stored card,
 * their subscription will be marked as expired.
 * 
 * Exported for testing purposes.
 */
export async function checkExpiringSubscriptions() {
  const today = new Date();
  
  // Find subscriptions that expire today and are active with auto-renewal enabled
  const expiringSubscriptions = await Subscription.find({
    status: 'ACTIVE',
    paymentStatus: 'paid', // Only process paid subscriptions
    autoRenewal: true,     // Only process subscriptions with auto-renewal enabled
    endDate: {
      // Get subscriptions that expire today (from start of day to end of day)
      $gte: startOfDay(today),
      $lte: endOfDay(today)
    }
  }).populate('userId packageId');

  logger.info(`Found ${expiringSubscriptions.length} subscriptions expiring today to process`);

  for (const subscription of expiringSubscriptions) {
    try {
      // Skip if already processed in this run cycle
      const subscriptionId = subscription._id?.toString();
      if (subscriptionId && processedSubscriptions.has(subscriptionId)) {
        logger.info(`Skipping already processed subscription ${subscriptionId}`);
        continue;
      }

      // Add to processed set before attempting renewal
      if (subscriptionId) {
        processedSubscriptions.add(subscriptionId);
      }

      await processSubscriptionRenewal(subscription);
    } catch (error) {
      logger.error(`Error processing subscription ${subscription._id}:`, error);
    }
  }
}

/**
 * Process an individual subscription for renewal
 * Attempts to charge the user's stored card for the next billing period.
 * If successful, extends the subscription by 1 month.
 * If unsuccessful, marks the subscription as canceled.
 * 
 * Exported for testing purposes.
 */
export async function processSubscriptionRenewal(subscription: any) {
  const user = subscription.userId;
  const packageInfo = subscription.packageId;

  if (!user || !packageInfo) {
    logger.warn(`Subscription ${subscription._id} has missing user or package info`);
    return;
  }

  // Check if the package is still available
  const currentPackage = await Package.findById(packageInfo._id);
  if (!currentPackage || !currentPackage.isActive) {
    logger.warn(`Package ${packageInfo._id} is no longer active, canceling subscription`);
    await Subscription.findByIdAndUpdate(subscription._id, {
      status: 'CANCELED',
      paymentStatus: 'expired',
      autoRenewal: false // Disable auto-renewal for canceled subscriptions
    });
    // Clear subscription from user document
    await User.findByIdAndUpdate(user._id, {
      $unset: { subscription: 1 },
      hasPackage: false
    });
    await sendRenewalFailedEmail(user, packageInfo, 'package_unavailable');
    return;
  }

  logger.info(`Processing monthly renewal for subscription ${subscription._id} (User: ${user._id}, Package: ${packageInfo._id})`);

  // Find user's primary/default card
  const primaryCard = await StoredCard.findOne({
    userId: user._id,
    isDefault: true,
    isActive: true
  });

  if (!primaryCard) {
    logger.info(`User ${user._id} has no primary card for auto-renewal, canceling subscription`);
    // Mark subscription as canceled if no payment method is available
    await Subscription.findByIdAndUpdate(subscription._id, {
      status: 'CANCELED',
      paymentStatus: 'expired',
      autoRenewal: false // Disable auto-renewal
    });
    // Clear subscription from user document
    await User.findByIdAndUpdate(user._id, {
      $unset: { subscription: 1 },
      hasPackage: false
    });
    await sendRenewalFailedEmail(user, packageInfo, 'no_primary_card');
    return;
  }

  try {
    // Create a server IP address for the payment process
    const ipAddress = '127.0.0.1';

    // Attempt to make payment with stored card, using the SAME package ID
    // This ensures the user keeps their current package type
    await makePaymentWithStoredCard(
      user._id.toString(),
      packageInfo._id.toString(), // Use the existing package ID to maintain package type
      packageInfo.price,
      ipAddress,
      primaryCard._id?.toString() || '',
    );

    // If successful, extend the subscription by 1 month
    const endDate = addMonths(new Date(subscription.endDate), 1); // Always renew for 1 month at a time

    // Update the subscription
    await Subscription.findByIdAndUpdate(
      subscription._id,
      {
        endDate,
        status: 'ACTIVE',
        paymentStatus: 'paid',
        renewalDate: endDate, // Set next renewal date
        $set: {
          remainingViewRequests: packageInfo.viewRequestLimit || 0,
          remainingCreateRequests: packageInfo.createRequestLimit || 0
        }
      }
    );

    // Log the renewal in a separate collection for auditing
    await createRenewalLog(
      subscription._id,
      user._id,
      packageInfo._id,
      packageInfo.price,
      'success',
      endDate,
      null
    );

    logger.info(`Successfully renewed subscription ${subscription._id} until ${format(endDate, 'yyyy-MM-dd')}`);

    // Send success email
    await sendRenewalSuccessEmail(user, packageInfo, endDate);
  } catch (error: any) {
    logger.error(`Failed to renew subscription ${subscription._id}:`, error);

    // Mark the subscription as canceled due to payment failure
    await Subscription.findByIdAndUpdate(subscription._id, {
      status: 'CANCELED',
      paymentStatus: 'failed',
      autoRenewal: false // Disable auto-renewal for canceled subscriptions
    });
    
    // Clear subscription from user document
    await User.findByIdAndUpdate(user._id, {
      $unset: { subscription: 1 },
      hasPackage: false
    });

    // Log the failed renewal attempt
    await createRenewalLog(
      subscription._id,
      user._id,
      packageInfo._id,
      packageInfo.price,
      'failed',
      null,
      null,
      error.message
    );

    // Send failure email
    await sendRenewalFailedEmail(user, packageInfo, 'payment_failed');
  }
}

/**
 * Create a log entry for a renewal attempt
 * This function writes to a renewal log collection for audit purposes
 */
async function createRenewalLog(
  subscriptionId: any,
  userId: any,
  packageId: any,
  amount: number,
  status: 'success' | 'failed',
  newEndDate: Date | null,
  session: mongoose.ClientSession | null,
  errorMessage?: string
) {
  try {
    // Define a simple schema for renewal logs if not already defined
    const renewalLogSchema = new mongoose.Schema({
      subscriptionId: { type: mongoose.Schema.Types.ObjectId, ref: 'Subscription' },
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      packageId: { type: mongoose.Schema.Types.ObjectId, ref: 'Package' },
      amount: Number,
      status: String,
      newEndDate: Date,
      errorMessage: String,
      timestamp: { type: Date, default: Date.now }
    });

    // Create model dynamically (or reuse existing)
    const RenewalLog = mongoose.models.RenewalLog ||
      mongoose.model('RenewalLog', renewalLogSchema);

    // Create the log entry
    const logData = {
      subscriptionId,
      userId,
      packageId,
      amount,
      status,
      newEndDate,
      errorMessage
    };

    // Always create without session - we don't want to use transactions for logging
    await RenewalLog.create(logData);
  } catch (error) {
    logger.error('Failed to create renewal log:', error);
    // Don't throw, just log the error since this is a non-critical operation
  }
}

/**
 * Send email notification for successful renewal
 */
async function sendRenewalSuccessEmail(user: any, packageInfo: any, newEndDate: Date) {
  // Use the standardized package renewal email template
  const startDate = new Date();
  await sendPackageRenewalEmail(
    user.email,
    user.firstName || '',
    user.lastName || '',
    packageInfo.name,
    packageInfo.price,
    packageInfo.currency || 'TRY',
    startDate,
    newEndDate,
    user.preferredLanguage || 'tr'
  );
}

/**
 * Send email notification for failed renewal
 */
async function sendRenewalFailedEmail(
  user: any,
  packageInfo: any,
  reason: 'no_primary_card' | 'payment_failed' | 'package_unavailable'
) {
  const subject = '⚠️ Abonelik Yenileme Başarısız - E-Export City';
  
  let alertMessage = '';
  let mainMessage = '';
  let actionUrl = `${config.frontendUrl}/profile/subscription`;
  let actionText = 'Aboneliği Yönet';
  
  if (reason === 'no_primary_card') {
    alertMessage = 'Otomatik yenileme için kayıtlı bir kartınız bulunmuyor!';
    mainMessage = `<strong>${packageInfo.name}</strong> paketinizi otomatik yenileyemedik çünkü hesabınızda otomatik ödeme için tanımlı bir kart bulunmuyor.`;
    actionUrl = `${config.frontendUrl}/profile/cards`;
    actionText = 'Kart Ekle';
  } else if (reason === 'package_unavailable') {
    alertMessage = 'Paketiniz artık mevcut değil!';
    mainMessage = `<strong>${packageInfo.name}</strong> paketi artık sunulmadığı için aboneliğinizi yenileyemedik.`;
    actionUrl = `${config.frontendUrl}/packages`;
    actionText = 'Yeni Paket Seç';
  } else {
    alertMessage = 'Ödeme başarısız oldu!';
    mainMessage = `<strong>${packageInfo.name}</strong> paketinizin ödemesi başarısız olduğu için aboneliğinizi yenileyemedik.`;
    actionUrl = `${config.frontendUrl}/profile/cards`;
    actionText = 'Ödeme Yöntemini Güncelle';
  }
  
  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background-color: #FED7D7; border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: #E53E3E;">⚠️</span>
      </div>
    </div>
    
    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Otomatik Yenileme Başarısız</h2>
    
    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${user.firstName || ''} ${user.lastName || ''},
    </p>
    
    ${alertBox(alertMessage, 'error')}
    
    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 20px 0;">
      ${mainMessage}
    </p>
    
    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Ne Yapmalısınız?</h3>
    
    ${infoBox('Adımlar', [
      { label: '1. Adım', value: 'Hesabınıza giriş yapın' },
      { label: '2. Adım', value: reason === 'no_primary_card' ? 'Otomatik ödeme için kart ekleyin' : reason === 'package_unavailable' ? 'Yeni bir paket seçin' : 'Ödeme bilgilerinizi güncelleyin' },
      { label: '3. Adım', value: 'Aboneliğinizi yeniden başlatın' }
    ])}
    
    ${emailButton(actionText, actionUrl)}
    
    <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; margin: 30px 0;">
      <h4 style="color: #2D3748; font-size: 16px; font-weight: 600; margin: 0 0 10px 0;">Sıkça Sorulan Sorular</h4>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0 0 10px 0;">
        <strong>Hizmetlerime hala erişebilir miyim?</strong><br>
        Mevcut dönem sonuna kadar tüm özelliklerinize erişmeye devam edebilirsiniz.
      </p>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        <strong>Verilerim kaybolur mu?</strong><br>
        Hayır, tüm verileriniz güvende. Yeni bir paket aldığınızda kaldığınız yerden devam edebilirsiniz.
      </p>
    </div>
  `;
  
  const html = baseEmailTemplate(content);
  const text = `Abonelik yenileme başarısız: ${mainMessage}`;
  
  await sendEmail(user.email, subject, text, html);
}

/**
 * Check for subscriptions expiring in 7, 3, or 1 days and send warning emails to users without credit cards
 */
export async function checkUpcomingExpirations() {
  const today = new Date();
  const sevenDaysLater = addMonths(today, 0);
  sevenDaysLater.setDate(sevenDaysLater.getDate() + 7);
  const threeDaysLater = addMonths(today, 0);
  threeDaysLater.setDate(threeDaysLater.getDate() + 3);
  const oneDayLater = addMonths(today, 0);
  oneDayLater.setDate(oneDayLater.getDate() + 1);

  // Find active subscriptions with auto-renewal enabled
  const activeSubscriptions = await Subscription.find({
    status: 'ACTIVE',
    paymentStatus: 'paid',
    autoRenewal: true
  }).populate('userId packageId');

  logger.info(`Checking ${activeSubscriptions.length} active subscriptions for upcoming expirations`);

  for (const subscription of activeSubscriptions) {
    try {
      const user = subscription.userId;
      const packageInfo = subscription.packageId;
      
      if (!user || !packageInfo) continue;

      // Check if user has a primary card
      const primaryCard = await StoredCard.findOne({
        userId: user._id,
        isDefault: true,
        isActive: true
      });

      // Only send warnings to users without credit cards
      if (!primaryCard) {
        const subscriptionId = subscription._id?.toString();
        const endDate = new Date(subscription.endDate);
        
        // Check if expiring in 7 days
        if (isSameDay(endDate, sevenDaysLater) && 
            subscriptionId && !warnedSubscriptions.sevenDays.has(subscriptionId)) {
          await sendExpirationWarningEmail(user, packageInfo, 7);
          warnedSubscriptions.sevenDays.add(subscriptionId);
          logger.info(`Sent 7-day warning email to user ${user._id} for subscription ${subscriptionId}`);
        }
        // Check if expiring in 3 days
        else if (isSameDay(endDate, threeDaysLater) && 
                 subscriptionId && !warnedSubscriptions.threeDays.has(subscriptionId)) {
          await sendExpirationWarningEmail(user, packageInfo, 3);
          warnedSubscriptions.threeDays.add(subscriptionId);
          logger.info(`Sent 3-day warning email to user ${user._id} for subscription ${subscriptionId}`);
        }
        // Check if expiring in 1 day
        else if (isSameDay(endDate, oneDayLater) && 
                 subscriptionId && !warnedSubscriptions.oneDay.has(subscriptionId)) {
          await sendExpirationWarningEmail(user, packageInfo, 1);
          warnedSubscriptions.oneDay.add(subscriptionId);
          logger.info(`Sent 1-day warning email to user ${user._id} for subscription ${subscriptionId}`);
        }
      }
    } catch (error) {
      logger.error(`Error checking subscription ${subscription._id} for warnings:`, error);
    }
  }
}

/**
 * Send expiration warning email to users without credit cards
 */
async function sendExpirationWarningEmail(
  user: any,
  packageInfo: any,
  daysRemaining: number
) {
  const urgencyLevel = daysRemaining <= 1 ? 'critical' : daysRemaining <= 3 ? 'high' : 'medium';
  const urgencyEmoji = daysRemaining <= 1 ? '🚨' : daysRemaining <= 3 ? '⚠️' : '📅';
  const urgencyColor = daysRemaining <= 1 ? '#E53E3E' : daysRemaining <= 3 ? '#DD6B20' : '#D69E2E';
  const urgencyBg = daysRemaining <= 1 ? '#FED7D7' : daysRemaining <= 3 ? '#FEEBC8' : '#FEFCBF';
  
  const subject = daysRemaining <= 1 
    ? `🚨 ACİL: ${packageInfo.name} Paketiniz Yarın Sona Eriyor!`
    : daysRemaining <= 3
    ? `⚠️ ÖNEMLİ: ${packageInfo.name} Paketiniz ${daysRemaining} Gün İçinde Sona Eriyor!`
    : `📅 ${packageInfo.name} Paketiniz ${daysRemaining} Gün İçinde Sona Erecek`;

  const alertMessage = daysRemaining <= 1
    ? 'Paketiniz yarın sona eriyor ve otomatik yenileme için kayıtlı kartınız yok!'
    : daysRemaining <= 3
    ? `Paketiniz ${daysRemaining} gün içinde sona eriyor ve otomatik yenileme için kayıtlı kartınız yok!`
    : `Paketiniz ${daysRemaining} gün içinde sona erecek. Otomatik yenileme için kart eklemeyi unutmayın!`;

  const mainMessage = daysRemaining <= 1
    ? `<strong>${packageInfo.name}</strong> paketiniz yarın sona eriyor! Hizmetlerinizde kesinti yaşamamak için <strong>hemen şimdi</strong> ödeme yöntemi eklemeniz gerekiyor.`
    : daysRemaining <= 3
    ? `<strong>${packageInfo.name}</strong> paketiniz sadece ${daysRemaining} gün içinde sona eriyor! Kesintisiz hizmet için acilen bir ödeme yöntemi eklemeniz gerekiyor.`
    : `<strong>${packageInfo.name}</strong> paketiniz ${daysRemaining} gün içinde sona erecek. Otomatik yenileme için bir kart eklemenizi öneririz.`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background-color: ${urgencyBg}; border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px;">${urgencyEmoji}</span>
      </div>
    </div>
    
    <h2 style="color: ${urgencyColor}; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">
      ${daysRemaining <= 1 ? 'ACİL: Paketiniz Yarın Sona Eriyor!' : daysRemaining <= 3 ? 'Paketiniz Sona Ermek Üzere!' : 'Paketinizin Süresi Doluyor'}
    </h2>
    
    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${user.firstName || ''} ${user.lastName || ''},
    </p>
    
    ${alertBox(alertMessage, urgencyLevel === 'critical' ? 'error' : urgencyLevel === 'high' ? 'warning' : 'info')}
    
    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 20px 0;">
      ${mainMessage}
    </p>
    
    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Hemen Harekete Geçin!</h3>
    
    ${infoBox('Yapmanız Gerekenler', [
      { label: '1. Adım', value: 'Hesabınıza giriş yapın' },
      { label: '2. Adım', value: 'Profil sayfanızdan "Kartlarım" bölümüne gidin' },
      { label: '3. Adım', value: 'Yeni bir kart ekleyin ve varsayılan olarak ayarlayın' },
      { label: '4. Adım', value: 'Otomatik yenileme özelliği aktif olacaktır' }
    ])}
    
    ${emailButton('Hemen Kart Ekle', `${config.frontendUrl}/profile/cards`)}
    
    ${daysRemaining <= 3 ? `
    <div style="background-color: #FFF5F5; border: 2px solid ${urgencyColor}; border-radius: 8px; padding: 20px; margin: 30px 0;">
      <h4 style="color: ${urgencyColor}; font-size: 16px; font-weight: 600; margin: 0 0 10px 0;">
        ${urgencyEmoji} ${daysRemaining <= 1 ? 'SON ŞANSINIZ!' : 'ZAMAN AZALIYOR!'}
      </h4>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        ${daysRemaining <= 1 
          ? 'Bu, paketinizi yenilemek için son fırsatınız! Yarın paketiniz sona erecek ve tüm hizmetlerinize erişiminiz kesilecek.'
          : `Sadece ${daysRemaining} gününüz kaldı! Kart eklemeyi ertelemeyin, hizmetlerinizde kesinti yaşamayın.`}
      </p>
    </div>
    ` : ''}
    
    <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; margin: 30px 0;">
      <h4 style="color: #2D3748; font-size: 16px; font-weight: 600; margin: 0 0 10px 0;">Neden Kart Eklemelisiniz?</h4>
      <ul style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0; padding-left: 20px;">
        <li>Hizmetlerinizde kesinti yaşamazsınız</li>
        <li>Otomatik yenileme ile uğraşmazsınız</li>
        <li>Dilediğiniz zaman iptal edebilirsiniz</li>
        <li>Güvenli ödeme altyapımızla kartınız korunur</li>
      </ul>
    </div>
    
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-top: 30px;">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 12px; line-height: 18px; margin: 0;">
            Alternatif olarak, manuel ödeme yapmak için <a href="${config.frontendUrl}/packages" style="color: #2C7A7B;">paketler sayfasını</a> ziyaret edebilirsiniz.
          </p>
        </td>
      </tr>
    </table>
  `;
  
  const html = baseEmailTemplate(content);
  const text = `${subject}\n\n${mainMessage}\n\nHemen kart eklemek için: ${config.frontendUrl}/profile/cards`;
  
  await sendEmail(user.email, subject, text, html);
}