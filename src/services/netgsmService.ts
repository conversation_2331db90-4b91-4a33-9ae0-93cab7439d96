import axios from 'axios';

interface NetGSMConfig {
  usercode: string;
  password: string;
  msgheader: string;
}

class NetGSMService {
  private baseUrl = 'https://api.netgsm.com.tr';
  private config: NetGSMConfig;

  constructor() {
    const { NETGSM_USERCODE, NETGSM_PASSWORD, NETGSM_MSGHEADER } = process.env;

    // Set optional config - don't throw errors if credentials are missing
    this.config = {
      usercode: NETGSM_USERCODE || '',
      password: NETGSM_PASSWORD || '',
      msgheader: NETGSM_MSGHEADER || ''
    };
    
    // Log that service is not fully configured if credentials are missing
    if (!NETGSM_USERCODE || !NETGSM_PASSWORD || !NETGSM_MSGHEADER) {
      console.log('NetGSM service is not properly configured. SMS functionality will be disabled.');
    }
  }

  async sendSMS(phoneNumber: string, message: string): Promise<boolean> {
    try {
      // Check if NetGSM is properly configured
      if (!this.config.usercode || !this.config.password || !this.config.msgheader) {
        console.log('NetGSM is not configured. SMS to', phoneNumber, 'was not sent.');
        return false;
      }
      
      // Format phone number (remove +90 if exists)
      const formattedPhone = phoneNumber.replace('+90', '');

      const response = await axios.post(
        `${this.baseUrl}/sms/send/get`,
        null,
        {
          params: {
            usercode: this.config.usercode,
            password: this.config.password,
            msgheader: this.config.msgheader,
            gsmno: formattedPhone,
            message: message,
            dil: 'TR'
          }
        }
      );

      // NetGSM returns a string response with status code
      const responseCode = response.data.toString().split(' ')[0];

      // Check if the message was sent successfully
      return responseCode === '00' || responseCode === '01' || responseCode === '02';
    } catch (error:any) {
      console.error('Error sending SMS:', error);
      return false;
    }
  }

  async checkBalance(): Promise<number> {
    try {
      // Check if NetGSM is properly configured
      if (!this.config.usercode || !this.config.password || !this.config.msgheader) {
        console.log('NetGSM is not configured. Unable to check balance.');
        return 0;
      }

      const response = await axios.get(
        `${this.baseUrl}/balance/list/get`,
        {
          params: {
            usercode: this.config.usercode,
            password: this.config.password
          }
        }
      );

      // Parse the balance from response
      const balance = parseFloat(response.data.toString());
      return isNaN(balance) ? 0 : balance;
    } catch (error:any) {
      console.error('Error checking balance:', error);
      return 0;
    }
  }
}

// Create a safe exported instance that won't crash the application
let netgsmService: NetGSMService | { sendSMS: () => Promise<boolean>; checkBalance: () => Promise<number> };

try {
  netgsmService = new NetGSMService();
} catch (error) {
  console.error('Failed to initialize NetGSM service:', error);
  // Use a dummy service with the same interface
  netgsmService = {
    sendSMS: async () => false,
    checkBalance: async () => 0
  };
}

export { netgsmService };
