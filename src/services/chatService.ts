import { IMessage, Message } from '../models/Message';

/**
 * Retrieves the chat history for a specific room.
 * @param roomId - The ID of the chat room.
 * @returns A promise that resolves to an array of messages.
 * @throws An error if retrieval fails.
 */
export const getChatHistory = async (roomId: string): Promise<IMessage[]> => {
  try {
    const messages:any = await Message.find({ roomId })
      .sort({ timestamp: 1 }) // Sort messages in ascending order
      .lean(); // Return plain JavaScript objects
    return messages;
  } catch (error:any) {
    console.error(`Error fetching chat history for room ${roomId}:`, error);
    throw new Error('Failed to retrieve chat history');
  }
};

/**
 * Saves a new message to the database.
 * @param roomId - The ID of the chat room.
 * @param senderId - The ID of the user sending the message.
 * @param messageContent - The content of the message.
 * @returns A promise that resolves when the message is saved.
 * @throws An error if saving fails.
 */
export const saveMessage = async (
  roomId: string,
  senderId: string,
  messageContent: string
): Promise<void> => {
  try {
    const newMessage = new Message({
      roomId,
      senderId,
      message: messageContent,
      timestamp: new Date(),
    });
    await newMessage.save();
  } catch (error:any) {
    console.error(`Error saving message to room ${roomId}:`, error);
    throw new Error('Failed to save message');
  }
};