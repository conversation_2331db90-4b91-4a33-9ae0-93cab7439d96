import { Message } from '../models/Message';
import { User } from '../models/User';
import { sendNotification } from './notificationService';
import { sendMessageNotification } from './socketService';

export const createMessage = async (
  senderId: string,
  receiverId: string,
  content: string,
  productId?: string
) => {
  try {
    const message = new Message({
      senderId,
      recipientId: receiverId,
      content,
      productId,
      read: false,
      timestamp: new Date()
    });

    await message.save();

    // Get sender details for notification
    const sender = await User.findById(senderId).select('firstName lastName');

    // Send socket notification and SMS if user is offline
    await sendMessageNotification(senderId, receiverId, content, productId);

    // Always send a notification to the system
    await sendNotification(receiverId, {
      type: 'newMessage',
      title: 'Yeni Mesaj',
      message: `${sender?.firstName} ${sender?.lastName} size bir mesaj gönderdi: ${content.substring(0, 50)}${content.length > 50 ? '...' : ''}`,
      data: {
        messageId: message._id,
        senderId,
        productId,
        url: `/messages/${senderId}`
      },
      sendEmail: true // Only send email, SMS is handled by socketService
    });

    return message;
  } catch (error:any) {
    console.error('Error creating message:', error);
    throw error;
  }
};

export const markMessageAsRead = async (messageId: string, userId: string) => {
  try {
    const message = await Message.findOneAndUpdate(
      { _id: messageId, recipientId: userId },
      { read: true },
      { new: true }
    );

    if (message) {
      // Removed io.to(`user_${userId}`).emit('messageRead', { messageId });
    }

    return message;
  } catch (error:any) {
    console.error('Error marking message as read:', error);
    throw error;
  }
};

export const getConversation = async (userId: string, otherUserId: string) => {
  try {
    return await Message.find({
      $or: [
        { senderId: userId, recipientId: otherUserId },
        { senderId: otherUserId, recipientId: userId }
      ]
    })
      .sort({ timestamp: 1 })
      .populate('senderId', 'firstName lastName')
      .populate('recipientId', 'firstName lastName')
      .lean();
  } catch (error:any) {
    console.error('Error getting conversation:', error);
    throw error;
  }
};

export const getUserConversations = async (userId: string) => {
  try {
    // Get all messages where user is either sender or receiver
    const messages = await Message.find({
      $or: [{ senderId: userId }, { recipientId: userId }]
    })
      .sort({ timestamp: -1 })
      .populate('senderId', 'firstName lastName')
      .populate('recipientId', 'firstName lastName')
      .lean();

    // Group messages by conversation partner
    const conversations = messages.reduce((acc: any, message: any) => {
      const partnerId = message.senderId.toString() === userId
        ? message.recipientId.toString()
        : message.senderId.toString();

      if (!acc[partnerId]) {
        acc[partnerId] = {
          partner: message.senderId.toString() === userId ? message.recipientId : message.senderId,
          lastMessage: message,
          unreadCount: message.recipientId.toString() === userId && !message.read ? 1 : 0
        };
      } else if (message.recipientId.toString() === userId && !message.read) {
        acc[partnerId].unreadCount++;
      }

      return acc;
    }, {});

    return Object.values(conversations);
  } catch (error:any) {
    console.error('Error getting user conversations:', error);
    throw error;
  }
};
