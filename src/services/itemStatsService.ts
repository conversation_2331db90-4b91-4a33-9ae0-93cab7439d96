import { Types } from 'mongoose';
import { ItemStats } from '../models/ItemStats';

export class ItemStatsService {
  static async incrementViewCount(itemId: Types.ObjectId | string): Promise<void> {
    await ItemStats.findOneAndUpdate(
      { itemId: itemId },
      { 
        $inc: { viewCount: 1 },
        $set: { lastViewed: new Date() }
      },
      { upsert: true }
    );
  }

  static async incrementInteractionCount(itemId: Types.ObjectId | string): Promise<void> {
    await ItemStats.findOneAndUpdate(
      { itemId: itemId },
      { 
        $inc: { interactionCount: 1 },
        $set: { lastInteraction: new Date() }
      },
      { upsert: true }
    );
  }

  static async getPopularItems(limit: number = 10): Promise<Types.ObjectId[]> {
    const popularItems = await ItemStats.find()
      .sort({ viewCount: -1, interactionCount: -1 })
      .limit(limit)
      .select('itemId');
    
    return popularItems.map(item => item.itemId);
  }

  static async getMostInteractedItems(limit: number = 10): Promise<Types.ObjectId[]> {
    const interactedItems = await ItemStats.find()
      .sort({ interactionCount: -1 })
      .limit(limit)
      .select('itemId');
    
    return interactedItems.map(item => item.itemId);
  }
}
