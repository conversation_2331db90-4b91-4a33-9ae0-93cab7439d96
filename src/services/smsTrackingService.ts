import { Schema, model, Document } from 'mongoose';

// Interface for SMS tracking
interface ISMSTracking extends Document {
  userId: string;
  productId: string;
  lastSentAt: Date;
}

// Schema for SMS tracking
const SMSTrackingSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, required: true },
  productId: { type: Schema.Types.ObjectId, required: true },
  lastSentAt: { type: Date, required: true }
});

// Create compound index for userId and productId
SMSTrackingSchema.index({ userId: 1, productId: 1 });

// Model for SMS tracking
const SMSTracking = model<ISMSTracking>('SMSTracking', SMSTrackingSchema);

export const canSendSMS = async (userId: string, productId: string): Promise<boolean> => {
  try {
    const tracking = await SMSTracking.findOne({ userId, productId });

    if (!tracking) {
      return true;
    }

    // Check if 1 hour has passed since the last SMS
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    return tracking.lastSentAt < oneHourAgo;
  } catch (error:any) {
    console.error('Error checking SMS tracking:', error);
    return false;
  }
};

export const updateSMSTracking = async (userId: string, productId: string): Promise<void> => {
  try {
    await SMSTracking.findOneAndUpdate(
      { userId, productId },
      { lastSentAt: new Date() },
      { upsert: true }
    );
  } catch (error:any) {
    console.error('Error updating SMS tracking:', error);
  }
};
