import nodemailer from 'nodemailer';
import dotenv from 'dotenv';
import { config } from '../config';
import logger from '../utils/logger';
import { baseEmailTemplate, emailButton, alertBox, featureList, infoBox, divider } from './emailTemplates';

dotenv.config();

// Email service state
interface EmailConfig {
  host: string;
  port: number;
  user: string;
  pass: string;
  fromName: string;
  isDev: boolean;
  isValid: boolean;
}

// Email queue for retry mechanism
interface QueuedEmail {
  to: string;
  subject: string;
  text: string;
  html?: string;
  retryCount: number;
  lastAttempt: Date;
}

// Constants
const MAX_RETRIES = 3;
const RETRY_DELAY = 5 * 60 * 1000; // 5 minutes

// Email configuration from config.ts
const emailConfig: EmailConfig = {
  host: config.smtp.host,
  port: config.smtp.port,
  user: config.smtp.user,
  pass: config.smtp.pass,
  fromName: process.env.EMAIL_FROM_NAME || 'Export City',
  isDev: process.env.NODE_ENV !== 'production',
  isValid: false
};

// Email from address - ensure consistent format
const EMAIL_FROM = emailConfig.user;
const EMAIL_FROM_NAME = emailConfig.fromName;

// Create and initialize the transporter
let transporter: nodemailer.Transporter;
const emailQueue: QueuedEmail[] = [];

/**
 * Validates that all required email configuration is present
 */
function validateEmailConfig(): boolean {
  const requiredFields = ['host', 'port', 'user', 'pass'];
  const missingFields = requiredFields.filter(field => !emailConfig[field as keyof EmailConfig]);

  if (missingFields.length > 0) {
    logger.error(`Email configuration is invalid. Missing required fields: ${missingFields.join(', ')}`);
    return false;
  }

  return true;
}

/**
 * Initializes the email transporter
 */
async function initializeTransporter(): Promise<boolean> {
  // Validate email configuration first
  emailConfig.isValid = validateEmailConfig();
  if (!emailConfig.isValid) {
    logger.error('Email configuration validation failed. Email service will not work properly.');
    return false;
  }

  logger.info(`Initializing email transport with configuration:
  - Mail server: ${emailConfig.host}:${emailConfig.port}
  - Username: ${emailConfig.user}
  - From: ${EMAIL_FROM_NAME} <${EMAIL_FROM}>
  - Development mode: ${emailConfig.isDev ? 'ON' : 'OFF'}`);

  // Create transporter
  transporter = nodemailer.createTransport({
    host: emailConfig.host,
    port: emailConfig.port,
    secure: emailConfig.port === 465, // true for 465, false for other ports
    auth: {
      user: emailConfig.user,
      pass: emailConfig.pass
    },
    tls: {
      // do not fail on invalid certs
      rejectUnauthorized: false
    },
    logger: emailConfig.isDev,
    debug: emailConfig.isDev
  });

  // Test the connection
  try {
    await transporter.verify();
    logger.info(`Email server connection successful`);

    // Process any queued emails after successful connection
    if (emailQueue.length > 0) {
      logger.info(`Processing ${emailQueue.length} queued emails`);
      processEmailQueue();
    }

    return true;
  } catch (error: any) {
    logger.error(`Email server connection error: ${error.message}`);
    logger.error('Email service may not work. Please check your mail server configuration.');
    return false;
  }
}

/**
 * Process the email queue, retrying failed emails
 */
async function processEmailQueue() {
  const now = new Date();

  // Process emails in the queue that are ready for retry
  for (let i = 0; i < emailQueue.length; i++) {
    const queuedEmail = emailQueue[i];

    // Skip if not ready for retry
    if ((now.getTime() - queuedEmail.lastAttempt.getTime()) < RETRY_DELAY) {
      continue;
    }

    try {
      // Send the email
      const result = await actualSendEmail(queuedEmail.to, queuedEmail.subject, queuedEmail.text, queuedEmail.html);

      // If successful, remove from queue
      if (result) {
        logger.info(`Successfully sent queued email to ${queuedEmail.to} after ${queuedEmail.retryCount} retries`);
        emailQueue.splice(i, 1);
        i--; // Adjust index after removal
      }
    } catch (error) {
      queuedEmail.retryCount++;
      queuedEmail.lastAttempt = new Date();

      if (queuedEmail.retryCount >= MAX_RETRIES) {
        logger.error(`Failed to send email to ${queuedEmail.to} after ${MAX_RETRIES} attempts. Removing from queue.`);
        emailQueue.splice(i, 1);
        i--; // Adjust index after removal
      } else {
        logger.warn(`Retry attempt ${queuedEmail.retryCount}/${MAX_RETRIES} for email to ${queuedEmail.to}`);
      }
    }
  }
}

// Initialize the transporter
initializeTransporter().catch(err => {
  logger.error('Failed to initialize email transport:', err);
});

// Set up regular processing of the email queue
setInterval(processEmailQueue, RETRY_DELAY / 2);

/**
 * Internal function to actually send an email
 */
async function actualSendEmail(to: string, subject: string, text: string, html?: string): Promise<nodemailer.SentMessageInfo | null> {
  // Check for development mode
  if (emailConfig.isDev && process.env.EMAIL_DEV_MODE === 'log') {
    logger.info(`[DEV MODE] Email would be sent:
      To: ${to}
      Subject: ${subject}
      Text: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}
      HTML: ${html ? 'Yes' : 'No'}`);

    // Return mock successful result in dev mode
    return {
      messageId: `mock-message-id-${Date.now()}`,
      envelope: { from: EMAIL_FROM, to: [to] },
      accepted: [to],
      rejected: [],
      pending: [],
      response: 'Mock response in development mode'
    } as nodemailer.SentMessageInfo;
  }

  // Ensure email configuration is valid
  if (!emailConfig.isValid) {
    throw new Error('Email configuration is invalid');
  }

  // Ensure transporter is initialized
  if (!transporter) {
    const initialized = await initializeTransporter();
    if (!initialized || !transporter) {
      throw new Error('Email transport not available');
    }
  }

  // Send the email
  const mailOptions = {
    from: `${EMAIL_FROM_NAME} <${EMAIL_FROM}>`,
    to,
    subject,
    text,
    html
  };

  try {
    const info = await transporter.sendMail(mailOptions);
    logger.info(`Email sent successfully to ${to} - Message ID: ${info.messageId}`);
    return info;
  } catch (error: any) {
    logger.error(`Error sending email to ${to}: ${error.message}`);
    throw error;
  }
}

/**
 * Send an email with retry capability
 * @param to Email recipient
 * @param subject Email subject
 * @param text Plain text content
 * @param html Optional HTML content
 * @param priority If true, will attempt to send immediately and retry on failure
 * @returns The email info if sent successfully
 */
export const sendEmail = async (
  to: string,
  subject: string,
  text: string,
  html?: string,
  priority: boolean = false
): Promise<nodemailer.SentMessageInfo | null> => {
  try {
    return await actualSendEmail(to, subject, text, html);
  } catch (error: any) {
    logger.error(`Failed to send email to ${to}: ${error.message}`);

    // Add to retry queue if priority is true or for all emails
    if (priority || process.env.RETRY_ALL_EMAILS === 'true') {
      emailQueue.push({
        to,
        subject,
        text,
        html,
        retryCount: 1,
        lastAttempt: new Date()
      });

      logger.info(`Email to ${to} added to retry queue (${emailQueue.length} emails in queue)`);
    }

    // If not a priority email, we can just let it fail
    if (!priority) {
      throw new Error(`Failed to send email: ${error.message}`);
    }

    return null;
  }
};

/**
 * Send an email validation or password reset email
 * @param to Recipient email
 * @param validationLink The validation or reset link
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendValidationEmail = async (to: string, validationLink: string, language: string = 'tr') => {
  const subject = "E-posta Doğrulama - E-Export City";

  const content = `
    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">E-posta Adresinizi Doğrulayın</h2>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 20px 0;">
      Merhaba,
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
      E-Export City'e hoş geldiniz! Hesabınızı aktif hale getirmek için lütfen e-posta adresinizi doğrulayın.
    </p>

    ${emailButton('E-posta Adresimi Doğrula', validationLink)}

    ${alertBox('Bu bağlantı güvenlik nedeniyle 24 saat içinde geçerliliğini yitirecektir.', 'warning')}

    <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 30px 0 0 0;">
      Yukarıdaki düğme çalışmıyorsa, aşağıdaki bağlantıyı kopyalayıp tarayıcınıza yapıştırabilirsiniz:
    </p>

    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 10px 0;">
      <tr>
        <td style="background-color: #F7FAFC; padding: 12px; border-radius: 4px; word-break: break-all;">
          <code style="color: #2C7A7B; font-size: 12px;">${validationLink}</code>
        </td>
      </tr>
    </table>

    ${divider()}

    <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 20px 0 0 0; text-align: center;">
      Bu e-postayı siz talep etmediyseniz, lütfen dikkate almayın.
    </p>
  `;

  const html = baseEmailTemplate(content);
  const text = `E-posta adresinizi doğrulamak için lütfen aşağıdaki bağlantıya tıklayın: ${validationLink}`;

  try {
    // This is a critical email, so we set priority to true for automatic retry
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error('Error sending validation email:', error);
    throw new Error(`Failed to send validation email: ${error.message}`);
  }
};

/**
 * Send a password change confirmation email
 * @param to Recipient email
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendPasswordChangeSuccessEmail = async (to: string, language: string = 'tr') => {
  const subject = "Şifre Değişikliği Başarılı - E-Export City";

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 80px; height: 80px; background-color: #F0FFF4; border-radius: 50%; line-height: 80px;">
        <span style="font-size: 40px; color: #38A169;">✓</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Şifreniz Başarıyla Değiştirildi</h2>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Hesabınızın şifresi başarıyla güncellendi. Artık yeni şifrenizi kullanarak giriş yapabilirsiniz.
    </p>

    ${alertBox('<strong>Güvenlik Uyarısı:</strong> Bu değişikliği siz yapmadıysanız, hesabınızın güvenliği risk altında olabilir. Lütfen derhal bizimle iletişime geçin.', 'error')}

    ${infoBox('Güvenlik İpuçları', [
      { label: 'Güçlü Şifre', value: 'En az 8 karakter, büyük/küçük harf, rakam ve özel karakter içeren şifreler kullanın' },
      { label: 'Şifre Paylaşımı', value: 'Şifrenizi asla başkalarıyla paylaşmayın' },
      { label: 'Düzenli Güncelleme', value: 'Şifrenizi düzenli aralıklarla değiştirin' },
      { label: 'Farklı Şifreler', value: 'Her platform için farklı şifreler kullanın' }
    ])}

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 20px 0;">
            Yardıma mı ihtiyacınız var?
          </p>
          ${emailButton('Destek Merkezi', `${config.frontendUrl}/support`, '#718096')}
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);
  const text = 'Şifreniz başarıyla değiştirildi. Eğer bu değişikliği siz yapmadıysanız, lütfen derhal destek ekibimizle iletişime geçin.';

  try {
    // Security-related emails should be prioritized
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error('Error sending password change success email:', error);
    throw new Error(`Failed to send password change success email: ${error.message}`);
  }
};

// New email notification functions

/**
 * Send a welcome email to a newly registered user
 * @param to Recipient email
 * @param firstName User's first name
 * @param lastName User's last name
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendWelcomeEmail = async (to: string, firstName: string, lastName: string, language: string = 'tr') => {
  try {
    logger.info(`Preparing welcome email for user: ${firstName} ${lastName} (${to}) with language: ${language}`);

    const subject = 'E-Export City\'e Hoş Geldiniz! 🎉';
    const text = `Merhaba ${firstName} ${lastName},\n\nE-Export City'e hoş geldiniz! Platformumuza katıldığınız için heyecanlıyız.\n\nArtık pazaryerimizi keşfedebilir, işletmelerle bağlantı kurabilir ve yeni fırsatlar keşfedebilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

    const content = `
      <div style="text-align: center; margin-bottom: 30px;">
        <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #2C7A7B 0%, #276749 100%); border-radius: 50%; line-height: 100px;">
          <span style="font-size: 50px; color: white;">🎆</span>
        </div>
      </div>

      <h2 style="color: #2D3748; font-size: 32px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Hoş Geldiniz, ${firstName}!</h2>

      <p style="color: #4A5568; font-size: 18px; line-height: 28px; margin: 0 0 30px 0; text-align: center;">
        E-Export City ailesine katıldığınız için çok mutluyuz. 🎉
      </p>

      <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
        E-Export City, Türkiye'nin lider dijital ihracat platformu olarak size global pazarlarda başarılı olmanız için gereken tüm araçları sağlıyor.
      </p>

      <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Neler Yapabilirsiniz?</h3>

      ${featureList([
        'Binlerce potansiyel alıcı ve satıcı ile bağlantı kurun',
        'Ürünlerinizi global pazarda sergileyin',
        'Güvenli mesajlaşma sistemi ile iş ortaklarınızla iletişim kurun',
        'Sektörünüzdeki en son gelişmeleri takip edin',
        'Profesyonel destek ekibimizden yardım alın'
      ])}

      ${emailButton('Hesabımı Keşfet', `${config.frontendUrl}/profile`)}

      ${infoBox('İlk Adımlar', [
        { label: '1. Adım', value: 'Profilinizi tamamlayın ve şirket bilgilerinizi ekleyin' },
        { label: '2. Adım', value: 'Ürünlerinizi yükleyin ve detaylı açıklamalar ekleyin' },
        { label: '3. Adım', value: 'Size uygun paketi seçin ve tüm özelliklere erişin' },
        { label: '4. Adım', value: 'Alıcılarla iletişime geçin ve ihracatınızı başlatın' }
      ])}

      ${divider()}

      <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
          <td align="center">
            <h3 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 20px 0;">Yardıma mı İhtiyacınız Var?</h3>
            <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0 0 20px 0;">
              Destek ekibimiz size yardımcı olmak için hazır!<br>
              <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
              <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
            </p>
          </td>
        </tr>
      </table>
    `;

    const html = baseEmailTemplate(content);

    logger.info(`Sending welcome email to: ${to}`);
    // Welcome emails are important but shouldn't block registration
    const result = await sendEmail(to, subject, text, html, true);
    logger.info(`Welcome email sent successfully to ${to}`);
    return result;
  } catch (error) {
    logger.error(`Failed to send welcome email to ${to}:`, error);
    // Don't throw the error - this allows the registration to continue even if email fails
    // Return null to indicate the email wasn't sent
    return null;
  }
};

/**
 * Send a package purchase confirmation email
 */
/**
 * Send a package purchase confirmation email
 * @param to Recipient email
 * @param firstName User's first name
 * @param lastName User's last name
 * @param packageName Package name
 * @param packagePrice Package price
 * @param currency Currency code
 * @param startDate Subscription start date
 * @param endDate Subscription end date
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendPackagePurchaseEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  packageName: string,
  packagePrice: number,
  currency: string,
  startDate: Date,
  endDate: Date,
  language: string = 'tr'
) => {
  const subject = 'Paket Satın Alma Onayı - E-Export City ✨';

  const formattedStartDate = startDate.toLocaleDateString('tr-TR');
  const formattedEndDate = endDate.toLocaleDateString('tr-TR');
  const formattedPrice = new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency || 'TRY'
  }).format(packagePrice);

  const text = `Merhaba ${firstName} ${lastName},\n\n${packageName} paketini satın aldığınız için teşekkür ederiz. Aboneliğiniz şu anda aktif.\n\nPaket Detayları:\n- Paket: ${packageName}\n- Fiyat: ${formattedPrice}\n- Başlangıç Tarihi: ${formattedStartDate}\n- Bitiş Tarihi: ${formattedEndDate}\n\nArtık paketinizde bulunan tüm özelliklerin keyfini çıkarabilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #48BB78 0%, #38A169 100%); border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: white;">✓</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 32px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">İşleminiz Başarıyla Tamamlandı!</h2>

    <p style="color: #4A5568; font-size: 18px; line-height: 28px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName}, <strong>${packageName}</strong> paketini başarıyla satın aldınız.
    </p>

    ${infoBox('Paket Bilgileriniz', [
      { label: 'Paket Adı', value: packageName },
      { label: 'Tutar', value: formattedPrice },
      { label: 'Başlangıç Tarihi', value: formattedStartDate },
      { label: 'Bitiş Tarihi', value: formattedEndDate },
      { label: 'Durum', value: 'Aktif ✅' }
    ])}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Paketinize Dahil Özellikler</h3>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 20px 0;">
      Artık aşağıdaki tüm özelliklere erişebilirsiniz:
    </p>

    ${featureList([
      'Sınırsız ürün listeleme',
      'Gelişmiş mesajlaşma sistemi',
      'Öncelikli müşteri desteği',
      'Detaylı istatistik ve raporlama',
      'E-posta ve SMS bildirimleri'
    ])}

    ${emailButton('Paket Detaylarını Görüntüle', `${config.frontendUrl}/profile/subscription`)}

    ${alertBox('Otomatik yenileme aktif! Paketiniz bitiş tarihinde otomatik olarak yenilenecektir. Dilediğiniz zaman iptal edebilirsiniz.', 'info')}

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
            Sorularınız mı var? Bizimle iletişime geçin:<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
            <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    // Cancellation confirmations are critical for business operations
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending package cancellation email to ${to}:`, error);
    return null;
  }
};

/**
 * Send a package cancellation confirmation email
 */
/**
 * Send a package cancellation confirmation email
 * @param to Recipient email
 * @param firstName User's first name
 * @param lastName User's last name
 * @param packageName Package name
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendPackageCancellationEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  packageName: string,
  _language: string = 'tr'
) => {
  const subject = 'Paket İptal Onayı - E-Export City';
  const text = `Merhaba ${firstName} ${lastName},\n\n${packageName} paketine olan aboneliğiniz isteğiniz üzerine iptal edilmiştir.\n\nAyrıldığınız için üzgünüz. Herhangi bir geri bildiriminiz veya sorunuz varsa, lütfen bizimle iletişime geçmekten çekinmeyin.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background-color: #FED7D7; border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: #E53E3E;">⚠️</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Paket İptal İşleminiz Tamamlandı</h2>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName}, <strong>${packageName}</strong> paketine olan aboneliğiniz başarıyla iptal edildi.
    </p>

    ${alertBox('Aboneliğiniz iptal edildi. Mevcut dönem sonuna kadar tüm özelliklerden yararlanmaya devam edebilirsiniz.', 'warning')}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0; text-align: center;">Geri Dönmeniz İçin Her Zaman Buradayız!</h3>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      E-Export City ailesinden ayrıldığınız için üzgünüz. Tekrar ihtiyacınız olduğunda size en uygun paketi seçebilirsiniz.
    </p>

    ${emailButton('Diğer Paketleri İncele', `${config.frontendUrl}/packages`)}

    ${divider()}

    <div style="background-color: #F7FAFC; border-radius: 8px; padding: 25px; margin: 30px 0;">
      <h4 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 15px 0; text-align: center;">Geri Bildiriminiz Bizim İçin Değerli</h4>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0 0 20px 0; text-align: center;">
        Hizmetlerimizi geliştirmek için görüşlerinize ihtiyacımız var. Bize neden ayrıldığınızı söyler misiniz?
      </p>
      <table border="0" cellpadding="0" cellspacing="0" width="100%">
        <tr>
          <td align="center">
            <a href="${config.frontendUrl}/feedback" style="display: inline-block; padding: 12px 24px; background-color: #718096; color: white; text-decoration: none; border-radius: 6px; font-weight: 500;">Geri Bildirim Gönder</a>
          </td>
        </tr>
      </table>
    </div>

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Sorularınız için her zaman buradayız:<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    // Cancellation confirmations are critical for business operations
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending package cancellation email to ${to}:`, error);
    return null;
  }
};

/**
 * Send a package renewal confirmation email
 */
/**
 * Send a package renewal confirmation email
 * @param to Recipient email
 * @param firstName User's first name
 * @param lastName User's last name
 * @param packageName Package name
 * @param packagePrice Package price
 * @param currency Currency code
 * @param startDate Subscription start date
 * @param endDate Subscription end date
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendPackageRenewalEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  packageName: string,
  packagePrice: number,
  currency: string,
  startDate: Date,
  endDate: Date,
  _language: string = 'tr'
) => {
  const subject = 'Paket Yenileme Onayı - E-Export City 🎉';

  const formattedStartDate = startDate.toLocaleDateString('tr-TR');
  const formattedEndDate = endDate.toLocaleDateString('tr-TR');
  const formattedPrice = new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: currency || 'TRY'
  }).format(packagePrice);

  const text = `Merhaba ${firstName} ${lastName},\n\n${packageName} paketine olan aboneliğiniz başarıyla yenilenmiştir.\n\nPaket Detayları:\n- Paket: ${packageName}\n- Fiyat: ${formattedPrice}\n- Başlangıç Tarihi: ${formattedStartDate}\n- Bitiş Tarihi: ${formattedEndDate}\n\nPaketinizde bulunan tüm özellikleri kullanmaya devam edebilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #4299E1 0%, #3182CE 100%); border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: white;">🔄</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 32px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Paketiniz Yenilendi!</h2>

    <p style="color: #4A5568; font-size: 18px; line-height: 28px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName}, güveniniz için teşekkür ederiz! 🙏
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      <strong>${packageName}</strong> paketiniz otomatik olarak yenilendi ve kesintisiz hizmet almaya devam edeceksiniz.
    </p>

    ${infoBox('Yenilenen Paket Detayları', [
      { label: 'Paket Adı', value: packageName },
      { label: 'Yenileme Tutarı', value: formattedPrice },
      { label: 'Yeni Dönem Başlangıcı', value: formattedStartDate },
      { label: 'Yeni Dönem Bitişi', value: formattedEndDate },
      { label: 'Durum', value: 'Aktif ✅' }
    ])}

    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #E6FFFA 100%); border-radius: 12px; padding: 25px; margin: 30px 0;">
      <h3 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 15px 0;">🎁 Sadakat Avantajları</h3>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        E-Export City ailesiyle birlikte olduğunuz için teşekkür ederiz! Sadık müşterilerimize özel kampanyalar ve fırsatlardan haberdar olmak için bildirimlerinizi açık tutun.
      </p>
    </div>

    ${emailButton('Abonelik Detaylarım', `${config.frontendUrl}/profile/subscription`)}

    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin-top: 30px;">
      <tr>
        <td style="background-color: #F7FAFC; border-radius: 8px; padding: 20px;">
          <h4 style="color: #2D3748; font-size: 16px; font-weight: 600; margin: 0 0 10px 0;">💡 İpucu</h4>
          <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
            Abonelik ayarlarınızdan otomatik yenilemeyi kapatabilir veya farklı bir pakete geçiş yapabilirsiniz.
          </p>
        </td>
      </tr>
    </table>

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Fatura ve ödeme detaylarınız için hesabınıza giriş yapabilirsiniz.<br>
            <a href="${config.frontendUrl}/profile/invoices" style="color: #2C7A7B; text-decoration: none;">Faturalarım →</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    // Cancellation confirmations are critical for business operations
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending package cancellation email to ${to}:`, error);
    return null;
  }
};

/**
 * Send a package expiration notification email
 */
/**
 * Send a package expiration notification email
 * @param to Recipient email
 * @param firstName User's first name
 * @param lastName User's last name
 * @param packageName Package name
 * @param expiryDate Expiration date
 * @param daysRemaining Days remaining until expiration
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendPackageExpirationEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  packageName: string,
  expiryDate: Date,
  daysRemaining: number,
  _language: string = 'tr'
) => {
  const subject = `⏰ ${packageName} Paketiniz ${daysRemaining} Gün İçinde Sona Erecek!`;

  const formattedExpiryDate = expiryDate.toLocaleDateString('tr-TR');

  const text = `Merhaba ${firstName} ${lastName},\n\n${packageName} paketine olan aboneliğiniz ${formattedExpiryDate} tarihinde, ${daysRemaining} gün içinde sona erecektir.\n\nHizmetlerimizden kesintisiz yararlanmaya devam etmek için lütfen aboneliğinizi son kullanma tarihinden önce yenileyin.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const urgencyColor = daysRemaining <= 3 ? '#E53E3E' : daysRemaining <= 7 ? '#DD6B20' : '#D69E2E';
  const urgencyBg = daysRemaining <= 3 ? '#FED7D7' : daysRemaining <= 7 ? '#FEEBC8' : '#FEFCBF';
  const urgencyEmoji = daysRemaining <= 3 ? '🚨' : daysRemaining <= 7 ? '⚠️' : '📅';

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background-color: ${urgencyBg}; border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px;">${urgencyEmoji}</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Paketiniz Sona Ermek Üzere!</h2>

    <div style="background-color: ${urgencyBg}; border-left: 4px solid ${urgencyColor}; padding: 20px; border-radius: 4px; margin: 20px 0;">
      <p style="color: ${urgencyColor}; font-size: 18px; line-height: 24px; margin: 0; font-weight: 600;">
        ${daysRemaining <= 3 ? 'ACİL:' : daysRemaining <= 7 ? 'ÖNEMLİ:' : 'HATIRLATMA:'}
        <strong>${packageName}</strong> paketiniz <strong>${daysRemaining} gün</strong> içinde sona erecek!
      </p>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 10px 0 0 0;">
        Son kullanma tarihi: <strong>${formattedExpiryDate}</strong>
      </p>
    </div>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName}, hizmetlerimizden kesintisiz yararlanmaya devam etmek için paketinizi yenileyebilirsiniz.
    </p>

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Paketinizi Yenilemenin Avantajları</h3>

    ${featureList([
      'Kesintisiz hizmet garantisi',
      'Tüm özellikleriniz korunur',
      'Mevcut verileriniz kaybolmaz',
      'Özel yenileme indirimleri',
      'Sadakat puanları kazanma fırsatı'
    ])}

    ${emailButton('Hemen Yenile', `${config.frontendUrl}/profile/subscription`)}

    ${daysRemaining <= 3 ? alertBox('Paketiniz sona erdikten sonra bazı özelliklerinize erişemeyebilirsiniz. Hemen yenileyin!', 'error') : ''}

    <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; margin: 30px 0;">
      <h4 style="color: #2D3748; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">💡 Otomatik Yenileme</h4>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        Bir daha bu konuda endişelenmeyin! Otomatik yenilemeyi aktif ederek paketinizin hiç sona ermemesini sağlayabilirsiniz.
      </p>
      <table border="0" cellpadding="0" cellspacing="0" style="margin-top: 15px;">
        <tr>
          <td>
            <a href="${config.frontendUrl}/profile/subscription/auto-renew" style="display: inline-block; padding: 10px 20px; background-color: #718096; color: white; text-decoration: none; border-radius: 6px; font-size: 14px;">Otomatik Yenilemeyi Aç</a>
          </td>
        </tr>
      </table>
    </div>

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Sorularınız mı var? Size yardımcı olmaktan mutluluk duyarız!<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
            <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    // Expiration notifications are critical for business operations
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending package expiration email to ${to}:`, error);
    return null;
  }
};

// ==================== ITEM/PRODUCT EMAIL FUNCTIONS ====================

/**
 * Send an email when a new item/product is created and submitted for approval
 * @param to Recipient email (item creator)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param itemName Item/product name
 * @param itemType Item type (Product or Service)
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendItemCreatedEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  itemName: string,
  itemType: string,
  language: string = 'tr'
) => {
  const typeText = itemType.toLowerCase() === 'product' ? 'Ürün' : 'Hizmet';
  const subject = `${typeText} Başarıyla Oluşturuldu - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\n"${itemName}" ${typeText.toLowerCase()} başarıyla oluşturuldu ve onay için gönderildi.\n\n${typeText}ünüz admin ekibimiz tarafından incelendikten sonra yayınlanacaktır. Bu süreç genellikle 24-48 saat sürmektedir.\n\nDurum güncellemeleri için profilinizi kontrol edebilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #4299E1 0%, #3182CE 100%); border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: white;">📦</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 32px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">${typeText} Oluşturuldu!</h2>

    <p style="color: #4A5568; font-size: 18px; line-height: 28px; margin: 0 0 30px 0; text-align: center;">
      Merhaba ${firstName} ${lastName}! 👋
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
      <strong>"${itemName}"</strong> ${typeText.toLowerCase()} başarıyla oluşturuldu ve onay sürecine alındı.
    </p>

    ${infoBox(`${typeText} Bilgileri`, [
      { label: `${typeText} Adı`, value: itemName },
      { label: 'Tür', value: typeText },
      { label: 'Durum', value: 'Onay Bekliyor ⏳' },
      { label: 'Oluşturma Tarihi', value: new Date().toLocaleDateString('tr-TR') }
    ])}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Sırada Ne Var?</h3>

    ${featureList([
      'Admin ekibimiz ürününüzü 24-48 saat içinde inceleyecek',
      'Onaylandıktan sonra ürününüz platformda yayınlanacak',
      'Alıcılar ürününüzü görebilecek ve sizinle iletişime geçebilecek',
      'E-posta bildirimi ile onay durumundan haberdar olacaksınız'
    ])}

    ${alertBox('İnceleme sırasında ürün kurallarımıza uygun olmayan içerikler reddedilebilir. Detaylı bilgi için kullanım koşullarımızı inceleyebilirsiniz.', 'info')}

    ${emailButton('Ürünlerimi Görüntüle', `${config.frontendUrl}/profile/items`)}

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <h3 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 20px 0;">Daha Fazla ${typeText} Eklemek İster misiniz?</h3>
          <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0 0 20px 0;">
            Platformumuzda ne kadar çok ürün sergilerseniz, o kadar fazla alıcıya ulaşabilirsiniz!
          </p>
          <a href="${config.frontendUrl}/add-product" style="display: inline-block; padding: 12px 24px; background-color: #48BB78; color: white; text-decoration: none; border-radius: 6px; font-weight: 500;">Yeni ${typeText} Ekle</a>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending item creation email to ${to}:`, error);
    return null;
  }
};

/**
 * Send an email when an item/product is approved by admin
 * @param to Recipient email (item creator)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param itemName Item/product name
 * @param itemType Item type (Product or Service)
 * @param itemId Item ID for linking
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendItemApprovedEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  itemName: string,
  itemType: string,
  itemId: string,
  language: string = 'tr'
) => {
  const typeText = itemType.toLowerCase() === 'product' ? 'Ürün' : 'Hizmet';
  const subject = `🎉 ${typeText} Onaylandı ve Yayınlandı - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\nHarika haber! "${itemName}" ${typeText.toLowerCase()} onaylandı ve artık platformumuzda yayında.\n\nPotansiyel alıcılar ürününüzü görebilir ve sizinle iletişime geçebilir.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #48BB78 0%, #38A169 100%); border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: white;">🎉</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 32px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Tebrikler! ${typeText} Onaylandı</h2>

    <p style="color: #4A5568; font-size: 18px; line-height: 28px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName}, harika haber! 🎊
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      <strong>"${itemName}"</strong> ${typeText.toLowerCase()} başarıyla onaylandı ve artık platformumuzda yayında!
    </p>

    ${alertBox(`${typeText}ünüz artık canlı! Potansiyel alıcılar ${typeText.toLowerCase()}ünüzü görebilir ve sizinle iletişime geçebilir.`, 'success')}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Şimdi Neler Yapabilirsiniz?</h3>

    ${featureList([
      'Ürününüzün detay sayfasını ziyaret edebilirsiniz',
      'Gelen mesajları ve talepleri takip edebilirsiniz',
      'Ürün istatistiklerinizi görebilirsiniz',
      'Sosyal medyada paylaşarak daha fazla kişiye ulaşabilirsiniz'
    ])}

    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 30px 0;">
      <tr>
        <td align="center">
          <a href="${config.frontendUrl}/item/${itemId}" style="display: inline-block; padding: 15px 30px; background-color: #4299E1; color: white; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; margin-right: 15px;">${typeText}ümü Görüntüle</a>
          <a href="${config.frontendUrl}/profile/items" style="display: inline-block; padding: 15px 30px; background-color: #48BB78; color: white; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">Tüm ${typeText}lerim</a>
        </td>
      </tr>
    </table>

    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #E6FFFA 100%); border-radius: 12px; padding: 25px; margin: 30px 0;">
      <h3 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 15px 0;">💡 Başarı İpuçları</h3>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        ${typeText}ünüzün daha fazla görüntülenmesi için kaliteli fotoğraflar ekleyin, detaylı açıklamalar yazın ve düzenli olarak güncelleyin. Hızlı yanıt veren satıcılar daha çok tercih edilir!
      </p>
    </div>

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            ${typeText}ünüzle ilgili sorularınız mı var?<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
            <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending item approval email to ${to}:`, error);
    return null;
  }
};

/**
 * Send an email when an item/product is rejected by admin
 * @param to Recipient email (item creator)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param itemName Item/product name
 * @param itemType Item type (Product or Service)
 * @param rejectionReason Optional reason for rejection
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendItemRejectedEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  itemName: string,
  itemType: string,
  rejectionReason?: string,
  language: string = 'tr'
) => {
  const typeText = itemType.toLowerCase() === 'product' ? 'Ürün' : 'Hizmet';
  const subject = `${typeText}ünüz İnceleme Sonucu - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\nMaalesef "${itemName}" ${typeText.toLowerCase()}ünüz onaylanmadı.\n\n${rejectionReason ? `Sebep: ${rejectionReason}\n\n` : ''}${typeText}ünüzü kurallara uygun şekilde düzenleyerek tekrar gönderebilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background-color: #FED7D7; border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: #E53E3E;">⚠️</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">${typeText} İnceleme Sonucu</h2>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName},
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
      Maalesef <strong>"${itemName}"</strong> ${typeText.toLowerCase()}ünüz inceleme sürecinden geçemedi ve yayınlanamadı.
    </p>

    ${rejectionReason ? alertBox(`<strong>Ret Sebebi:</strong> ${rejectionReason}`, 'error') : ''}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Neler Yapabilirsiniz?</h3>

    ${featureList([
      `${typeText}ünüzü platform kurallarına uygun şekilde düzenleyin`,
      'Açıklamaları daha detaylı ve açık hale getirin',
      'Uygun kategoride olduğundan emin olun',
      'Kaliteli ve alakalı görseller ekleyin',
      'Tekrar onaya gönderin'
    ])}

    ${infoBox('Platform Kurallarımız', [
      { label: 'İçerik', value: 'Açık, dürüst ve gerçek bilgiler' },
      { label: 'Görseller', value: 'Kaliteli ve ürünle ilgili fotoğraflar' },
      { label: 'Kategori', value: 'Doğru kategori seçimi' },
      { label: 'Yasallık', value: 'Yasal olmayan ürünler kabul edilmez' }
    ])}

    ${emailButton('Ürünümü Düzenle', `${config.frontendUrl}/profile/items`)}

    <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; margin: 30px 0;">
      <h4 style="color: #2D3748; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">🤝 Yardıma İhtiyacınız mı Var?</h4>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        ${typeText} oluşturma konusunda yardıma ihtiyacınız varsa, destek ekibimiz size rehberlik edebilir. Bizimle iletişime geçmekten çekinmeyin!
      </p>
    </div>

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Sorularınız için buradayız:<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
            <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending item rejection email to ${to}:`, error);
    return null;
  }
};

/**
 * Send an email when an item/product is cancelled/disabled by admin
 * @param to Recipient email (item creator)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param itemName Item/product name
 * @param itemType Item type (Product or Service)
 * @param cancelReason Optional reason for cancellation
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendItemCancelledEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  itemName: string,
  itemType: string,
  cancelReason?: string,
  language: string = 'tr'
) => {
  const typeText = itemType.toLowerCase() === 'product' ? 'Ürün' : 'Hizmet';
  const subject = `${typeText}ünüz Yayından Kaldırıldı - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\n"${itemName}" ${typeText.toLowerCase()}ünüz platformumuzdan kaldırıldı.\n\n${cancelReason ? `Sebep: ${cancelReason}\n\n` : ''}Sorularınız için destek ekibimizle iletişime geçebilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background-color: #FEEBC8; border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: #DD6B20;">🔒</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">${typeText} Yayından Kaldırıldı</h2>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName},
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
      <strong>"${itemName}"</strong> ${typeText.toLowerCase()}ünüz platformumuzdan kaldırılmıştır ve artık alıcılar tarafından görüntülenememektedir.
    </p>

    ${cancelReason ? alertBox(`<strong>Kaldırma Sebebi:</strong> ${cancelReason}`, 'warning') : ''}

    <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; margin: 30px 0;">
      <h4 style="color: #2D3748; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">📞 İtiraz veya Sorularınız mı Var?</h4>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        Bu kararla ilgili bir itirazınız varsa veya daha fazla bilgi almak istiyorsanız, destek ekibimizle iletişime geçebilirsiniz. Size yardımcı olmaktan mutluluk duyarız.
      </p>
    </div>

    ${emailButton('Destek Ekibi ile İletişim', 'mailto:<EMAIL>')}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Platform Kurallarımızı Hatırlayın</h3>

    ${featureList([
      'Tüm ürünler gerçek ve satışa hazır olmalıdır',
      'Açık ve dürüst ürün açıklamaları yapılmalıdır',
      'Uygun kategori seçilmelidir',
      'Yasal olmayan ürünler kabul edilmez',
      'Spam ve sahte içerikler yasaktır'
    ])}

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Bizimle iletişime geçin:<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
            <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending item cancellation email to ${to}:`, error);
    return null;
  }
};

// ==================== STORE EMAIL FUNCTIONS ====================

/**
 * Send an email when a new store is created and submitted for approval
 * @param to Recipient email (store owner)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param storeName Store name
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendStoreCreatedEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  storeName: string,
  language: string = 'tr'
) => {
  const subject = `Mağazanız Başarıyla Oluşturuldu - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\n"${storeName}" mağazanız başarıyla oluşturuldu ve onay için gönderildi.\n\nMağazanız admin ekibimiz tarafından incelendikten sonra yayınlanacaktır. Bu süreç genellikle 24-48 saat sürmektedir.\n\nDurum güncellemeleri için profilinizi kontrol edebilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #5B21B6 0%, #7C3AED 100%); border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: white;">🏪</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 32px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Mağazanız Oluşturuldu!</h2>

    <p style="color: #4A5568; font-size: 18px; line-height: 28px; margin: 0 0 30px 0; text-align: center;">
      Merhaba ${firstName} ${lastName}! 👋
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
      <strong>"${storeName}"</strong> mağazanız başarıyla oluşturuldu ve onay sürecine alındı.
    </p>

    ${infoBox('Mağaza Bilgileri', [
      { label: 'Mağaza Adı', value: storeName },
      { label: 'Durum', value: 'Onay Bekliyor ⏳' },
      { label: 'Oluşturma Tarihi', value: new Date().toLocaleDateString('tr-TR') }
    ])}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Sırada Ne Var?</h3>

    ${featureList([
      'Admin ekibimiz mağazanızı 24-48 saat içinde inceleyecek',
      'Onaylandıktan sonra mağazanız platformda yayınlanacak',
      'Ürün ve hizmetlerinizi eklemeye başlayabileceksiniz',
      'Potansiyel alıcılar mağazanızı ziyaret edebilecek'
    ])}

    ${alertBox('Mağaza onaylandıktan sonra ürün ve hizmetlerinizi eklemeyi unutmayın!', 'info')}

    ${emailButton('Mağazamı Görüntüle', `${config.frontendUrl}/profile/store`)}

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <h3 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 20px 0;">Başarı İpuçları</h3>
          <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0 0 20px 0;">
            Mağazanız onaylandıktan sonra logo, kapak görseli ve detaylı açıklama ekleyerek profilinizi tamamlayın. Profesyonel bir profil daha çok müşteri çeker!
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending store creation email to ${to}:`, error);
    return null;
  }
};

/**
 * Send an email when a store is approved by admin
 * @param to Recipient email (store owner)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param storeName Store name
 * @param storeId Store ID for linking
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendStoreApprovedEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  storeName: string,
  storeId: string,
  language: string = 'tr'
) => {
  const subject = `🎉 Mağazanız Onaylandı ve Yayınlandı - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\nHarika haber! "${storeName}" mağazanız onaylandı ve artık platformumuzda yayında.\n\nŞimdi ürün ve hizmetlerinizi ekleyerek satışa başlayabilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #48BB78 0%, #38A169 100%); border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: white;">🎉</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 32px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Tebrikler! Mağazanız Onaylandı</h2>

    <p style="color: #4A5568; font-size: 18px; line-height: 28px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName}, harika haber! 🎊
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      <strong>"${storeName}"</strong> mağazanız başarıyla onaylandı ve artık E-Export City'de yayında!
    </p>

    ${alertBox('Mağazanız artık canlı! Müşteriler mağazanızı ziyaret edebilir ve ürünlerinizi görebilir.', 'success')}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Şimdi Neler Yapabilirsiniz?</h3>

    ${featureList([
      'Ürün ve hizmetlerinizi ekleyin',
      'Mağaza profilinizi tamamlayın (logo, kapak görseli vb.)',
      'Sosyal medya hesaplarınızı bağlayın',
      'Gelen mesajları ve talepleri takip edin',
      'Mağaza istatistiklerinizi görüntüleyin'
    ])}

    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 30px 0;">
      <tr>
        <td align="center">
          <a href="${config.frontendUrl}/store/${storeId}" style="display: inline-block; padding: 15px 30px; background-color: #4299E1; color: white; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; margin-right: 15px;">Mağazamı Görüntüle</a>
          <a href="${config.frontendUrl}/add-product" style="display: inline-block; padding: 15px 30px; background-color: #48BB78; color: white; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">Ürün Ekle</a>
        </td>
      </tr>
    </table>

    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #E6FFFA 100%); border-radius: 12px; padding: 25px; margin: 30px 0;">
      <h3 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 15px 0;">💡 İlk Adımlar</h3>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        1. Profesyonel bir logo ve kapak görseli ekleyin<br>
        2. En popüler ürünlerinizi yükleyin<br>
        3. Detaylı ve açık ürün açıklamaları yazın<br>
        4. Müşteri mesajlarına hızlı yanıt verin
      </p>
    </div>

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Başarılı satışlar dileriz!<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
            <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending store approval email to ${to}:`, error);
    return null;
  }
};

/**
 * Send an email when a store is rejected by admin
 * @param to Recipient email (store owner)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param storeName Store name
 * @param rejectionReason Optional reason for rejection
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendStoreRejectedEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  storeName: string,
  rejectionReason?: string,
  language: string = 'tr'
) => {
  const subject = `Mağazanız İnceleme Sonucu - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\nMaalesef "${storeName}" mağazanız onaylanmadı.\n\n${rejectionReason ? `Sebep: ${rejectionReason}\n\n` : ''}Mağazanızı kurallara uygun şekilde düzenleyerek tekrar gönderebilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background-color: #FED7D7; border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: #E53E3E;">⚠️</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Mağaza İnceleme Sonucu</h2>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName},
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
      Maalesef <strong>"${storeName}"</strong> mağazanız inceleme sürecinden geçemedi ve yayınlanamadı.
    </p>

    ${rejectionReason ? alertBox(`<strong>Ret Sebebi:</strong> ${rejectionReason}`, 'error') : ''}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Neler Yapabilirsiniz?</h3>

    ${featureList([
      'Mağaza bilgilerinizi platform kurallarına uygun şekilde düzenleyin',
      'Eksik veya hatalı bilgileri tamamlayın',
      'Gerçek ve doğru şirket bilgileri kullanın',
      'Uygun logo ve görseller ekleyin',
      'Tekrar onaya gönderin'
    ])}

    ${infoBox('Platform Kurallarımız', [
      { label: 'Şirket Bilgileri', value: 'Gerçek ve doğrulanabilir olmalı' },
      { label: 'Logo/Görseller', value: 'Telif hakkı ihlali içermemeli' },
      { label: 'Açıklamalar', value: 'Açık ve profesyonel olmalı' },
      { label: 'İletişim Bilgileri', value: 'Güncel ve erişilebilir olmalı' }
    ])}

    ${emailButton('Mağazamı Düzenle', `${config.frontendUrl}/profile/store`)}

    <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; margin: 30px 0;">
      <h4 style="color: #2D3748; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">🤝 Destek Alabileceğiniz Konular</h4>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        • Mağaza profili oluşturma rehberi<br>
        • Onay kriterlerimiz hakkında detaylı bilgi<br>
        • Teknik sorunlar için yardım<br>
        • Platform kuralları ve politikalarımız
      </p>
    </div>

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Sorularınız için bizimle iletişime geçin:<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
            <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending store rejection email to ${to}:`, error);
    return null;
  }
};

/**
 * Send an email when a store is disabled/deactivated by admin
 * @param to Recipient email (store owner)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param storeName Store name
 * @param disableReason Optional reason for disabling
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendStoreDisabledEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  storeName: string,
  disableReason?: string,
  language: string = 'tr'
) => {
  const subject = `Mağazanız Devre Dışı Bırakıldı - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\n"${storeName}" mağazanız platformumuzda devre dışı bırakıldı.\n\n${disableReason ? `Sebep: ${disableReason}\n\n` : ''}Sorularınız için destek ekibimizle iletişime geçebilirsiniz.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background-color: #FEEBC8; border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: #DD6B20;">🔒</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 28px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Mağaza Devre Dışı Bırakıldı</h2>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName},
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0;">
      <strong>"${storeName}"</strong> mağazanız platformumuzda devre dışı bırakılmıştır. Mağazanız ve ürünleriniz artık müşteriler tarafından görüntülenememektedir.
    </p>

    ${disableReason ? alertBox(`<strong>Devre Dışı Bırakma Sebebi:</strong> ${disableReason}`, 'warning') : ''}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Bu Durumun Etkileri</h3>

    ${featureList([
      'Mağazanız arama sonuçlarında görünmeyecek',
      'Müşteriler mağazanıza erişemeyecek',
      'Tüm ürünleriniz görünmez olacak',
      'Yeni sipariş veya mesaj alamayacaksınız'
    ])}

    <div style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; margin: 30px 0;">
      <h4 style="color: #2D3748; font-size: 16px; font-weight: 600; margin: 0 0 15px 0;">📞 İtiraz veya Açıklama</h4>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        Bu kararla ilgili bir itirazınız varsa veya durumu açıklamak isterseniz, lütfen destek ekibimizle iletişime geçin. Mağazanızın tekrar aktif edilmesi için gerekli adımları birlikte değerlendirebiliriz.
      </p>
    </div>

    ${emailButton('Destek Ekibi ile İletişim', 'mailto:<EMAIL>')}

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Size yardımcı olmak için buradayız:<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a> |
            <a href="tel:+905400668000" style="color: #2C7A7B; text-decoration: none;">+90 ************</a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending store disabled email to ${to}:`, error);
    return null;
  }
};

/**
 * Send an email when a store is re-enabled by admin
 * @param to Recipient email (store owner)
 * @param firstName User's first name
 * @param lastName User's last name
 * @param storeName Store name
 * @param storeId Store ID for linking
 * @param language User's preferred language
 * @returns Email info or null if failed but queued
 */
export const sendStoreEnabledEmail = async (
  to: string,
  firstName: string,
  lastName: string,
  storeName: string,
  storeId: string,
  language: string = 'tr'
) => {
  const subject = `🎉 Mağazanız Tekrar Aktif - E-Export City`;

  const text = `Merhaba ${firstName} ${lastName},\n\nHarika haber! "${storeName}" mağazanız tekrar aktif edildi.\n\nMağazanız ve ürünleriniz artık müşteriler tarafından görüntülenebilir.\n\nSaygılarımızla,\nE-Export City Ekibi`;

  const content = `
    <div style="text-align: center; margin-bottom: 30px;">
      <div style="display: inline-block; width: 100px; height: 100px; background: linear-gradient(135deg, #48BB78 0%, #38A169 100%); border-radius: 50%; line-height: 100px;">
        <span style="font-size: 50px; color: white;">🔓</span>
      </div>
    </div>

    <h2 style="color: #2D3748; font-size: 32px; font-weight: 700; margin: 0 0 20px 0; text-align: center;">Mağazanız Tekrar Aktif!</h2>

    <p style="color: #4A5568; font-size: 18px; line-height: 28px; margin: 0 0 30px 0; text-align: center;">
      Sayın ${firstName} ${lastName}, harika haber! 🎊
    </p>

    <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 30px 0; text-align: center;">
      <strong>"${storeName}"</strong> mağazanız başarıyla aktif edildi ve artık müşterilerinize tekrar ulaşabilirsiniz!
    </p>

    ${alertBox('Mağazanız artık aktif! Tüm ürün ve hizmetleriniz müşteriler tarafından görüntülenebilir.', 'success')}

    <h3 style="color: #2D3748; font-size: 20px; font-weight: 600; margin: 30px 0 20px 0;">Kaldığınız Yerden Devam Edin</h3>

    ${featureList([
      'Mağazanız arama sonuçlarında tekrar görünecek',
      'Müşteriler ürünlerinizi görebilecek',
      'Yeni mesaj ve siparişler alabileceksiniz',
      'Tüm mağaza özellikleriniz aktif'
    ])}

    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 30px 0;">
      <tr>
        <td align="center">
          <a href="${config.frontendUrl}/store/${storeId}" style="display: inline-block; padding: 15px 30px; background-color: #4299E1; color: white; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px;">Mağazama Git</a>
        </td>
      </tr>
    </table>

    <div style="background: linear-gradient(135deg, #EBF8FF 0%, #E6FFFA 100%); border-radius: 12px; padding: 25px; margin: 30px 0;">
      <h3 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 15px 0;">💪 Güçlü Bir Dönüş İçin</h3>
      <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">
        • Ürün listenizi güncelleyin<br>
        • Bekleyen mesajları yanıtlayın<br>
        • Yeni kampanyalar başlatın<br>
        • Sosyal medyada duyuru yapın
      </p>
    </div>

    ${divider()}

    <table border="0" cellpadding="0" cellspacing="0" width="100%">
      <tr>
        <td align="center">
          <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
            Başarılı satışlar dileriz!<br>
            <a href="mailto:<EMAIL>" style="color: #2C7A7B; text-decoration: none;"><EMAIL></a>
          </p>
        </td>
      </tr>
    </table>
  `;

  const html = baseEmailTemplate(content);

  try {
    return await sendEmail(to, subject, text, html, true);
  } catch (error: any) {
    logger.error(`Error sending store enabled email to ${to}:`, error);
    return null;
  }
};
