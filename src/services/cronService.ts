import cron from 'node-cron';
import { Subscription } from '../models/Subscription';
import { StoredCard } from '../models/StoredCard';
import { Package } from '../models/Package';
import { checkIyzicoSubscriptionStatus, makePaymentWithStoredCard, retrySubscriptionPayment } from './iyzicoPaymentService';
import { addMonths, startOfDay, endOfDay } from 'date-fns';
import { sendEmail } from './emailService';
import { IUser, User } from '../models/User';
import logger from '../utils/logger';

// Define a type for subscription statuses
type SubscriptionStatus =
  | 'ACTIVE'
  | 'EXPIRED'
  | 'UNPAID'
  | 'CANCELED'
  | 'PENDING'
  | 'UPGRADED'
  | 'FAILED';

const getPreferredLanguage = (user: any) => {
  return user?.preferredLanguage || process.env.DEFAULT_LANGUAGE || 'tr';
};

/**
 * Initializes all cron jobs for the application
 */
export const initCronJobs = () => {
  // Run daily at midnight
  cron.schedule('0 0 * * *', async () => {
    try {
      logger.info('Running daily cleanup jobs');
      await cleanupExpiredSubscriptions();
      logger.info('Daily cleanup jobs completed');
    } catch (error) {
      logger.error('Error in daily cleanup job:', error);
    }
  });

  // Run weekly on Sunday at 1am
  cron.schedule('0 1 * * 0', async () => {
    try {
      logger.info('Running weekly maintenance jobs');
      await updateReferralStats();
      logger.info('Weekly maintenance jobs completed');
    } catch (error) {
      logger.error('Error in weekly maintenance job:', error);
    }
  });
};

/**
 * Updates subscription status for expired subscriptions
 */
async function cleanupExpiredSubscriptions() {
  const now = new Date();

  // Find all subscriptions that need to be expired
  const expiredSubscriptions = await Subscription.find({
    endDate: { $lt: now },
    status: { $in: ['ACTIVE', 'TRIAL', 'CANCELED'] }
  }).populate('userId');

  let updatedCount = 0;

  for (const subscription of expiredSubscriptions) {
    try {
      // Update subscription status to EXPIRED
      subscription.status = 'EXPIRED';
      await subscription.save();
      
      // Clear subscription from user document
      if (subscription.userId) {
        await User.findByIdAndUpdate(subscription.userId._id, {
          $unset: { subscription: 1 },
          hasPackage: false
        });
      }
      
      updatedCount++;
    } catch (error) {
      logger.error(`Error expiring subscription ${subscription._id}:`, error);
    }
  }

  logger.info(`Updated ${updatedCount} expired subscriptions and cleared user subscription fields`);
}

/**
 * Updates referral statistics for all users
 */
async function updateReferralStats() {
  try {
    // Find all users with referrals
    const usersWithReferrals = await User.find({
      referrals: { $exists: true, $ne: [] }
    });

    let updatedCount = 0;

    for (const user of usersWithReferrals) {
      // Check if each referral has an active package
      if (user.referrals && user.referrals.length > 0) {
        for (let i = 0; i < user.referrals.length; i++) {
          const referralId = user.referrals[i]._id;

          const referredUser = await User.findById(referralId);

          if (referredUser) {
            // Check if user has an active subscription
            const hasActivePackage = await Subscription.exists({
              userId: referredUser._id,
              status: 'ACTIVE'
            });

            // Update the hasPackage status if it's different
            if (!!hasActivePackage !== user.referrals[i].hasPackage) {
              user.referrals[i].hasPackage = !!hasActivePackage;
              updatedCount++;
            }
          }
        }

        await user.save();
      }
    }

    logger.info(`Updated package status for ${updatedCount} referrals`);
  } catch (error) {
    logger.error('Error updating referral stats:', error);
  }
}

export const startCronJobs = () => {
  // Check subscription status daily at 8 AM
  cron.schedule('0 8 * * *', async () => {
    console.log('Running subscription status check cron job...');
    await checkSubscriptionStatus();
  });

  // Check for failed payments daily at 9 AM
  cron.schedule('0 9 * * *', async () => {
    console.log('Running failed payment check cron job...');
    await checkFailedPayments();
  });
};

async function checkSubscriptionStatus() {
  try {
    const today = new Date();
    const subscriptions = await Subscription.find({
      status: { $in: ['ACTIVE', 'UNPAID', 'PENDING'] },
      renewalDate: {
        $gte: startOfDay(today),
        $lte: endOfDay(today)
      }
    }).populate('packageId userId');

    for (const subscription of subscriptions) {
      try {
        // Check subscription status with Iyzico
        const status: SubscriptionStatus = await checkIyzicoSubscriptionStatus(subscription.iyzicoSubscriptionReferenceCode);

        switch (status) {
          case 'ACTIVE':
            // Update next billing date
            const nextBillingDate = addMonths(subscription.nextBillingDate, 1);
            const packageDoc = subscription.packageId as any;
            await Subscription.findByIdAndUpdate(subscription._id, {
              nextBillingDate,
              $set: {
                remainingViewRequests: packageDoc.viewRequestLimit,
                remainingCreateRequests: packageDoc.createRequestLimit
              }
            });
            break;

          case 'CANCELED':
          case 'EXPIRED':
          case 'UNPAID':
            // Handle failed or inactive payment
            await handleFailedPayment(subscription);
            break;
        }
      } catch (error:any) {
        console.error(`Error processing subscription ${subscription._id}:`, error);
        // Send notification to admin
        await sendEmail(
          process.env.ADMIN_EMAIL as string,
          'Subscription Processing Error',
          `Error processing subscription ${subscription._id}: ${error.message}`
        );
      }
    }
  } catch (error) {
    console.error('Error in checkSubscriptionStatus:', error);
  }
};

const handleFailedPayment = async (subscription: any) => {
  const user = subscription.userId as IUser;
  const language = getPreferredLanguage(user);

  // Send email to user
  await sendEmail(
    user.email,
    language === 'en' ? 'Payment Failed' : 'Ödeme Başarısız',
    language === 'en'
      ? `Your subscription payment has failed. Please update your payment information to continue using our services.`
      : `Abonelik ödemeniz başarısız oldu. Hizmetlerimizi kullanmaya devam edebilmek için lütfen ödeme bilgilerinizi güncelleyin.`
  );

  // Deactivate subscription if payment fails
  await Subscription.findByIdAndUpdate(subscription._id, {
    isActive: false,
    status: 'payment_failed'
  });

  // Clear subscription from user document
  await User.findByIdAndUpdate(user._id, {
    $unset: { subscription: 1 },
    hasPackage: false
  });
};

const checkFailedPayments = async () => {
  try {
    const failedSubscriptions = await Subscription.find({
      status: 'payment_failed',
      paymentStatus: 'failed',
      endDate: {
        $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
      }
    }).populate('userId packageId');

    for (const subscription of failedSubscriptions) {
      // Try to charge again with stored card
      const result = await retrySubscriptionPayment(subscription);

      if (result.status === 'success') {
        await Subscription.findByIdAndUpdate(subscription._id, {
          isActive: true,
          status: 'active',
          nextBillingDate: addMonths(new Date(), 1)
        });

        // Send success email
        const user = await User.findById(subscription.userId);
        const language = getPreferredLanguage(user);

        await sendEmail(
          user?.email as string,
          language === 'en' ? 'Payment Successful' : 'Ödeme Başarılı',
          language === 'en'
            ? 'Your subscription payment has been processed successfully.'
            : 'Abonelik ödemeniz başarıyla gerçekleştirildi.'
        );
      }
    }
  } catch (error) {
    console.error('Error in checkFailedPayments:', error);
  }
};
