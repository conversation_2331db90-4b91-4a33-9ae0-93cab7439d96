import { config } from '../config';

// Base email template with world-class design
export const baseEmailTemplate = (content: string) => `
<!DOCTYPE html>
<html lang="tr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>E-Export City</title>
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings>
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <![endif]-->
  <style>
    /* Reset styles */
    body, table, td, a { -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; }
    table, td { mso-table-lspace: 0pt; mso-table-rspace: 0pt; }
    img { -ms-interpolation-mode: bicubic; }
    img { border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none; }
    table { border-collapse: collapse !important; }
    body { height: 100% !important; margin: 0 !important; padding: 0 !important; width: 100% !important; }
    
    /* Mobile styles */
    @media screen and (max-width: 600px) {
      .mobile-hide { display: none !important; }
      .mobile-center { text-align: center !important; }
      .container { padding: 0 !important; width: 100% !important; }
      .content { padding: 10px !important; }
      .button { width: 100% !important; }
    }
    
    /* Dark mode styles */
    @media (prefers-color-scheme: dark) {
      .dark-mode-bg { background-color: #1a1a1a !important; }
      .dark-mode-text { color: #ffffff !important; }
    }
  </style>
</head>
<body style="margin: 0; padding: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; background-color: #f7fafc;">
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td align="center" style="padding: 40px 0;">
        <table class="container" border="0" cellpadding="0" cellspacing="0" width="600" style="background-color: #ffffff; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          <!-- Header -->
          <tr>
            <td style="background: linear-gradient(135deg, #2C7A7B 0%, #276749 100%); padding: 40px 20px; text-align: center; border-radius: 16px 16px 0 0;">
              <table border="0" cellpadding="0" cellspacing="0" width="100%">
                <tr>
                  <td align="center">
                    <h1 style="color: #ffffff; font-size: 32px; font-weight: 700; margin: 0; letter-spacing: -0.5px;">E-Export City</h1>
                    <p style="color: #E6FFFA; font-size: 16px; margin: 10px 0 0 0;">Küresel Ticaretin Dijital Adresi</p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          
          <!-- Content -->
          <tr>
            <td class="content" style="padding: 40px 30px;">
              ${content}
            </td>
          </tr>
          
          <!-- Footer -->
          <tr>
            <td style="background-color: #f7fafc; padding: 30px; text-align: center; border-radius: 0 0 16px 16px;">
              <table border="0" cellpadding="0" cellspacing="0" width="100%">
                <tr>
                  <td align="center" style="padding-bottom: 20px;">
                    <table border="0" cellpadding="0" cellspacing="0">
                      <tr>
                        <td align="center" style="padding: 0 10px;">
                          <a href="https://facebook.com/eexportcity" style="text-decoration: none;">
                            <img src="https://img.icons8.com/color/48/facebook.png" alt="Facebook" width="32" height="32" style="display: block;">
                          </a>
                        </td>
                        <td align="center" style="padding: 0 10px;">
                          <a href="https://twitter.com/eexportcity" style="text-decoration: none;">
                            <img src="https://img.icons8.com/color/48/twitter.png" alt="Twitter" width="32" height="32" style="display: block;">
                          </a>
                        </td>
                        <td align="center" style="padding: 0 10px;">
                          <a href="https://linkedin.com/company/eexportcity" style="text-decoration: none;">
                            <img src="https://img.icons8.com/color/48/linkedin.png" alt="LinkedIn" width="32" height="32" style="display: block;">
                          </a>
                        </td>
                        <td align="center" style="padding: 0 10px;">
                          <a href="https://instagram.com/eexportcity" style="text-decoration: none;">
                            <img src="https://img.icons8.com/color/48/instagram-new.png" alt="Instagram" width="32" height="32" style="display: block;">
                          </a>
                        </td>
                      </tr>
                    </table>
                  </td>
                </tr>
                <tr>
                  <td align="center">
                    <p style="color: #718096; font-size: 14px; line-height: 20px; margin: 0;">
                      © ${new Date().getFullYear()} E-Export City. Tüm hakları saklıdır.
                    </p>
                    <p style="color: #718096; font-size: 12px; line-height: 18px; margin: 10px 0 0 0;">
                      Bu e-posta size E-Export City tarafından gönderilmiştir.<br>
                      <a href="${config.frontendUrl}/unsubscribe" style="color: #2C7A7B; text-decoration: underline;">E-posta tercihlerini güncelle</a> | 
                      <a href="${config.frontendUrl}/privacy" style="color: #2C7A7B; text-decoration: underline;">Gizlilik Politikası</a>
                    </p>
                  </td>
                </tr>
              </table>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
</html>
`;

// Button component
export const emailButton = (text: string, href: string, color: string = '#2C7A7B') => `
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td align="center" style="padding: 20px 0;">
        <table border="0" cellpadding="0" cellspacing="0">
          <tr>
            <td align="center" style="border-radius: 8px; background-color: ${color};">
              <a href="${href}" target="_blank" style="display: inline-block; padding: 16px 32px; font-size: 16px; font-weight: 600; color: #ffffff; text-decoration: none; border-radius: 8px; background-color: ${color}; border: 2px solid ${color}; transition: all 0.3s ease;">${text}</a>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
`;

// Alert box component
export const alertBox = (content: string, type: 'info' | 'warning' | 'error' | 'success' = 'info') => {
  const colors = {
    info: { bg: '#EBF8FF', border: '#3182CE', text: '#2B6CB0' },
    warning: { bg: '#FEFCBF', border: '#D69E2E', text: '#975A16' },
    error: { bg: '#FED7D7', border: '#E53E3E', text: '#C53030' },
    success: { bg: '#F0FFF4', border: '#38A169', text: '#276749' }
  };
  
  const color = colors[type];
  
  return `
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 20px 0;">
      <tr>
        <td style="background-color: ${color.bg}; border-left: 4px solid ${color.border}; padding: 16px; border-radius: 4px;">
          <p style="color: ${color.text}; font-size: 14px; line-height: 20px; margin: 0;">${content}</p>
        </td>
      </tr>
    </table>
  `;
};

// Feature list component
export const featureList = (features: string[]) => `
  <table border="0" cellpadding="0" cellspacing="0" width="100%" style="margin: 20px 0;">
    ${features.map(feature => `
      <tr>
        <td style="padding: 8px 0;">
          <table border="0" cellpadding="0" cellspacing="0">
            <tr>
              <td style="width: 24px; vertical-align: top;">
                <img src="https://img.icons8.com/color/24/checkmark.png" alt="✓" width="20" height="20" style="display: block;">
              </td>
              <td style="padding-left: 10px;">
                <p style="color: #4A5568; font-size: 14px; line-height: 20px; margin: 0;">${feature}</p>
              </td>
            </tr>
          </table>
        </td>
      </tr>
    `).join('')}
  </table>
`;

// Info box component
export const infoBox = (title: string, items: { label: string; value: string }[]) => `
  <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #F7FAFC; border-radius: 8px; padding: 20px; margin: 20px 0;">
    <tr>
      <td>
        <h3 style="color: #2D3748; font-size: 18px; font-weight: 600; margin: 0 0 16px 0;">${title}</h3>
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
          ${items.map(item => `
            <tr>
              <td style="padding: 6px 0;">
                <strong style="color: #4A5568; font-size: 14px;">${item.label}:</strong>
                <span style="color: #2D3748; font-size: 14px; margin-left: 8px;">${item.value}</span>
              </td>
            </tr>
          `).join('')}
        </table>
      </td>
    </tr>
  </table>
`;

// Divider component
export const divider = () => `
  <table border="0" cellpadding="0" cellspacing="0" width="100%">
    <tr>
      <td style="padding: 20px 0;">
        <table border="0" cellpadding="0" cellspacing="0" width="100%">
          <tr>
            <td style="border-bottom: 1px solid #E2E8F0;"></td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
`;