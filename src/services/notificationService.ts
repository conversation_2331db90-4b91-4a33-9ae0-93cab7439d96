// services/notificationService.ts
import { Notification } from '../models/Notification';
import { io } from '../index';
import { netgsmService } from './netgsmService';
import { User } from '../models/User';
import { sendEmail } from './emailService';
import { baseEmailTemplate, emailButton, alertBox } from './emailTemplates';

interface NotificationData {
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  sendSMS?: boolean;
  sendEmail?: boolean;
}

export const sendNotification = async (userId: string, notification: NotificationData) => {
  try {
    // Create notification in database
    const newNotification = new Notification({
      userId,
      type: notification.type,
      title: notification.title,
      message: notification.message,
      data: notification.data,
      read: false,
      timestamp: new Date()
    });

    await newNotification.save();

    // Get user details for SMS and email notifications
    const user = await User.findById(userId)
      .populate<{ subscription: { features: { smsNotification: boolean, emailNotification: boolean } } }>('subscription')
      .exec();

    // Check if user has SMS notifications enabled in their subscription
    if (notification.sendSMS && user?.subscription && 'features' in user.subscription && user.subscription.features?.smsNotification && user.phoneNumber) {
      try {
        await netgsmService.sendSMS(user.phoneNumber, notification.message);
      } catch (error:any) {
        console.error('Error sending SMS notification:', error);
      }
    }

    // Check if user has email notifications enabled in their subscription
    if (notification.sendEmail && user?.subscription && 'features' in user.subscription && user.subscription.features?.emailNotification && user.email) {
      const content = `
        <h2 style="color: #2D3748; font-size: 24px; font-weight: 700; margin: 0 0 20px 0;">${notification.title}</h2>
        
        <p style="color: #4A5568; font-size: 16px; line-height: 24px; margin: 0 0 20px 0;">
          ${notification.message}
        </p>
        
        ${notification.data?.url ? emailButton('Detayları Görüntüle', notification.data.url) : ''}
        
        ${notification.type === 'warning' || notification.type === 'alert' ? 
          alertBox('Bu bildirim önemli bir güncelleme içeriyor. Lütfen en kısa sürede kontrol edin.', 'warning') : 
          ''
        }
      `;
      
      const html = baseEmailTemplate(content);
      
      await sendEmail(
        user.email,
        notification.title,
        notification.message,
        html
      );
    }

    // Emit socket event to user
    io.to(`user_${userId}`).emit('notification', {
      ...notification,
      _id: newNotification._id,
      timestamp: newNotification.timestamp
    });

    // If the notification is for a new message and we have product data,
    // also emit to the product room
    if (notification.type === 'newMessage' && notification.data?.productId) {
      io.to(`product_${notification.data.productId}`).emit('notification', {
        ...notification,
        _id: newNotification._id,
        timestamp: newNotification.timestamp
      });
    }

    return newNotification;
  } catch (error:any) {
    console.error('Error sending notification:', error);
    throw error;
  }
};

export const markNotificationAsRead = async (notificationId: string, userId: string) => {
  try {
    const notification = await Notification.findOneAndUpdate(
      { _id: notificationId, userId },
      { read: true },
      { new: true }
    );

    if (notification) {
      io.to(`user_${userId}`).emit('notificationRead', { notificationId });
    }

    return notification;
  } catch (error:any) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
};

export const getUnreadNotifications = async (userId: string) => {
  try {
    return await Notification.find({ userId, read: false })
      .sort({ timestamp: -1 })
      .lean();
  } catch (error:any) {
    console.error('Error getting unread notifications:', error);
    throw error;
  }
};

export const deleteNotification = async (notificationId: string, userId: string) => {
  try {
    const notification = await Notification.findOneAndDelete({ _id: notificationId, userId });

    if (notification) {
      io.to(`user_${userId}`).emit('notificationDeleted', { notificationId });
    }

    return notification;
  } catch (error:any) {
    console.error('Error deleting notification:', error);
    throw error;
  }
};