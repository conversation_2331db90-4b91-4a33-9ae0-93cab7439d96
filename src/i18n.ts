import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Function to dynamically import all JSON files from a directory
async function importAll(context: Record<string, () => Promise<any>>) {
  const files: { [key: string]: any } = {};
  for (const key in context) {
    const matched = key.match(/\.\/locales\/\w+\/(.+)\.json$/);
    if (matched && matched[1]) {
      const module = await context[key]();
      files[matched[1]] = module.default;
    }
  }
  return files;
}

// Import all JSON files from all language folders
const importTranslations = async () => {
  const enContext = import.meta.glob('./locales/en/**/*.json');
  const trContext = import.meta.glob('./locales/tr/**/*.json');
  const arContext = import.meta.glob('./locales/ar/**/*.json');
  const ruContext = import.meta.glob('./locales/ru/**/*.json');
  const frContext = import.meta.glob('./locales/fr/**/*.json');
  const esContext = import.meta.glob('./locales/es/**/*.json');
  const deContext = import.meta.glob('./locales/de/**/*.json');
  const itContext = import.meta.glob('./locales/it/**/*.json');
  const zhContext = import.meta.glob('./locales/zh/**/*.json');

  const enTranslations = await importAll(enContext);
  const trTranslations = await importAll(trContext);
  const arTranslations = await importAll(arContext);
  const ruTranslations = await importAll(ruContext);
  const frTranslations = await importAll(frContext);
  const esTranslations = await importAll(esContext);
  const deTranslations = await importAll(deContext);
  const itTranslations = await importAll(itContext);
  const zhTranslations = await importAll(zhContext);

  // Get all namespaces
  const namespaces = Array.from(new Set([
    ...Object.keys(enTranslations),
    ...Object.keys(trTranslations),
    ...Object.keys(arTranslations),
    ...Object.keys(ruTranslations),
    ...Object.keys(frTranslations),
    ...Object.keys(esTranslations),
    ...Object.keys(deTranslations),
    ...Object.keys(itTranslations),
    ...Object.keys(zhTranslations)
  ]));

  await i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag', 'path', 'subdomain'],
        caches: ['localStorage'],
        lookupLocalStorage: 'i18nextLng',
        lookupFromNavigatorOnce: true,
        checkWhitelist: true
      },
      resources: {
        tr: trTranslations,
        en: enTranslations,
        ar: arTranslations,
        ru: ruTranslations,
        fr: frTranslations,
        es: esTranslations,
        de: deTranslations,
        it: itTranslations,
        zh: zhTranslations,
      },
      fallbackLng: 'en',
      load: 'languageOnly', // Strip region code (e.g., 'en-US' becomes 'en')
      supportedLngs: ['en', 'tr', 'ar', 'ru', 'fr', 'es', 'de', 'it', 'zh'],
      ns: namespaces,
      defaultNS: 'common',
      debug: import.meta.env.DEV,
      interpolation: {
        escapeValue: false,
      },
      // Add RTL support
      dir: (lng: string) => lng === 'ar' ? 'rtl' : 'ltr'
    } as any);

  return i18n;
};

export default importTranslations();