/**
 * Email Service Test File
 *
 * This file demonstrates the various email templates from the email service.
 * It's designed to run in development mode to show how emails would appear.
 * It also saves HTML email templates to files for browser viewing.
 *
 * To run: ts-node test-email-service.ts
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import nodemailer from 'nodemailer';
import {
  sendWelcomeEmail,
  sendPackagePurchaseEmail,
  sendPackageCancellationEmail,
  sendPackageRenewalEmail,
  sendPackageExpirationEmail,
  sendValidationEmail,
  sendPasswordChangeSuccessEmail
} from './services/emailService';

// Ensure environment variables are loaded
dotenv.config();

// Set to true to enable sending test emails
const ENABLE_EMAIL_SENDING = true;

// Directory where email HTML files will be saved
const EMAIL_OUTPUT_DIR = path.join(__dirname, '../email-previews');

// Ensure the output directory exists
if (!fs.existsSync(EMAIL_OUTPUT_DIR)) {
  fs.mkdirSync(EMAIL_OUTPUT_DIR, { recursive: true });
  console.log(`Created email output directory: ${EMAIL_OUTPUT_DIR}`);
}

// Recipient email for testing - change this to your email for testing
const TEST_EMAIL = '<EMAIL>';

// Test data
const testData = {
  firstName: 'John',
  lastName: 'Doe',
  packageName: 'Premium Business',
  packagePrice: 499.99,
  currency: 'USD',
  startDate: new Date(),
  endDate: new Date(new Date().setMonth(new Date().getMonth() + 12)), // 12 months later
  expiryDate: new Date(new Date().setDate(new Date().getDate() + 7)), // 7 days later
  daysRemaining: 7,
  validationLink: 'https://e-exportcity.com.tr/validate?token=sample-token-12345',
  languages: ['en', 'tr']
};

/**
 * Save HTML email content to a file for browser viewing
 */
function saveEmailHtml(emailType: string, language: string, html: string): string {
  // Create a filename based on the email type and language
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const filename = `${emailType}_${language}_${timestamp}.html`;
  const filepath = path.join(EMAIL_OUTPUT_DIR, filename);

  // Write the HTML content to the file
  fs.writeFileSync(filepath, html);
  console.log(`📄 Saved email HTML to: ${filepath}`);

  return filepath;
}

/**
 * Generate sample HTML template for an email type
 */
function generateSampleEmailHtml(emailType: string, language: string): string {
  const title = emailType.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');

  return `
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>${title} Email Preview</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #0047AB;
      color: white;
      padding: 20px;
      text-align: center;
      border-radius: 5px 5px 0 0;
    }
    .content {
      padding: 20px;
      border: 1px solid #ddd;
      border-top: none;
      border-radius: 0 0 5px 5px;
    }
    .footer {
      text-align: center;
      font-size: 12px;
      color: #777;
      margin-top: 20px;
    }
    .button {
      background-color: #0047AB;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 3px;
      display: inline-block;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>E-Export City</h1>
    <p>${title} - ${language === 'en' ? 'English' : 'Turkish'}</p>
  </div>
  <div class="content">
    <h2>${language === 'en' ? 'This is a sample email template' : 'Bu bir örnek e-posta şablonudur'}</h2>
    <p>${language === 'en' ?
      'This is a sample preview of the email that would be sent to the recipient.' :
      'Bu, alıcıya gönderilecek e-postanın örnek bir önizlemesidir.'}</p>
    <p>${language === 'en' ?
      'In a real implementation, this would contain the actual HTML content of the email.' :
      'Gerçek bir uygulamada, bu, e-postanın gerçek HTML içeriğini içerecektir.'}</p>
    <a href="#" class="button">${language === 'en' ? 'Sample Button' : 'Örnek Düğme'}</a>
  </div>
  <div class="footer">
    <p>E-Export City Platform, ${new Date().getFullYear()}</p>
  </div>
</body>
</html>
  `;
}

/**
 * Custom function to capture email and generate HTML preview
 */
async function captureAndSaveEmail(
  emailFunction: Function,
  args: any[],
  emailType: string,
  language: string
): Promise<any> {
  try {
    // Call the original email function to get its result
    const result = await emailFunction(...args);

    // Generate sample HTML for this email type and save it
    const html = generateSampleEmailHtml(emailType, language);
    saveEmailHtml(emailType, language, html);

    return result;
  } catch (error) {
    console.error(`Error during email processing (${emailType}):`, error);
    return null;
  }
}

/**
 * Run all email tests
 */
/**
 * Test Gmail configuration specifically
 */
async function testGmailConnection() {
  console.log('🔍 Testing Gmail SMTP Connection...');

  // Set to production mode to test actual email sending
  process.env.NODE_ENV = 'production';
  process.env.EMAIL_DEV_MODE = '';

  try {
    // Import the function to initialize the transporter
    const { emailService }: any = await import('./services/emailService.js');
    const initializeTransporter = (emailService as any).initializeTransporter;

    if (typeof initializeTransporter === 'function') {
      const result = await initializeTransporter();
      if (result) {
        console.log('✅ Gmail SMTP connection successful!');

        // Send a simple test email
        console.log(`📧 Sending a test email to ${TEST_EMAIL}...`);
        const sendResult = await (emailService as any).sendEmail(
          TEST_EMAIL,
          'Gmail SMTP Test',
          'This is a test email sent from the E-Export City platform to verify Gmail SMTP settings.',
          '<h1>Gmail SMTP Test</h1><p>This is a test email sent from the E-Export City platform to verify Gmail SMTP settings.</p>',
          true
        );

        if (sendResult) {
          console.log('✅ Test email sent successfully!');
          console.log('MessageId:', sendResult.messageId);
          console.log('Envelope:', sendResult.envelope);
        } else {
          console.log('❌ Failed to send test email, but no error was thrown.');
        }
      } else {
        console.log('❌ Gmail SMTP connection failed.');
      }
    } else {
      console.log('❌ initializeTransporter function not found.');
    }
  } catch (error) {
    console.error('❌ Error testing Gmail connection:', error);
  }
}

async function runEmailTests() {
  console.log('🚀 Starting Email Service Tests\n');
  console.log(`Email HTML previews will be saved to: ${EMAIL_OUTPUT_DIR}\n`);

  // For template previews, use dev mode
  process.env.NODE_ENV = 'development';
  process.env.EMAIL_DEV_MODE = 'log';

  // Test each email type with both languages
  for (const language of testData.languages) {
    console.log(`\n==== Testing emails in ${language === 'en' ? 'English' : 'Turkish'} ====\n`);

    // Welcome Email
    console.log('\n🔹 WELCOME EMAIL:');
    await captureAndSaveEmail(
      sendWelcomeEmail,
      [
        TEST_EMAIL,
        testData.firstName,
        testData.lastName,
        language
      ],
      'welcome_email',
      language
    );

    // Package Purchase Email
    console.log('\n🔹 PACKAGE PURCHASE EMAIL:');
    await captureAndSaveEmail(
      sendPackagePurchaseEmail,
      [
        TEST_EMAIL,
        testData.firstName,
        testData.lastName,
        testData.packageName,
        testData.packagePrice,
        testData.currency,
        testData.startDate,
        testData.endDate,
        language
      ],
      'package_purchase',
      language
    );

    // Package Cancellation Email
    console.log('\n🔹 PACKAGE CANCELLATION EMAIL:');
    await captureAndSaveEmail(
      sendPackageCancellationEmail,
      [
        TEST_EMAIL,
        testData.firstName,
        testData.lastName,
        testData.packageName,
        language
      ],
      'package_cancellation',
      language
    );

    // Package Renewal Email
    console.log('\n🔹 PACKAGE RENEWAL EMAIL:');
    await captureAndSaveEmail(
      sendPackageRenewalEmail,
      [
        TEST_EMAIL,
        testData.firstName,
        testData.lastName,
        testData.packageName,
        testData.packagePrice,
        testData.currency,
        testData.startDate,
        testData.endDate,
        language
      ],
      'package_renewal',
      language
    );

    // Package Expiration Email
    console.log('\n🔹 PACKAGE EXPIRATION EMAIL:');
    await captureAndSaveEmail(
      sendPackageExpirationEmail,
      [
        TEST_EMAIL,
        testData.firstName,
        testData.lastName,
        testData.packageName,
        testData.expiryDate,
        testData.daysRemaining,
        language
      ],
      'package_expiration',
      language
    );

    // Validation Email
    console.log('\n🔹 VALIDATION EMAIL:');
    await captureAndSaveEmail(
      sendValidationEmail,
      [
        TEST_EMAIL,
        testData.validationLink,
        language
      ],
      'validation_email',
      language
    );

    // Password Change Success Email
    console.log('\n🔹 PASSWORD CHANGE SUCCESS EMAIL:');
    await captureAndSaveEmail(
      sendPasswordChangeSuccessEmail,
      [
        TEST_EMAIL,
        language
      ],
      'password_change',
      language
    );
  }

  console.log('\n✅ Email Service Tests Completed');
}

// Enable actual email sending if desired
if (ENABLE_EMAIL_SENDING) {
  process.env.NODE_ENV = 'production';
  process.env.EMAIL_DEV_MODE = '';
  console.log('⚠️ WARNING: Emails will be actually sent to', TEST_EMAIL);
}

/**
 * Test Gmail SMTP connection specifically
 */
async function testGmailSMTP() {
  console.log('\n🔍 Testing Gmail SMTP Connection...');

  // Ensure production mode to test actual email sending
  process.env.NODE_ENV = 'production';
  process.env.EMAIL_DEV_MODE = '';

  try {
    // Send a simple test email using welcome email template
    console.log(`📧 Sending a test email to ${TEST_EMAIL}...`);

    const result = await sendWelcomeEmail(
      TEST_EMAIL,
      testData.firstName,
      testData.lastName,
      'en'
    );

    if (result) {
      console.log('✅ Gmail SMTP connection successful!');
      console.log('✅ Test email sent successfully!');
      console.log('MessageId:', result.messageId);
      console.log('Envelope:', JSON.stringify(result.envelope));
      console.log('Response:', result.response);
      return true;
    } else {
      console.log('⚠️ Email might have been queued for retry due to connection issues.');
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing Gmail connection:', error);
    return false;
  }
}

// Decide which tests to run
console.log('\n🚀 Starting Email Service Tests');

if (ENABLE_EMAIL_SENDING) {
  // First test Gmail connection
  console.log('\n==== Testing Gmail SMTP Connection ====');

  testGmailSMTP()
    .then(success => {
      console.log('\n==== Gmail SMTP test completed ====');

      if (success) {
        console.log('\n✅ Gmail is properly configured. You can now send emails.');
      } else {
        console.log('\n❌ Gmail configuration test failed. Please check your settings.');
        console.log('   - Verify EMAIL_USER and EMAIL_APP_PASSWORD in .env or config');
        console.log('   - Ensure "Less secure app access" is enabled for your Gmail account');
        console.log('   - Make sure to use an App Password if 2FA is enabled');
      }

      // Also run template tests if desired (using dev mode)
      // Uncomment the next line to run template tests as well
      // runEmailTests();
    })
    .catch(error => {
      console.error('Error during Gmail test:', error);
    });
} else {
  // Just run email template tests in dev mode
  runEmailTests()
    .then(() => {
      console.log('\nAll tests completed successfully.');
      console.log(`\n📂 You can view the email HTML files in: ${EMAIL_OUTPUT_DIR}`);
      console.log('   Open these files in your browser to see the email templates.');
      console.log('\n💡 Tip: You can use your browser to open the HTML files directly from the directory.');
    })
    .catch(error => {
      console.error('Error running tests:', error);
    });
}
