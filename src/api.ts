import axios from 'axios';
import { IUser, IAuthResponse, ICity, ICountry } from './types/user';
import { IConversation, IMessage, IMessageResponse } from './types/message';
import { IReview, IReviewResponse } from './types/review';
import { IPackage } from './types/package';
import { IItem, IItemRequest } from './types/item';
import { CategoryUpdateInput, ICategory } from './types/category';
import { IStore } from './types/store';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  withCredentials: true
});


const setUserToken = (token: string) => {
  localStorage.setItem('userToken', token);
};

export const getUserToken = () => {
  return localStorage.getItem('userToken');
};

// Add interceptors to inject the correct token and language
api.interceptors.request.use((config: any) => {
  const language = localStorage.getItem('i18nextLng') || 'tr';
  config.headers['Accept-Language'] = language;

  if (!config.headers) {
    config.headers = {};
  }

  // Check if we should use admin token based on both URL and stored state
  const token = getUserToken();

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  return config;
}, (error) => {
  return Promise.reject(error);
});

api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      const isAdminRoute = error.config.url?.startsWith('/admin');
      if (isAdminRoute) {
        localStorage.removeItem('adminToken');
      } else {
        localStorage.removeItem('userToken');
      }
    }
    return Promise.reject(error);
  }
);

// Chat and Messaging Endpoints
export const getMessages = async (): Promise<IConversation[]> => {
  const response = await api.get<IConversation[]>('/messages');
  return response.data;
};

export const sendMessage = async (data: { content: string; recipientId: string; productId?: string; productName?: string; roomId?: string }): Promise<IMessageResponse> => {
  const response = await api.post<IMessageResponse>('/messages', data);
  return response.data;
};

export const getConversation = async (otherUserId: string): Promise<IMessage[]> => {
  const response = await api.get<IMessage[]>(`/messages/conversation/${otherUserId}`);
  return response.data;
};

export const getChatHistory = async (roomId: string): Promise<any[]> => {
  const response = await api.get<any[]>(`/chat/${roomId}`);
  return response.data;
};

// Live Chat API
export const startLiveChat = async (data: { name: string, email?: string, subject?: string, anonymousId?: string }): Promise<any> => {
  const response = await api.post<any>('/live-chat/start', data);
  return response.data;
};

export const getLiveChatHistory = async (chatId: string, anonymousId?: string): Promise<any> => {
  const url = anonymousId
    ? `/live-chat/${chatId}?anonymousId=${anonymousId}`
    : `/live-chat/${chatId}`;
  const response = await api.get<any>(url);
  return response.data;
};

export const sendLiveChatMessage = async (chatId: string, data: { content: string, anonymousId?: string }): Promise<any> => {
  const response = await api.post<any>(`/live-chat/${chatId}/message`, data);
  return response.data;
};

export const closeLiveChat = async (chatId: string, anonymousId?: string): Promise<any> => {
  const response = await api.post<any>(`/live-chat/${chatId}/close`, { anonymousId });
  return response.data;
};

export const markLiveChatMessagesAsRead = async (chatId: string, anonymousId?: string): Promise<any> => {
  const response = await api.post<any>(`/live-chat/${chatId}/read`, { anonymousId });
  return response.data;
};

// Admin Live Chat API
export const getActiveLiveChats = async (): Promise<any> => {
  const response = await api.get<any>('/live-chat/admin/active');
  return response.data;
};

export const getClosedLiveChats = async (page = 1, limit = 10): Promise<any> => {
  const response = await api.get<any>(`/live-chat/admin/closed?page=${page}&limit=${limit}`);
  return response.data;
};

export const getArchivedLiveChats = async (page = 1, limit = 10): Promise<any> => {
  const response = await api.get<any>(`/live-chat/admin/archived?page=${page}&limit=${limit}`);
  return response.data;
};

export const getLiveChatStatistics = async (): Promise<any> => {
  const response = await api.get<any>('/live-chat/admin/statistics');
  return response.data;
};

export const archiveLiveChat = async (chatId: string): Promise<any> => {
  const response = await api.post<any>(`/live-chat/admin/${chatId}/archive`);
  return response.data;
};

export const deleteMessage = async (messageId: string): Promise<{ success: boolean }> => {
  const response = await api.delete<{ success: boolean }>(`/messages/${messageId}`);
  return response.data;
};

// Notification Endpoints
export const getUnreadNotifications = () => {
  return api.get('/notifications/unread');
};

export const markNotificationAsRead = (notificationId: string) => {
  return api.post(`/notifications/${notificationId}/read`);
};

export const markAllNotificationsAsRead = () => {
  return api.post('/notifications/read-all');
};

export const deleteNotification = (notificationId: string) => {
  return api.delete(`/notifications/${notificationId}`);
};

export const clearAllNotifications = () => {
  return api.post('/notifications/clear-all');
};

export const deleteItemRequest = (itemId: string) => {
  return api.delete(`/items/${itemId}`);
};

export const getOwnedItemRequests = async (): Promise<IItemRequest[]> => {
  const response = await api.get<IItemRequest[]>('/items/owned');
  return response.data;
};

interface ItemRequestData {
  name: string;
  description: string;
  type: 'product' | 'service';
  listingType?: 'demand' | 'sale';
  category: string;
  images: string[];
}

export const createItemRequest = async (data: ItemRequestData): Promise<any> => {
  try {
    const response = await api.post('/items', data);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const updateItemRequest = async (requestId: string, data: ItemRequestData): Promise<any> => {
  try {
    const response = await api.put(`/items/${requestId}`, data);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Item Endpoints
export const getItems = async (): Promise<IItem[]> => {
  const response = await api.get<IItem[]>('/items');
  return response.data;
};
export const getOwnedItemsByType = async (type: string): Promise<IItem[]> => {
  const response = await api.get<IItem[]>(`/items/owned/type/${type}`);
  return response.data;
};
export const getItemsByType = async (type: string): Promise<IItem[]> => {
  const response = await api.get<IItem[]>(`/items/type/${type}`);
  return response.data;
};

export const getItemsByCategoryId = async (categoryId: string): Promise<IItem[]> => {
  const response = await api.get<IItem[]>(`/items/category/${categoryId}`);
  return response.data;
};

export const getItemsListing = async (): Promise<IItem[]> => {
  const response = await api.get<IItem[]>('/items/listing');
  return response.data;
};

export const getFilters = async (type: string = 'all', categoryIds?: string): Promise<any[]> => {
  const response = await api.get<any[]>('/items/filters?type=' + type + (categoryIds ? `&categoryIds=${categoryIds}` : ''));
  return response.data;
};

export const createItem = async (itemData: FormData) => {
  // Create item with provided form data

  const response = await api.post('/items', itemData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
  return response.data;
};

export const updateItem = async (itemId: string, itemData: any) => {
  const response = await api.put(`/items/${itemId}`, itemData);
  return response.data;
};

export const deleteItem = async (itemId: string) => {
  const response = await api.delete(`/items/${itemId}`);
  return response.data;
};

export const getItemById = async (id: string): Promise<IItem> => {
  const response = await api.get<IItem>(`/items/${id}`);
  return response.data;
};

export const approveItem = async (id: string): Promise<IItem> => {
  const response = await api.put<IItem>(`/items/${id}/approve`);
  return response.data;
};

export const manageContent = async (contentId: string, action: 'approve' | 'reject' | 'delete'): Promise<IItem> => {
  const response = await api.put<IItem>('/items/manage', { contentId, action });
  return response.data;
};

// Category Endpoints
export const getCategories = async (categoryLevel?: '0' | '1'): Promise<ICategory[]> => {
  const response = await api.get<ICategory[]>('/items/categories' + (categoryLevel ? `?categoryLevel=${categoryLevel}` : ''));
  return response.data;
};

export const getCategoriesByType = async (type: string) => {
  try {
    const response = await api.get(`${import.meta.env.VITE_API_BASE_URL}/items/categories/type/${type}`);
    return response.data;
  } catch (error) {
    // Handle error fetching categories by type
    throw error;
  }
};

export const getCategoriesByParentId = async (parentId: string) => {
  try {
    const response = await api.get(`${import.meta.env.VITE_API_BASE_URL}/items/categories/${parentId}/children`);
    return response.data;
  } catch (error) {
    // Handle error fetching subcategories
    throw error;
  }
};

export const createCategory = async (categoryData: Partial<ICategory>): Promise<ICategory> => {
  const response = await api.post<ICategory>('/items/categories', categoryData);
  return response.data;
};

export const updateCategory = async (id: string, categoryData: CategoryUpdateInput): Promise<ICategory> => {
  const response = await api.put<ICategory>(`/items/categories/${id}`, categoryData);
  return response.data;
};

export const deleteCategory = async (id: string): Promise<void> => {
  await api.delete(`/items/categories/${id}`);
};

// Package Endpoints
export const getPackages = async (): Promise<any> => {
  const response = await api.get<any>('/packages');
  return response.data;
};

// Auth Endpoints
export const registerUser = async (userData: Partial<IUser>): Promise<IAuthResponse> => {
  const response = await api.post<IAuthResponse>('/auth/register', userData);
  setUserToken(response.data.token); // Store user token
  return response.data;
};

export const forgotPassword = async (email: string): Promise<{ message: string }> => {
  // Construct the correct frontend URL for password reset
  const frontendUrl = import.meta.env.VITE_API_BASE_URL?.includes('localhost') 
    ? 'http://localhost:5173'
    : 'https://www.e-exportcity.com';
  
  const response = await api.post<{ message: string }>('/auth/forgot-password', { 
    email,
    resetUrl: `${frontendUrl}/reset-password`
  });
  return response.data;
};

export const resetPassword = async (token: string, password: string): Promise<{ message: string }> => {
  const response = await api.post<{ message: string }>('/auth/reset-password', { token, password });
  return response.data;
};

export const loginUser = async (userData: { email: string; password: string }): Promise<IAuthResponse> => {
  const response = await api.post<IAuthResponse>('/auth/login', userData);
  setUserToken(response.data.token); // Store user token
  localStorage.removeItem('adminToken');
  return response.data;
};

export const validateRegistration = async (userData: Partial<IUser>): Promise<IAuthResponse> => {
  const response = await api.post<IAuthResponse>('/auth/validate-registration', userData);
  return response.data;
};

export const getCountries = async (): Promise<ICountry[]> => {
  const response = await api.get<ICountry[]>('/auth/countries');
  return response.data;
};

export const getCities = async (countryCode: string): Promise<ICity[]> => {
  const response = await api.get<ICity[]>(`/auth/cities/${countryCode}`);
  return response.data;
};

// User Endpoints
export const getUserProfile = async (): Promise<IUser> => {
  try {
    const response = await api.get<{ success: boolean; data: IUser }>('/users/profile');
    if (!response.data || !response.data.success || !response.data.data) {
      throw new Error('No profile data received');
    }
    return response.data.data;
  } catch (error: any) {
    throw error;
  }
};

export const updateUserProfile = async (userData: Partial<IUser>): Promise<IUser> => {
  const response = await api.put<IUser>('/users/profile', userData);
  return response.data;
};

// Subscription Endpoints
export const getUserSubscription = async (): Promise<any> => {
  const response = await api.get<any>('/subscriptions');
  return response.data;
};

export const getActiveSubscription = async (): Promise<any> => {
  try {
    // Try to get subscription directly from user data first
    const userProfile: any = await getUserProfile().catch(() => null);
    console.log('User profile for subscription check:', userProfile);

    // If user has hasPackage flag, we know they have an active subscription
    if (userProfile && userProfile.hasPackage === true) {
      console.log('User has active package according to user profile');

      // If subscription ID is available in user data, use that
      if (userProfile.subscription) {
        console.log('User has subscription ID in profile:', userProfile.subscription);
      }

      // Try to get full subscription details
      try {
        const response = await api.get<any>('/subscriptions/active');
        console.log('Active subscription response:', response.data);
        return response.data;
      } catch (subError) {
        console.warn('Could not fetch subscription details, but user has active package', subError);
        // Return a minimal subscription object since we know user has a package
        return {
          status: 'ACTIVE',
          _id: userProfile.subscription || 'unknown-id'
        };
      }
    }

    // Default path - try the subscriptions endpoint
    const response = await api.get<any>('/subscriptions/active');
    console.log('Active subscription response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching active subscription:', error);
    throw error;
  }
};

export const cancelSubscription = async (subscriptionId: string): Promise<{ message: string }> => {
  const response = await api.post<{ message: string }>(`/packages/subscription/${subscriptionId}/cancel`);
  return response.data;
};

export const renewSubscription = async (packageId: string): Promise<{ paymentPageUrl: string }> => {
  const response = await api.post<{ paymentPageUrl: string }>('/subscriptions/renew', { packageId });
  return response.data;
};

// Review Endpoints
export const getReviewList = async (productId: string): Promise<IReviewResponse> => {
  const response = await api.get<IReviewResponse>(`/reviews/${productId}`);
  return response.data;
};

export const createReview = async (reviewData: Partial<IReview>): Promise<IReviewResponse> => {
  const response = await api.post<IReviewResponse>('/reviews', reviewData);
  return response.data;
};

export const getUserPackages = async (): Promise<IPackage[]> => {
  const response = await api.get<IPackage[]>('/packages');
  return response.data;
};

export const initiateSubscription = async (packageId: string): Promise<{ paymentPageUrl: string }> => {
  const response = await api.post<{ paymentPageUrl: string }>(`/subscriptions/${packageId}/initiate`);
  return response.data;
};

export const initiatePackagePurchase = async (packageId: string, data: {
  cardInfo?: {
    cardHolderName: string;
    cardNumber: string;
    expireMonth: string;
    expireYear: string;
    cvc: string;
  };
  storedCardId?: string;
  saveCard?: boolean;
  cardAlias?: string;
  isUpgrade?: boolean;
  callbackUrl: string;
}): Promise<{ paymentPageUrl: string; html3DS?: string; token?: string }> => {
  try {
    const endpoint = data.isUpgrade ? `/packages/${packageId}/upgrade` : `/packages/${packageId}/purchase`;
    const response = await api.post(endpoint, data);
    return response.data;
  } catch (error: any) {
    if (axios.isAxiosError(error)) {
      // Handle error response for package purchase
      throw new Error(error.response?.data?.message || 'Failed to initiate package purchase');
    }
    throw error;
  }
};

export const completePackagePurchase = async (token: string): Promise<{ message: string; subscription: any }> => {
  const response = await api.post<{ message: string; subscription: any }>('/packages/complete-purchase', { token });
  return response.data;
};

// Subscription limit checks
export const checkViewRequest = async (itemId: string) => {
  try {
    const response = await api.get(`/subscriptions/check-view-request?itemId=${itemId}`);
    return response.data;
  } catch (error: any) {
    // If the endpoint doesn't exist (404), return a default response that allows viewing
    if (error.response?.status === 404) {
      // Silent fail - don't log anything to console
      return {
        isOwner: false,
        alreadyViewed: false,
        hasRemaining: true,
        totalViews: 0
      };
    }
    throw error;
  }
};

export const useViewRequest = async (itemId: string) => {
  try {
    const response = await api.post('/subscriptions/use-view-request', { itemId });
    return response.data;
  } catch (error: any) {
    // If the endpoint doesn't exist (404), return a default success response
    if (error.response?.status === 404) {
      // Silent fail - don't log anything to console
      return { success: true };
    }
    throw error;
  }
};

// Store view tracking endpoints - silently fail and return defaults when endpoints don't exist yet
export const checkStoreViewRequest = async (storeId: string) => {
  try {
    const response = await api.get(`/subscriptions/check-store-view-request?storeId=${storeId}`);
    return response.data;
  } catch (error: any) {
    // If the endpoint doesn't exist (404), return a default response that allows viewing
    if (error.response?.status === 404) {
      // Silent fail - don't log anything to console
      return {
        isOwner: false,
        alreadyViewed: false,
        hasRemaining: true,
        totalViews: 0
      };
    }
    throw error;
  }
};

export const useStoreViewRequest = async (storeId: string) => {
  try {
    const response = await api.post('/subscriptions/use-store-view-request', { storeId });
    return response.data;
  } catch (error: any) {
    // If the endpoint doesn't exist (404), return a default success response
    if (error.response?.status === 404) {
      // Silent fail - don't log anything to console
      return { success: true };
    }
    throw error;
  }
};

export const checkCreateRequest = async () => {
  const response = await api.get('/subscriptions/check-create-request');
  return response.data;
};

export const useCreateRequest = async () => {
  const response = await api.post('/subscriptions/use-create-request');
  return response.data;
};

// Store Endpoints
export const getStores = (): Promise<IStore[]> => {
  return api.get('/stores').then(response => response.data);
};

export const getStoreById = (id: string): Promise<IStore> => {
  return api.get(`/stores/${id}`).then(response => response.data);
};

export const getStoreItems = (id: string): Promise<IItem[]> => {
  return api.get(`/stores/${id}/items`).then(response => response.data);
};

export const getUserStores = (): Promise<IStore[]> => {
  return api.get('/stores/user/stores').then(response => response.data);
};

export const createStore = (storeData: Partial<IStore>): Promise<IStore> => {
  return api.post('/stores', storeData).then(response => response.data);
};

export const updateStore = (id: string, storeData: Partial<IStore>): Promise<IStore> => {
  return api.put(`/stores/${id}`, storeData).then(response => response.data);
};

export const deleteStore = (id: string): Promise<{ message: string }> => {
  return api.delete(`/stores/${id}`).then(response => response.data);
};

// Home Ads
export const getActiveHomeAds = async () => {
  const response = await api.get('/home-ads/active');
  return response.data;
};

export const createHomeAd = async (title: string, imageFile: File) => {
  const formData = new FormData();
  formData.append('title', title);
  formData.append('image', imageFile);

  const response = await api.post('/home-ads', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};

export const cancelHomeAd = async (adId: string) => {
  const response = await api.delete(`/home-ads/${adId}`);
  return response.data;
};

export const getUserHomeAds = async () => {
  const response = await api.get('/home-ads/user');
  return response.data;
};

// Add this new file upload API
export const uploadFiles = async (files: File[] | File): Promise<string[]> => {
  try {
    const formData = new FormData();

    if (Array.isArray(files)) {
      files.forEach(file => {
        formData.append('files', file);
      });
    } else {
      // Handle single file case
      formData.append('files', files);
    }

    const response = await api.post('/upload/files', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data.paths;
  } catch (error: any) {
    // Handle error uploading files
    throw error;
  }
};

export { api };