import dotenv from 'dotenv';

dotenv.config();

export const config = {
  appUrl: process.env.APP_URL,
  frontendUrl: process.env.FRONTEND_URL || 'https://e-exportcity.com.tr',
  iyzico: {
    apiKey: process.env.IYZICO_API_KEY,
    secretKey: process.env.IYZICO_SECRET_KEY,
    uri: process.env.IYZICO_URI,
  },
  smtp: {
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: Number(process.env.EMAIL_PORT) || 587,
    secure: false,
    user: process.env.EMAIL_USER || '<EMAIL>',
    pass: process.env.EMAIL_APP_PASSWORD || 'sscq revx igps fpaz',
  },
};
