import { Router } from 'express';
import { 
  startChat,
  getChatHistory,
  sendChatMessage,
  closeChat,
  archiveChat,
  getActiveChats,
  getClosedChats,
  getArchivedChats,
  getChatStatistics,
  markMessagesAsRead
} from '../controllers/liveChatController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { adminMiddleware } from '../middlewares/adminMiddleware';

const router = Router();

// Public routes (no auth required)
router.post('/start', startChat);
router.get('/:chatId', getChatHistory);
router.post('/:chatId/message', sendChatMessage);
router.post('/:chatId/close', closeChat);
router.post('/:chatId/read', markMessagesAsRead);

// Admin routes (require auth and admin role)
router.use('/admin', authMiddleware, adminMiddleware);
router.get('/admin/active', getActiveChats);
router.get('/admin/closed', getClosedChats);
router.get('/admin/archived', getArchivedChats);
router.get('/admin/statistics', getChatStatistics);
router.post('/admin/:chatId/archive', archiveChat);

export default router;