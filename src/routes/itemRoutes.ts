import { Router, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import multer from 'multer';
import path from 'path';
import {
  getCategoriesByItemType,
  getCategoriesByParentId,
  getCategories,
  getItems,
  getItemsByType,
  getItemById,
  getItemsByCategory,
  getFilters,
  getItemsListing,
  createItem,
  deleteItem,
  getOwnedItemsByType,
  getOwnedItems,
  updateItem,
  getMostViewedItems,
} from '../controllers/itemController';
import {
  createItemRequest,
  getOwnedItemRequests,
  updateItemRequest,
  deleteItemRequest,
} from '../controllers/itemRequestController';
import { authMiddleware } from '../middlewares/authMiddleware';

const router = Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/');
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb: any) => {
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  },
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

router.get('/filters', getFilters as RequestHandler);
router.get('/most-viewed', getMostViewedItems as RequestHandler);

// Public Routes (no auth required)
router.get('/categories', getCategories as RequestHandler);
router.get('/categories/type/:type', getCategoriesByItemType as RequestHandler);
router.get('/categories/:id/children', getCategoriesByParentId as RequestHandler);
router.get('/listing', getItemsListing as RequestHandler);
router.get('/type/:type', getItemsByType as RequestHandler);
router.get('/category/:categoryId', getItemsByCategory as RequestHandler);
router.get('/:itemId', getItemById as RequestHandler);
router.get('/', getItems as RequestHandler);


// Protected routes that require authentication
router.use(authMiddleware);

// Item Request Routes
router.post('/requests', upload.array('images', 5), createItemRequest as RequestHandler);
router.get('/requests/owned', getOwnedItemRequests as RequestHandler);
router.put('/requests/:requestId', upload.array('images', 5), updateItemRequest as RequestHandler);
router.delete('/requests/:requestId', deleteItemRequest as RequestHandler);

// Item Routes
router.post('/', upload.array('images', 5), createItem as RequestHandler);
router.delete('/:itemId', deleteItem as RequestHandler);
router.get('/owned', getOwnedItems as RequestHandler);
router.get('/owned/type/:type', getOwnedItemsByType as RequestHandler);
router.put('/:itemId', upload.array('images', 5), updateItem as RequestHandler); // Add this line


export default router;