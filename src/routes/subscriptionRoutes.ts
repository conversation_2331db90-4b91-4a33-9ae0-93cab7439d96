import { Router } from 'express';
import { 
  initiateSubscription, 
  completeSubscription, 
  getUserSubscription, 
  cancelSubscription, 
  getActiveSubscription,
  renewSubscription,
  checkViewRequest,
  useViewRequest,
  checkCreateRequest,
  useCreateRequest,
  checkStoreViewRequest,
  useStoreViewRequest
} from '../controllers/subscriptionController';
import { authMiddleware } from '../middlewares/authMiddleware';

const router = Router();

/**
 * @swagger
 * /api/subscriptions/initiate:
 *   post:
 *     summary: Initiate a subscription
 *     tags: [Subscriptions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Subscription initiated successfully
 *       401:
 *         description: Unauthorized
 */
router.post('/initiate', authMiddleware, initiateSubscription);

/**
 * @swagger
 * /api/subscriptions/complete:
 *   get:
 *     summary: Complete a subscription
 *     tags: [Subscriptions]
 *     responses:
 *       200:
 *         description: Subscription completed successfully
 *       401:
 *         description: Unauthorized
 */
router.get('/complete', completeSubscription);

/**
 * @swagger
 * /api/subscriptions/user:
 *   get:
 *     summary: Get user's subscriptions
 *     tags: [Subscriptions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of user's subscriptions
 *       401:
 *         description: Unauthorized
 */
router.get('/user', authMiddleware, getUserSubscription);

/**
 * @swagger
 * /api/subscriptions/cancel:
 *   post:
 *     summary: Cancel user's subscription
 *     tags: [Subscriptions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Subscription cancelled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *       401:
 *         description: Unauthorized
 */
router.post('/cancel', authMiddleware, cancelSubscription);

/**
 * @swagger
 * /api/subscriptions/active:
 *   get:
 *     summary: Get user's active subscription
 *     tags: [Subscriptions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User's active subscription details
 *       401:
 *         description: Unauthorized
 */
router.get('/active', authMiddleware, getActiveSubscription);

/**
 * @swagger
 * /api/subscriptions/renew:
 *   post:
 *     summary: Renew subscription with a package
 *     tags: [Subscriptions]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               packageId:
 *                 type: string
 *             required:
 *               - packageId
 *     responses:
 *       200:
 *         description: Payment page URL for subscription renewal
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 paymentPageUrl:
 *                   type: string
 *       401:
 *         description: Unauthorized
 *       400:
 *         description: Invalid package ID
 */
router.post('/renew', authMiddleware, renewSubscription);

// Subscription limit check routes
router.get('/check-view-request', authMiddleware, checkViewRequest);
router.post('/use-view-request', authMiddleware, useViewRequest);
router.get('/check-create-request', authMiddleware, checkCreateRequest);
router.post('/use-create-request', authMiddleware, useCreateRequest);

// Store view request routes
router.get('/check-store-view-request', authMiddleware, checkStoreViewRequest);
router.post('/use-store-view-request', authMiddleware, useStoreViewRequest);

export default router;
