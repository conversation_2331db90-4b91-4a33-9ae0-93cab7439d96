import express from 'express';
import {
  createTicket,
  getUserTickets,
  getTicketById,
  updateTicket,
  addResponse
} from '../controllers/ticketController';
import { upload } from '../config/multer';
import { authMiddleware } from '../middlewares/authMiddleware';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authMiddleware);

// User ticket routes
router.post('/', upload.array('images', 5), createTicket);
router.get('/user', getUserTickets);
router.get('/:id', getTicketById);
router.put('/:id', updateTicket);
router.post('/:id/responses', addResponse);

export default router;
