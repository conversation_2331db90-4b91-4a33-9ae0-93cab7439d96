import { Router, Request, Response } from 'express';
import { registerUser, loginUser, getCities, getCountries, forgotPassword, resetPassword } from '../controllers/authController';
const router = Router();

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *             properties:
 *               name:
 *                 type: string
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: User registered successfully
 *       400:
 *         description: Bad request
 */
router.post('/register', (req: Request, res: Response) => registerUser(req, res));

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Log in a user
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Login successful
 *       401:
 *         description: Invalid credentials
 */
router.post('/login', (req: Request, res: Response) => loginUser(req, res));

/**
 * @swagger
 * /api/auth/cities/{countryCode}:
 *   get:
 *     summary: Get cities by country code
 *     tags: [Auth]
 *     parameters:
 *       - in: path
 *         name: countryCode
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: List of cities
 *       400:
 *         description: Bad request
 */
router.get('/cities/:countryCode', (req: Request, res: Response) => getCities(req, res));

/**
 * @swagger
 * /api/auth/countries:
 *   get:
 *     summary: Get all countries
 *     tags: [Auth]
 *     responses:
 *       200:
 *         description: List of countries
 *       500:
 *         description: Server error
 */
router.get('/countries', (req: Request, res: Response) => getCountries(req, res));

/**
 * @swagger
 * /api/auth/forgot-password:
 *   post:
 *     summary: Request password reset
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password reset email sent
 *       400:
 *         description: Bad request
 */
router.post('/forgot-password', (req: Request, res: Response) => forgotPassword(req, res));

/**
 * @swagger
 * /api/auth/reset-password:
 *   post:
 *     summary: Reset password with token
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - password
 *             properties:
 *               token:
 *                 type: string
 *               password:
 *                 type: string
 *     responses:
 *       200:
 *         description: Password reset successful
 *       400:
 *         description: Invalid token
 */
router.post('/reset-password', (req: Request, res: Response) => resetPassword(req, res));

export default router;
