import { Router } from 'express';
import { createPackage, updatePackage, deletePackage, getPackages, getPackageById, subscribeToPackage, completePackagePurchase, initiatePackagePurchase, completePackagePurchaseService, cancelSubscription, reactivateSubscription, initiatePackageUpgrade } from '../controllers/packageController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { adminMiddleware } from '../middlewares/adminMiddleware';
import { verifyPayment } from '../services/iyzicoPaymentService';
import { Payment } from '../models/Payment';
import { Package } from '../models/Package';
import { Subscription } from '../models/Subscription';
import { User } from '../models/User';
import { paymentRateLimit } from '../middlewares/cardRateLimitMiddleware';

const router = Router();

router.post('/', adminMiddleware, createPackage);
router.put('/:packageId', adminMiddleware, updatePackage);
router.delete('/:packageId', adminMiddleware, deletePackage);
router.get('/', getPackages);
router.get('/:packageId', adminMiddleware, getPackageById);
router.post('/:packageId/subscribe', authMiddleware, subscribeToPackage);
router.post('/:packageId/purchase', authMiddleware, paymentRateLimit, initiatePackagePurchase);
router.post('/:packageId/upgrade', authMiddleware, paymentRateLimit, initiatePackageUpgrade);
router.post('/packages/purchase/complete', authMiddleware, completePackagePurchase);
router.post('/subscription/:id/cancel', authMiddleware, cancelSubscription);
router.post('/subscription/:id/reactivate', authMiddleware, reactivateSubscription);

router.post('/payment-callback', async (req: any, res: any) => {
  try {
    let paymentParams = {
      token: '',
      conversationId: '',
      paymentId: '',
      mdStatus: '',
      status: '',
      conversationData: ''
    };

    if (req.method === 'POST') {
      if (req.body) {
        const formData = typeof req.body === 'string' ? new URLSearchParams(req.body) : req.body;

        paymentParams = {
          token: formData.get?.('token') || formData.token || '',
          conversationId: formData.get?.('conversationId') || formData.conversationId || '',
          paymentId: formData.get?.('paymentId') || formData.paymentId || '',
          mdStatus: formData.get?.('mdStatus') || formData.mdStatus || '',
          status: formData.get?.('status') || formData.status || '',
          conversationData: formData.get?.('conversationData') || formData.conversationData || ''
        };
      }
    } else {
      paymentParams = {
        token: req.query.token as string || '',
        conversationId: req.query.conversationId as string || '',
        paymentId: req.query.paymentId as string || '',
        mdStatus: req.query.mdStatus as string || '',
        status: req.query.status as string || '',
        conversationData: req.query.conversationData as string || ''
      };
    }

    if (!paymentParams.token && paymentParams.conversationId) {
      const payment = await Payment.findOne({
        'iyzico.conversationId': paymentParams.conversationId,
        status: 'pending'
      });

      if (payment?.iyzico?.token) {
        paymentParams.token = payment.iyzico.token;
      }
    }

    if (paymentParams.status === 'success' && paymentParams.paymentId) {
      const pendingPayments = await Payment.find({
        'iyzico.conversationId': paymentParams.conversationId,
        status: 'pending'
      });

      if (pendingPayments.length > 0) {
        const payment: any = pendingPayments[0];

        if (pendingPayments.length > 1) {
          await Payment.updateMany(
            {
              _id: { $ne: payment._id },
              'iyzico.conversationId': paymentParams.conversationId,
              status: 'pending'
            },
            {
              status: 'failed',
              updatedAt: new Date()
            }
          );
        }

        const result = await completePackagePurchaseService(
          payment.userId,
          payment.packageId
        );

        if (result.success) {
          await Payment.findByIdAndUpdate(payment._id, {
            status: 'completed',
            updatedAt: new Date(),
            'iyzico.paymentId': paymentParams.paymentId,
            'iyzico.status': 'success'
          });

          return res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=success&packageId=${payment.packageId}`);
        } else {
          return res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=error&messageKey=${encodeURIComponent(result.message)}`);
        }
      }
    }

    if (paymentParams.token) {
      const verificationResult = await verifyPayment(paymentParams.token);
      if (!verificationResult.success) {
        return res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=error&messageKey=verificationFailed`);
      }
    }

    const paymentRecord: any = await Payment.findOne({
      'iyzico.conversationId': paymentParams.conversationId,
      status: 'pending'
    });

    if (!paymentRecord) {
      return res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=error&messageKey=paymentNotFound`);
    }

    const result = await completePackagePurchaseService(
      paymentRecord.userId,
      paymentRecord.packageId
    );

    if (result.success) {
      res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=success&packageId=${paymentRecord.packageId}`);
    } else {
      res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=error&messageKey=${encodeURIComponent(result.message)}`);
    }
  } catch (error:any) {
    res.redirect(`${process.env.APP_FE_URL}/payment-callback?status=error&messageKey=${encodeURIComponent(error.message)}`);
  }
});

export default router;