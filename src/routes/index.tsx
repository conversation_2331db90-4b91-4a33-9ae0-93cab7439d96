import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Loader2 } from "lucide-react";

// User Pages
import Login from "@/pages/Login";
import Register from "@/pages/Register";
import ForgotPassword from "@/pages/ForgotPassword";
import ResetPassword from "@/pages/ResetPassword";
import Home from "@/pages/Home";
import UserProfile from "@/pages/UserProfile";
import PackageSelection from "@/pages/PackageSelection";
import PackageListing from "@/pages/PackageListing";
import EditPackage from "@/pages/EditPackage";
import AdminEditPackage from "@/pages/admin/EditPackage";
import ItemListing from "@/pages/ItemListing";
import ItemView from "@/pages/ItemView";
import MessageCenter from "@/pages/MessageCenter";
import Stores from "@/pages/Stores";
import StoreDetail from "@/pages/StoreDetail";
import IyzicoPaymentCallback from "@/pages/IyzicoPaymentCallback";
import DesignPackages from "@/pages/DesignPackages";

// Public Pages
import About from "@/pages/About";
import FAQ from "@/pages/FAQ";
import Contact from "@/pages/Contact";
import Partnership from "@/pages/Partnership";
import GlobalAccess from "@/pages/reasons/GlobalAccess";
import SecurePlatform from "@/pages/reasons/SecurePlatform";
import FastGrowth from "@/pages/reasons/FastGrowth";
import Partnerships from "@/pages/reasons/Partnerships";
import PrivacyPolicy from "@/pages/PrivacyPolicy";

// Admin Pages
import AdminLogin from "@/pages/admin/AdminLogin";
import AdminDashboard from "@/pages/admin/AdminDashboard";
import ManageUsers from "@/pages/admin/ManageUsers";
import ManagePackages from "@/pages/admin/ManagePackages";
import ManageProductsServices from "@/pages/admin/ManageProductsServices";
import ManageRepresentatives from "@/pages/admin/ManageRepresentatives";
import ManageTickets from "@/pages/admin/ManageTickets";

const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  const location = window.location;
  const isAdminRoute = location.pathname.startsWith("/admin");

  if (isAdminRoute) {
    return <Navigate to="/admin" replace />;
  }

  if (!isAdminRoute && isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { isAuthenticated, isLoading } = useAuth();
  const location = window.location;
  const isAdminRoute = location.pathname.startsWith("/admin");

  if (isAdminRoute) {
    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      );
    }
    if (!isAuthenticated) {
      return <Navigate to="/login" replace />;
    }
  }

  return <>{children}</>;
};

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public Routes */}
      <Route
        path="/login"
        element={
          <PublicRoute>
            <Login />
          </PublicRoute>
        }
      />

      <Route path="/register" element={<Register />} />

      <Route
        path="/forgot-password"
        element={
          <PublicRoute>
            <ForgotPassword />
          </PublicRoute>
        }
      />

      <Route
        path="/reset-password"
        element={
          <PublicRoute>
            <ResetPassword />
          </PublicRoute>
        }
      />

      <Route
        path="/admin/login"
        element={
          <PublicRoute>
            <AdminLogin />
          </PublicRoute>
        }
      />

      <Route path="/payment-callback" element={<IyzicoPaymentCallback />} />

      {/* Public Static Pages - No Authentication Required */}
      <Route path="/about" element={<About />} />
      <Route path="/faq" element={<FAQ />} />
      <Route path="/contact" element={<Contact />} />
      <Route path="/partnership" element={<Partnership />} />
      <Route path="/reasons/global-access" element={<GlobalAccess />} />
      <Route path="/reasons/secure-platform" element={<SecurePlatform />} />
      <Route path="/reasons/fast-growth" element={<FastGrowth />} />
      <Route path="/reasons/partnerships" element={<Partnerships />} />
      <Route path="/design-packages" element={<DesignPackages />} />
      <Route path="/privacy-policy" element={<PrivacyPolicy />} />

      {/* Protected User Routes */}
      <Route path="/" element={<Home />} />

      <Route
        path="/profile"
        element={
          <ProtectedRoute>
            <UserProfile />
          </ProtectedRoute>
        }
      />

      <Route
        path="/packages"
        element={<PackageSelection />}
      />

      <Route
        path="/packages/:id"
        element={
          <ProtectedRoute>
            <PackageListing />
          </ProtectedRoute>
        }
      />

      <Route
        path="/edit-package/:id"
        element={
          <ProtectedRoute>
            <EditPackage />
          </ProtectedRoute>
        }
      />

      {/* Public routes that were previously protected */}
      <Route path="/items" element={<ItemListing />} />
      <Route path="/items/:id" element={<ItemView />} />
      <Route path="/stores" element={<Stores />} />
      <Route path="/stores/:id" element={<StoreDetail />} />

      <Route
        path="/messages"
        element={
          <ProtectedRoute>
            <MessageCenter />
          </ProtectedRoute>
        }
      />

      {/* Protected Admin Routes */}
      <Route
        path="/admin"
        element={
          <ProtectedRoute>
            <AdminDashboard />
          </ProtectedRoute>
        }
      />

      <Route
        path="/admin/users"
        element={
          <ProtectedRoute>
            <ManageUsers />
          </ProtectedRoute>
        }
      />

      <Route
        path="/admin/packages"
        element={
          <ProtectedRoute>
            <ManagePackages />
          </ProtectedRoute>
        }
      />

      <Route
        path="/admin/packages/:id/edit"
        element={
          <ProtectedRoute>
            <AdminEditPackage />
          </ProtectedRoute>
        }
      />

      <Route
        path="/admin/products-services"
        element={
          <ProtectedRoute>
            <ManageProductsServices />
          </ProtectedRoute>
        }
      />

      <Route
        path="/admin/representatives"
        element={
          <ProtectedRoute>
            <ManageRepresentatives />
          </ProtectedRoute>
        }
      />

      <Route
        path="/admin/tickets"
        element={
          <ProtectedRoute>
            <ManageTickets />
          </ProtectedRoute>
        }
      />

      {/* Fallback route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default AppRoutes;
