import { Router } from 'express';
import { getStoredCards, addStoredCard, removeStoredCard, setDefaultCard } from '../controllers/cardController';
import { authMiddleware } from '../middlewares/authMiddleware';
import { addCardRateLimit, viewCardRateLimit } from '../middlewares/cardRateLimitMiddleware';

const router = Router();

router.get('/', authMiddleware, viewCardRateLimit, getStoredCards);
router.post('/', authMiddleware, addCardRateLimit, addStoredCard);
router.delete('/:cardId', authMiddleware, removeStoredCard);
router.put('/:cardId/default', authMiddleware, setDefaultCard);

export default router;
