import express from 'express';
import multer from 'multer';
import fs from 'fs';
import { authMiddleware } from '../middlewares/authMiddleware';
import { adminMiddleware } from '../middlewares/adminMiddleware';
import {
  createHomeAd,
  getUserHomeAds,
  getActiveHomeAds,
  getAdminHomeAds,
  approveHomeAd,
  rejectHomeAd,
  reactivateHomeAd,
  incrementAdClicks,
  getPendingHomeAds,
  deleteHomeAd,
  adminDeleteHomeAd,
} from '../controllers/homeAdController';
import path from 'path';

const router = express.Router();


// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../../uploads');
const homeAdUploadsDir = path.join(uploadsDir, 'home-ads');

// Ensure directories exist
[uploadsDir, homeAdUploadsDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});

// Configure storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Check the route to determine the upload directory
    cb(null, homeAdUploadsDir);
  },
  filename: function (req, file, cb) {
    // Get file extension
    const ext = path.extname(file.originalname).toLowerCase();
    // Create unique filename with original name
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + ext);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 2MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Only images are allowed'));
    }
  }
});

// User routes
router.post('/', authMiddleware, upload.single('image'), createHomeAd);
router.get('/user', authMiddleware, getUserHomeAds);
router.get('/active', getActiveHomeAds);
router.put('/:id/activate', authMiddleware, reactivateHomeAd);
router.post('/:id/click', incrementAdClicks);
router.delete('/:id', authMiddleware, deleteHomeAd);

// Admin routes
router.get('/admin', adminMiddleware, getAdminHomeAds);
router.get('/admin/pending', adminMiddleware, getPendingHomeAds);
router.put('/admin/:id/approve', adminMiddleware, approveHomeAd);
router.put('/admin/:id/reject', adminMiddleware, rejectHomeAd);
router.delete('/admin/home-ads/:id', adminMiddleware, adminDeleteHomeAd); // Match frontend API path

export default router;
