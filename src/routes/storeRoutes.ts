import express from 'express';
import { authMiddleware } from '../middlewares/authMiddleware';
import { getStores, getStoreById, getStoreItems, getUserStoreProfile, getUserStores, createStore, updateStore, deleteStore, updateUserProfile, getMostViewedStores } from '../controllers/storeController';
import { upload } from '../config/multer';

const router = express.Router();

// Public routes
router.get('/', getStores);
router.get('/most-viewed', getMostViewedStores);
router.get('/:id', getStoreById);
router.get('/:id/items', getStoreItems);

// Protected routes (require authentication)
router.get('/user/profile', authMiddleware, getUserStoreProfile);
router.put('/user/profile', authMiddleware, upload.fields([
  { name: 'logo', maxCount: 1 },
  { name: 'coverImage', maxCount: 1 }
]), updateUserProfile);
router.get('/user/stores', authMiddleware, getUserStores);
router.post('/', authMiddleware, upload.fields([
  { name: 'logo', maxCount: 1 },
  { name: 'coverImage', maxCount: 1 }
]), createStore);
router.put('/:id', authMiddleware, upload.fields([
  { name: 'logo', maxCount: 1 },
  { name: 'coverImage', maxCount: 1 }
]), updateStore);
router.delete('/:id', authMiddleware, deleteStore);

export default router;
