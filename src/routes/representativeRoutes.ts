import express from 'express';
import {
  getActiveRepresentatives,
  getRepresentativeById,
  createRepresentative,
  updateRepresentative,
  deleteRepresentative
} from '../controllers/representativeController';
import { adminMiddleware } from '../middlewares/adminMiddleware';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const router = express.Router();

// Setup multer storage for profile pictures
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = 'uploads/representatives';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const ext = path.extname(file.originalname);
    cb(null, `${Date.now()}-${file.fieldname}${ext}`);
  }
});

// Create multer upload instance with proper configuration
const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (file.fieldname === 'profilePicture') {
      const allowedMimes = ['image/jpeg', 'image/png', 'image/jpg'];
      if (allowedMimes.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Invalid file type'));
      }
    } else {
      cb(null, false);
    }
  }
}).single('profilePicture');

// Update the routes to use a wrapper that properly handles the form data
const uploadMiddleware = (req: any, res: any, next: any) => {
  upload(req, res, function (err) {
    if (err) {
      return res.status(400).json({
        success: false,
        error: err.message
      });
    }
    // Log the body after multer processes it
    console.log('Form data received:', req.body);
    next();
  });
};

// Public routes
router.get('/active', getActiveRepresentatives);
router.get('/:id', getRepresentativeById);

// Protected routes (admin only)
router.use(adminMiddleware);
router.post('/', uploadMiddleware, createRepresentative);
router.put('/:id', uploadMiddleware, updateRepresentative);
router.delete('/:id', deleteRepresentative);

export default router;
