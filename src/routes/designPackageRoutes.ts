import { Router } from 'express';
import {
  getDesignPackages,
  getDesignPackageById,
  createDesignPackageOrder,
  getUserDesignPackageOrders
} from '../controllers/designPackageController';
import { authMiddleware as authenticateUser } from '../middlewares/authMiddleware';

const router = Router();

// Public routes
router.get('/design-packages', getDesignPackages);
router.get('/design-packages/:id', getDesignPackageById);

// User routes (require authentication)
router.post('/design-packages/:packageId/order', authenticateUser, createDesignPackageOrder);
router.get('/design-packages/orders/my', authenticateUser, getUserDesignPackageOrders);

export default router;