import { Router } from 'express';
import { getCardAuditLogs, getSuspiciousCards, getCardUsageStats } from '../controllers/cardAuditController';
import { adminMiddleware } from '../middlewares/adminMiddleware';

const router = Router();

// All routes require admin authentication
router.use(adminMiddleware);

// Get audit logs with filtering
router.get('/logs', getCardAuditLogs);

// Get suspicious cards and recent failures
router.get('/suspicious', getSuspiciousCards);

// Get usage statistics
router.get('/stats', getCardUsageStats);

export default router;