import { Router } from 'express';
import { sendMessage, getMessages, getConversation, deleteMessage } from '../controllers/messageController';
import { authMiddleware } from '../middlewares/authMiddleware';

const router = Router();

router.post('/', authMiddleware, sendMessage);
router.get('/', authMiddleware, getMessages);
router.get('/conversation/:otherUserId', authMiddleware, getConversation);
router.delete('/:messageId', authMiddleware, deleteMessage);

export default router;
