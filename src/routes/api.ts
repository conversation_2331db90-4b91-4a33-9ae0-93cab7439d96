import express from 'express';
import authRoutes from './authRoutes';
import userRoutes from './userRoutes';
import itemRoutes from './itemRoutes';
import subscriptionRoutes from './subscriptionRoutes';
import messageRoutes from './messageRoutes';
import homeAdRoutes from './homeAdRoutes';
import storeRoutes from './storeRoutes';
import cardRoutes from './cardRoutes';
import reviewRoutes from './reviewRoutes';
import adminRoutes from './adminRoutes';
import notificationRoutes from './notificationRoutes';
import packageRoutes from './packageRoutes';
import ticketRoutes from './ticketRoutes';
import liveChatRoutes from './liveChatRoutes';
import designPackageRoutes from './designPackageRoutes';
import cardAuditRoutes from './cardAuditRoutes';

const router = express.Router();

router.use('/admin', adminRoutes);
router.use('/auth', authRoutes);
router.use('/users', userRoutes);
router.use('/items', itemRoutes);
router.use('/subscriptions', subscriptionRoutes);
router.use('/messages', messageRoutes);
router.use('/home-ads', homeAdRoutes);
router.use('/stores', storeRoutes);
router.use('/cards', cardRoutes);
router.use('/card-audit', cardAuditRoutes);
router.use('/reviews', reviewRoutes);
router.use('/notifications', notificationRoutes);
router.use('/packages', packageRoutes);
router.use('/tickets', ticketRoutes);
router.use('/live-chat', liveChatRoutes);
router.use('/', designPackageRoutes);

export default router;
