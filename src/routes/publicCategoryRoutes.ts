import { Router } from 'express';
import { getPublicCategories, getPublicSubCategories } from '../controllers/categoryController';

const router = Router();

/**
 * @swagger
 * /api/public/categories:
 *   get:
 *     summary: Get public categories for registration page
 *     tags: [Categories]
 *     responses:
 *       200:
 *         description: Successfully retrieved public categories
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Category'
 *       500:
 *         description: Internal server error
 */
router.get('/', getPublicCategories);

/**
 * @swagger
 * /api/public/categories/{categoryId}/subcategories:
 *   get:
 *     summary: Get public sub-categories for a specific category
 *     tags: [Categories]
 *     parameters:
 *       - in: path
 *         name: categoryId
 *         required: true
 *         schema:
 *           type: string
 *         description: The category ID
 *     responses:
 *       200:
 *         description: Successfully retrieved sub-categories
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Category'
 *       500:
 *         description: Internal server error
 */
router.get('/:categoryId/subcategories', getPublicSubCategories);

export default router;