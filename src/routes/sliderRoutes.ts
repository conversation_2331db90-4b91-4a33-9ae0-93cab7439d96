import { Router } from 'express';
import { getSliders, createSlider, updateSlider, deleteSlider, upload } from '../controllers/sliderController';
import { adminMiddleware } from '../middlewares/adminMiddleware';

const router = Router();

/**
 * @swagger
 * /api/sliders:
 *   get:
 *     summary: Get all active sliders
 *     tags: [Sliders]
 *     description: Retrieves all slider images for the homepage carousel
 *     responses:
 *       200:
 *         description: List of sliders
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Slider'
 *       500:
 *         description: Server error
 */
router.get('/', getSliders);

// Admin routes - protected by admin middleware
router.use(adminMiddleware);

/**
 * @swagger
 * /api/sliders:
 *   post:
 *     summary: Create a new slider
 *     tags: [Sliders]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               web:
 *                 type: string
 *                 format: binary
 *                 description: Web version of the slider image
 *               mobile:
 *                 type: string
 *                 format: binary
 *                 description: Mobile version of the slider image
 *               link:
 *                 type: string
 *                 description: URL or ID where slider redirects
 *               order:
 *                 type: number
 *                 description: Display order
 *     responses:
 *       201:
 *         description: Slider created successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.post('/', upload.fields([
  { name: 'web', maxCount: 1 },
  { name: 'mobile', maxCount: 1 }
]), createSlider);

/**
 * @swagger
 * /api/sliders/{id}:
 *   put:
 *     summary: Update a slider
 *     tags: [Sliders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               web:
 *                 type: string
 *                 format: binary
 *               mobile:
 *                 type: string
 *                 format: binary
 *               link:
 *                 type: string
 *               order:
 *                 type: number
 *     responses:
 *       200:
 *         description: Slider updated successfully
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Slider not found
 *       500:
 *         description: Server error
 */
router.put('/:id', upload.fields([
  { name: 'web', maxCount: 1 },
  { name: 'mobile', maxCount: 1 }
]), updateSlider);

/**
 * @swagger
 * /api/sliders/{id}:
 *   delete:
 *     summary: Delete a slider
 *     tags: [Sliders]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Slider deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Slider not found
 *       500:
 *         description: Server error
 */
router.delete('/:id', deleteSlider);

export default router;
