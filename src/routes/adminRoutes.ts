import { Router } from 'express';
import {
  approveItem,
  getDashboardStats,
  manageUser,
  manageContent,
  registerAdmin,
  loginAdmin,
  getUsers,
  createPackage,
  deletePackage,
  getPackages,
  getPackageById,
  updatePackage,
  getAdminProfile,
  rejectItem,
  applyPackageToUser,
  getUserSubscriptions,
  getRenewalLogs,
  toggleAutoRenewal,
  getHomeAds,
  disableItem,
  enableItem
} from '../controllers/adminController';
import {
  getAllRepresentatives,
  getRepresentativeById,
  createRepresentative,
  updateRepresentative,
  deleteRepresentative
} from '../controllers/representativeController';
import {
  getAllTickets,
  getAdminTicketById,
  updateAdminTicket,
  adminRespondToTicket
} from '../controllers/ticketController';
import {
  getSliders,
  createSlider,
  updateSlider,
  deleteSlider
} from '../controllers/sliderController';
import {
  getPendingHomeAds,
  adminDeleteHomeAd,
  updateHomeAdStatusByAdmin
} from '../controllers/homeAdController';
import {
  getAdminStores,
  getAdminStoreById,
  approveStore,
  rejectStore,
  toggleStoreStatus,
  adminDeleteStore
} from '../controllers/storeController';
import {
  getActiveChats,
  getClosedChats,
  getArchivedChats,
  getChatStatistics,
  archiveChat,
  getChatHistory,
  sendChatMessage,
  markMessagesAsRead
} from '../controllers/liveChatController';
import { adminMiddleware } from '../middlewares/adminMiddleware';
import { getItems } from '../controllers/itemController';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { createDesignPackage, deleteDesignPackage, getAllDesignPackageOrders, getDesignPackages, updateDesignPackage, updateOrderStatus } from '../controllers/designPackageController';

const router = Router();

// Setup multer storage for admin uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = 'uploads/admin';
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept all image file types by checking if mimetype starts with 'image/'
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only image files are allowed.'));
    }
  }
});

// Public admin routes (no auth required)
router.post('/login', loginAdmin);
router.post('/register', registerAdmin);
router.get('/sliders', getSliders);

// Protected admin routes
//router.use(authMiddleware); // Apply auth middleware to all routes below
router.use(adminMiddleware); // Apply admin middleware to all routes below

// Profile route
/**
 * @swagger
 * /api/admin/profile:
 *   get:
 *     summary: Get admin profile
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved admin profile
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                 name:
 *                   type: string
 *                 email:
 *                   type: string
 *                 role:
 *                   type: string
 *                 isActive:
 *                   type: boolean
 *                 date:
 *                   type: string
 *                   format: date-time
 *       500:
 *         description: Error fetching admin profile
 */
router.get('/profile', getAdminProfile);

// Dashboard routes
/**
 * @swagger
 * /api/admin/dashboard/stats:
 *   get:
 *     summary: Get dashboard statistics
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved dashboard stats
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userCount:
 *                   type: integer
 *                 itemCount:
 *                   type: integer
 *                 productCount:
 *                   type: integer
 *                 serviceCount:
 *                   type: integer
 *       500:
 *         description: Error fetching dashboard stats
 */
router.get('/dashboard/stats', getDashboardStats);

// User management routes
/**
 * @swagger
 * /api/admin/users:
 *   get:
 *     summary: Get a list of all users
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of users
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   _id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   email:
 *                     type: string
 *                   role:
 *                     type: string
 *                   isActive:
 *                     type: boolean
 *                   date:
 *                     type: string
 *                     format: date-time
 *       500:
 *         description: Error retrieving users
 */
router.get('/users', getUsers);

/**
 * @swagger
 * /api/admin/users/{userId}/{action}:
 *   post:
 *     summary: Manage user (activate, deactivate, delete)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *       - in: path
 *         name: action
 *         required: true
 *         schema:
 *           type: string
 *           enum: [activate, deactivate, delete]
 *     responses:
 *       200:
 *         description: User managed successfully
 *       400:
 *         description: Invalid action or user not found
 *       500:
 *         description: Error managing user
 */
router.post('/users/:userId/:action', manageUser);

// Package management routes
/**
 * @swagger
 * /api/admin/packages:
 *   get:
 *     summary: Get all packages (admin view)
 *     tags: [Admin, Packages]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of all packages
 *       500:
 *         description: Server error
 */
router.get('/packages', getPackages);
router.get('/packages/:packageId', getPackageById);

/**
 * @swagger
 * /api/admin/packages:
 *   post:
 *     summary: Create a new package
 *     tags: [Admin, Packages]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Package'
 *     responses:
 *       201:
 *         description: Package created successfully
 *       500:
 *         description: Server error
 */
router.post('/packages', createPackage);

/**
 * @swagger
 * /api/admin/packages/{packageId}:
 *   put:
 *     summary: Update a package
 *     tags: [Admin, Packages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: packageId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Package'
 *     responses:
 *       200:
 *         description: Package updated successfully
 *       404:
 *         description: Package not found
 *       500:
 *         description: Server error
 */
router.put('/packages/:packageId', updatePackage);

/**
 * @swagger
 * /api/admin/packages/{packageId}:
 *   delete:
 *     summary: Delete a package
 *     tags: [Admin, Packages]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: packageId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Package deleted successfully
 *       404:
 *         description: Package not found
 *       500:
 *         description: Server error
 */
router.delete('/packages/:packageId', deletePackage);

// Ticket management routes
router.get('/tickets', getAllTickets);
router.get('/tickets/:id', getAdminTicketById);
router.put('/tickets/:id', updateAdminTicket);
router.post('/tickets/:id/respond', adminRespondToTicket);

// Content management routes
/**
 * @swagger
 * /api/admin/content/{action}:
 *   put:
 *     summary: Manage content (approve, reject, delete)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: action
 *         required: true
 *         schema:
 *           type: string
 *           enum: [approve, reject, delete]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - contentId
 *             properties:
 *               contentId:
 *                 type: string
 *     responses:
 *       200:
 *         description: Content managed successfully
 *       400:
 *         description: Invalid action or content not found
 *       500:
 *         description: Error managing content
 */
router.put('/content/:action', manageContent);

// For compatibility, support both PUT and POST methods
router.post('/content/:action', manageContent);

// Add more specific routes for better REST compatibility
router.put('/content/:contentId/:action', (req, res) => {
  req.params.contentId = req.params.contentId;
  req.params.action = req.params.action;
  return manageContent(req, res);
});

router.post('/content/:contentId/:action', (req, res) => {
  req.params.contentId = req.params.contentId;
  req.params.action = req.params.action;
  return manageContent(req, res);
});

// Add route for home ads
router.get('/home-ads/pending', adminMiddleware, getPendingHomeAds)
router.get('/home-ads', getHomeAds)

// Home Ad Management (Admin)
router.put('/home-ads/:id/:status', adminMiddleware, updateHomeAdStatusByAdmin);
router.delete('/home-ads/:id', adminMiddleware, adminDeleteHomeAd);

/**
 * @swagger
 * /api/admin/items/{itemId}/approve:
 *   post:
 *     summary: Approve an item (product or service)
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: itemId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Item approved successfully
 *       404:
 *         description: Item not found
 *       500:
 *         description: Server error
 */
router.post('/items/:itemId/approve', approveItem);
router.post('/items/:itemId/reject', rejectItem);

// Slider Management Routes
router.post('/sliders', adminMiddleware, upload.fields([
  { name: 'webImage', maxCount: 1 },
  { name: 'mobileImage', maxCount: 1 }
]), createSlider);
router.put('/sliders/:id', adminMiddleware, upload.fields([
  { name: 'webImage', maxCount: 1 },
  { name: 'mobileImage', maxCount: 1 }
]), updateSlider);
router.delete('/sliders/:id', adminMiddleware, deleteSlider);

// Representative management routes
router.get('/representatives', getAllRepresentatives);
router.get('/representatives/:id', getRepresentativeById);
router.post('/representatives', createRepresentative);
router.put('/representatives/:id', updateRepresentative);
router.delete('/representatives/:id', deleteRepresentative);

// Products and Services management routes
router.get('/items', getItems);
router.put('/items/:id/approve', approveItem);
router.put('/items/:id/reject', rejectItem);
router.put('/items/:itemId/disable', disableItem);
router.put('/items/:itemId/enable', enableItem);

// File upload endpoint for admin
router.post('/upload', upload.array('files', 10), (req: any, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded'
      });
    }

    const files = Array.isArray(req.files) ? req.files : [req.files];
    const uploadedFiles = files.map((file: any) => file.path.replace(/\\/g, '/').replace('uploads/', ''));

    res.status(200).json({
      success: true,
      files: uploadedFiles
    });
  } catch (error: any) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

/**
 * @swagger
 * /api/admin/users/{userId}/apply-package:
 *   put:
 *     summary: Apply a package to a user
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - packageId
 *               - duration
 *             properties:
 *               packageId:
 *                 type: string
 *                 description: ID of the package to apply
 *               duration:
 *                 type: number
 *                 description: Duration in months
 *     responses:
 *       201:
 *         description: Package applied to user successfully
 *       400:
 *         description: Missing required fields
 *       404:
 *         description: User or package not found
 *       500:
 *         description: Error applying package to user
 */
router.put('/users/:userId/apply-package', applyPackageToUser);

/**
 * @swagger
 * /api/admin/users/{userId}/subscriptions:
 *   get:
 *     summary: Get a user's subscriptions
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User subscriptions retrieved successfully
 *       404:
 *         description: User not found
 *       500:
 *         description: Error retrieving user subscriptions
 */
router.get('/users/:userId/subscriptions', getUserSubscriptions);

/**
 * @swagger
 * /api/admin/renewals/logs:
 *   get:
 *     summary: Get auto-renewal logs
 *     description: Retrieves logs of auto-renewal attempts
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Number of items per page
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [success, failed]
 *         description: Filter by status
 *     responses:
 *       200:
 *         description: Renewal logs retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */
router.get('/renewals/logs', getRenewalLogs);

/**
 * @swagger
 * /api/admin/subscriptions/{subscriptionId}/auto-renewal:
 *   put:
 *     summary: Toggle auto-renewal for a subscription
 *     description: Enable or disable auto-renewal for a specific subscription
 *     tags: [Admin]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: subscriptionId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the subscription
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - enableAutoRenewal
 *             properties:
 *               enableAutoRenewal:
 *                 type: boolean
 *                 description: Whether to enable auto-renewal
 *     responses:
 *       200:
 *         description: Auto-renewal setting updated successfully
 *       400:
 *         description: Bad request
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Subscription not found
 *       500:
 *         description: Server error
 */
router.put('/subscriptions/:subscriptionId/auto-renewal', toggleAutoRenewal);

// Store Management (Admin)
router.get('/stores', adminMiddleware, getAdminStores);
router.get('/stores/:id', adminMiddleware, getAdminStoreById);
router.put('/stores/:id/approve', adminMiddleware, approveStore);
router.put('/stores/:id/reject', adminMiddleware, rejectStore);
router.put('/stores/:id/toggle-status', adminMiddleware, toggleStoreStatus);
router.delete('/stores/:id', adminMiddleware, adminDeleteStore);

// Live Chat Management (Admin)
router.get('/live-chat/active', getActiveChats);
router.get('/live-chat/closed', getClosedChats);
router.get('/live-chat/archived', getArchivedChats);
router.get('/live-chat/statistics', getChatStatistics);
router.post('/live-chat/:chatId/archive', archiveChat);

// Direct access to chat sessions for admins
router.get('/live-chat/:chatId', getChatHistory);
router.post('/live-chat/:chatId/message', sendChatMessage);
router.post('/live-chat/:chatId/read', markMessagesAsRead);


// Design Package Management (Admin)
router.post('/design-packages', createDesignPackage);
router.get('/design-packages', getDesignPackages);
router.put('/design-packages/:id', updateDesignPackage);
router.delete('/design-packages/:id', deleteDesignPackage);
router.get('/design-packages/orders', getAllDesignPackageOrders);
router.put('/design-packages/orders/:id/status', updateOrderStatus);


export default router;
