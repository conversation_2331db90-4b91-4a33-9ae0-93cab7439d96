import AdminDashboard from "@/pages/admin/AdminDashboard";
import ManagePackages from "@/pages/admin/ManagePackages";
import ManageProductsServices from "@/pages/admin/ManageProductsServices";
import ManageRepresentatives from "@/pages/admin/ManageRepresentatives";
import ManageTickets from "@/pages/admin/ManageTickets";
import ManageUsers from "@/pages/admin/ManageUsers";
import ManageDesignPackages from "@/pages/admin/ManageDesignPackages";
import EditDesignPackage from "@/pages/admin/EditDesignPackage";
import { Settings } from "lucide-react";
import { RouteObject } from "react-router-dom";

const adminRoutes: RouteObject[] = [
  {
    path: "/admin",
    children: [
      {
        index: true,
        element: <AdminDashboard />,
      },
      {
        path: "users",
        element: <ManageUsers />,
      },
      {
        path: "products",
        element: <ManageProductsServices />,
      },
      {
        path: "packages",
        element: <ManagePackages />,
      },
      {
        path: "representatives",
        element: <ManageRepresentatives />,
      },
      {
        path: "tickets",
        element: <ManageTickets />,
      },
      {
        path: "design-packages",
        element: <ManageDesignPackages />,
      },
      {
        path: "design-packages/new",
        element: <EditDesignPackage />,
      },
      {
        path: "design-packages/edit/:id",
        element: <EditDesignPackage />,
      },
      {
        path: "settings",
        element: <Settings />,
      },
    ],
  },
];

export default adminRoutes;
