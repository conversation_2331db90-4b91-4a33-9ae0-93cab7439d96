import axios, { AxiosInstance } from 'axios';
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

//const API_BASE_URL = import.meta.env.VITE_API_URL || 'https://api.e-exportcity.com/api';
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5050/api';

export const useApi = (): AxiosInstance => {
  const navigate = useNavigate();
  const api = axios.create({
    baseURL: API_BASE_URL,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  useEffect(() => {
    const requestInterceptor = api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    const responseInterceptor = api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('token');
          navigate('/login');
        }
        return Promise.reject(error);
      }
    );

    return () => {
      api.interceptors.request.eject(requestInterceptor);
      api.interceptors.response.eject(responseInterceptor);
    };
  }, [navigate]);

  return api;
};