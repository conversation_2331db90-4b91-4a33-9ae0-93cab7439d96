import { useState, useCallback, useEffect } from 'react';
import { getCountries, getCities } from '@/api';
import { ICountry, ICity } from '@/types/user';

export const useCountries = () => {
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [cities, setCities] = useState<ICity[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch countries from the API
  useEffect(() => {
    const fetchCountries = async () => {
      setLoading(true);
      setError(null);

      try {
        const data = await getCountries();
        setCountries(data);
      } catch (err) {
        console.error('Error fetching countries:', err);
        setError('Failed to load countries');
      } finally {
        setLoading(false);
      }
    };

    fetchCountries();
  }, []);

  // Function to fetch cities for a given country
  const getCitiesByCountry = useCallback(async (countryCode: string) => {
    if (!countryCode) {
      setCities([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const data = await getCities(countryCode);
      setCities(data);
    } catch (err) {
      console.error(`Error fetching cities for country ${countryCode}:`, err);
      setError('Failed to load cities');
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    countries,
    cities,
    getCities: getCitiesByCountry,
    loading,
    error,
  };
};
