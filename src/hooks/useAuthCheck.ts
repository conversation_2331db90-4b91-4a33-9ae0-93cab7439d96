import { getUserProfile } from '@/api';
import { getAdminProfile } from '@/adminApi';
import { IUser } from '@/types/user';
import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

export const useAuthCheck = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');
  const [isAuthenticated, setIsAuthenticated] = useState(!!localStorage.getItem(isAdminRoute ? 'adminToken' : 'userToken'));
  const [user, setUser] = useState<IUser | null>(null);

  useEffect(() => {
    const token = localStorage.getItem(isAdminRoute ? 'adminToken' : 'userToken');
    setIsAuthenticated(!!token);
  }, [isAdminRoute]);

  useEffect(() => {
    if (isAuthenticated && !user) {
      const fetchUser = async () => {
        try {
          const response = isAdminRoute
            ? await getAdminProfile()
            : await getUserProfile();
          setUser(response);
        } catch (error: unknown) {
          console.error('Failed to fetch profile:', error);
          setUser(null);
          localStorage.removeItem(isAdminRoute ? 'adminToken' : 'userToken');
          setIsAuthenticated(false);
        }
      };
      fetchUser();
    }
  }, [isAuthenticated, user, isAdminRoute]);

  useEffect(() => {
    if (!isAuthenticated) {
      const publicRoutes = [
        '/',
        '/login',
        '/register',
        '/forgot-password',
        '/reset-password',
        '/admin/login',
        '/packages',
        '/about',
        '/partnership',
        '/faq',
        '/reasons/',
        '/representatives'
      ];
      if (publicRoutes.some(route => location.pathname.startsWith(route))) {
        return;
      }
      navigate(isAdminRoute ? '/admin/login' : '/login');
    }
  }, [navigate, isAdminRoute, isAuthenticated, location.pathname]);

  return { isAdminRoute, isAuthenticated, isLoggedIn: isAuthenticated, user, setUser };
};
