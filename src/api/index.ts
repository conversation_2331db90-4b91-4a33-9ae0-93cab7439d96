import axios from 'axios';
import { ICategory } from '../types/category';
import { IItem } from '../types/item';

// Re-export design package API functions
export * from './designPackageApi';

const API_URL = import.meta.env.VITE_API_BASE_URL.replace(/\/api$/, '');

// Category-related endpoints
export const getCategoriesByType = async (type: string): Promise<ICategory[]> => {
  try {
    const response = await axios.get(`${API_URL}/api/categories/type/${type}`, {
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const getCategoriesByParentId = async (parentId: string): Promise<ICategory[]> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/public/categories/${parentId}/subcategories`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    // Extract the data array from the API response
    return response.data.data || [];
  } catch (error: any) {
    throw error;
  }
};

// User items endpoints
export const getOwnedItemsByType = async (type: string): Promise<IItem[]> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/items/owned/type/${type}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Subscription and request checks
export const checkCreateRequest = async (): Promise<boolean> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/subscriptions/check-create`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data.canCreate;
  } catch (error: any) {
    throw error;
  }
};

export const getActiveSubscription = async () => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/subscriptions/active`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Advertisement endpoints
export const createHomeAd = async (itemId: string) => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.post(`${API_URL}/api/advertisements/home`,
      { itemId },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        withCredentials: true,
      }
    );
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Item request endpoints
export const createItemRequest = async (itemData: any): Promise<any> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.post(`${API_URL}/api/items/requests`, itemData, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const updateItemRequest = async (requestId: string, itemData: any): Promise<any> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.put(`${API_URL}/api/items/requests/${requestId}`, {
      itemData
    }, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const getOwnedItemRequests = async (): Promise<any> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/items/requests/owned`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const deleteItemRequest = async (requestId: string): Promise<any> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.delete(`${API_URL}/api/items/requests/${requestId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const deleteItem = async (itemId: string): Promise<any> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.delete(`${API_URL}/api/items/${itemId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const getCategories = async (): Promise<ICategory[]> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/categories`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Country and city endpoints
export const getCountries = async (): Promise<any[]> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/auth/countries`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const getCities = async (countryCode: string): Promise<any[]> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/auth/cities/${countryCode}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Get filters and filtered items from backend
export const getFilters = async (type: string, categoryIds?: string[]) => {
  try {
    // Prepare query parameters
    const params: { type?: string; categoryIds?: string } = {};

    if (type && type !== 'all') {
      params.type = type;
    }

    if (categoryIds && categoryIds.length > 0) {
      params.categoryIds = categoryIds.join(',');
    }

    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/items/filters`, {
      params,
      headers: {
        Authorization: token ? `Bearer ${token}` : '',
      },
      withCredentials: true,
    });

    return response.data;
  } catch (error: any) {
    console.error('Error fetching filters:', error);
    throw error;
  }
};

// Create base axios instance for reuse
export const api = axios.create({
  baseURL: `${API_URL}/api`,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add authorization token to every request if available
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('userToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Store view request functions
export const checkStoreViewRequest = async (storeId: string) => {
  try {
    const response = await api.get(`/subscriptions/check-store-view-request?storeId=${storeId}`);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

export const useStoreViewRequest = async (storeId: string) => {
  try {
    const response = await api.post('/subscriptions/use-store-view-request', { storeId });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};
