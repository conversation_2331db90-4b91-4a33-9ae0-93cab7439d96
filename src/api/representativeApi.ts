import { api } from '@/api';
import { IRepresentativeResponse } from '@/types/representative';

export const getActiveRepresentatives = async (): Promise<IRepresentativeResponse> => {
  try {
    // Get the representatives with country and city names directly from the API
    const response = await api.get('/representatives/active');
    return response.data;
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || 'Failed to fetch active representatives'
    };
  }
};