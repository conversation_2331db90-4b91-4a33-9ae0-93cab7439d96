import { adminApi } from '@/adminApi';
import {api} from '../api';

export const getCancellationRequests = () => {
  return api.get('/admin/cancellation-requests');
};

export const approveCancellation = (requestId: string) => {
  return api.post(`/admin/cancellation-requests/${requestId}/approve`);
};

export const rejectCancellation = (requestId: string) => {
  return api.post(`/admin/cancellation-requests/${requestId}/reject`);
};

export const revertCancellationRequest = (requestId: string) => {
  return api.post(`/subscriptions/cancel-request/${requestId}/revert`);
};

// Interface for Design Package (Admin) - matches backend model
export interface IDesignPackageAdmin {
  _id: string;
  name: string; // Primary language, e.g., Turkish
  nameEn: string; // English name
  description: string; // Primary language description
  descriptionEn: string; // English description
  price: number;
  currency: string; // e.g., "USD", "TRY", "EUR"
  icon: string; // Icon name like 'Globe', 'Layers', 'PenTool'
  features: string[];
  featuresEn: string[];
  deliveryTime: number; // in days
  revisionCount: number; // Number of allowed revisions
  isPopular: boolean;
  isActive: boolean;
  order: number; // For sorting in lists
  createdAt?: string;
  updatedAt?: string;
}

// API functions for Admin Design Packages

export const getAdminDesignPackages = async (): Promise<IDesignPackageAdmin[]> => {
  const response = await adminApi.get<IDesignPackageAdmin[]>('/admin/design-packages');
  return response.data;
};

export const getAdminDesignPackage = async (id: string): Promise<IDesignPackageAdmin | null> => {
  try {
    const response = await adminApi.get<IDesignPackageAdmin>(`/admin/design-packages/${id}`);
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null;
    }
    throw error;
  }
};

export const createAdminDesignPackage = async (data: Partial<IDesignPackageAdmin>): Promise<IDesignPackageAdmin> => {
  const response = await adminApi.post<IDesignPackageAdmin>('/admin/design-packages', data);
  return response.data;
};

export const updateAdminDesignPackage = async (id: string, data: Partial<IDesignPackageAdmin>): Promise<IDesignPackageAdmin | null> => {
  try {
    const response = await adminApi.put<IDesignPackageAdmin>(`/admin/design-packages/${id}`, data);
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return null;
    }
    throw error;
  }
};

export const deleteAdminDesignPackage = async (id: string): Promise<void> => {
  await adminApi.delete(`/admin/design-packages/${id}`);
};

// Interface for Design Package Orders (Admin View)
export interface IDesignPackageOrderAdmin {
  _id: string;
  orderNumber: string;
  userId: { _id: string; username: string; email: string; }; // Basic user info
  packageId: { _id: string; name: string; nameEn?: string }; // Basic package info
  price: number;
  currency: string;
  status: string; // e.g., PENDING, PAID, IN_PROGRESS, COMPLETED, CANCELLED
  paymentId?: string;
  notes?: string;
  files?: Array<{ name: string; url: string; uploadedAt: Date }>;
  deliveryDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export const getAdminDesignPackageOrders = async (packageId?: string): Promise<IDesignPackageOrderAdmin[]> => {
  const url = packageId
    ? `/admin/design-packages/orders?packageId=${packageId}`
    : '/admin/design-packages/orders';
  const response = await adminApi.get<IDesignPackageOrderAdmin[]>(url);
  return response.data;
};

