import { api } from '../api';

interface StoredCard {
  _id: string;
  cardAlias: string;
  lastFourDigits: string;
  cardType: string;
  cardAssociation: string;
  cardFamily: string;
  isDefault: boolean;
}

export const getStoredCards = async (): Promise<StoredCard[]> => {
  const response = await api.get(`/cards`);
  return response.data;
};

export const removeStoredCard = async (cardId: string): Promise<void> => {
  await api.delete(`/cards/${cardId}`);
};

export const setDefaultCard = async (cardId: string): Promise<void> => {
  await api.put(`/cards/${cardId}/default`, {});
};

export const addStoredCard = async (cardData: {
  cardHolderName: string;
  cardNumber: string;
  expireMonth: string;
  expireYear: string;
  cvc: string;
  cardAlias?: string;
}): Promise<StoredCard> => {
  const response = await api.post(`/cards`, cardData);
  return response.data.card;
};
