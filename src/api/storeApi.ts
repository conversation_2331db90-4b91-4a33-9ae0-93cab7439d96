import axios from 'axios';
import { IStore } from '../types/store';
import { IItem } from '../types/item';
import { api } from '@/api';

const API_URL = import.meta.env.VITE_API_BASE_URL;

// Get user's store
export const getUserStore = async (): Promise<IStore> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/stores/user/profile`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data.store;
  } catch (error: any) {
    throw error;
  }
};

// Create store
export const createStore = async (data: FormData | Record<string, any>): Promise<IStore> => {
  try {
    const token = localStorage.getItem('userToken');
    const isFormData = data instanceof FormData;

    const response = await axios.post(`${API_URL}/stores`, data, {
      headers: {
        'Content-Type': isFormData ? 'multipart/form-data' : 'application/json',
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Update store
export const updateStore = async (data: FormData | Record<string, any>): Promise<IStore> => {
  try {
    const token = localStorage.getItem('userToken');
    const isFormData = data instanceof FormData;

    const response = await axios.put(`${API_URL}/stores/user/profile`, data, {
      headers: {
        'Content-Type': isFormData ? 'multipart/form-data' : 'application/json',
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Get all stores
export const getStores = async (): Promise<IStore[]> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await api.get(`/stores`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      withCredentials: true,
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Get store by id
export const getStoreById = async (id: string): Promise<IStore> => {
  try {
    const response = await axios.get(`${API_URL}/stores/${id}`, {
      withCredentials: true,
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching store:', error);
    throw error;
  }
};

// Get store items
export const getStoreItems = async (id: string): Promise<IItem[]> => {
  try {
    const response = await axios.get(`${API_URL}/stores/${id}/items`, {
      withCredentials: true,
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching store items:', error);
    throw error;
  }
};

// Get most viewed stores
export const getMostViewedStores = async (): Promise<IStore[]> => {
  try {
    const response = await axios.get(`${API_URL}/stores/most-viewed`, {
      withCredentials: true,
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching most viewed stores:', error);
    throw error;
  }
};

// Get stores with calculated view counts from their items
export const getStoresWithItemViewCounts = async (): Promise<IStore[]> => {
  try {
    // Get all stores
    const storesResponse = await getStores();
    const stores = storesResponse;
    
    // Process all stores in parallel for better performance
    const storePromises = stores.map(async (store) => {
      try {
        // Get all items for this store
        const items = await getStoreItems(store._id);
        
        // Calculate the sum of all item view counts
        const totalViews = items.reduce((sum: number, item: any) => {
          return sum + (item.viewCount || 0);
        }, 0);
        
        // Create a new store object with the calculated viewCount
        return {
          ...store,
          viewCount: totalViews
        };
      } catch (error) {
        console.error(`Error fetching items for store ${store._id}:`, error);
        // If there's an error, preserve the existing view count or use 0
        return {
          ...store,
          viewCount: store.viewCount || 0
        };
      }
    });
    
    // Wait for all store processing to complete
    const processedStores = await Promise.all(storePromises);
    return processedStores;
  } catch (error: any) {
    console.error('Error calculating store item view counts:', error);
    throw error;
  }
};
