import axios from 'axios';

// Define the API_URL using Vite's environment variables
//const API_URL = import.meta.env.VITE_API_URL || 'https://api.e-exportcity.com';
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:5050';

// Define slider types
export interface ISlider {
  _id: string;
  web: string;       // Path to web version of slider image
  mobile: string;    // Path to mobile version of slider image
  link: string;      // Link or ID where slider redirects
  order: number;     // Order of display
  active?: boolean;  // Whether slider is active
  createdAt: string;
  updatedAt: string;
}

export interface ISliderFormData {
  web?: string;
  mobile?: string;
  link?: string;
  order: number;
  active?: boolean;
}

/**
 * Fetches all active sliders for the homepage
 * @returns Array of slider objects
 */
export const getSliders = async (): Promise<ISlider[]> => {
  try {
    const response = await axios.get(`${API_URL}/api/sliders`);
    return response.data;
  } catch (error) {
    console.error('Error fetching sliders:', error);
    return [];
  }
};

/**
 * Admin: Fetches all sliders (active and inactive)
 * @returns Array of all slider objects
 */
export const getAllSliders = async (): Promise<ISlider[]> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.get(`${API_URL}/api/admin/sliders`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching all sliders:', error);
    throw error;
  }
};

/**
 * Admin: Creates a new slider with file uploads
 * @param sliderData Object containing slider data (web, mobile, link, order, active)
 * @returns Created slider object
 */
export const createSlider = async (sliderData: {
  webImage?: File;
  mobileImage?: File;
  link: string;
  order: number;
  active?: boolean;
}): Promise<ISlider> => {
  try {
    const token = localStorage.getItem('adminToken');

    const formData = new FormData();
    if (sliderData.webImage) formData.append('webImage', sliderData.webImage);
    if (sliderData.mobileImage) formData.append('mobileImage', sliderData.mobileImage);
    formData.append('link', sliderData.link);
    formData.append('order', sliderData.order.toString());
    if (sliderData.active !== undefined) formData.append('active', sliderData.active.toString());

    console.log('Sending slider data:', {
      webImage: sliderData.webImage ? 'File present' : 'No file',
      mobileImage: sliderData.mobileImage ? 'File present' : 'No file',
      link: sliderData.link,
      order: sliderData.order
    });

    const response = await axios.post(`${API_URL}/api/admin/sliders`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error creating slider:', error);
    throw error;
  }
};

/**
 * Admin: Updates an existing slider
 * @param id Slider ID
 * @param sliderData Object containing slider data to update
 * @returns Updated slider object
 */
export const updateSlider = async (id: string, sliderData: {
  webImage?: File;
  mobileImage?: File;
  link?: string;
  order?: number;
  active?: boolean;
}): Promise<ISlider> => {
  try {
    const token = localStorage.getItem('userToken');

    const formData = new FormData();
    if (sliderData.webImage) formData.append('webImage', sliderData.webImage);
    if (sliderData.mobileImage) formData.append('mobileImage', sliderData.mobileImage);
    if (sliderData.link) formData.append('link', sliderData.link);
    if (sliderData.order !== undefined) formData.append('order', sliderData.order.toString());
    if (sliderData.active !== undefined) formData.append('active', sliderData.active.toString());

    const response = await axios.put(`${API_URL}/api/admin/sliders/${id}`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error updating slider:', error);
    throw error;
  }
};

/**
 * Admin: Deletes a slider
 * @param id Slider ID
 * @returns Success message
 */
export const deleteSlider = async (id: string): Promise<{ message: string }> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.delete(`${API_URL}/api/admin/sliders/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting slider:', error);
    throw error;
  }
};

/**
 * Admin: Reorders sliders
 * @param sliders Array of objects with id and order properties
 * @returns Success message
 */
export const reorderSliders = async (sliders: { id: string; order: number }[]): Promise<{ message: string }> => {
  try {
    const token = localStorage.getItem('userToken');
    const response = await axios.put(
      `${API_URL}/api/admin/sliders/reorder`,
      { sliders },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error('Error reordering sliders:', error);
    throw error;
  }
};