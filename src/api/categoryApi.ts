import { api } from "./index";
import { getCategoriesByParentId } from "./index";
import { getItemsByCategory as fetchItemsByCategory } from "./itemApi";

export const categoryApi = {
  // Get all subcategories for a parent category - use existing endpoint
  getSubcategories: (parentId: string) =>
    getCategoriesByParentId(parentId),

  // Get items by category ID - use existing endpoint
  getItemsByCategory: (categoryId: string) =>
    fetchItemsByCategory(categoryId),

  // Get all categories
  getAllCategories: () =>
    api.get('/categories'),

  // Get single category details
  getCategoryById: (id: string) =>
    api.get(`/categories/${id}`)
};