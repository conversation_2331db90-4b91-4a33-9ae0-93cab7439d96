import { api } from "@/api";

export interface IUser {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  address: string;
}

export const getUserProfile = async (): Promise<IUser> => {
  const response = await api.get(`/users/profile`, {
    withCredentials: true
  });
  return response.data;
};

export const updateUserProfile = async (userData: Partial<IUser>): Promise<IUser> => {
  const response = await api.put(`/users/profile`, userData, {
    withCredentials: true
  });
  return response.data;
};
