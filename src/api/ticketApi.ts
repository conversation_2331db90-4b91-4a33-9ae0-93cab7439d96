import { api } from '@/api';
import { ITicket, ITicketApiResponse, IUpdateTicket } from '@/types/ticket';
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_BASE_URL.replace(/\/api$/, '');

export const ticketApi = {
  getAll: async (): Promise<ITicketApiResponse<ITicket[]>> => {
    try {
      const response = await api.get('/tickets');
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch tickets'
      };
    }
  },

  getUserTickets: async (): Promise<ITicketApiResponse<ITicket[]>> => {
    try {
      const response = await api.get('/tickets/user');
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch user tickets'
      };
    }
  },

  getById: async (id: string): Promise<ITicketApiResponse<ITicket>> => {
    try {
      const response = await api.get(`/tickets/${id}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to fetch ticket'
      };
    }
  },

  create: async (formData: FormData): Promise<ITicketApiResponse<ITicket>> => {
    try {
      const token = localStorage.getItem('userToken');
      const response = await axios.post(`${API_URL}/api/tickets`, formData, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data',
        },
        withCredentials: true
      });
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      console.error('Create ticket error:', error.response?.data || error.message);
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to create ticket'
      };
    }
  },

  update: async (id: string, data: IUpdateTicket): Promise<ITicketApiResponse<ITicket>> => {
    try {
      const response = await api.put(`/tickets/${id}`, data);
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to update ticket'
      };
    }
  },

  addResponse: async (ticketId: string, message: string): Promise<ITicketApiResponse<ITicket>> => {
    try {
      const response = await api.post(`/tickets/${ticketId}/responses`, { message });
      return {
        success: true,
        data: response.data
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || 'Failed to add response'
      };
    }
  }
};