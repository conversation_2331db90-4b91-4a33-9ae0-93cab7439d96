import { api } from '@/api';
import { IItem } from '@/types/item';

// Create item
export const createItem = async (formData: FormData): Promise<IItem> => {
  try {
    const response = await api.post('/items', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Get items by type
export const getItemsByType = async (type: string): Promise<IItem[]> => {
  try {
    const response = await api.get(`/items/type/${type}`);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Get items by category
export const getItemsByCategory = async (categoryId: string): Promise<IItem[]> => {
  try {
    const response = await api.get(`/items/category/${categoryId}`);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Get item by ID
export const getItemById = async (id: string): Promise<IItem> => {
  try {
    const response = await api.get(`/items/${id}`);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Update item
export const updateItem = async (id: string, itemData: FormData | Partial<IItem>): Promise<IItem> => {
  try {
    const response = await api.put(`/items/${id}`, itemData);
    return response.data;
  } catch (error: any) {
    throw error;
  }
};

// Delete item
export const deleteItem = async (id: string): Promise<void> => {
  try {
    await api.delete(`/items/${id}`);
  } catch (error: any) {
    throw error;
  }
};

// Get all items (public endpoint)
export const getItems = async (): Promise<IItem[]> => {
  try {
    const response = await api.get('/items');
    return response.data;
  } catch (error: any) {
    throw error;
  }
};