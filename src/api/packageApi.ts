import { adminApi } from '@/adminApi';
import { api } from '../api';
import { IPackage } from '../types/package';

export const getAdminPackages = async (): Promise<IPackage[]> => {
  const response = await adminApi.get<IPackage[]>('/admin/packages');
  return response.data;
};

// Removing this duplicate function as it exists in api.ts
// This avoids having two different implementations of the same function
// If you need packages, import { getPackages } from '../api';

export const getPackageById = async (id: string): Promise<IPackage> => {
  const response = await api.get<IPackage>(`/packages/${id}`);
  return response.data;
};


export const getAdminPackageById = async (id: string): Promise<IPackage> => {
  const response = await adminApi.get<IPackage>(`/admin/packages/${id}`);
  return response.data;
};

export const updatePackage = async (id: string, packageData: Partial<IPackage>): Promise<IPackage> => {
  const response = await adminApi.put<IPackage>(`/admin/packages/${id}`, packageData);
  return response.data;
};

export const updateAdminPackage = async (id: string, packageData: Partial<IPackage>): Promise<IPackage> => {
  const response = await api.put<IPackage>(`/packages/${id}`, packageData);
  return response.data;
};

export const createPackage = async (packageData: Omit<IPackage, '_id'>): Promise<IPackage> => {
  const response = await adminApi.post<IPackage>('/admin/packages', packageData);
  return response.data;
};

export const deletePackage = async (id: string): Promise<{ message: string }> => {
  const response = await adminApi.delete<{ message: string }>(`/admin/packages/${id}`);
  return response.data;
};

export const initiatePackagePurchase = async (packageId: string, cardInfo: any): Promise<{ paymentPageUrl: string }> => {
  const response = await api.post<{ paymentPageUrl: string }>(`/packages/${packageId}/purchase`, cardInfo);
  return response.data;
};

export const cancelSubscription = async (subscriptionId: string, reason: string): Promise<{ message: string }> => {
  const response = await api.post<{ message: string }>(`/packages/subscription/${subscriptionId}/cancel`, { reason });
  return response.data;
};

export const reactivateSubscription = async (subscriptionId: string): Promise<{ message: string }> => {
  const response = await api.post<{ message: string }>(`/packages/subscription/${subscriptionId}/reactivate`);
  return response.data;
};
