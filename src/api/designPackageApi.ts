import { api } from ".";

export interface DesignPackage {
  _id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  currency: string;
  icon: string;
  features: string[];
  featuresEn: string[];
  deliveryTime: number;
  revisionCount: number;
  isPopular: boolean;
  isActive: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

export interface DesignPackageOrder {
  _id: string;
  userId: string;
  packageId: DesignPackage;
  orderNumber: string;
  price: number;
  currency: string;
  status: 'PENDING' | 'PAID' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'REFUNDED';
  paymentStatus: 'PENDING' | 'SUCCESS' | 'FAILED' | 'REFUNDED';
  paymentId?: string;
  iyzicoPaymentId?: string;
  iyzicoPaymentTransactionId?: string;
  notes?: string;
  deliveryDate?: string;
  completedDate?: string;
  files?: {
    name: string;
    url: string;
    uploadedAt: string;
  }[];
  createdAt: string;
  updatedAt: string;
}

// Get all design packages
export const getDesignPackages = async (): Promise<{ packages: DesignPackage[] }> => {
  const response = await api.get('/design-packages');
  return response.data;
};

// Get design package by ID
export const getDesignPackageById = async (id: string): Promise<DesignPackage> => {
  const response = await api.get(`/design-packages/${id}`);
  return response.data;
};

// Create design package order
export const createDesignPackageOrder = async (
  packageId: string,
  orderData: {
    cardInfo?: {
      cardHolderName: string;
      cardNumber: string;
      expireMonth: string;
      expireYear: string;
      cvc: string;
      cardAlias?: string;
    };
    storedCardId?: string;
    saveCard?: boolean;
    notes?: string;
  }
): Promise<{
  message: string;
  order: {
    _id: string;
    orderNumber: string;
    status: string;
    paymentStatus: string;
  };
  paymentPageUrl?: string;
}> => {
  const response = await api.post(`/design-packages/${packageId}/order`, orderData);
  return response.data;
};

// Get user's design package orders
export const getUserDesignPackageOrders = async (): Promise<{ orders: DesignPackageOrder[] }> => {
  const response = await api.get('/design-packages/orders/my');
  return response.data;
};