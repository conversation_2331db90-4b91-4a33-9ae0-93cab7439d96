export interface IRepresentative {
  _id?: string;
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  profilePicture?: string;
  password?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // New fields
  title?: string;
  company?: string;
  languages?: string[];
  experience?: string;
  expertise?: string[];
  region?: string;
  verified?: boolean;
  countryName?: string;
  cityName?: string;
}

export interface ICreateRepresentative {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  password: string;
  
  // New fields
  title?: string;
  company?: string;
  languages?: string[];
  experience?: string;
  expertise?: string[];
  region?: string;
  profilePicture?: string;
  verified?: boolean;
}

export interface IUpdateRepresentative {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  country?: string;
  city?: string;
  password?: string;
  isActive?: boolean;
  
  // New fields
  title?: string;
  company?: string;
  languages?: string[];
  experience?: string;
  expertise?: string[];
  region?: string;
  profilePicture?: string;
  verified?: boolean;
}

export interface IRepresentativeResponse {
  success: boolean;
  data?: IRepresentative | IRepresentative[];
  message?: string;
  error?: string;
}