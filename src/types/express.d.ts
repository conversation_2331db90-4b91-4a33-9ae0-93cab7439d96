import { Request } from 'express';
import { IUser } from '../models/User';
import { IStore } from '../models/Store';

declare global {
  namespace Express {
    interface Request {
      user?: IUser;
      store?: IStore | null;
      rawBody?: string | Buffer;
    }
  }
}

export interface AuthRequest extends Request {
  user?: IUser;
  store?: IStore | null;
  rawBody?: string | Buffer;
}

export {};
