export interface IUser {
  _id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  password?: string;
  birthDate: string | Date;
  address: string;
  city?: string;
  country?: string;
  zipCode: string;
  categoryLevel1Id?: string;
  categoryLevel2Id?: string;
  categoryLevel3Id?: string;
  referralCode: string;
  referrals: IReferralUser[];
  hasPackage: boolean;
}

export interface IAuthResponse {
  success: boolean;
  message: string;
  token: string;
  user: IUser;
}

export interface IServiceUserResponse {
  users: IUser[];
}

export interface ICountry {
  _id: string;
  name: string;
  code: string;
  country_id: string;
  cities?: string[];
}

export interface ICity {
  city_id: string;
  name: string;
  country: string | ICountry;
}

export interface IReferralUser {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  hasPackage: boolean;
}
