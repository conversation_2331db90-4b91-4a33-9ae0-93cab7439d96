export interface ISender {
  _id: string;
  email: string;
}

export interface IProductConversation {
  productId: string;
  productName: string;
  productImage: any;
  conversations: IConversation[];
}

export interface IConversation {
  user: IMessageUser;
  messages: IMessage[];
  unreadCount: number;
  lastMessage?: IMessage;
  roomId: string;
}

export interface IMessageUser {
  _id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
}

export interface IMessage {
  _id: string;
  content: string;
  senderId: string | IMessageUser | any;
  recipientId: string | IMessageUser | any;
  productId: string;
  roomId: string;
  createdAt: Date;
  read: boolean;
  senderName?: string;
}

export interface IMessageSocketData {
  content: string;
  senderId: string;
  recipientId: string;
  roomId: string;
  productId: string;
  senderName?: string;
}

export interface ITypingData {
  userId: string;
  roomId: string;
  userName: string;
}

export interface IMessageResponse {
  _id: string;
  content: string;
  senderId: string;
  recipientId: string;
  productId: string;
  roomId: string;
  createdAt: Date;
  read: boolean;
  sender?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  recipient?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  product?: {
    _id: string;
    name: string;
    images: string[];
  };
}

export interface INotification {
  _id: string;
  type: string;
  sender: string;
  preview: string;
  createdAt: Date;
  data?: {
    productId?: string;
    roomId?: string;
  };
}