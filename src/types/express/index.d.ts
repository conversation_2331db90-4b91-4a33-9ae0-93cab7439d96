declare namespace Express {
  export interface Request {
    user?: {
      id: string;
      [key: string]: any;
    };
    files?: {
      [fieldname: string]: {
        fieldname: string;
        originalname: string;
        encoding: string;
        mimetype: string;
        destination: string;
        filename: string;
        path: string;
        size: number;
      } | {
        fieldname: string;
        originalname: string;
        encoding: string;
        mimetype: string;
        destination: string;
        filename: string;
        path: string;
        size: number;
      }[];
    };
  }
}
