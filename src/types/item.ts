import { ICategory } from "./category";
import { IUser } from "./user";

export interface IItem {
  _id: string;
  name: string;
  description: string;
  category: ICategory | string;
  type: 'product' | 'service';
  isApproved: boolean;
  date: string;
  store: {
    _id: string;
    name: string;
    description: string;
    owner: IUser;
    logo?: string;
    coverImage?: string;
    isActive: boolean;
    isApproved: boolean;
    date: string;
    address?: string;
    phone?: string;
    email?: string;
    website?: string;
    location?: {
      city?: string;
      country?: string;
    };
    socialMedia?: {
      facebook?: string;
      twitter?: string;
      instagram?: string;
      linkedin?: string;
    };
  };
  status: 'ACTIVE' | 'INACTIVE' | 'DELETED';
  listingType?: 'sale' | 'demand';
  images: string[];
  features?: string[];
  requirements?: string[];
  deadline?: string;
  duration?: string;
  location?: string;
  viewCount?: number;
  createdAt: string;
  updatedAt: string;
}

export interface IItemRequest {
  _id: string;
  item: IItem;
  user: IUser;
  message: string;
  date: Date;
}

export interface IItemResponse {
  _id: string;
  item: IItem;
  user: IUser;
  message: string;
  date: Date;
}