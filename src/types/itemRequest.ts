import { ICategory } from './category';
import { IStore } from './store';

export interface IItemRequest {
  _id: string;
  name: string;
  description: string;
  category: ICategory;
  type: 'product' | 'service';
  listingType: 'demand' | 'sale';
  store: IStore;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  images: string[];
  previousRequestId?: string;
  createdAt: string;
  updatedAt: string;
}