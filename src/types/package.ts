export interface IPackage {
  _id: string;
  name: string;
  nameEn: string;
  description: string;
  price: number;
  type: 'standard' | 'addon';
  viewRequestLimit: number;
  createRequestLimit: number;
  emailNotification: boolean;
  smsNotification: boolean;
  languageIntroRights: number;
  messagingAllowed: boolean;
  homepageAd: boolean;
  yearEndSectorReport: boolean;
  isActive: boolean;
  features: string[];
  maxMessages: number;
  duration: number;
  order: number;
  isCurrentPackage?: boolean;
  isUpgradeable?: boolean;
  upgradePrice?: number;
  currency?: string;
}