export type TicketType = 'product' | 'service';
export type TicketStatus = 'pending' | 'in_progress' | 'resolved' | 'closed';

export interface ITicketResponse {
  _id: string;
  userId: string;
  message: string;
  isAdmin: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ITicket {
  _id: string;
  userId: string;
  title: string;
  type: TicketType;
  category: string;
  subcategory?: string;
  country: string;
  city: string;
  amount: number;
  description: string;
  images: string[];
  status: TicketStatus;
  responses: ITicketResponse[];
  createdAt: string;
  updatedAt: string;
}

export interface ICreateTicket {
  title: string;
  type: TicketType;
  category: string;
  subcategory?: string;
  country: string;
  city: string;
  amount: number;
  description: string;
  images?: File[];
}

export interface ITicketComment {
  id: string;
  message: string;
  isAdmin: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface IUpdateTicket {
  status?: TicketStatus;
  response?: string;
}

export interface ITicketApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
}