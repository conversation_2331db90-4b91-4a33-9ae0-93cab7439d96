import { IUser } from './user';

export interface IStore {
  _id: string;
  name: string;
  description: string;
  owner: IUser;
  logo?: string;
  coverImage?: string;
  isActive: boolean;
  isApproved: boolean;
  date: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  type?: 'company' | 'broker';
  viewCount?: number;
  location?: {
    city?: string;
    country?: string;
  };
  socialMedia?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
  createdAt: string;
  updatedAt: string;
}
