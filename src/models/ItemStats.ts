import mongoose, { Schema, Document, Types } from 'mongoose';

export interface IItemStats extends Document {
  itemId: Types.ObjectId;
  viewCount: number;
  interactionCount: number;
  lastViewed: Date;
  lastInteraction: Date;
}

const ItemStatsSchema = new Schema<IItemStats>({
  itemId: { 
    type: Schema.Types.ObjectId, 
    ref: 'Item', 
    required: true,
    unique: true 
  },
  viewCount: { 
    type: Number, 
    default: 0 
  },
  interactionCount: { 
    type: Number, 
    default: 0 
  },
  lastViewed: { 
    type: Date,
    default: Date.now 
  },
  lastInteraction: { 
    type: Date,
    default: Date.now 
  }
}, {
  timestamps: true
});

// Create indexes for efficient querying
ItemStatsSchema.index({ viewCount: -1 });
ItemStatsSchema.index({ interactionCount: -1 });
ItemStatsSchema.index({ lastViewed: -1 });
ItemStatsSchema.index({ lastInteraction: -1 });

export const ItemStats = mongoose.model<IItemStats>('ItemStats', ItemStatsSchema);
