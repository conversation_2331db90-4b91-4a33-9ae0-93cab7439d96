import mongoose, { Schema, Document } from 'mongoose';
import { encrypt, decrypt } from '../utils/encryption';

export interface IStoredCard extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  cardToken: string;
  cardUserKey: string;
  cardAlias: string;
  binNumber: string;
  lastFourDigits: string;
  cardType: string;
  cardAssociation: string;
  cardFamily: string;
  isDefault: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // New security fields
  lastUsedAt?: Date;
  lastUsedIP?: string;
  usageCount: number;
  createdIP?: string;
  failedAttempts: number;
  lastFailedAttempt?: Date;
  expiresAt: Date;
  // Methods
  trackUsage(ipAddress: string, success: boolean): Promise<this>;
  trackFailedPayment(ipAddress: string): Promise<this>;
  isSuspicious(): boolean;
}

const StoredCardSchema: Schema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  cardToken: {
    type: String,
    required: true,
    set: encrypt,
    get: decrypt
  },
  cardUserKey: {
    type: String,
    required: true,
    set: encrypt,
    get: decrypt
  },
  cardAlias: {
    type: String,
    required: true
  },
  binNumber: {
    type: String,
    required: true
  },
  lastFourDigits: {
    type: String,
    required: true
  },
  cardType: {
    type: String,
    required: true
  },
  cardAssociation: {
    type: String,
    required: true
  },
  cardFamily: {
    type: String
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  // Security tracking fields
  lastUsedAt: {
    type: Date
  },
  lastUsedIP: {
    type: String
  },
  usageCount: {
    type: Number,
    default: 0
  },
  createdIP: {
    type: String
  },
  failedAttempts: {
    type: Number,
    default: 0
  },
  lastFailedAttempt: {
    type: Date
  },
  // Data retention - cards expire after 2 years
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 2 * 365 * 24 * 60 * 60 * 1000), // 2 years
    index: { expireAfterSeconds: 0 }
  }
}, {
  timestamps: true,
  toJSON: { getters: true },
  toObject: { getters: true }
});

// Indexes for security and performance
StoredCardSchema.index({ userId: 1, isActive: 1 });
// Removed duplicate index - expiresAt already has TTL index in field definition
StoredCardSchema.index({ lastUsedAt: -1 });

// Method to track card usage
StoredCardSchema.methods.trackUsage = function(ipAddress: string, success: boolean) {
  if (success) {
    this.lastUsedAt = new Date();
    this.lastUsedIP = ipAddress;
    this.usageCount += 1;
    this.failedAttempts = 0; // Reset failed attempts on successful use
  } else {
    this.failedAttempts += 1;
    this.lastFailedAttempt = new Date();
  }
  return this.save();
};

// Method to check if card is suspicious
StoredCardSchema.methods.isSuspicious = function(): boolean {
  // Card is suspicious if:
  // - More than 5 failed attempts in the last hour
  // - More than 10 failed attempts total
  // - Sudden location change (would need GeoIP implementation)
  
  if (this.failedAttempts > 10) return true;
  
  if (this.lastFailedAttempt && this.failedAttempts > 5) {
    const hourAgo = new Date(Date.now() - 60 * 60 * 1000);
    if (this.lastFailedAttempt > hourAgo) return true;
  }
  
  return false;
};

// Method to track failed payment attempts
StoredCardSchema.methods.trackFailedPayment = function(ipAddress: string) {
  return this.trackUsage(ipAddress, false);
};

export const StoredCard = mongoose.model<IStoredCard>('StoredCard', StoredCardSchema);
