import mongoose, { Schema, Document } from 'mongoose';
import { Category } from './Category';

export interface IItem extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  category: mongoose.Types.ObjectId;
  type: 'product' | 'service';
  listingType?: 'demand' | 'sale';
  price?: number;
  isApproved: boolean;
  date: Date;
  store: mongoose.Types.ObjectId;
  status: 'ACTIVE' | 'INACTIVE' | 'DELETED';
  images: string[]; // Array of image URLs
  isDisabled: boolean;
}

const ItemSchema: Schema<IItem> = new mongoose.Schema({
  _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  category: { type: Schema.Types.ObjectId, ref: 'Category', required: true },
  type: { type: String, enum: ['product', 'service'], required: true },
  listingType: { type: String, enum: ['demand', 'sale'], required: false },
  price: { type: Number, required: false },
  isApproved: { type: Boolean, default: true },
  date: { type: Date, default: Date.now },
  store: { type: Schema.Types.ObjectId, ref: 'Store', required: true },
  status: {
    type: String,
    enum: ['ACTIVE', 'INACTIVE', 'DELETED'],
    default: 'ACTIVE'
  },
  images: { type: [String], default: [], validate: [arrayLimit, '{PATH} exceeds the limit of 5'] },
  isDisabled: { type: Boolean, default: false },
}, {
  timestamps: true
});

function arrayLimit(val: string[]) {
  return val.length <= 5;
}

// Add index for faster queries
ItemSchema.index({ status: 1, store: 1 });
ItemSchema.index({ status: 1, category: 1 });

export const Item = mongoose.model<IItem>('Item', ItemSchema);