import mongoose, { Schema, Document } from 'mongoose';

export interface IPayment extends Document {
  subscriptionId?: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  packageId?: mongoose.Types.ObjectId;
  designPackageOrderId?: mongoose.Types.ObjectId;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  paymentDate: Date;
  nextBillingDate?: Date;
  type?: 'subscription' | 'addon_purchase' | 'design_package';
  metadata?: Record<string, any>;
  iyzico?: {
    conversationId?: string;
    paymentId?: string;
    token?: string;
    locale?: string;
    conversationData?: string;
    saveCard?: boolean;
    cardInfo?: {
      cardHolderName: string;
      cardNumber: string;
      expireMonth: string;
      expireYear: string;
      cvc: string;
    };
    cardAlias?: string;
  };
}

const PaymentSchema = new Schema<IPayment>({
  subscriptionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subscription',
    required: false
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  packageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Package',
    required: false
  },
  designPackageOrderId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'DesignPackageOrder',
    required: false
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    default: 'USD'
  },
  status: {
    type: String,
    enum: ['pending', 'completed', 'failed', 'refunded'],
    required: true,
    default: 'pending'
  },
  paymentMethod: {
    type: String,
    required: true,
    default: 'card'
  },
  paymentDate: {
    type: Date,
    default: Date.now
  },
  nextBillingDate: {
    type: Date
  },
  type: {
    type: String,
    enum: ['subscription', 'addon_purchase', 'design_package']
  },
  metadata: {
    type: Schema.Types.Mixed
  },
  iyzico: {
    conversationId: String,
    paymentId: String,
    token: String,
    locale: String,
    conversationData: String,
    saveCard: Boolean,
    cardInfo: {
      cardHolderName: String,
      cardNumber: String,
      expireMonth: String,
      expireYear: String,
      cvc: String
    },
    cardAlias: String
  }
}, {
  timestamps: true
});

// Indexes for common queries
PaymentSchema.index({ userId: 1, status: 1 });
PaymentSchema.index({ subscriptionId: 1, status: 1 });
PaymentSchema.index({ paymentDate: -1 });

// Index for iyzico token lookups
PaymentSchema.index({ 'iyzico.token': 1 });

export const Payment = mongoose.model<IPayment>('Payment', PaymentSchema);
