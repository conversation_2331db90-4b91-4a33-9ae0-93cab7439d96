import { Schema, model, Document } from 'mongoose';

export interface ITicket extends Document {
  userId: string;
  title: string;
  type: 'product' | 'service';
  category: string;
  subcategory?: string;
  country: string;
  city: string;
  amount: number;
  description: string;
  status: 'pending' | 'in_progress' | 'resolved' | 'closed';
  images?: string[];
  responses?: {
    message: string;
    isAdmin: boolean;
    createdAt: Date;
    updatedAt: Date;
  }[];
  createdAt: Date;
  updatedAt: Date;
}

const ticketSchema = new Schema<ITicket>(
  {
    userId: {
      type: String,
      required: true,
      ref: 'User'
    },
    title: {
      type: String,
      required: true
    },
    type: {
      type: String,
      enum: ['product', 'service'],
      required: true
    },
    category: {
      type: String,
      required: true
    },
    subcategory: {
      type: String
    },
    country: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'resolved', 'closed'],
      default: 'pending'
    },
    images: [{
      type: String
    }],
    responses: [{
      message: {
        type: String,
        required: true
      },
      isAdmin: {
        type: Boolean,
        default: false
      },
      createdAt: {
        type: Date,
        default: Date.now
      },
      updatedAt: {
        type: Date,
        default: Date.now
      }
    }]
  },
  {
    timestamps: true
  }
);

export default model<ITicket>('Ticket', ticketSchema);
