import mongoose, { Schema, Document } from 'mongoose';
import { ICountry } from './Country';

export interface ICity extends Document {
  city_id: string;
  name: string;
  country: mongoose.Types.ObjectId | ICountry;
}

const CitySchema: Schema<ICity> = new mongoose.Schema({
  city_id: { type: String, required: true },
  name: { type: String, required: true },
  country: { type: mongoose.Schema.Types.ObjectId, ref: 'Country', required: true },
});

CitySchema.index({ name: 1, country: 1 }, { unique: true });

export const City = mongoose.model<ICity>('City', CitySchema);