import mongoose, { Schema, Document } from 'mongoose';
import { IPackage } from './Package';

export interface ISubscription extends Document {
  userId: mongoose.Types.ObjectId;
  packageId: mongoose.Types.ObjectId;
  parentSubscriptionId?: mongoose.Types.ObjectId;
  startDate: Date;
  endDate: Date;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  status: 'ACTIVE' | 'EXPIRED' | 'UNPAID' | 'CANCELED' | 'PENDING' | 'UPGRADED' | 'FAILED';
  paymentStatus: 'paid' | 'pending' | 'failed' | 'expired';
  renewalDate: Date;
  nextBillingDate: Date;
  iyzicoSubscriptionReferenceCode: string;
  autoRenewal: boolean;
  usageHistory: {
    viewRequestsUsed: number;
    createRequestsUsed: number;
  };
  features: {
    emailNotification: boolean;
    smsNotification: boolean;
    messagingAllowed: boolean;
    homepageAd: boolean;
    languageIntroRights: number;
  };
  addons?: Array<{
    packageId: mongoose.Types.ObjectId;
    status: string;
  }>;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PopulatedSubscription extends Omit<mongoose.Document, 'packageId'> {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  packageId: IPackage;
  startDate: Date;
  endDate: Date;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  status: string;
  paymentStatus: string;
}

const SubscriptionSchema: Schema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  packageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Package',
    required: true
  },
  parentSubscriptionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subscription'
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  remainingViewRequests: {
    type: Number,
    default: 0
  },
  remainingCreateRequests: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['ACTIVE', 'EXPIRED', 'UNPAID', 'CANCELED', 'PENDING', 'UPGRADED', 'FAILED'],
    default: 'PENDING'
  },
  paymentStatus: {
    type: String,
    enum: ['paid', 'pending', 'failed', 'expired'],
    default: 'pending'
  },
  renewalDate: {
    type: Date
  },
  nextBillingDate: {
    type: Date
  },
  iyzicoSubscriptionReferenceCode: {
    type: String
  },
  autoRenewal: {
    type: Boolean,
    default: true // Default to true for monthly auto-renewal
  },
  usageHistory: {
    viewRequestsUsed: {
      type: Number,
      default: 0
    },
    createRequestsUsed: {
      type: Number,
      default: 0
    }
  },
  features: {
    emailNotification: {
      type: Boolean,
      default: false
    },
    smsNotification: {
      type: Boolean,
      default: false
    },
    messagingAllowed: {
      type: Boolean,
      default: false
    },
    homepageAd: {
      type: Boolean,
      default: false
    },
    languageIntroRights: {
      type: Number,
      default: 0
    }
  },
  addons: [{
    packageId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Package'
    },
    status: {
      type: String,
      default: 'ACTIVE'
    }
  }]
}, {
  timestamps: true
});

// Add index for faster queries
SubscriptionSchema.index({ userId: 1, endDate: 1 });

export const Subscription: mongoose.Model<ISubscription> = mongoose.model<ISubscription>('Subscription', SubscriptionSchema);
