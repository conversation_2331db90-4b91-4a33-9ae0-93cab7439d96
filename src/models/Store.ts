import mongoose, { Schema, Document } from 'mongoose';

export interface IStore extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  description: string;
  owner: mongoose.Types.ObjectId;
  logo?: string;
  coverImage?: string;
  isActive: boolean;
  isApproved: boolean;
  date: Date;
  viewCount: number;
  address?: string;
  phone?: string;
  items?: mongoose.Types.ObjectId[];
  email?: string;
  website?: string;
  location?: {
    city: string;
    country: string;
  };
  socialMedia?: {
    facebook?: string;
    twitter?: string;
    instagram?: string;
    linkedin?: string;
  };
}

const StoreSchema: Schema<IStore> = new mongoose.Schema({
  _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
  name: { type: String, required: true },
  description: { type: String, required: true },
  owner: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  logo: { type: String },
  coverImage: { type: String },
  isActive: { type: Boolean, default: true },
  isApproved: { type: Boolean, default: false },
  date: { type: Date, default: Date.now },
  viewCount: { type: Number, default: 0 },
  address: { type: String },
  phone: { type: String },
  email: { type: String },
  items: [{ type: Schema.Types.ObjectId, ref: 'Item' }],
  website: { type: String },
  location: {
    city: { type: String },
    country: { type: String }
  },
  socialMedia: {
    facebook: { type: String },
    twitter: { type: String },
    instagram: { type: String },
    linkedin: { type: String }
  }
}, {
  timestamps: true
});

export const Store = mongoose.model<IStore>('Store', StoreSchema);
