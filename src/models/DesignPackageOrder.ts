import mongoose, { Document, Types, Schema } from 'mongoose';

export type OrderStatus = 'PENDING' | 'PAID' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'REFUNDED';
export type PaymentStatus = 'PENDING' | 'SUCCESS' | 'FAILED' | 'REFUNDED';

export interface IDesignPackageOrder {
  _id?: Types.ObjectId;
  userId: Types.ObjectId;
  packageId: Types.ObjectId;
  orderNumber: string;
  price: number;
  currency: string;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentId?: string;
  iyzicoPaymentId?: string;
  iyzicoPaymentTransactionId?: string;
  notes?: string;
  deliveryDate?: Date;
  completedDate?: Date;
  files?: {
    name: string;
    url: string;
    uploadedAt: Date;
  }[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IDesignPackageOrderDocument extends IDesignPackageOrder, Document {
  _id: Types.ObjectId;
}

const DesignPackageOrderSchema = new Schema<IDesignPackageOrderDocument>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  packageId: {
    type: Schema.Types.ObjectId,
    ref: 'DesignPackage',
    required: true
  },
  orderNumber: {
    type: String,
    required: true,
    unique: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'TRY', 'EUR']
  },
  status: {
    type: String,
    enum: ['PENDING', 'PAID', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'REFUNDED'],
    default: 'PENDING',
    required: true
  },
  paymentStatus: {
    type: String,
    enum: ['PENDING', 'SUCCESS', 'FAILED', 'REFUNDED'],
    default: 'PENDING',
    required: true
  },
  paymentId: {
    type: String,
    sparse: true
  },
  iyzicoPaymentId: {
    type: String,
    sparse: true
  },
  iyzicoPaymentTransactionId: {
    type: String,
    sparse: true
  },
  notes: {
    type: String
  },
  deliveryDate: {
    type: Date
  },
  completedDate: {
    type: Date
  },
  files: [{
    name: {
      type: String,
      required: true
    },
    url: {
      type: String,
      required: true
    },
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }]
}, {
  timestamps: true
});

// Indexes
DesignPackageOrderSchema.index({ userId: 1, status: 1 });
// Removed duplicate index - orderNumber already has unique index from field definition
DesignPackageOrderSchema.index({ createdAt: -1 });

// Pre-save hook to generate order number
DesignPackageOrderSchema.pre('save', async function(next) {
  if (!this.orderNumber) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    this.orderNumber = `DP-${year}${month}${day}-${random}`;
  }
  next();
});

export const DesignPackageOrder = mongoose.model<IDesignPackageOrderDocument>('DesignPackageOrder', DesignPackageOrderSchema);