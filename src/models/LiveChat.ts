import { Schema, model, Document, Types } from 'mongoose';

export interface ILiveChatMessage {
  content: string;
  senderId: string;
  senderType: 'user' | 'admin' | 'anonymous';
  timestamp: Date;
  read: boolean;
}

export interface ILiveChat extends Document {
  _id: Types.ObjectId;
  userId?: Types.ObjectId;
  anonymousId?: string;
  name: string;
  email?: string;
  subject?: string;
  status: 'active' | 'closed' | 'archived';
  messages: ILiveChatMessage[];
  lastMessage?: ILiveChatMessage;
  unreadCount: number;
  createdAt: Date;
  updatedAt: Date;
  
  // Method declarations
  addMessage(message: ILiveChatMessage): Promise<ILiveChat>;
  markAsRead(): Promise<ILiveChat>;
  closeChat(): Promise<ILiveChat>;
  archiveChat(): Promise<ILiveChat>;
}

const liveChatMessageSchema = new Schema<ILiveChatMessage>({
  content: {
    type: String,
    required: true,
    trim: true,
  },
  senderId: {
    type: String,
    required: true,
  },
  senderType: {
    type: String,
    enum: ['user', 'admin', 'anonymous'],
    required: true,
  },
  timestamp: {
    type: Date,
    default: Date.now,
  },
  read: {
    type: Boolean,
    default: false,
  },
});

const liveChatSchema = new Schema<ILiveChat>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    anonymousId: {
      type: String,
      required: false,
    },
    name: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: false,
    },
    subject: {
      type: String,
      required: false,
    },
    status: {
      type: String,
      enum: ['active', 'closed', 'archived'],
      default: 'active',
    },
    messages: [liveChatMessageSchema],
    lastMessage: {
      type: liveChatMessageSchema,
      required: false,
    },
    unreadCount: {
      type: Number,
      default: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
liveChatSchema.index({ userId: 1 });
liveChatSchema.index({ anonymousId: 1 });
liveChatSchema.index({ status: 1 });
liveChatSchema.index({ createdAt: 1 });

// Virtual field for chat session duration
liveChatSchema.virtual('duration').get(function () {
  if (this.status === 'active') {
    return Date.now() - this.createdAt.getTime();
  }

  const lastMessageTime = this.messages.length > 0
    ? this.messages[this.messages.length - 1].timestamp.getTime()
    : this.updatedAt.getTime();

  return lastMessageTime - this.createdAt.getTime();
});

// Methods
liveChatSchema.methods.addMessage = async function(message: ILiveChatMessage) {
  this.messages.push(message);
  this.lastMessage = message;

  if (message.senderType !== 'admin') {
    this.unreadCount += 1;
  }

  await this.save();
  return this;
};

liveChatSchema.methods.markAsRead = async function() {
  if (this.unreadCount > 0) {
    this.messages.forEach((message: ILiveChatMessage) => {
      if (!message.read) {
        message.read = true;
      }
    });

    this.unreadCount = 0;
    await this.save();
  }

  return this;
};

liveChatSchema.methods.closeChat = async function() {
  this.status = 'closed';
  await this.save();
  return this;
};

liveChatSchema.methods.archiveChat = async function() {
  this.status = 'archived';
  await this.save();
  return this;
};

export const LiveChat = model<ILiveChat>('LiveChat', liveChatSchema);