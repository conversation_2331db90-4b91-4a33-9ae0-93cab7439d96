import mongoose, { Schema, Document } from 'mongoose';
import bcrypt from 'bcryptjs';

export interface IUser extends Document {
  _id: mongoose.Types.ObjectId;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  password: string;
  role: 'user' | 'admin';
  subscription?: mongoose.Types.ObjectId;
  date: Date;
  birthDate: Date;
  isActive: boolean;
  address: string;
  city: string;
  country: string;
  zipCode: string;
  ipAddress: string;
  categoryLevel1Id?: mongoose.Types.ObjectId;
  categoryLevel2Id?: mongoose.Types.ObjectId;
  categoryLevel3Id?: mongoose.Types.ObjectId;
  resetPasswordToken?: string;
  resetPasswordExpires?: Date;
  referralCode?: string;
  referredBy?: mongoose.Types.ObjectId;
  referrals?: {
    _id: mongoose.Types.ObjectId;
    firstName: string;
    lastName: string;
    email: string;
    createdAt: string;
    hasPackage: boolean;
  }[];
  hasPackage: boolean;
  profileImage?: string;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const UserSchema: Schema<IUser> = new mongoose.Schema({
  _id: { type: mongoose.Schema.Types.ObjectId, auto: true },
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  phoneNumber: { type: String, required: true },
  email: {
    type: String,
    required: true,
    unique: true,
    match: [/^\S+@\S+\.\S+$/, 'Please enter a valid email address'],
  },
  password: {
    type: String,
    required: true,
    select: false // Don't include password by default in queries
  },
  birthDate: { type: Date, required: true },
  role: { type: String, enum: ['user', 'admin'], default: 'user' },
  subscription: { type: mongoose.Types.ObjectId, ref: 'Subscription' },
  date: { type: Date, default: Date.now },
  isActive: { type: Boolean, default: true },
  address: { type: String, required: true },
  city: { type: String, required: false },
  country: { type: String, required: true },
  zipCode: { type: String, required: true },
  ipAddress: { type: String, required: true },
  categoryLevel1Id: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  categoryLevel2Id: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  categoryLevel3Id: { type: mongoose.Schema.Types.ObjectId, ref: 'Category' },
  resetPasswordToken: { type: String },
  resetPasswordExpires: { type: Date },
  referralCode: {
    type: String,
    required: false,
    unique: true
  },
  referrals: [
    {
      _id: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      firstName: String,
      lastName: String,
      email: String,
      createdAt: String,
      hasPackage: Boolean,
    }
  ],
  referredBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User', default: null },
  hasPackage: {
    type: Boolean,
    default: false
  },
  profileImage: { type: String, required: false }
});

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
  } catch (error: any) {
    next(error);
  }

  if (!this.referralCode) {
    // Generate a unique 8-character code
    const generateCode = () => {
      return Math.random().toString(36).substring(2, 10).toUpperCase();
    };

    let code = generateCode();
    let isUnique = false;

    // Keep generating until we find a unique code
    while (!isUnique) {
      const existingUser = await User.findOne({ referralCode: code });
      if (!existingUser) {
        isUnique = true;
      } else {
        code = generateCode();
      }
    }

    this.referralCode = code;
  }
  next();
});

UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  try {
    if (!this.password) {
      throw new Error('Password field not selected');
    }
    const isMatch = await bcrypt.compare(candidatePassword, this.password);
    return isMatch;
  } catch (error) {
    console.error('Password comparison error:', error);
    throw error;
  }
};

export const User = mongoose.model<IUser>('User', UserSchema);