import mongoose, { Document, Types, Schema } from 'mongoose';

export interface IDesignPackage {
  _id?: Types.ObjectId;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  currency: string;
  icon: string;
  features: string[];
  featuresEn: string[];
  deliveryTime: number; // in days
  revisionCount: number;
  isPopular: boolean;
  isActive: boolean;
  order: number;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IDesignPackageDocument extends IDesignPackage, Document {
  _id: Types.ObjectId;
}

const DesignPackageSchema = new Schema<IDesignPackageDocument>({
  name: {
    type: String,
    required: true,
    trim: true
  },
  nameEn: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  descriptionEn: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'USD',
    enum: ['USD', 'TRY', 'EUR']
  },
  icon: {
    type: String,
    required: true // Will store icon name like 'Globe', 'Layers', 'PenTool'
  },
  features: [{
    type: String,
    required: true
  }],
  featuresEn: [{
    type: String,
    required: true
  }],
  deliveryTime: {
    type: Number,
    required: true,
    default: 7 // days
  },
  revisionCount: {
    type: Number,
    required: true,
    default: 3
  },
  isPopular: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes
DesignPackageSchema.index({ isActive: 1, order: 1 });
DesignPackageSchema.index({ price: 1 });

export const DesignPackage = mongoose.model<IDesignPackageDocument>('DesignPackage', DesignPackageSchema);