import mongoose, { Schema, Document } from 'mongoose';

export interface IReview extends Document {
  content: string;
  rating: number;
  userId: mongoose.Schema.Types.ObjectId;
  productId: mongoose.Schema.Types.ObjectId;
  date: Date;
}

const ReviewSchema: Schema<IReview> = new mongoose.Schema({
  content: {
    type: String,
    required: [true, 'Review content is required'],
    trim: true,
  },
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: 1,
    max: 5, // Assuming ratings are between 1 and 5
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required'],
  },
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: [true, 'Product ID is required'],
  },
  date: {
    type: Date,
    default: Date.now,
  },
});

export const Review = mongoose.model<IReview>('Review', ReviewSchema);
