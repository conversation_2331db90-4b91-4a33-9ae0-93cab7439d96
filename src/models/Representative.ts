import { Schema, model, Document } from 'mongoose';

export interface IRepresentative extends Document {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  profilePicture?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // New fields
  title?: string;
  company?: string;
  languages?: string[];
  expertise?: string[];
  experience?: string;
  region?: string;
  verified?: boolean;
}

const representativeSchema = new Schema<IRepresentative>(
  {
    firstName: {
      type: String,
      required: true,
      trim: true
    },
    lastName: {
      type: String,
      required: true,
      trim: true
    },
    email: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      lowercase: true
    },
    phoneNumber: {
      type: String,
      required: true,
      trim: true
    },
    country: {
      type: String,
      required: true
    },
    city: {
      type: String,
      required: true
    },
    profilePicture: {
      type: String,
      default: ''
    },
    isActive: {
      type: Boolean,
      default: true
    },
    // New fields
    title: {
      type: String,
      default: ''
    },
    company: {
      type: String,
      default: ''
    },
    languages: {
      type: [String],
      default: []
    },
    expertise: {
      type: [String],
      default: []
    },
    experience: {
      type: String,
      default: ''
    },
    region: {
      type: String,
      default: ''
    },
    verified: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true
  }
);

export default model<IRepresentative>('Representative', representativeSchema);
