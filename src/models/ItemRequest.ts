import mongoose, { Schema, Document } from 'mongoose';
import { IUser } from './User';
import { ICategory } from './Category';
import { IStore } from './Store';

export interface IItemRequest extends Document {
  name: string;
  description: string;
  category: mongoose.Types.ObjectId;
  type: 'product' | 'service';
  listingType: 'demand' | 'sale';
  store: mongoose.Types.ObjectId;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'DELETED';
  images: string[];
  createdAt: Date;
  updatedAt: Date;
  rejectionReason?: string;
  previousRequestId?: mongoose.Types.ObjectId;
}

const itemRequestSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  type: {
    type: String,
    enum: ['product', 'service'],
    required: true
  },
  listingType: {
    type: String,
    enum: ['demand', 'sale'],
    required: true
  },
  images: [{
    type: String
  }],
  store: {
    type: Schema.Types.ObjectId,
    ref: 'Store',
    required: true
  },
  status: {
    type: String,
    enum: ['PENDING', 'APPROVED', 'REJECTED', 'DELETED'],
    default: 'PENDING'
  },
  rejectionReason: {
    type: String
  },
  previousRequestId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ItemRequest',
    required: false
  }
}, {
  timestamps: true
});

export const ItemRequest = mongoose.model<IItemRequest>('ItemRequest', itemRequestSchema);
