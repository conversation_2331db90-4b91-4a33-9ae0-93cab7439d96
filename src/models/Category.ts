import mongoose, { Schema, Document } from 'mongoose';

export interface ICategory extends Document {
  id: string; // Using string to accommodate numbering like '1.1'
  name: string;
  nameEn: string;
  parent_id?: string; // Reference to the parent category's id
  level: number;
  type: 'Service' | 'Product';
}

const CategorySchema: Schema = new Schema({
  id: { type: String, required: true, unique: true },
  name: { type: String, required: true },
  nameEn: { type: String, required: true },
  parent_id: { type: String, default: null },
  level: { type: Number, required: true },
  type: { type: String, enum: ['Service', 'Product'], required: true },
});

export const Category = mongoose.model<ICategory>('Category', CategorySchema);