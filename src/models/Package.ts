import mongoose, { Schema, Document } from 'mongoose';

export interface IPackage extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  type: 'standard' | 'addon';
  viewRequestLimit: number;
  createRequestLimit: number;
  emailNotification: boolean;
  smsNotification: boolean;
  languageIntroRights: number;
  messagingAllowed: boolean;
  homepageAd: boolean;
  yearEndSectorReport: boolean;
  isActive: boolean;
  features: string[];
  maxMessages: number;
  duration: number;
  order: number;
  currency: string;
  upgradePrice?: number;
  isCurrentPackage?: boolean;
  isUpgradeable?: boolean;
  canPurchaseAddon?: boolean;
}

const PackageSchema: Schema = new Schema({
  name: {
    type: String,
    required: true
  },
  nameEn: {
    type: String,
    required: true
  },
  description: {
    type: String,
    required: true
  },
  descriptionEn: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  type: {
    type: String,
    enum: ['standard', 'addon'],
    required: true
  },
  viewRequestLimit: {
    type: Number,
    required: true
  },
  createRequestLimit: {
    type: Number,
    required: true
  },
  emailNotification: {
    type: Boolean,
    default: false
  },
  smsNotification: {
    type: Boolean,
    default: false
  },
  languageIntroRights: {
    type: Number,
    default: 0
  },
  messagingAllowed: {
    type: Boolean,
    default: false
  },
  homepageAd: {
    type: Boolean,
    default: false
  },
  yearEndSectorReport: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  features: [{
    type: String
  }],
  maxMessages: {
    type: Number,
    default: 0
  },
  duration: {
    type: Number,
    required: true
  },
  order: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    default: 'USD'
  },
  upgradePrice: {
    type: Number
  }
}, {
  timestamps: true
});

// Add indexes for faster queries
PackageSchema.index({ type: 1, isActive: 1 });
PackageSchema.index({ order: 1 });

export const Package = mongoose.model<IPackage>('Package', PackageSchema);