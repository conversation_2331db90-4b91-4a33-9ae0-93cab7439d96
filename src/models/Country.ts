import mongoose, { Schema, Document } from 'mongoose';

export interface ICountry extends Document {
  _id: string;
  name: string;
  code: string;
  country_id: string;
  nameTr: string;
}

const CountrySchema: Schema<ICountry> = new mongoose.Schema({
  name: { type: String, required: true },
  code: { type: String, required: true },
  country_id: { type: String, required: true },
  nameTr: { type: String, required: false },
});

export const Country = mongoose.model<ICountry>('Country', CountrySchema);