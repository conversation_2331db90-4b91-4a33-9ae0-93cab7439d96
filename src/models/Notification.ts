import { Schema, model, Document, Types } from 'mongoose';
import { IUser } from './User';

/**
 * Interface representing a Notification document in MongoDB.
 */
export interface INotification extends Document {
  userId: Types.ObjectId | IUser['_id'];
  message: string;
  timestamp: Date;
  read: boolean;
}

/**
 * Mongoose schema for notifications.
 */
export const NotificationSchema = new Schema<INotification>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    message: {
      type: String,
      required: true,
      trim: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    read: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true, // Automatically manage createdAt and updatedAt fields
  }
);

/**
 * Mongoose model for notifications.
 */
const Notification = model<INotification>('Notification', NotificationSchema);

export { Notification };