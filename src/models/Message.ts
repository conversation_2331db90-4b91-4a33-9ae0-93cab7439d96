// models/Message.ts
import { Schema, model, Document, Types } from 'mongoose';
import { IUser } from './User';

export interface IMessage extends Document {
  _id: Types.ObjectId;
  senderId: Types.ObjectId | IUser['_id'];
  recipientId: Types.ObjectId | IUser['_id'];
  content: string;
  timestamp: Date;
  read: boolean;
  productId?: Types.ObjectId;
  productName?: string;
  createdAt?: Date;
  roomId?: string;
}

const MessageSchema = new Schema<IMessage>({
  _id: { type: Schema.Types.ObjectId, auto: true },
  senderId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  recipientId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  content: {
    type: String,
    required: true,
    trim: true,
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true,
  },
  read: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  productId: {
    type: Schema.Types.ObjectId,
    ref: 'Item',
    required: false,
  },
  productName: {
    type: String,
    required: false,
  },
  roomId: {
    type: String,
    required: false,
  },
}, {
  timestamps: true,
});

// Indexes for better query performance
MessageSchema.index({ senderId: 1, recipientId: 1 });
MessageSchema.index({ roomId: 1 });
MessageSchema.index({ productId: 1 });

// Virtual population for sender and recipient
MessageSchema.virtual('sender', {
  ref: 'User',
  localField: 'senderId',
  foreignField: '_id',
  justOne: true,
});

MessageSchema.virtual('recipient', {
  ref: 'User',
  localField: 'recipientId',
  foreignField: '_id',
  justOne: true,
});

// Method to mark message as read
MessageSchema.methods.markAsRead = async function() {
  if (!this.read) {
    this.read = true;
    await this.save();
  }
  return this;
};

// Middleware to generate roomId if not provided
MessageSchema.pre('save', function(next) {
  if (!this.roomId) {
    // Create a consistent roomId based on the sorted user IDs
    const users = [(this.senderId as Types.ObjectId).toString(), (this.recipientId as Types.ObjectId).toString()].sort();
    this.roomId = `${users[0]}_${users[1]}`;
    if (this.productId) {
      this.roomId += `_${this.productId}`;
    }
  }
  next();
});

export const Message = model<IMessage>('Message', MessageSchema);