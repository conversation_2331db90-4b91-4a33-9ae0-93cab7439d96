import mongoose, { Schema, Document } from 'mongoose';
import { IUser } from './User';

export interface IHomeAd extends Document {
  userId: IUser['_id'];
  title: string;
  image: string;
  clicks: number;
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  rejectionReason?: string;
  createdAt: Date;
  expiresAt: Date;
}

const HomeAdSchema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: true
  },
  image: {
    type: String,
    required: true
  },
  clicks: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'expired'],
    default: 'pending'
  },
  rejectionReason: {
    type: String
  },
  expiresAt: {
    type: Date,
    required: true
  }
}, {
  timestamps: true
});

export const HomeAd = mongoose.model<IHomeAd>('HomeAd', HomeAdSchema);
