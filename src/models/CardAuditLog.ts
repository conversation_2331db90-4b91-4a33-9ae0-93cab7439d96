import mongoose, { Schema, Document } from 'mongoose';

export interface ICardAuditLog extends Document {
  userId: mongoose.Types.ObjectId;
  cardId?: mongoose.Types.ObjectId;
  action: 'created' | 'used' | 'updated' | 'deleted' | 'failed_payment' | 'viewed' | 'set_default';
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  amount?: number;
  currency?: string;
  packageId?: mongoose.Types.ObjectId;
  metadata?: Record<string, any>;
}

const CardAuditLogSchema: Schema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  cardId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'StoredCard',
    index: true
  },
  action: {
    type: String,
    required: true,
    enum: ['created', 'used', 'updated', 'deleted', 'failed_payment', 'viewed', 'set_default'],
    index: true
  },
  ipAddress: {
    type: String
  },
  userAgent: {
    type: String
  },
  timestamp: {
    type: Date,
    default: Date.now,
    required: true
  },
  success: {
    type: Boolean,
    required: true,
    default: true
  },
  errorMessage: {
    type: String
  },
  amount: {
    type: Number
  },
  currency: {
    type: String
  },
  packageId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Package'
  },
  metadata: {
    type: Schema.Types.Mixed
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
CardAuditLogSchema.index({ userId: 1, timestamp: -1 });
CardAuditLogSchema.index({ cardId: 1, timestamp: -1 });
CardAuditLogSchema.index({ action: 1, success: 1, timestamp: -1 });

// TTL index to automatically delete old audit logs after 2 years
CardAuditLogSchema.index({ timestamp: 1 }, { expireAfterSeconds: 63072000 }); // 2 years in seconds

export const CardAuditLog = mongoose.model<ICardAuditLog>('CardAuditLog', CardAuditLogSchema);