import mongoose, { Document, Schema } from 'mongoose';

export interface ISlider extends Document {
  web: string;
  mobile: string;
  link?: string;
  header?: string;
  description?: string;
  linkText?: string;
  order: number;
  createdAt: Date;
  updatedAt: Date;
}

const sliderSchema = new Schema({
  web: {
    type: String,
    required: true
  },
  mobile: {
    type: String,
    required: true
  },
  link: {
    type: String,
    required: false
  },
  header: {
    type: String,
    required: false
  },
  description: {
    type: String,
    required: false
  },
  linkText: {
    type: String,
    required: false
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

export const Slider = mongoose.model<ISlider>('Slider', sliderSchema);
