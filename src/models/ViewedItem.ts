import mongoose, { Schema, Document } from 'mongoose';

export interface IViewedItem extends Document {
  userId: mongoose.Types.ObjectId;
  itemId: mongoose.Types.ObjectId;
  itemType: 'item' | 'store';
  viewedAt: Date;
}

const ViewedItemSchema: Schema = new Schema({
  userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  itemId: { type: mongoose.Schema.Types.ObjectId, required: true },
  itemType: { type: String, enum: ['item', 'store'], default: 'item' },
  viewedAt: { type: Date, default: Date.now }
}, { timestamps: true });

// Create a compound index to ensure a user can only have one view record per item/store
ViewedItemSchema.index({ userId: 1, itemId: 1, itemType: 1 }, { unique: true });

export const ViewedItem = mongoose.model<IViewedItem>('ViewedItem', ViewedItemSchema);
