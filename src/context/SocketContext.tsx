import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { io, Socket } from "socket.io-client";

interface SocketContextType {
  socket: Socket | null;
  unreadCount: number;
  notifications: Notification[];
  clearNotifications: () => void;
  typingUsers: Map<string, { roomId: string; userName: string }>;
  playNotificationSound: () => void;
  on?: (event: string, listener: (...args: any[]) => void) => void;
  off?: (event: string) => void;
  emit?: (event: string, ...args: any[]) => void;
}

interface Notification {
  _id?: string;
  type: string;
  sender: string;
  preview: string;
  timestamp: Date;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  unreadCount: 0,
  notifications: [],
  clearNotifications: () => { },
  typingUsers: new Map(),
  playNotificationSound: () => { },
  on: undefined,
  off: undefined,
  emit: undefined,
});

export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [unreadCount, setUnreadCount] = useState(0);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [typingUsers, setTypingUsers] = useState<
    Map<string, { roomId: string; userName: string }>
  >(new Map());
  const notificationSound = new Audio("/notification.mp3");

  const playNotificationSound = useCallback(() => {
    notificationSound
      .play()
      .catch((error) => console.error("Error playing notification:", error));
  }, []);

  useEffect(() => {
    const token =
      localStorage.getItem("userToken") || localStorage.getItem("adminToken");
    if (!token) {
      console.log("No authentication token found");
      return;
    }

    console.log("Initializing socket connection");

    // Always use production API URL for socket connections
    // Local server connection attempts consistently fail, so we're bypassing them completely
    const effectiveSocketUrl = import.meta.env.VITE_SOCKET_URL || 'https://api.e-exportcity.com';

    console.log("Using production socket URL:", effectiveSocketUrl);

    const newSocket = io(effectiveSocketUrl, {
      auth: { token },
      path: "/socket.io",
      transports: ["websocket", "polling"], // Try websocket first, then fall back to polling
      reconnectionAttempts: 5,   // More reconnection attempts
      reconnectionDelay: 1000,   // Start with shorter delay
      timeout: 20000,            // Longer connection timeout
      reconnection: true,
      reconnectionDelayMax: 5000, // Maximum delay between reconnections
      autoConnect: true,
      forceNew: true,
      withCredentials: true,
    });

    // Log transport change events
    (newSocket.io.engine as any).on('transport', function (transport: { name: any; }) {
      console.log("Socket transport selected:", transport.name);
    });

    console.log("Socket connection attempt started with transport options:", newSocket.io.opts.transports);

    setSocket(newSocket);

    newSocket.on("connect", () => {
      console.log("Socket connected successfully with ID:", newSocket.id);
      // Get user ID from token
      try {
        const tokenData = JSON.parse(atob(token.split(".")[1]));
        console.log("User ID from token:", tokenData.id);

        // Check if user is admin
        const isAdmin = tokenData.isAdmin || false;
        console.log("Is admin:", isAdmin);

        // Join user's personal room
        newSocket.emit("join:room", tokenData.id);
        newSocket.emit("notifications:get");

        // Join admin room if applicable
        if (isAdmin) {
          console.log("Joining admin room");
          newSocket.emit('admin:join', {});
        }
      } catch (error) {
        console.error("Error parsing token:", error);
      }
    });

    newSocket.on(
      "typing:start",
      (data: { userId: string; roomId: string; userName: string }) => {
        console.log("Received typing:start in context:", data);
        setTypingUsers((prev) => {
          const newMap = new Map(prev);
          newMap.set(data.userId, {
            roomId: data.roomId,
            userName: data.userName,
          });
          console.log("Updated typing users map:", Object.fromEntries(newMap));
          return newMap;
        });
      },
    );

    newSocket.on(
      "typing:stop",
      (data: { userId: string; roomId: string; userName: string }) => {
        console.log("Received typing:stop in context:", data);
        setTypingUsers((prev) => {
          const newMap = new Map(prev);
          newMap.delete(data.userId);
          console.log(
            "Updated typing users map after delete:",
            Object.fromEntries(newMap),
          );
          return newMap;
        });
      },
    );

    newSocket.on("notifications:count", (count: number) => {
      setUnreadCount(count);
    });

    newSocket.on("new_notification", (notification) => {
      console.log("Received new notification:", notification);
      if (!document.hidden && window.location.pathname !== "/messages") {
        playNotificationSound();
      }
      setNotifications((prev) => [
        {
          type: notification.type,
          sender: notification.sender,
          preview: notification.preview,
          timestamp: new Date(notification.timestamp),
        },
        ...prev,
      ]);
    });

    newSocket.on("disconnect", (reason) => {
      console.log("Socket disconnected. Reason:", reason);
      // Clear typing users on disconnect
      setTypingUsers(new Map());

      // Attempt to reconnect if disconnected unexpectedly
      if (reason === 'io server disconnect') {
        // The server has forcefully disconnected the socket
        console.log("Server forcefully disconnected. Attempting to reconnect...");
        newSocket.connect();
      }
    });

    newSocket.on("connect_error", (error: any) => {
      console.error("Socket connection error:", error);
    });

    return () => {
      console.log("Cleaning up socket connection");
      newSocket.off("typing:start");
      newSocket.off("typing:stop");
      newSocket.off("notifications:count");
      newSocket.off("new_notification");
      newSocket.off("disconnect");
      newSocket.off("connect_error");
      newSocket.close();
    };
  }, []);

  const clearNotifications = useCallback(() => {
    if (socket) {
      socket.emit("notifications:clear");
      setNotifications([]);
    }
  }, [socket]);

  return (
    <SocketContext.Provider
      value={{
        socket,
        unreadCount,
        notifications,
        clearNotifications,
        typingUsers,
        playNotificationSound,
        on: socket?.on,
        off: socket?.off,
        emit: socket?.emit,
      }}
    >
      {children}
    </SocketContext.Provider>
  );
};

export const useSocket = () => useContext(SocketContext);
