import React, { createContext, useContext, useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { IUser } from "@/types/user";
import { getUserProfile } from "@/api";
import { getAdminProfile } from "@/adminApi";

interface AuthContextType {
  isAuthenticated: boolean;
  user: IUser | null;
  login: (userData: IUser, token: string, isAdmin?: boolean) => void;
  logout: () => void;
  isLoading: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<IUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  const validateToken = async () => {
    const adminToken = localStorage.getItem("adminToken");
    const userToken = localStorage.getItem("userToken");

    // Clear loading state early if no tokens are present
    if (!adminToken && !userToken) {
      setIsLoading(false);
      setIsAuthenticated(false);
      setUser(null);
      setIsAdmin(false);

      // Only redirect if not on a login page and trying to access protected route
      if (
        location.pathname !== "/login" &&
        location.pathname !== "/forgot-password" &&
        location.pathname !== "/reset-password" &&
        location.pathname !== "/payment-callback" &&
        location.pathname !== "/register" &&
        location.pathname !== "/admin/login" &&
        location.pathname !== "/" &&
        location.pathname !== "/about" &&
        location.pathname !== "/partnership" &&
        location.pathname !== "/faq" &&
        location.pathname !== "/packages" &&
        location.pathname !== "/design-packages" &&
        !location.pathname.startsWith("/reasons/") &&
        !location.pathname.startsWith("/representatives")
      ) {
        if (location.pathname.startsWith("/admin")) {
          navigate("/admin/login", { replace: true });
        } else {
          navigate("/login", { replace: true });
        }
      }
      return;
    }

    try {
      if (adminToken) {
        // Try admin token first
        try {
          const adminProfile = await getAdminProfile();
          setUser(adminProfile);
          setIsAuthenticated(true);
          setIsAdmin(true);

          // ALWAYS redirect to admin dashboard unless already on an admin route
          // This is a critical fix to ensure admin users are always in the admin panel
          if (!location.pathname.startsWith("/admin")) {
            console.log("Admin user detected on user route, redirecting to admin dashboard");
            navigate("/admin/dashboard", { replace: true });
            // Return early to prevent further code execution
            return;
          }
        } catch (error) {
          console.error("Admin token validation failed:", error);
          localStorage.removeItem("adminToken");
          setIsAdmin(false);
          setIsAuthenticated(false);
          setUser(null);

          if (location.pathname.startsWith("/admin")) {
            navigate("/admin/login", { replace: true });
          }
        }
      } else if (userToken) {
        // Try user token if no admin token or admin validation failed
        try {
          const userProfile = await getUserProfile();
          setUser(userProfile);
          setIsAuthenticated(true);
          setIsAdmin(false);

          // Redirect to user dashboard if on admin routes
          if (location.pathname.startsWith("/admin")) {
            navigate("/", { replace: true });
          }
        } catch (error) {
          console.error("User token validation failed:", error);
          localStorage.removeItem("userToken");
          setIsAuthenticated(false);
          setUser(null);

          if (!location.pathname.startsWith("/admin")) {
            navigate("/login", { replace: true });
          }
        }
      }
    } catch (error) {
      console.error("Token validation error:", error);
      localStorage.removeItem("adminToken");
      localStorage.removeItem("userToken");
      setIsAuthenticated(false);
      setUser(null);
      setIsAdmin(false);

      if (location.pathname.startsWith("/admin")) {
        navigate("/admin/login", { replace: true });
      } else {
        navigate("/login", { replace: true });
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    validateToken();
  }, [location.pathname]);

  // Force redirect admin users to the admin area whenever detected
  useEffect(() => {
    if (isAuthenticated && !isLoading && isAdmin) {
      // If admin user is on ANY non-admin page, immediately redirect
      if (!location.pathname.startsWith('/admin')) {
        console.log("Admin user detected on user route, forcing redirect to admin dashboard");
        navigate('/admin/dashboard', { replace: true });
      }
    }
  }, [isAdmin, isAuthenticated, isLoading, location.pathname, navigate]);

  const login = (userData: IUser, token: string, isAdmin: boolean = false) => {
    // First clear any existing tokens to prevent conflicts
    localStorage.removeItem("adminToken");
    localStorage.removeItem("userToken");

    // Set the appropriate token
    localStorage.setItem(isAdmin ? "adminToken" : "userToken", token);

    // Update state
    setUser(userData);
    setIsAuthenticated(true);
    setIsAdmin(isAdmin);

    // Redirect based on user type
    if (isAdmin) {
      console.log("Admin login detected, navigating to admin dashboard");
      navigate("/admin/dashboard", { replace: true });
    } else {
      navigate("/");
    }
  };

  const logout = () => {
    const isAdminRoute = location.pathname.startsWith("/admin");
    localStorage.removeItem(isAdminRoute ? "adminToken" : "userToken");
    setUser(null);
    setIsAuthenticated(false);
    setIsAdmin(false);

    if (isAdminRoute) {
      navigate("/admin/login");
    } else {
      navigate("/login");
    }
  };

  if (isLoading) {
    return null; // or return a loading spinner
  }

  return (
    <AuthContext.Provider
      value={{ isAuthenticated, user, login, logout, isLoading, isAdmin }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
