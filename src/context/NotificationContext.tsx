import React, { createContext, useContext, useEffect, useState } from "react";
import { useSocket } from "./SocketContext";
import { useToast } from "@chakra-ui/react";
import {
  getUnreadNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
} from "../api";
import { useLocation } from "react-router-dom";
import { useAuth } from "./AuthContext";

interface INotification {
  _id: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  read: boolean;
  userId: string;
}

interface NotificationContextType {
  notifications: INotification[];
  unreadCount: number;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  clearNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined,
);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<INotification[]>([]);
  const { socket } = useSocket();
  const toast = useToast();
  const location = useLocation();
  const { isAuthenticated } = useAuth();

  const isAdminRoute = location.pathname.startsWith("/admin");

  // Fetch notifications when authenticated and not in admin route
  useEffect(() => {
    if (!isAuthenticated || isAdminRoute) {
      setNotifications([]);
      return;
    }

    const fetchNotifications = async () => {
      try {
        const response = await getUnreadNotifications();
        setNotifications(response.data);
      } catch (error: any) {
        if (error?.response?.status !== 401) {
          console.error("Error fetching notifications:", error);
        }
      }
    };

    fetchNotifications();
  }, [isAuthenticated, isAdminRoute]);

  // Socket listener for new notifications
  useEffect(() => {
    if (!socket || !isAuthenticated || isAdminRoute) return;

    const handleNewNotification = (notification: INotification) => {
      setNotifications((prev) => [notification, ...prev]);

      if (Notification.permission === "granted") {
        new Notification(notification.title, {
          body: notification.message,
        });
      }

      toast({
        title: notification.title || "New Notification",
        description: notification.message,
        status: "info",
        duration: 5000,
        isClosable: true,
        position: "top-right",
      });
    };

    socket.on("notification", handleNewNotification);

    return () => {
      socket.off("notification", handleNewNotification);
    };
  }, [socket, isAuthenticated, isAdminRoute, toast]);

  const markAsRead = async (notificationId: string) => {
    if (isAdminRoute) return;

    try {
      await markNotificationAsRead(notificationId);
      setNotifications((prev) =>
        prev.map((n) => (n._id === notificationId ? { ...n, read: true } : n)),
      );
    } catch (error) {
      console.error("Error marking notification as read:", error);
    }
  };

  const markAllAsRead = async () => {
    if (isAdminRoute) return;

    try {
      await markAllNotificationsAsRead();
      setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
    } catch (error) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  const clearNotification = (notificationId: string) => {
    setNotifications((prev) => prev.filter((n) => n._id !== notificationId));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter(
    (notification) => !notification.read,
  ).length;

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        markAsRead,
        markAllAsRead,
        clearNotification,
        clearAllNotifications,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider",
    );
  }
  return context;
};

export default NotificationProvider;
