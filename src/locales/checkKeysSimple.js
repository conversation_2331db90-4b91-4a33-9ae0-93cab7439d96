const fs = require('fs');
const path = require('path');

// Read the English admin.json file
const enAdminPath = path.join(__dirname, 'en', 'admin.json');
const enAdmin = JSON.parse(fs.readFileSync(enAdminPath, 'utf8'));

// Read the Arabic admin.json file
const arAdminPath = path.join(__dirname, 'ar', 'admin.json');
const arAdmin = JSON.parse(fs.readFileSync(arAdminPath, 'utf8'));

// Check if a nested key exists in an object
function hasKey(obj, path) {
  const parts = path.split('.');
  let current = obj;
  
  for (const part of parts) {
    if (!current || typeof current !== 'object' || !(part in current)) {
      return false;
    }
    current = current[part];
  }
  
  return true;
}

// Representative keys we want to check
const repKeys = [
  'representatives.title',
  'representatives.add_new',
  'representatives.edit',
  'representatives.createRepresentative',
  'representatives.table.name',
  'representatives.table.email',
  'representatives.table.phone',
  'representatives.table.country',
  'representatives.table.city',
  'representatives.table.status',
  'representatives.table.actions',
  'representatives.form.first_name',
  'representatives.form.last_name',
  'representatives.form.email',
  'representatives.form.phone',
  'representatives.form.country',
  'representatives.form.city',
  'representatives.form.select_country',
  'representatives.form.select_city',
  'representatives.form.is_active',
  'representatives.form.profile_picture',
  'representatives.form.upload_picture',
  'representatives.status.active',
  'representatives.status.inactive',
  'representatives.actions.edit',
  'representatives.actions.delete',
  'representatives.actions.save',
  'representatives.actions.cancel',
  'representatives.actions.confirm',
  'representatives.messages.fetch_error',
  'representatives.messages.delete_confirm',
  'representatives.messages.delete_success',
  'representatives.messages.create_success',
  'representatives.messages.update_success',
  'representatives.messages.invalid_image'
];

// Check English keys
console.log('\n===== CHECKING ENGLISH ADMIN.JSON =====');
let enMissing = 0;

repKeys.forEach(key => {
  if (!hasKey(enAdmin, key)) {
    console.log(`Missing key in English: ${key}`);
    enMissing++;
  }
});

if (enMissing === 0) {
  console.log('All representative keys present in English admin.json ✅');
} else {
  console.log(`${enMissing} keys missing in English admin.json ❌`);
}

// Check Arabic keys
console.log('\n===== CHECKING ARABIC ADMIN.JSON =====');
let arMissing = 0;

repKeys.forEach(key => {
  if (!hasKey(arAdmin, key)) {
    console.log(`Missing key in Arabic: ${key}`);
    arMissing++;
  }
});

if (arMissing === 0) {
  console.log('All representative keys present in Arabic admin.json ✅');
} else {
  console.log(`${arMissing} keys missing in Arabic admin.json ❌`);
}
