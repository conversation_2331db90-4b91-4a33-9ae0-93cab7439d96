{"title": "Richiesta di cancellazione abbonamento", "reason": "Motivo della cancellazione", "submit": "Invia richiesta", "cancel": "<PERSON><PERSON><PERSON>", "success": {"title": "Successo", "description": "Richiesta di cancellazione inviata con successo"}, "error": {"title": "Errore", "description": "Errore durante l'invio della richiesta di cancellazione. Riprova più tardi."}, "reasonPlaceholder": "Per favore, spiega perché vuoi cancellare il tuo abbonamento", "confirmationTitle": "Conferma richiesta di cancellazione", "confirmationMessage": "Sei sicuro di voler richiedere la cancellazione del tuo abbonamento? Questa richiesta sarà esaminata dal nostro team amministrativo.", "revertRequest": "Annulla richiesta di cancellazione", "revertSuccess": {"title": "Successo", "description": "Richiesta di cancellazione annullata con successo"}, "revertError": {"title": "Errore", "description": "Errore durante l'annullamento della richiesta. Riprova più tardi."}, "status": {"pending": "In attesa", "approved": "A<PERSON>rovato", "rejected": "Rifiutato", "reverted": "<PERSON><PERSON><PERSON> da<PERSON>'utente"}, "admin": {"title": "Richieste di cancellazione abbonamento", "noRequests": "Nessuna richiesta di cancellazione trovata", "approve": "<PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>", "requestDate": "Data richiesta", "status": "Stato", "user": "Utente", "package": "<PERSON><PERSON><PERSON>", "actions": "Azioni", "viewReason": "Visualizza motivo"}}