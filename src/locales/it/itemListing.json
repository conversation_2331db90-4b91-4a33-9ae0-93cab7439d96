{"selectTypeAndMode": "Seleziona Tipo e Modalità", "search": {"placeholder": "Cerca prodotti o servizi..."}, "sort": {"newest": "Prima i più recenti", "oldest": "Prima i più vecchi", "mostViewed": "<PERSON><PERSON>", "leastViewed": "<PERSON><PERSON>"}, "filters": {"button": "<PERSON><PERSON><PERSON>"}, "clearFilters": "<PERSON><PERSON><PERSON> filtri", "all": "<PERSON><PERSON>", "location": {"unknown": "Posizione sconosciuta", "label": "Posizione"}, "mode": "Modalità", "store": "Negozio", "categories": "Categorie", "page": {"description": "Prodotti e servizi di tendenza nel commercio di esportazione globale"}, "description": "Descrizione", "addProduct": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addService": "Aggiungi Servizio", "requestProduct": "<PERSON><PERSON>", "requestService": "<PERSON><PERSON>", "addProductDesc": "Crea un nuovo elenco di prodotti da vendere", "addServiceDesc": "Crea un nuovo elenco di servizi da offrire", "requestProductDesc": "<PERSON><PERSON> un prodotto che stai cercando", "requestServiceDesc": "Richiedi un servizio di cui hai bisogno", "basicInfo": "Informazioni di Base", "title": "Prodotti e Servizi", "createItemRequest": "<PERSON><PERSON> richiesta articolo", "editItemRequest": "Modifica richiesta articolo", "editItem": "Modifica articolo", "name": "Nome", "enterName": "Inserisci nome", "type": "Tipo", "product": "<PERSON><PERSON><PERSON>", "service": "<PERSON><PERSON><PERSON>", "category": "Categoria", "selectCategory": "Seleziona categoria", "listingType": "Tipo di annuncio", "sale": "<PERSON><PERSON><PERSON>", "demand": "<PERSON><PERSON>", "enterDescription": "Inserisci descrizione", "htmlDescription": "Descrizione HTML", "images": "<PERSON><PERSON><PERSON><PERSON>", "uploadImages": "Carica immagini", "image": "<PERSON><PERSON><PERSON><PERSON>", "preview": "Anteprima", "removeImage": "<PERSON><PERSON><PERSON><PERSON> immagine", "noImagesUploaded": "Nessuna immagine caricata", "cancel": "<PERSON><PERSON><PERSON>", "update": "Aggiorna", "create": "<PERSON><PERSON>", "updating": "Aggiornamento in corso...", "creating": "Creazione in corso...", "errorFetchingCategories": "Errore durante il recupero delle categorie", "pleaseCompleteAllFields": "Compila tutti i campi obbligatori", "errorUploadingFiles": "Errore durante il caricamento dei file", "successMessages": {"itemRequestUpdated": "Richiesta articolo aggiornata con successo", "itemRequestCreated": "Richiesta articolo creata con successo"}, "errorSavingItemRequest": "Errore durante il salvataggio della richiesta articolo", "placeholders": {"productName": "Inserisci il nome del prodotto", "serviceName": "Inserisci il nome del servizio", "productDescription": "Descrivi il tuo prodotto nel dettaglio", "serviceDescription": "Descrivi il tuo servizio nel dettaglio", "productRequestDescription": "Des<PERSON>rivi il prodotto che stai cercando", "serviceRequestDescription": "Descrivi il servizio di cui hai bisogno", "requestTitle": "Inserisci il titolo della richiesta"}, "categoryLevels": {"main": "<PERSON><PERSON>", "level": "<PERSON><PERSON> {{level}}", "select": "Seleziona categoria {{level}}"}, "types": {"product": "<PERSON><PERSON><PERSON>", "service": "<PERSON><PERSON><PERSON>"}, "item": {"price": "${{price}}", "category": "Categoria", "viewDetails": "Visualizza <PERSON>", "noItems": "<PERSON><PERSON><PERSON> articolo trovato"}, "errors": {"loadData": {"title": "Errore di Caricamento", "description": "Impossibile caricare gli articoli. Riprova più tardi."}, "loadCategoryItems": {"title": "Errore di Caricamento", "description": "Impossibile caricare gli articoli della categoria. Riprova più tardi."}, "loadSubcategories": {"title": "Errore di Caricamento", "description": "Impossibile caricare le sottocategorie. Riprova più tardi."}, "noCreateRequests": {"title": "Richieste Esaurite", "description": "Nessuna richiesta di creazione rimanente. Aggiorna il tuo pacchetto."}, "upgradeRequired": {"title": "Aggiornamento Necessario", "description": "Aggiorna il tuo pacchetto per creare più articoli."}, "createFailed": {"title": "Creazione Fallita", "description": "Impossibile creare l'articolo. Riprova più tardi."}, "updateFailed": {"title": "Aggiornamento Fallito", "description": "Impossibile aggiornare l'articolo. Riprova più tardi."}, "deleteFailed": {"title": "Eliminazione Fallita", "description": "Impossibile eliminare l'articolo. Riprova più tardi."}, "adCreateFailed": {"title": "Creazione Annuncio Fallita", "description": "Impossibile creare l'annuncio. Riprova più tardi."}, "tooManyFiles": {"title": "Troppi File", "description": "Hai superato il numero massimo di file consentiti. Seleziona meno file."}, "maxFiveFiles": {"title": "Limite File Superato", "description": "Massimo 5 file consentiti."}, "validation": {"title": "Errore di Validazione", "description": "Correggi gli errori nel modulo e riprova."}, "categoryRequired": {"title": "Categoria Richiesta", "description": "Seleziona una categoria."}, "subcategoryLoad": {"title": "Errore di Caricamento", "description": "Impossibile caricare le sottocategorie. Riprova più tardi."}, "categoryLoad": {"title": "Errore di Caricamento", "description": "Impossibile caricare le categorie. Riprova più tardi."}, "imageUpload": {"title": "Errore di Caricamento", "description": "Impossibile caricare le immagini. Riprova più tardi."}, "missingImage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Un'immagine è richiesta per questo articolo."}, "pleaseSelectImage": {"title": "Imma<PERSON><PERSON> rich<PERSON>a", "description": "Seleziona un'immagine per l'annuncio."}, "itemLoadFailed": {"title": "Errore di Caricamento", "description": "Impossibile caricare i dati dell'articolo"}, "itemSaveFailed": {"title": "Errore di Salvataggio", "description": "Impossibile salvare i dati dell'articolo"}}, "success": {"itemCreated": "Articolo creato con successo", "itemUpdated": "Articolo aggiornato con successo", "itemDeleted": "Articolo eliminato con successo", "adCreated": {"title": "<PERSON><PERSON><PERSON>", "description": "Il tuo annuncio è stato creato con successo."}}, "buttons": {"createItem": "Crea nuovo articolo", "edit": "Modifica", "delete": "Elimina", "create": "<PERSON><PERSON>", "updateItem": "Aggiorna articolo", "publishProduct": "Pubblica prodotto", "publishService": "Pubblica servizio", "publishRequest": "Pubblica richiesta", "uploadImages": "Carica immagini", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "promote": "<PERSON><PERSON><PERSON><PERSON>", "upgrade": "<PERSON><PERSON><PERSON>", "message": "Messaggio"}, "fields": {"name": "Nome", "description": "Descrizione", "productName": "Nome del prodotto", "serviceName": "Nome del servizio", "productDescription": "Descrizione del prodotto", "serviceDescription": "Descrizione del servizio", "requestDetails": "Dettagli della richiesta", "requestTitle": "<PERSON><PERSON>", "price": "Prezzo", "images": "<PERSON><PERSON><PERSON><PERSON>", "imageHelp": "", "category": "Categoria", "adTitle": "<PERSON>lo dell'annuncio", "adTitleHelper": "Inserisci un titolo accattivante per il tuo annuncio", "adImage": "Immagine dell'annuncio", "adImageHelper": "L'immagine deve essere inferiore a 2MB e in formato JPG, PNG o GIF"}, "modals": {"create": {"title": "Crea nuovo articolo", "success": "Articolo creato con successo"}, "edit": {"title": "Modifica articolo", "success": "Articolo aggiornato con successo"}, "message": {"title": "Invia messaggio"}}, "dialogs": {"delete": {"title": "Elimina articolo", "message": "Sei sicuro di voler eliminare questo articolo? Questa azione non può essere annullata.", "success": "Articolo eliminato con successo"}, "viewRequest": {"title": "Usa richiesta di visualizzazione", "message": "Vuoi utilizzare una delle tue richieste di visualizzazione per vedere questo articolo?", "confirm": "Sì, visualizza articolo", "cancel": "No, annulla", "remaining": "<PERSON><PERSON><PERSON>"}, "homeAd": {"title": "<PERSON><PERSON> annuncio in homepage", "message": "Vuoi promuovere questo articolo sulla homepage per {{duration}} gior<PERSON>?", "success": "<PERSON><PERSON><PERSON> creato con successo"}, "upgrade": {"title": "Aggiorna il tuo pacchetto", "message": "Promuovi i tuoi articoli sulla homepage e raggiungi più clienti aggiornando a un pacchetto con funzione di annuncio in homepage!", "benefits": {"title": "<PERSON><PERSON><PERSON> dell'annuncio in homepage:", "visibility": "Maggiore visibilità per i tuoi prodotti", "traffic": "Più traffico per i tuoi annunci", "sales": "Maggiori possibilità di vendita"}}}, "seeDetails": "Visualizza <PERSON>", "myItemsTitle": "I miei articoli", "products": "<PERSON><PERSON>tti", "services": "<PERSON><PERSON><PERSON>", "allCategories": "<PERSON><PERSON> le categorie", "pendingRequests": "<PERSON><PERSON> in sospeso", "approvedItems": "Articoli approvati", "cannotEditApprovedItem": "Impossibile modificare l'articolo approvato", "itemRequestDeleted": "Richiesta articolo eliminata", "itemDeleted": "Articolo eliminato", "errorDeletingItemRequest": "Errore durante l'eliminazione della richiesta articolo", "deleteItemRequestConfirmation": "Sei sicuro di voler eliminare questa richiesta articolo?", "selectType": "Seleziona tipo", "mainCategory": "Categoria principale", "subCategory": "Sottocategoria {{level}}", "noItemsFound": "<PERSON><PERSON><PERSON> articolo trovato", "loading": "Caricamento...", "errorFetchingItems": "Impossibile caricare gli articoli", "createNewRequest": "Crea nuova richiesta", "deleteItemRequest": "Elimina richiesta articolo", "whatsapp": {"contact": "Contatta via WhatsApp", "message": "<PERSON><PERSON><PERSON>, sono interessato al tuo articolo: {{itemName}}"}, "language": {"english": "<PERSON><PERSON><PERSON>", "turkish": "<PERSON><PERSON><PERSON>", "german": "Tedesco", "select": "Seleziona lingua"}}