{"tabs": {"profile": "<PERSON>ilo", "subscription": "Abbonamento", "store": "Azienda", "cards": "<PERSON><PERSON>", "support_tickets": "Ticket di <PERSON>", "homepage_ads": "Annunci Homepage", "items": "Articoli", "references": "R<PERSON><PERSON><PERSON>"}, "profile": {"title": "<PERSON>ilo <PERSON>", "personalInfo": "Informazioni Personali", "firstName": "Nome", "lastName": "Cognome", "email": "Email", "birthDate": "Data di Nascita", "phoneNumber": "Numero di Telefono", "country": "<PERSON><PERSON>", "city": "Città", "identityNumber": "Numero di Identità", "address": "<PERSON><PERSON><PERSON><PERSON>", "zipCode": "CAP", "password": "Password", "passwordPlaceholder": "<PERSON><PERSON> vuoto per mantenere la password attuale", "updateProfile": "Aggiorna Profilo", "name": "Nome", "role": "<PERSON><PERSON><PERSON>", "status": "Stato"}, "subscription": {"currentPackage": "Pacchetto Attuale", "active": "Attivo", "cancelled": "<PERSON><PERSON><PERSON>", "addon": "Pacchetto Aggiuntivo", "activeAddons": "<PERSON><PERSON><PERSON> Aggiuntivi Attivi", "remainingDays": "<PERSON><PERSON><PERSON>", "days": "<PERSON>ior<PERSON>", "endDate": "Data di Scadenza", "remainingRequests": "<PERSON><PERSON>", "viewRequests": "Visualizza Richieste", "createRequests": "<PERSON><PERSON>", "features": "Funzionalità", "noActiveSubscription": "Nessun abbonamento attivo", "cancelButton": "<PERSON><PERSON><PERSON>", "reactivateButton": "Riattiva Abbonamento", "cancelConfirmTitle": "<PERSON><PERSON><PERSON>", "cancelConfirmMessage": "Sei sicuro di voler annullare il tuo abbonamento?", "cancelActiveUntil": "Il tuo abbonamento rimarrà attivo fino al {{date}}", "cancelAddonWarning": "Nota: L'annullamento di questo abbonamento cancellerà anche tutti i pacchetti aggiuntivi associati.", "cancelSuccess": "Abbonamento annullato con successo", "cancelError": "Errore durante l'annullamento dell'abbonamento", "reactivateSuccess": "Abbonamento riattivato con successo", "reactivateError": "Errore durante la riattivazione dell'abbonamento", "fetchPackagesError": "Errore durante il recupero dei pacchetti", "addonInfo": "Informazioni Pacchetto Aggiuntivo", "connectedToPackage": "Collegato al Pacchetto", "originalPrice": "Prezzo Originale", "proRatedPrice": "Prezzo Proporzionale", "standardPackageInfo": "Questo è un pacchetto standard. L'acquisto sostituirà il tuo pacchetto attuale.", "noSubscription": "<PERSON><PERSON><PERSON>", "noSubscriptionMessage": "Non hai alcun abbonamento attivo. Sfoglia i nostri pacchetti per iniziare.", "browsePackages": "<PERSON><PERSON><PERSON>"}, "features": {"emailNotifications": "Notifiche Email", "smsNotifications": "Notifiche SMS", "languageIntro": "Diritti di Introduzione Lingua", "basicMessaging": "Messaggistica Base", "unlimitedMessaging": "Messaggistica Illimitata", "homepageAd": "Annuncio Homepage", "sectorReport": "Report di Settore", "additionalViews": "Richieste di Visualizzazione Aggiuntive", "additionalCreates": "Richieste di Creazione Aggiuntive"}, "store": {"createStore": "<PERSON><PERSON>", "updateStore": "Aggiorna Azienda", "name": "Nome <PERSON>", "description": "Descrizione Azienda", "logo": "Logo Azienda", "coverImage": "Immagine di Copertina", "address": "Indirizzo <PERSON>", "phone": "Telefono Azienda", "email": "<PERSON><PERSON>", "website": "Sito Web Azienda", "socialMedia": "Link Social Media"}, "cards": {"savedCards": "Carte <PERSON>", "addNewCard": "Aggiungi Nuova Carta", "noSavedCards": "Nessuna carta salvata trovata", "unnamedCard": "Carta Senza Nome", "cardAlias": "<PERSON><PERSON>", "cardAliasPlaceholder": "es. La Mia Carta Personale", "cardHolderName": "Nome del Titolare", "cardHolderNamePlaceholder": "Nome sulla <PERSON>", "cardNumber": "Numero Carta", "expireMonth": "Mese", "expireYear": "<PERSON><PERSON>", "cvc": "CVC", "addCard": "Aggiungi Carta"}, "success": {"profileUpdated": {"title": "Profilo <PERSON>", "description": "Il tuo profilo è stato aggiornato con successo"}, "storeCreated": {"title": "<PERSON><PERSON><PERSON>", "description": "Il tuo negozio è stato creato con successo"}, "storeUpdated": {"title": "Negozio <PERSON>", "description": "Il tuo negozio è stato aggiornato con successo"}, "cardAdded": {"title": "Carta Aggiunta", "description": "La tua carta è stata aggiunta con successo"}, "cardRemoved": {"title": "<PERSON><PERSON>", "description": "La tua carta è stata rimossa con successo"}, "defaultCardSet": {"title": "Carta Predefinita Impostata", "description": "La tua carta predefinita è stata impostata con successo"}}, "errors": {"failedToLoadProfile": {"title": "Impossibile Caricare il Profilo", "description": "Per favore riprova più tardi"}, "failedToUpdateProfile": {"title": "Impossibile Aggiornare il Profilo", "description": "Per favore controlla le tue informazioni e riprova"}, "failedToLoadStore": {"title": "Impossibile Caricare il Negozio", "description": "Per favore riprova più tardi"}, "failedToCreateStore": {"title": "Impossibile Creare il Negozio", "description": "Per favore controlla le tue informazioni e riprova"}, "failedToUpdateStore": {"title": "Impossibile Aggiornare il Negozio", "description": "Per favore controlla le tue informazioni e riprova"}, "failedToLoadCards": {"title": "Impossibile Caricare le Carte", "description": "Impossibile caricare le tue carte salvate. Per favore riprova più tardi"}, "failedToAddCard": {"title": "Impossibile Aggiungere la Carta", "description": "Impossibile salvare la tua carta. Per favore controlla i dettagli e riprova"}, "failedToRemoveCard": {"title": "Impossibile Rimuovere la Carta", "description": "Impossibile rimuovere la carta. Per favore riprova più tardi"}, "failedToSetDefaultCard": {"title": "Impossibile Impostare la Carta Predefinita", "description": "Impossibile impostare la carta predefinita. Per favore riprova più tardi"}}, "loading": "Caricamento in corso...", "common": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Conferma", "open_menu": "Apri menu", "close": "<PERSON><PERSON>", "navigation": "Navigazione"}, "dashboard": {"welcome": "<PERSON><PERSON><PERSON> nella Tua Dashboard", "latest": "Ult<PERSON>i Annunci Homepage"}, "roles": {"user": "Utente", "admin": "Amministratore", "representative": "Rapp<PERSON>ent<PERSON>"}, "status": {"active": "Attivo", "inactive": "Inattivo"}, "homepageAd": {"manage": "Gestisci Annunci Homepage", "latest": "<PERSON> <PERSON><PERSON>ltimi Annun<PERSON>"}}