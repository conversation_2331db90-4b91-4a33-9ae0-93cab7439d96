const fs = require('fs');
const path = require('path');

// Paths to language directories
const enDir = path.join(__dirname, 'en');
const arDir = path.join(__dirname, 'ar');

// Function to get all JSON files in a directory
const getJsonFiles = (dir) => {
  return fs.readdirSync(dir)
    .filter(file => file.endsWith('.json'))
    .map(file => ({ 
      name: file,
      path: path.join(dir, file)
    }));
};

// Function to read and parse JSON file
const readJsonFile = (filePath) => {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return null;
  }
};

// Function to find missing keys in a nested object structure
const findMissingKeys = (source, target, parentKey = '') => {
  const missingKeys = [];
  
  Object.keys(source).forEach(key => {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;
    
    if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
      // If it's a nested object, check if the key exists in target
      if (!target[key] || typeof target[key] !== 'object') {
        missingKeys.push({
          key: currentKey,
          value: source[key]
        });
      } else {
        // Recursively check nested objects
        const nestedMissingKeys = findMissingKeys(source[key], target[key], currentKey);
        missingKeys.push(...nestedMissingKeys);
      }
    } else {
      // If it's a leaf value, check if the key exists in target
      if (!target.hasOwnProperty(key)) {
        missingKeys.push({
          key: currentKey,
          value: source[key]
        });
      }
    }
  });
  
  return missingKeys;
};

// Function to find duplicate keys in a nested object structure
const findDuplicateKeys = (obj, parentKey = '') => {
  const duplicates = [];
  const keys = new Set();
  
  Object.keys(obj).forEach(key => {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;
    
    if (keys.has(key)) {
      duplicates.push(currentKey);
    }
    
    keys.add(key);
    
    if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
      const nestedDuplicates = findDuplicateKeys(obj[key], currentKey);
      duplicates.push(...nestedDuplicates);
    }
  });
  
  return duplicates;
};

// Function to set a nested value in an object
const setNestedValue = (obj, path, value) => {
  const keys = path.split('.');
  let current = obj;
  
  for (let i = 0; i < keys.length - 1; i++) {
    if (!current[keys[i]] || typeof current[keys[i]] !== 'object') {
      current[keys[i]] = {};
    }
    current = current[keys[i]];
  }
  
  current[keys[keys.length - 1]] = value;
};

// Function to compare two language files
const compareLanguageFiles = (enFile, arFile) => {
  const enData = readJsonFile(enFile.path);
  const arData = readJsonFile(arFile.path);
  
  if (!enData || !arData) {
    return null;
  }
  
  const missingInAr = findMissingKeys(enData, arData);
  const duplicatesInAr = findDuplicateKeys(arData);
  
  return {
    fileName: enFile.name,
    missingKeysCount: missingInAr.length,
    duplicatesCount: duplicatesInAr.length,
    missingKeys: missingInAr,
    duplicates: duplicatesInAr
  };
};

// Function to generate a report for all files
const generateComparisonReport = () => {
  const enFiles = getJsonFiles(enDir);
  const arFiles = getJsonFiles(arDir);
  
  // Map English files to their names for easy lookup
  const enFilesMap = new Map(enFiles.map(file => [file.name, file]));
  
  const results = [];
  let totalMissingKeys = 0;
  let totalDuplicates = 0;
  
  // Check for matching Arabic files for each English file
  enFiles.forEach(enFile => {
    const arFile = arFiles.find(file => file.name === enFile.name);
    
    if (arFile) {
      const result = compareLanguageFiles(enFile, arFile);
      if (result) {
        results.push(result);
        totalMissingKeys += result.missingKeysCount;
        totalDuplicates += result.duplicatesCount;
      }
    } else {
      console.error(`No matching Arabic file for ${enFile.name}`);
      results.push({
        fileName: enFile.name,
        missingKeysCount: 'FILE MISSING',
        duplicatesCount: 0,
        missingKeys: [],
        duplicates: []
      });
    }
  });
  
  // Check for Arabic files that don't have matching English files
  arFiles.forEach(arFile => {
    if (!enFilesMap.has(arFile.name)) {
      console.error(`No matching English file for ${arFile.name}`);
      results.push({
        fileName: arFile.name,
        missingKeysCount: 0,
        duplicatesCount: 0,
        missingKeys: [],
        duplicates: [],
        extraFile: true
      });
    }
  });
  
  return {
    totalFiles: enFiles.length,
    totalMissingKeys,
    totalDuplicates,
    fileResults: results
  };
};

// Function to fix missing keys by adding them to the Arabic files
const fixMissingKeys = (dryRun = true) => {
  const enFiles = getJsonFiles(enDir);
  const arFiles = getJsonFiles(arDir);
  
  // Map Arabic files to their names for easy lookup
  const arFilesMap = new Map(arFiles.map(file => [file.name, file]));
  
  let totalFixed = 0;
  
  enFiles.forEach(enFile => {
    const arFile = arFilesMap.get(enFile.name);
    
    if (arFile) {
      const enData = readJsonFile(enFile.path);
      const arData = readJsonFile(arFile.path);
      
      if (!enData || !arData) {
        return;
      }
      
      const missingInAr = findMissingKeys(enData, arData);
      
      if (missingInAr.length > 0) {
        console.log(`Found ${missingInAr.length} missing keys in ${arFile.name}`);
        
        let dataModified = false;
        missingInAr.forEach(({ key, value }) => {
          console.log(`  - Adding ${key}`);
          setNestedValue(arData, key, value);
          dataModified = true;
          totalFixed++;
        });
        
        if (dataModified && !dryRun) {
          fs.writeFileSync(arFile.path, JSON.stringify(arData, null, 2), 'utf8');
          console.log(`  Saved changes to ${arFile.name}`);
        }
      }
    }
  });
  
  console.log(`Total keys fixed: ${totalFixed}`);
  if (dryRun) {
    console.log('This was a dry run. No files were modified.');
    console.log('Run with fixMissingKeys(false) to apply the changes.');
  }
  
  return totalFixed;
};

// Run the comparison report
const report = generateComparisonReport();
console.log('Language Comparison Report:');
console.log('==========================');
console.log(`Total files checked: ${report.totalFiles}`);
console.log(`Total missing keys: ${report.totalMissingKeys}`);
console.log(`Total duplicates: ${report.totalDuplicates}`);
console.log('\nDetails:');

// Sort file results by missing keys count (highest first)
report.fileResults
  .sort((a, b) => {
    if (a.missingKeysCount === 'FILE MISSING') return -1;
    if (b.missingKeysCount === 'FILE MISSING') return 1;
    return b.missingKeysCount - a.missingKeysCount;
  })
  .forEach(result => {
    console.log(`\n${result.fileName}:`);
    if (result.missingKeysCount === 'FILE MISSING') {
      console.log('  ARABIC FILE MISSING');
    } else {
      console.log(`  Missing keys: ${result.missingKeysCount}`);
      console.log(`  Duplicates: ${result.duplicatesCount}`);
      
      if (result.missingKeysCount > 0) {
        console.log('  Missing key details:');
        result.missingKeys.forEach(({ key }) => {
          console.log(`    - ${key}`);
        });
      }
      
      if (result.duplicatesCount > 0) {
        console.log('  Duplicate key details:');
        result.duplicates.forEach(key => {
          console.log(`    - ${key}`);
        });
      }
    }
  });

// Save the report to a JSON file for further analysis
fs.writeFileSync(
  path.join(__dirname, 'language-analysis-report.json'),
  JSON.stringify(report, null, 2),
  'utf8'
);

console.log('\nAnalysis report saved to language-analysis-report.json');
console.log('\nTo fix missing keys, run:');
console.log('  fixMissingKeys(false)');
console.log('To see what would be fixed without making changes, run:');
console.log('  fixMissingKeys(true)');

// Uncomment to run the fix with a dry run (no changes made)
// fixMissingKeys(true);

// Uncomment to apply the fixes to the Arabic files
// fixMissingKeys(false);
