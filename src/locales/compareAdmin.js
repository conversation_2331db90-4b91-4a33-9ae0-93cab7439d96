import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read the English and Arabic admin.json files
const enAdminPath = path.join(__dirname, 'en', 'admin.json');
const arAdminPath = path.join(__dirname, 'ar', 'admin.json');

// Function to read and parse JSON file
const readJsonFile = (filePath) => {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return null;
  }
};

// Function to find missing keys in nested objects
const findMissingKeys = (source, target, parentKey = '') => {
  const missingKeys = [];
  
  Object.keys(source).forEach(key => {
    const currentKey = parentKey ? `${parentKey}.${key}` : key;
    
    if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
      // If target doesn't have this key or it's not an object, consider all nested keys missing
      if (!target[key] || typeof target[key] !== 'object') {
        missingKeys.push({
          key: currentKey,
          value: source[key]
        });
      } else {
        // Recursively check nested objects
        const nestedMissingKeys = findMissingKeys(source[key], target[key], currentKey);
        missingKeys.push(...nestedMissingKeys);
      }
    } else {
      // For leaf values, check if key exists
      if (!target.hasOwnProperty(key)) {
        missingKeys.push({
          key: currentKey,
          value: source[key]
        });
      }
    }
  });
  
  return missingKeys;
};

// Read both files
const enAdmin = readJsonFile(enAdminPath);
const arAdmin = readJsonFile(arAdminPath);

if (!enAdmin || !arAdmin) {
  console.error('Failed to read admin files');
  process.exit(1);
}

// Find missing keys in Arabic file
const missingInAr = findMissingKeys(enAdmin, arAdmin);

// Find keys in Arabic that don't exist in English
const extraInAr = findMissingKeys(arAdmin, enAdmin);

console.log('==== Admin.json Comparison Report ====');
console.log(`\nMissing keys in Arabic (${missingInAr.length}):`);
if (missingInAr.length > 0) {
  missingInAr.forEach(({ key, value }) => {
    console.log(`  - ${key}: ${typeof value === 'object' ? 'Object' : JSON.stringify(value)}`);
  });
} else {
  console.log('  None! Arabic has all keys from English.');
}

console.log(`\nExtra keys in Arabic (${extraInAr.length}):`);
if (extraInAr.length > 0) {
  extraInAr.forEach(({ key }) => {
    console.log(`  - ${key}`);
  });
} else {
  console.log('  None! Arabic has no extra keys.');
}

// Function to fix missing keys by adding them to the Arabic file
const fixMissingKeys = () => {
  if (missingInAr.length === 0) {
    console.log('\nNo keys to fix!');
    return;
  }
  
  // Deep copy of the Arabic admin file
  const updatedArAdmin = JSON.parse(JSON.stringify(arAdmin));
  
  // Function to set a nested value
  const setNestedValue = (obj, path, value) => {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
  };
  
  // Add all missing keys
  missingInAr.forEach(({ key, value }) => {
    setNestedValue(updatedArAdmin, key, value);
    console.log(`Added key: ${key}`);
  });
  
  // Save the updated file
  fs.writeFileSync(arAdminPath, JSON.stringify(updatedArAdmin, null, 2), 'utf8');
  console.log(`\nUpdated ${arAdminPath} with ${missingInAr.length} missing keys.`);
};

console.log('\nTo add missing keys to the Arabic admin.json file, uncomment the next line in the script.');
// fixMissingKeys();
