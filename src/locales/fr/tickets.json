{"title": "Tickets de Support", "create_ticket": "<PERSON><PERSON>er un Nouveau Ticket", "noTickets": "Aucun ticket trouvé", "table": {"id": "ID", "title": "Titre", "type": "Type", "category": "<PERSON><PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "status": "Statut", "created_at": "<PERSON><PERSON><PERSON>", "actions": "Actions"}, "actions": {"view": "Voir"}, "form": {"title": "Titre", "title_placeholder": "Entrez le titre du ticket", "type": "Type", "select_type": "Sélectionnez le Type", "type_product": "Produit", "type_service": "Service", "category": "<PERSON><PERSON><PERSON><PERSON>", "select_category": "Sélectionnez la Catégorie", "subcategory": "Sous-cat<PERSON><PERSON><PERSON> (Optionnel)", "select_subcategory": "Sélectionnez la Sous-catégorie", "country": "Pays", "select_country": "Sélectionnez le Pays", "city": "Ville", "select_city": "Sélectionnez la Ville", "amount": "<PERSON><PERSON>", "amount_placeholder": "<PERSON><PERSON><PERSON> le montant", "description": "Description", "description_placeholder": "Entrez votre description", "images": "Images", "upload_images": "Télécharger des Images", "max_files": "Maximum {{count}} fi<PERSON>ers autorisés", "max_size": "<PERSON><PERSON> maximale du fichier : {{size}}MB", "allowed_types": "Types de fichiers autorisés : {{types}}", "status": "Statut", "submit": "So<PERSON><PERSON><PERSON>", "submitting": "Soumission en cours...", "create_header": "Créer un nouveau ticket", "update_header": "Modifier le ticket", "cancel": "Annuler", "product": "Produit", "service": "Service"}, "validation": {"title_required": "Le titre est requis", "type_required": "Le type est requis", "category_required": "La catégorie est requise", "country_required": "Le pays est requis", "city_required": "La ville est requise", "amount_required": "Le montant est requis", "amount_min": "Le montant doit être supérieur à 0", "description_required": "La description est requise", "description_min_length": "La description doit comporter au moins 20 caractères", "file_required": "Au moins une image est requise", "file_type": "Type de fichier invalide. Seuls les fichiers image sont autorisés", "file_size": "La taille du fichier dépasse la limite de {{size}}MB", "max_files": "Maximum {{count}} fi<PERSON>ers autorisés", "required_fields": "Veuillez remplir tous les champs obligatoires"}, "messages": {"create_success": "Ticket créé avec succès", "create_error": "Échec de la création du ticket", "update_success": "Ticket mis à jour avec succès", "update_error": "Échec de la mise à jour du ticket", "fetch_error": "Échec de la récupération des tickets", "response_added": "Réponse ajoutée avec succès", "response_error": "Échec de l'ajout de la réponse", "file_upload_error": "Échec du téléchargement du/des fichier(s)"}, "status": {"pending": "En attente", "in_progress": "En cours", "resolved": "R<PERSON>ol<PERSON>", "closed": "<PERSON><PERSON><PERSON>"}, "types": {"product": "Produit", "service": "Service"}, "responses": "Réponses", "add_response": "Ajouter une Réponse", "response_placeholder": "Écrivez votre réponse ici...", "admin": "Admin", "you": "Vous", "user": "Utilisa<PERSON>ur", "send": "Envoyer", "cancellation_request": "Demande d'annulation d'abonnement", "cancellation_ticket_created": "Votre demande d'annulation a été soumise comme ticket."}