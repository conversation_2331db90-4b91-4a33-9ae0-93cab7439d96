{"title": "Demande d'annulation d'abonnement", "reason": "<PERSON><PERSON><PERSON> d'annulation", "submit": "Envoyer la demande", "cancel": "Annuler", "success": {"title": "Su<PERSON>ès", "description": "De<PERSON><PERSON> d'annulation envoy<PERSON> avec succès"}, "error": {"title": "<PERSON><PERSON><PERSON>", "description": "Erreur lors de l'envoi de la demande d'annulation. Veuillez réessayer plus tard."}, "reasonPlaceholder": "Veuillez expliquer pourquoi vous souhaitez annuler votre abonnement", "confirmationTitle": "Confirmer la demande d'annulation", "confirmationMessage": "Êtes-vous sûr de vouloir demander l'annulation de votre abonnement ? Cette demande sera examinée par notre équipe d'administration.", "revertRequest": "Annuler la demande d'annulation", "revertSuccess": {"title": "Su<PERSON>ès", "description": "Demande d'annulation annulée avec succès"}, "revertError": {"title": "<PERSON><PERSON><PERSON>", "description": "Erreur lors de l'annulation de la demande. Veuillez réessayer plus tard."}, "status": {"pending": "En attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "reverted": "Annulé par l'utilisateur"}, "admin": {"title": "Demandes d'annulation d'abonnement", "noRequests": "Aucune demande d'annulation trouvée", "approve": "Approuver", "reject": "Refuser", "requestDate": "Date de la demande", "status": "Statut", "user": "Utilisa<PERSON>ur", "package": "Forfait", "actions": "Actions", "viewReason": "Voir le motif"}}