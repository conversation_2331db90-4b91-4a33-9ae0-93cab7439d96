{"packages": {"headerTag": "Élargissez votre réseau commercial !", "title": "E-exportcity est à vos côtés avec des forfaits adaptés à vos besoins !", "description": "Que vous fassiez vos premiers pas ou que vous visiez à vous développer sur le marché mondial, numérisez votre entreprise avec les forfaits flexibles et orientés objectifs d'e-exportcity.", "mostPopular": "Le plus populaire", "current": "Forfait actuel", "buyNow": "Acheter maintenant", "addonHeader": "Forfaits additionnels", "addonTitle": "Forfaits supplémentaires selon vos besoins", "addonDescription": "Renforcez votre forfait actuel, accédez à plus d'opportunités", "footer": {"title": "E-exportcity ile yeni nesil ticaret platformunda ağını büyüt!", "description": "E-exportcity, Türkiye'nin önde gelen dijital ihracat platformu olarak, işletmelerin global pazarlara açılmasını kolaylaştırıyor. Gelişmiş teknoloji altyapımız, uzman ihracat danışmanlarımız ve kapsamlı hizmet ağımızla, ihracatçılarımızın uluslararası ticaretteki rekabet gücünü artırıyoruz. Siz de e-exportcity ailesine katılarak, dijital dünyanın sunduğu sınırsız fırsatlardan yararlanın ve işletmenizi global arenada büyütün.", "features": {"global": "Global Pazarlara Erişim", "experts": "İhracat Danışmanları", "support": "7/24 Müşteri Desteği", "technology": "Gelişmiş Teknoloji Altyapısı"}}}, "titles": {"choosePackage": "Choisir un forfait", "upgradePackage": "Mettre à niveau votre forfait", "standardPackages": "Forfaits standards", "addonPackages": "Forfaits additionnels", "packageSelection": "Sélection de forfait", "allPackages": "Tous les forfaits"}, "filters": {"all": "Tous les forfaits", "standard": "Forfaits standards", "addon": "Forfaits additionnels", "allItems": "Tous les forfaits"}, "package": {"name": "{{name}}", "price": "{{price}}€", "type": {"standard": "Forfait standard", "addon": "Forfait additionnel"}, "features": {"viewRequests": {"title": "{{count}} demandes de consultation", "description": "Nombre de demandes que vous pouvez consulter"}, "createRequests": {"title": "{{count}} demandes de création", "description": "Nombre de demandes que vous pouvez créer"}, "emailNotification": {"title": "Notifications par email", "description": "Recevoir des notifications par email"}, "smsNotification": {"title": "Notifications SMS", "description": "Recevoir des notifications SMS"}, "messagingAllowed": {"title": "Messagerie", "description": "Envoyer et recevoir des messages"}, "yearEndSectorReport": {"title": "Rapport sectoriel de fin d'année", "description": "Accès à l'analyse détaillée du secteur"}, "homePageAd": {"title": "Publicité sur la page d'accueil", "description": "Afficher des publicités sur la page d'accueil"}, "languageIntroRights": {"title": "Support multilingue", "description": "Accès aux options multilingues"}}, "status": {"current": "Forfait actuel"}, "action": {"requiresStandard": "Forfait standard requis", "select": "<PERSON><PERSON> le forfait", "upgrade": "Mettre à niveau", "addToPackage": "Ajouter au forfait"}, "alreadyPurchased": "Vous avez déjà ce forfait", "cannotDowngrade": "Impossible de passer à un forfait inférieur", "tooltips": {"viewRequests": "Nombre de demandes que vous pouvez consulter", "createRequests": "Nombre de demandes que vous pouvez créer"}, "compare": "Comparer", "purchase": "Acheter maintenant", "perMonth": "/mois", "loading": "Chargement des forfaits..."}, "payment": {"modal": {"title": "Détails du paiement", "packageDetails": "Forfait: {{name}}"}, "options": {"useSavedCard": "Utiliser une carte enregistrée", "useNewCard": "Utiliser une nouvelle carte"}, "form": {"paymentMethod": "Méthode de paiement", "cardHolderName": "Nom du titulaire", "cardHolderNamePlaceholder": "<PERSON>", "cardNumber": "Numéro de carte", "cardNumberPlaceholder": "XXXX XXXX XXXX XXXX", "expiryMonth": "Mois d'expiration", "expiryYear": "Année d'expiration", "cvv": "CVV", "cvvPlaceholder": "123", "saveCard": "Enregistrer la carte pour les paiements futurs", "selectCard": "Sélectionner une carte enregistrée", "cardOption": "{{alias}} ({{type}}, **** {{lastFour}})", "cvc": "CVV", "cvcPlaceholder": "123", "cardAlias": "Alias de la carte", "cardAliasPlaceholder": "Entrez un nom pour cette carte", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "processing": "Traitement du paiement en cours...", "payButton": "Effectuer le paiement", "required": "<PERSON><PERSON> <PERSON><PERSON>", "invalidCardNumber": "Geçersiz kart numarası", "invalidCvc": "Geçersiz CVV kodu", "errors": {"selectCard": "Lütfen bir kart seçin.", "cardHolderNameRequired": "Kart sahibinin adı zorunludur.", "invalidCardNumber": "Lütfen geçerli bir kart numarası girin.", "expiryDateRequired": "<PERSON> kullanma tarihi z<PERSON>.", "cardExpired": "Kartın süresi dolmuş.", "invalidCVC": "Lütfen geçerli bir güvenlik kodu (CVV) girin.", "cardAliasRequired": "Lütfen kartınız için bir isim belirtin.", "cardSaveError": {"description": "Kart kaydedilemedi. Lütfen tekrar deneyin."}}}, "errors": {"selectPackage": "Veuillez sélectionner un forfait", "selectSavedCard": "Veuillez sélectionner une carte enregistrée", "userOrPackageNotFound": "Utilisateur ou forfait introuvable", "noActiveStandardPackage": "Impossible d'acheter un module complémentaire sans forfait standard actif"}, "messages": {"3dSecureInit": "Erreur d'initialisation 3D Secure", "3dSecureError": "Erreur de paiement 3D Secure", "error": "<PERSON><PERSON><PERSON> de paiement", "success": "Paiement réussi ! Vous avez maintenant accès aux fonctionnalités du forfait.", "addonSuccess": "Forfait additionnel acheté avec succès ! Vos fonctionnalités ont été mises à jour.", "standardSuccess": "Forfait acheté avec succès ! Vous avez maintenant accès à toutes les fonctionnalités.", "processing": "Traitement de votre paiement en cours...", "unexpectedError": "Une erreur inattendue s'est produite lors du traitement du paiement", "missingToken": "Jeton de validation de paiement manquant", "verificationFailed": "La vérification du paiement a échoué. Veuillez réessayer ou contacter le support", "paymentNotFound": "Enregistrement de paiement introuvable. Veuillez réessayer ou contacter le support", "failed": "Paiement échoué. Veuillez vérifier vos informations de carte et réessayer"}, "actions": {"close": "<PERSON><PERSON><PERSON>"}, "info": "Informations de paiement", "processing": "Traitement de votre paiement en cours...", "pay": "Payer", "payAmount": "Payer {{amount}}€", "details": {"title": "<PERSON>é<PERSON> du forfait"}, "initiationError": "Erreur lors de l'initialisation du paiement"}, "subscription": {"addonInfo": "Informations sur le forfait additionnel", "connectedToPackage": "Connecté au forfait", "remainingDays": "Jours restants", "days": "jours", "originalPrice": "Prix original", "proRatedPrice": "Prix au prorata", "currentValue": "Valeur du forfait actuel", "newPackagePrice": "Prix du nouveau forfait", "upgradePrice": "Prix de la mise à niveau", "currentPackage": "Forfait actuel", "noSubscription": "Pas d'abonnement", "noSubscriptionMessage": "Vous n'avez actuellement aucun abonnement actif"}, "upgradeDetails": {"title": "<PERSON><PERSON><PERSON> de la mise à niveau", "description": "Choisissez le forfait le plus adapté à vos besoins", "loading": "Chargement des forfaits...", "error": "<PERSON><PERSON><PERSON>", "fetchError": "Erreur lors du chargement des forfaits", "package": {"notAvailable": "<PERSON><PERSON><PERSON>", "cannotDowngrade": "Daha düşük bir pakete geçiş yapamazsınız", "requiresStandard": "<PERSON><PERSON>", "select": "Paketi Seç", "upgrade": "Paketi Yükselt", "addToPackage": "<PERSON><PERSON>", "alreadyPurchased": "Bu pakete zaten sa<PERSON>iniz", "features": "<PERSON><PERSON><PERSON><PERSON>", "current": "Mev<PERSON>"}, "payment": {"modal": {"title": "Détails du paiement", "packageDetails": "Forfait: {{name}}"}, "options": {"useSavedCard": "Utiliser une carte enregistrée", "useNewCard": "Utiliser une nouvelle carte"}, "form": {"cardHolderName": "Nom du titulaire", "cardHolderNamePlaceholder": "<PERSON>", "cardNumber": "Numéro de carte", "cardNumberPlaceholder": "XXXX XXXX XXXX XXXX", "expiryMonth": "Mois d'expiration", "expiryYear": "Année d'expiration", "cvv": "CVV", "cvvPlaceholder": "123", "saveCard": "Enregistrer la carte pour les paiements futurs", "selectCard": "Sélectionner une carte enregistrée", "cardOption": "{{alias}} ({{type}}, **** {{lastFour}})"}, "processing": "Traitement du paiement...", "pay": "Payer", "payAmount": "Payer {{amount}}€"}, "subscription": {"currentPackage": "Forfait actuel", "addonInfo": "Informations sur le forfait additionnel", "connectedToPackage": "Connecté au forfait", "remainingDays": "Jours restants", "days": "jours", "originalPrice": "Prix original", "proRatedPrice": "Prix au prorata", "currentValue": "Valeur du forfait actuel", "newPackagePrice": "Prix du nouveau forfait", "upgradePrice": "Prix de la mise à niveau"}}, "designPackages": {"badge": "Profesyonel Tasarım <PERSON>i", "title": "Markanızı Öne Çıkaracak Tasarım Paketleri", "subtitle": "İhracat yolculuğunuzda markanızı global pazarda güçlendirecek profesyonel tasarım çözümleriyle yanınızdayız.", "mostPopular": "En Çok Tercih Edilen", "oneTime": "/tek seferlik", "purchase": "Satın Al", "notes": "Notlar", "notesTitle": "Notlar", "buyNow": "Satın Al", "notesPlaceholder": "Projenizle ilgili özel isteklerinizi belirtebilirsiniz", "whyUs": {"title": "Neden Bizimle Çalışmalısınız?", "subtitle": "Profesyonel tasarım ekibimiz ve deneyimimizle markanızı bir adım öne taşıyoruz"}, "features": {"original": {"title": "<PERSON>zg<PERSON><PERSON>", "description": "Her marka i<PERSON>in <PERSON> o<PERSON>, <PERSON>zgün ve akılda kalıcı çözümler"}, "fast": {"title": "Hızlı Teslimat", "description": "Projeleriniz için özenle belirlenen zaman planına uygun teslimat"}, "source": {"title": "<PERSON><PERSON><PERSON>", "description": "Tüm tasarımların kaynak dosyaları ile birlikte teslimat"}, "responsive": {"title": "Responsive Tasarım", "description": "Tüm cihazlarda kusursuz görüntülenen modern tasarımlar"}}}, "system": {"error": "<PERSON><PERSON>ur système", "backToDashboard": "Retour au tableau de bord", "backToPackages": "Retour aux forfaits"}, "error": {"fetchPackages": "Paketler yüklenirken hata o<PERSON>ş<PERSON>", "tryAgain": "<PERSON><PERSON><PERSON><PERSON> daha sonra tekrar deneyin"}, "fetchError": "Échec de récupération des détails d'abonnement", "fetchPackagesError": "Échec de récupération des forfaits", "paymentSuccess": "<PERSON><PERSON><PERSON>", "paymentFailed": "Échec du paiement", "loading": "Chargement des forfaits...", "checkSubscriptionError": "Erreur lors de la vérification du statut de l'abonnement"}