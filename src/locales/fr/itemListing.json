{"selectTypeAndMode": "Sélectionner le type et le mode", "search": {"placeholder": "Rechercher des produits ou services..."}, "sort": {"newest": "Plus récents d'abord", "oldest": "Plus anciens d'abord", "mostViewed": "Les plus consultés", "leastViewed": "Les moins consultés"}, "filters": {"button": "Filtres"}, "clearFilters": "Effacer les filtres", "all": "<PERSON>ut", "location": {"unknown": "<PERSON><PERSON> inconnu", "label": "<PERSON><PERSON>"}, "mode": "Mode", "store": "Ma<PERSON><PERSON>", "categories": "Catégories", "page": {"description": "Produits et services tendance dans le commerce d'exportation mondial"}, "description": "Description", "addProduct": "Ajouter un produit", "addService": "Ajouter un service", "requestProduct": "Demander un produit", "requestService": "Demander un service", "addProductDesc": "<PERSON><PERSON>er une nouvelle liste de produits à vendre", "addServiceDesc": "Créer une nouvelle liste de services à offrir", "requestProductDesc": "Demander un produit que vous recherchez", "requestServiceDesc": "Demander un service dont vous avez besoin", "basicInfo": "Informations de base", "title": "Produits et Services", "createItemRequest": "<PERSON><PERSON><PERSON> une demande d'article", "editItemRequest": "Modifier la demande d'article", "editItem": "Modifier l'article", "name": "Nom", "enterName": "Entrez le nom", "type": "Type", "product": "Produit", "service": "Service", "category": "<PERSON><PERSON><PERSON><PERSON>", "selectCategory": "Sélectionner une catégorie", "listingType": "Type d'annonce", "sale": "Vente", "demand": "<PERSON><PERSON><PERSON>", "enterDescription": "Entrez la description", "htmlDescription": "Description HTML", "images": "Images", "uploadImages": "Télécharger des images", "image": "Image", "preview": "<PERSON><PERSON><PERSON><PERSON>", "removeImage": "Supprimer l'image", "noImagesUploaded": "Aucune image téléchargée", "cancel": "Annuler", "update": "Mettre à jour", "create": "<PERSON><PERSON><PERSON>", "updating": "Mise à jour en cours...", "creating": "Création en cours...", "errorFetchingCategories": "Erreur lors de la récupération des catégories", "pleaseCompleteAllFields": "Veuillez remplir tous les champs obligatoires", "errorUploadingFiles": "Erreur lors du téléchargement des fichiers", "successMessages": {"itemRequestUpdated": "Demande d'article mise à jour avec succès", "itemRequestCreated": "Demande d'article créée avec succès"}, "errorSavingItemRequest": "Erreur lors de l'enregistrement de la demande d'article", "placeholders": {"productName": "Entrez le nom du produit", "serviceName": "Entrez le nom du service", "productDescription": "Décrivez votre produit en détail", "serviceDescription": "Décrivez votre service en détail", "productRequestDescription": "Dé<PERSON><PERSON>z le produit que vous recherchez", "serviceRequestDescription": "Décrivez le service dont vous avez besoin", "requestTitle": "Entrez le titre de la demande"}, "categoryLevels": {"main": "Principal", "level": "Niveau {{level}}", "select": "Sélectionner la catégorie {{level}}"}, "types": {"product": "Produit", "service": "Service"}, "item": {"price": "${{price}}", "category": "<PERSON><PERSON><PERSON><PERSON>", "viewDetails": "Voir les détails", "noItems": "Aucun article trouvé"}, "errors": {"loadData": {"title": "Erreur de Chargement", "description": "Échec du chargement des articles. Veuillez réessayer plus tard."}, "loadCategoryItems": {"title": "Erreur de Chargement", "description": "Échec du chargement des articles de la catégorie. Veuillez réessayer plus tard."}, "loadSubcategories": {"title": "Erreur de Chargement", "description": "Échec du chargement des sous-catégories. Veuillez réessayer plus tard."}, "noCreateRequests": {"title": "<PERSON><PERSON><PERSON>", "description": "Vous n'avez plus de demandes de création disponibles. Veuillez mettre à niveau votre forfait."}, "upgradeRequired": {"title": "Mise à Niveau Requise", "description": "Veuillez mettre à niveau votre forfait pour créer plus d'articles."}, "createFailed": {"title": "Erreur de Création", "description": "Échec de la création de l'article. Veuillez réessayer plus tard."}, "updateFailed": {"title": "<PERSON><PERSON><PERSON> <PERSON> Mise à Jour", "description": "Échec de la mise à jour de l'article. Veuillez réessayer plus tard."}, "deleteFailed": {"title": "<PERSON><PERSON><PERSON> de Suppression", "description": "Échec de la suppression de l'article. Veuillez réessayer plus tard."}, "adCreateFailed": {"title": "Erreur de Création d'Annonce", "description": "Échec de la création de l'annonce. Veuillez réessayer plus tard."}, "tooManyFiles": {"title": "Trop de Fichiers", "description": "Vous avez dépassé le nombre maximum de fichiers autorisés. Veuillez sélectionner moins de fichiers."}, "maxFiveFiles": {"title": "<PERSON><PERSON> Dépass<PERSON>", "description": "Maximum 5 fichiers autorisés."}, "validation": {"title": "Erreur de Validation", "description": "<PERSON><PERSON><PERSON><PERSON> corriger les erreurs dans le formulaire et réessayer."}, "categoryRequired": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Veuillez sélectionner une catégorie."}, "subcategoryLoad": {"title": "Erreur de Chargement", "description": "Échec du chargement des sous-catégories. Veuillez réessayer plus tard."}, "categoryLoad": {"title": "Erreur de Chargement", "description": "Échec du chargement des catégories. Veuillez réessayer plus tard."}, "tryAgain": {"title": "<PERSON><PERSON><PERSON>", "description": "Veuillez réessayer."}, "imageUpload": {"title": "E<PERSON>ur de Téléchargement d'Image", "description": "Échec du téléchargement des images. Veuillez réessayer plus tard."}, "missingImage": {"title": "Image Requise", "description": "Veuillez télécharger au moins une image."}, "pleaseSelectImage": {"title": "Image Requise", "description": "Veuillez sélectionner une image pour l'annonce."}, "itemLoadFailed": "Impossible de charger les données de l'article", "itemSaveFailed": "Impossible d'enregistrer les données de l'article"}, "success": {"itemCreated": "Article créé avec succès", "itemUpdated": "Article mis à jour avec succès", "itemDeleted": "Article supprimé avec succès", "adCreated": {"title": "Su<PERSON>ès", "description": "<PERSON><PERSON><PERSON> c<PERSON> avec succès."}}, "buttons": {"createItem": "Créer un nouvel article", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "updateItem": "Mettre à jour l'article", "publishProduct": "Publier le produit", "publishService": "Publier le service", "publishRequest": "Publier la demande", "uploadImages": "Télécharger des images", "save": "Enregistrer", "cancel": "Annuler", "confirm": "Confirmer", "promote": "Promouvoir", "upgrade": "Voir les forfaits", "message": "Message"}, "fields": {"name": "Nom", "description": "Description", "productName": "Nom du produit", "serviceName": "Nom du service", "productDescription": "Description du produit", "serviceDescription": "Description du service", "requestDetails": "<PERSON><PERSON><PERSON> de la demande", "requestTitle": "<PERSON><PERSON><PERSON> de la demande", "price": "Prix", "images": "Images", "imageHelp": "", "category": "<PERSON><PERSON><PERSON><PERSON>", "adTitle": "Titre de l'annonce", "adTitleHelper": "Entrez un titre accrocheur pour votre annonce", "adImage": "Image de l'annonce", "adImageHelper": "L'image doit être inférieure à 2 Mo et au format JPG, PNG ou GIF"}, "modals": {"create": {"title": "Créer un nouvel article", "success": "Article créé avec succès"}, "edit": {"title": "Modifier l'article", "success": "Article mis à jour avec succès"}, "message": {"title": "Envoyer un message"}}, "dialogs": {"delete": {"title": "Supprimer l'article", "message": "Êtes-vous sûr de vouloir supprimer cet article ? Cette action ne peut pas être annulée.", "success": "Article supprimé avec succès"}, "viewRequest": {"title": "Utiliser une demande de visualisation", "message": "Voulez-vous utiliser l'une de vos demandes de visualisation pour voir cet article ?", "confirm": "Oui, voir l'article", "cancel": "Non, annuler", "remaining": "restant"}, "homeAd": {"title": "C<PERSON>er une publicité sur la page d'accueil", "message": "Voulez-vous promouvoir cet article sur la page d'accueil pendant {{duration}} jours ?", "success": "Publicité créée avec succès"}, "upgrade": {"title": "Mettre à niveau votre forfait", "message": "Promouvoir vos articles sur la page d'accueil et atteindre plus de clients en passant à un forfait avec la fonction de publicité sur la page d'accueil !", "benefits": {"title": "Avantages de la publicité sur la page d'accueil :", "visibility": "Visibilité accrue pour vos produits", "traffic": "Plus de trafic vers vos annonces", "sales": "Plus de chances de réaliser des ventes"}}}, "seeDetails": "Voir les détails", "myItemsTitle": "Mes articles", "products": "Produits", "services": "Services", "allCategories": "Toutes les catégories", "pendingRequests": "Demandes en attente", "approvedItems": "Articles approuvés", "cannotEditApprovedItem": "Impossible de modifier un article approuvé", "itemRequestDeleted": "Demande d'article supprimée", "itemDeleted": "Article supprimé", "errorDeletingItemRequest": "Erreur lors de la suppression de la demande d'article", "deleteItemRequestConfirmation": "Êtes-vous sûr de vouloir supprimer cette demande d'article ?", "selectType": "Sélectionner le type", "mainCategory": "Catégorie principale", "subCategory": "Sous-caté<PERSON>ie {{level}}", "noItemsFound": "Aucun article trouvé", "loading": "Chargement...", "errorFetchingItems": "Erreur lors de la récupération des articles", "createNewRequest": "<PERSON><PERSON><PERSON> une nouvelle demande", "deleteItemRequest": "Supprimer la demande d'article", "whatsapp": {"contact": "Contacter via WhatsApp", "message": "<PERSON><PERSON><PERSON>, je suis intéressé par votre article: {{itemName}}"}, "language": {"english": "<PERSON><PERSON><PERSON>", "turkish": "<PERSON><PERSON>", "german": "Allemand", "select": "Sélectionner la langue"}}