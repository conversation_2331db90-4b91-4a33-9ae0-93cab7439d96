{"notFound": "Unternehmen nicht gefunden", "notFoundDesc": "Das gesuchte Unternehmen existiert nicht.", "products": "Produkte", "noProducts": "<PERSON>ine Produkte gefunden", "details": {"title": "Unternehmensdetails", "description": "Unternehmensbeschreibung", "contact": "Kontaktinformationen", "address": "Unternehmensadresse", "items": "Unternehmensprodukte", "noItems": "<PERSON>ine Produkte für dieses Unternehmen gefunden"}, "contact": "Kontaktinformationen", "fetchFailed": {"title": "Datenabruf feh<PERSON>schlagen", "description": "Unternehmensdaten konnten nicht abgerufen werden. Bitte versuchen Sie es später erneut."}, "title": "Unternehmen", "description": "Ihre vertrauenswürdigen Geschäftspartner im globalen Handel", "viewStore": "Unternehmensprofil anzeigen", "viewProfile": "Unternehmensprofil anzeigen", "noCoverImage": "<PERSON><PERSON>", "storeOwner": "{{firstName}} {{lastName}}", "coverImage": "{{storeName}} Titelbild", "storeProducts": "Unternehmensprodukte", "productImage": "{{productName}} Produktbild", "itemTypes": {"product": "Produkt", "service": "Dienstleistung"}, "storeType": "Unternehmenstyp", "types": {"company": "Unternehmen", "broker": "Makler"}, "storeCard": {"noLocation": "Standort nicht angegeben", "noDescription": "Keine Beschreibung verfügbar", "views": "Auf<PERSON><PERSON>", "viewProfile": "<PERSON><PERSON> anzeigen"}, "search": {"placeholder": "Unternehmen suchen..."}, "filters": {"button": "Filter", "sectorLabel": "Se<PERSON><PERSON>", "locationLabel": "<PERSON><PERSON>", "clear": "<PERSON><PERSON>", "sectors": {"all": "Alle Sektoren", "technology": "Technologie", "trade": "<PERSON>", "health": "Gesundheit", "construction": "Bau", "agriculture": "Landwirtschaft"}, "locations": {"all": "<PERSON><PERSON> Standorte", "istanbul": "Istanbul", "izmir": "Izmir", "ankara": "Ankara", "bursa": "Bursa", "antalya": "<PERSON><PERSON><PERSON>"}, "sort": {"newest": "Neueste", "oldest": "Älteste", "mostViewed": "Meistgesehen", "leastViewed": "Wenigstgesehen"}}, "noStores": {"title": "<PERSON><PERSON> gefunden", "message": "<PERSON><PERSON>, die Ihren Suchkriterien entsprechen. Versuchen Sie, <PERSON><PERSON><PERSON> <PERSON>lter oder Suchbegriffe anzupassen."}, "errors": {"title": "Fehler beim Laden der Unternehmen", "fetchFailed": "Unternehmen konnten nicht geladen werden. Bitte versuchen Si<PERSON> es später noch einmal.", "retry": "<PERSON><PERSON><PERSON> versuchen", "storeNotFound": {"title": "Unternehmen nicht gefunden", "description": "Das gesuchte Unternehmen konnte nicht gefunden werden oder wurde möglicherweise gelöscht."}}, "list": {"title": "Unternehmen", "noStores": "<PERSON><PERSON> gefunden", "search": "<PERSON><PERSON><PERSON><PERSON> suchen", "filter": "Unternehmen filtern"}}