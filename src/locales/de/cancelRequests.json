{"title": "Antrag auf Abonnement-Kündigung", "reason": "Kündigungsgrund", "submit": "<PERSON><PERSON><PERSON> e<PERSON>ichen", "cancel": "Abbrechen", "success": {"title": "Erfolg", "description": "Kündigungsantrag erfolgreich eingereicht"}, "error": {"title": "<PERSON><PERSON>", "description": "Fehler beim Einreichen des Kündigungsantrags. Bitte versuchen Sie es später erneut."}, "reasonPlaceholder": "Bitte erklären <PERSON>, warum Sie Ihr Abonnement kündigen möchten", "confirmationTitle": "Kündigungsantrag bestätigen", "confirmationMessage": "Sind <PERSON> sic<PERSON>, dass Sie die Kündigung Ihres Abonnements beantragen möchten? Dieser Antrag wird von unserem Administrationsteam geprüft.", "revertRequest": "Kündigungsantrag zurückziehen", "revertSuccess": {"title": "Erfolg", "description": "Kündigungsantrag erfolgreich zurückgezogen"}, "revertError": {"title": "<PERSON><PERSON>", "description": "Fehler beim Zurückziehen des Kündigungsantrags. Bitte versuchen Sie es später erneut."}, "status": {"pending": "In Bearbeitung", "approved": "<PERSON><PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reverted": "Vom Benutzer zurückgezogen"}, "admin": {"title": "Kündigungsanträge für Abonnements", "noRequests": "<PERSON><PERSON>gungsanträge gefunden", "approve": "<PERSON><PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON><PERSON>", "requestDate": "Antragsdatum", "status": "Status", "user": "<PERSON><PERSON><PERSON>", "package": "<PERSON><PERSON>", "actions": "Aktionen", "viewReason": "<PERSON><PERSON><PERSON> anzeigen"}}