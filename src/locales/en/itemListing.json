{"selectTypeAndMode": "Select Type and Mode", "search": {"placeholder": "Search products or services..."}, "sort": {"newest": "Newest First", "oldest": "Oldest First", "mostViewed": "Most Viewed", "leastViewed": "Least Viewed"}, "filters": {"button": "Filters"}, "clearFilters": "Clear Filters", "all": "All", "location": {"unknown": "Unknown location", "label": "Location"}, "mode": "Mode", "store": "Store", "categories": "Categories", "page": {"description": "Global trending products and services in export trade"}, "description": "Description", "addProduct": "Add Product", "addService": "Add Service", "requestProduct": "Request Product", "requestService": "Request Service", "addProductDesc": "Create a new product listing to sell", "addServiceDesc": "Create a new service listing to offer", "requestProductDesc": "Request a product you're looking for", "requestServiceDesc": "Request a service you need", "basicInfo": "Basic Information", "title": "Products and Services", "createItemRequest": "Create Item Request", "editItemRequest": "Edit Item Request", "editItem": "<PERSON>em", "name": "Name", "enterName": "Enter name", "type": "Type", "product": "Product", "service": "Service", "category": "Category", "selectCategory": "Select category", "listingType": "Listing Type", "sale": "Sale", "demand": "Demand", "enterDescription": "Enter description", "htmlDescription": "HTML Description", "images": "Images", "uploadImages": "Upload Images", "image": "Image", "preview": "Preview", "removeImage": "Remove Image", "noImagesUploaded": "No images uploaded", "cancel": "Cancel", "update": "Update", "create": "Create", "updating": "Updating...", "creating": "Creating...", "errorFetchingCategories": "Error fetching categories", "pleaseCompleteAllFields": "Please complete all required fields", "errorUploadingFiles": "Error uploading files", "placeholders": {"productName": "Enter product name", "serviceName": "Enter service name", "productDescription": "Describe your product in detail", "serviceDescription": "Describe your service in detail", "productRequestDescription": "Describe the product you're looking for", "serviceRequestDescription": "Describe the service you need", "requestTitle": "Enter request title"}, "successMessages": {"itemRequestUpdated": "Item request updated successfully", "itemRequestCreated": "Item request created successfully"}, "errorSavingItemRequest": "Error saving item request", "categoryLevels": {"main": "Main", "level": "Level {{level}}", "select": "Select category {{level}}"}, "types": {"product": "Product", "service": "Service"}, "item": {"price": "${{price}}", "category": "Category", "viewDetails": "View details", "noItems": "No items found"}, "errors": {"loadData": {"title": "Loading Error", "description": "Failed to load items. Please try again later."}, "loadCategoryItems": {"title": "Loading Error", "description": "Failed to load category items. Please try again later."}, "loadSubcategories": {"title": "Loading Error", "description": "Failed to load subcategories. Please try again later."}, "noCreateRequests": {"title": "No Requests Left", "description": "You have no create requests left. Please upgrade your plan."}, "upgradeRequired": {"title": "Upgrade Required", "description": "Please upgrade your plan to create more items."}, "createFailed": {"title": "Creation Error", "description": "Failed to create item. Please try again later."}, "updateFailed": {"title": "Update Error", "description": "Failed to update item. Please try again later."}, "deleteFailed": {"title": "Deletion Error", "description": "Failed to delete item. Please try again later."}, "adCreateFailed": {"title": "Ad Creation Error", "description": "Failed to create ad. Please try again later."}, "tooManyFiles": {"title": "Too Many Files", "description": "You have exceeded the maximum number of files allowed. Please select fewer files."}, "maxFiveFiles": {"title": "File Limit Exceeded", "description": "Maximum 5 files allowed."}, "validation": {"title": "Validation Error", "description": "Please fix the errors in the form and try again."}, "categoryRequired": {"title": "Category Required", "description": "Please select a category."}, "subcategoryLoad": {"title": "Loading Error", "description": "Failed to load subcategories. Please try again later."}, "categoryLoad": {"title": "Loading Error", "description": "Failed to load categories. Please try again later."}, "tryAgain": {"title": "Error", "description": "Please try again."}, "imageUpload": {"title": "Image Upload Error", "description": "Failed to upload images. Please try again later."}, "missingImage": {"title": "Image Required", "description": "Please upload at least one image."}, "pleaseSelectImage": {"title": "Image Required", "description": "Please select an image for the ad."}, "itemLoadFailed": "Failed to load item data", "itemSaveFailed": "Failed to save item data"}, "success": {"itemCreated": "Item successfully created", "itemUpdated": "Item successfully updated", "itemDeleted": "Item successfully deleted", "adCreated": {"title": "Success", "description": "Ad successfully created."}}, "buttons": {"createItem": "Create new item", "edit": "Edit", "delete": "Delete", "create": "Create", "updateItem": "Update Item", "publishProduct": "Publish Product", "publishService": "Publish Service", "publishRequest": "Publish Request", "uploadImages": "Upload Images", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "promote": "Promote", "upgrade": "View plans", "message": "Message", "viewDetails": "View Details"}, "fields": {"name": "Name", "description": "Description", "productName": "Product Name", "serviceName": "Service Name", "productDescription": "Product Description", "serviceDescription": "Service Description", "requestDetails": "Request Details", "requestTitle": "Request Title", "price": "Price", "images": "Images", "imageHelp": "Upload up to 5 images. The first image will be used as the main image.", "category": "Category", "adTitle": "Advertisement Title", "adTitleHelper": "Enter a catchy title for your advertisement", "adImage": "Advertisement Image", "adImageHelper": "Image must be less than 2MB and in JPG, PNG or GIF format"}, "modals": {"create": {"title": "Create new item", "success": "Item created successfully"}, "edit": {"title": "Edit item", "success": "Item updated successfully"}, "message": {"title": "Send message"}}, "dialogs": {"delete": {"title": "Delete item", "message": "Are you sure you want to delete this item? This action cannot be undone.", "success": "Item deleted successfully"}, "viewRequest": {"title": "Use view request", "message": "Would you like to use a view request to see this item?", "confirm": "Yes, view item", "cancel": "No, cancel", "remaining": "Remaining"}, "homeAd": {"title": "Create home ad", "message": "Would you like to promote this item on the home page for {{duration}} days?", "success": "Ad created successfully"}, "upgrade": {"title": "Upgrade your plan", "message": "Upgrade to a plan with the home ad feature to promote your items on the home page and reach more customers!", "benefits": {"title": "Benefits of home ads:", "visibility": "Increased product visibility", "traffic": "More traffic to your listings", "sales": "Greater chance of making sales"}}}, "seeDetails": "See Details", "myItemsTitle": "My Items", "products": "Products", "services": "Services", "allCategories": "All Categories", "pendingRequests": "Pending Requests", "approvedItems": "Approved Items", "cannotEditApprovedItem": "Cannot edit approved item", "itemRequestDeleted": "Item request deleted", "itemDeleted": "Item deleted", "errorDeletingItemRequest": "Error deleting item request", "deleteItemRequestConfirmation": "Are you sure you want to delete this item request?", "selectType": "Select type", "mainCategory": "Main category", "subCategory": "Subcategory {{level}}", "noItemsFound": "No items found", "loading": "Loading...", "errorFetchingItems": "Error fetching items", "createNewRequest": "Create New Request", "deleteItemRequest": "Delete Item Request", "whatsapp": {"contact": "Contact via WhatsApp", "message": "Hello, I'm interested in your item: {{itemName}}"}, "language": {"english": "English", "turkish": "Turkish", "german": "German", "select": "Select language"}}