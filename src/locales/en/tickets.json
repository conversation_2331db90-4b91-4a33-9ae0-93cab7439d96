{"title": "Support Tickets", "create_ticket": "Create New Ticket", "noTickets": "No tickets found", "table": {"id": "ID", "title": "Title", "type": "Type", "category": "Category", "amount": "Amount", "status": "Status", "created_at": "Created At", "actions": "Actions"}, "actions": {"view": "View"}, "form": {"title": "Title", "title_placeholder": "Enter ticket title", "type": "Type", "select_type": "Select Type", "type_product": "Product", "type_service": "Service", "category": "Category", "select_category": "Select Category", "subcategory": "Subcategory (Optional)", "select_subcategory": "Select Subcategory", "country": "Country", "select_country": "Select Country", "city": "City", "select_city": "Select City", "amount": "Amount", "amount_placeholder": "Enter amount", "description": "Description", "description_placeholder": "Enter your description", "images": "Images", "upload_images": "Upload Images", "max_files": "Maximum {{count}} files allowed", "max_size": "Maximum file size: {{size}}MB", "allowed_types": "Allowed file types: {{types}}", "status": "Status", "submit": "Submit", "submitting": "Submitting...", "create_header": "Create New Ticket", "update_header": "Edit Ticket", "cancel": "Cancel", "product": "Product", "service": "Service"}, "validation": {"title_required": "Title is required", "type_required": "Type is required", "category_required": "Category is required", "country_required": "Country is required", "city_required": "City is required", "amount_required": "Amount is required", "amount_min": "Amount must be greater than 0", "description_required": "Description is required", "description_min_length": "Description must be at least 20 characters", "file_required": "At least one image is required", "file_type": "Invalid file type. Only image files are allowed", "file_size": "File size exceeds {{size}}MB limit", "max_files": "Maximum {{count}} files allowed", "required_fields": "Please fill in all required fields"}, "messages": {"create_success": {"title": "Ticket Created", "description": "Ticket created successfully"}, "create_error": {"title": "Creation Failed", "description": "Failed to create ticket"}, "update_success": {"title": "Ticket Updated", "description": "Ticket updated successfully"}, "update_error": {"title": "Update Failed", "description": "Failed to update ticket"}, "fetch_error": {"title": "Fetch Failed", "description": "Failed to fetch tickets"}, "response_added": {"title": "Response Added", "description": "Response added successfully"}, "response_error": {"title": "Response Failed", "description": "Failed to add response"}, "file_upload_error": {"title": "Upload Failed", "description": "Failed to upload file(s)"}}, "status": {"pending": "Pending", "in_progress": "In Progress", "resolved": "Resolved", "closed": "Closed"}, "types": {"product": "Product", "service": "Service"}, "responses": "Responses", "add_response": "Add Response", "response_placeholder": "Write your response here...", "admin": "Admin", "you": "You", "user": "User", "send": "Send", "cancellation_request": "Subscription Cancellation Request", "cancellation_ticket_created": "Your cancellation request has been submitted as a ticket."}