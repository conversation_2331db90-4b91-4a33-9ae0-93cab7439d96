{"tabs": {"profile": "Profile", "subscription": "Subscription", "store": "Company", "cards": "Cards", "support_tickets": "Support Tickets", "homepage_ads": "Homepage Ads"}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "birthDate": "Birth Date", "phoneNumber": "Phone Number", "country": "Country", "city": "City", "identityNumber": "Identity Number", "address": "Address", "zipCode": "ZIP Code", "password": "Password", "passwordPlaceholder": "Leave blank to keep current password", "company": "Company", "position": "Position", "updateProfile": "Update Profile", "changePassword": "Change Password", "category": "Main Category", "selectCategory": "Select Main Category", "subCategory": "Parent Category", "selectSubCategory": "Select Parent Category", "level3Category": "Category", "selectLevel3Category": "Select Category", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "save": "Save", "cancel": "Cancel", "selectCountry": "Select Country", "selectCity": "Select City"}, "subscription": {"currentPackage": "Current Package", "mainPackage": "Main Package", "addonPackage": "Addon Package", "addons": "Addon Subscriptions", "active": "Active", "inactive": "Inactive", "packageDetails": "Package Details", "price": "Price", "validUntil": "<PERSON>id <PERSON>", "usageStatus": "Usage Status", "viewRequests": "View Requests", "createRequests": "Create Requests", "total": "Total", "used": "Used", "remaining": "Remaining", "featuresTitle": "Features", "featuresList": {"unlimited_messaging": "Unlimited Messaging", "unlimitedMessaging": "Unlimited Messaging", "sms_notifications": "SMS Notifications", "smsNotifications": "SMS Notifications", "email_notifications": "Email Notifications", "emailNotifications": "Email Notifications", "homepage_ad": "Homepage Advertisement", "homepageAd": "Homepage Advertisement", "sector_report": "Year-End Sector Report", "sectorReport": "Year-End Sector Report", "language_intro_rights": "Language Introduction Rights", "languageIntroRights": "Language Introduction Rights", "messagingAllowed": "Messaging Allowed"}, "additionalBenefits": "Additional Benefits", "benefits": {"homepageAd": "{{duration}} Days Homepage Advertisement", "languageIntro": "{{count}} Language Introduction Rights", "yearEndReport": "Year-End Sector Report"}, "noActivePackage": "No active subscription package", "standardPackageInfo": "Standard package information", "originalPrice": "Original Price", "proRatedPrice": "Pro-rated Price", "upgradeRequired": "Upgrade Required", "upgradeMessage": "You need to upgrade your package to use this feature"}, "stores": {"title": "Companies", "description": "Manage your companies and view their performance", "myStores": "My Companies", "createStore": "Create Company", "viewStore": "View Company", "noCoverImage": "No Cover Image", "noStores": "You don't have any companies yet", "storeDetails": "Company Details", "storePerformance": "Company Performance", "visitCount": "Visit Count", "messageCount": "Message Count", "viewCount": "View Count"}, "notifications": {"updateSuccess": {"title": "Profile Updated", "description": "Profile updated successfully"}, "updateError": {"title": "Update Failed", "description": "Failed to update profile"}, "passwordSuccess": {"title": "Password Updated", "description": "Password updated successfully"}, "passwordError": {"title": "Update Failed", "description": "Failed to update password"}, "passwordMismatch": {"title": "Password Mismatch", "description": "Passwords do not match"}}, "validation": {"required": "This field is required", "invalidEmail": "Please enter a valid email address", "invalidPhone": "Please enter a valid phone number", "passwordLength": "Password must be at least 6 characters long", "passwordMatch": "Passwords do not match", "invalidIdentity": "Please enter a valid identity number"}, "success": {"profileUpdated": "Profile Updated", "profileUpdatedDesc": "Your profile has been updated successfully", "passwordChanged": "Password Changed", "passwordChangedDesc": "Your password has been changed successfully"}, "errors": {"failedToLoadProfile": {"title": "Failed to Load Profile", "description": "Please try again later"}, "failedToUpdateProfile": {"title": "Failed to Update Profile", "description": "Please check your information and try again"}, "fetchSubCategoriesFailed": {"title": "Loading Error", "description": "Failed to fetch subcategories. Please try again later."}, "failedToChangePassword": {"title": "Failed to Change Password", "description": "Please check your password and try again"}, "currentPasswordIncorrect": {"title": "Invalid Password", "description": "Current password is incorrect. Please verify and try again."}}, "homepageAd": {"title": "Homepage Advertisements", "section": {"your_ads": "Your Advertisements"}, "notAvailable": "Not available", "table": {"image": "Image", "title": "Title", "clicks": "<PERSON>licks", "status": "Status", "created_at": "Created At", "expires_at": "Expires At", "rejection_reason": "Rejection Reason", "actions": "Actions"}, "status": {"pending": "Pending Review", "approved": "Active", "rejected": "Rejected", "expired": "Expired", "inactive": "Inactive", "active": "Active", "deleted": "Deleted"}, "actions": {"create": "Create New Advertisement", "reactivate": "Reactivate Advertisement"}, "form": {"title": "Advertisement Title", "title_placeholder": "Enter advertisement title...", "image": "Advertisement Image", "image_placeholder": "Upload image...", "submit": "Create Advertisement", "title_header": "Create Homepage Advertisement", "product": "Select Product (Optional)", "product_placeholder": "Select a product (optional)", "product_help": "Linking a product is optional but recommended", "image_help": "Maximum size: 5MB. Formats: JPG, PNG, GIF"}, "validation": {"title_required": "Title is required", "image_required": "Image is required", "image_size": "Image size must be less than 5MB", "image_type": "Only JPG, PNG and GIF images are allowed"}, "messages": {"create_success": {"title": "Ad Created", "description": "Advertisement created successfully"}, "create_error": {"title": "Creation Failed", "description": "Failed to create advertisement"}, "fetch_error": {"title": "Loading Error", "description": "Failed to fetch advertisements"}, "reactivate_success": {"title": "Ad Reactivated", "description": "Advertisement reactivated successfully"}, "reactivate_error": {"title": "Reactivation Failed", "description": "Failed to reactivate advertisement"}, "no_ads": {"title": "No Advertisements", "description": "You haven't created any advertisements yet"}, "delete_success": {"title": "Ad Deleted", "description": "Ad deleted successfully"}, "delete_error": {"title": "Deletion Failed", "description": "Failed to delete ad"}, "products_fetch_error": {"title": "Loading Error", "description": "Failed to load products"}}, "modal": {"delete_confirmation": "Delete Advertisement", "delete_warning": "Are you sure you want to delete this advertisement? This action cannot be undone.", "image_preview": "Ad Image Preview"}}, "referrals": {"title": "Your Referrals", "yourCode": "Your Referral Code", "shareUrl": "Share this URL", "totalReferrals": "Total Referrals", "activePackages": "Active Packages", "referralsList": "Referrals List", "name": "Name", "dateJoined": "Date Joined", "status": "Status", "activePackage": "Active Package", "noPackage": "No Package", "noReferralsYet": "You don't have any referrals yet", "email": "Email", "date": "Date"}}