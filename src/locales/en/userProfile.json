{"tabs": {"profile": "Profile", "items": "Items", "references": "References", "subscription": "Subscription", "store": "Company", "cards": "Cards", "support_tickets": "Support Tickets", "homepage_ads": "Homepage Ads"}, "profile": {"title": "User Profile", "personalInfo": "Personal Information", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "birthDate": "Birth Date", "phoneNumber": "Phone Number", "country": "Country", "city": "City", "identityNumber": "Identity Number", "address": "Address", "zipCode": "ZIP Code", "password": "Password", "passwordPlaceholder": "Leave blank to keep current password", "updateProfile": "Update Profile", "name": "Name", "role": "Role", "status": "Status"}, "subscription": {"currentPackage": "Current Package", "active": "Active", "cancelled": "Cancelled", "addon": "Addon Package", "activeAddons": "Active Addon Packages", "remainingDays": "Remaining Days", "days": "days", "designPackageOrders": "Design Package Orders", "endDate": "End Date", "remainingRequests": "Remaining Requests", "viewRequests": "View Requests", "createRequests": "Create Requests", "features": "Features", "deliveredFiles": "Delivered Files", "download": "Download", "deliveryDate": "Delivery Date", "noDesignPackageOrders": "No Design Package Orders", "noDesignPackageOrdersDescription": "You haven't ordered any design packages yet. Browse our design packages to get professional designs for your business.", "order": "Order", "notes": "Notes", "noActiveSubscription": "No active subscription", "noSubscription": "No Subscription Found", "noSubscriptionMessage": "You don't have any active subscription. Browse our packages to get started.", "browsePackages": "Browse Packages", "cancelButton": "Create Cancellation Request", "reactivateButton": "Reactivate Subscription", "cancelConfirmTitle": "Cancel Subscription", "cancelConfirmMessage": "Are you sure you want to cancel your subscription?", "cancelActiveUntil": "Your subscription will remain active until {{date}}", "cancelAddonWarning": "Note: Cancelling this subscription will also cancel all associated addon packages.", "cancelSuccess": "Subscription cancelled successfully", "cancelError": "Error cancelling subscription", "reactivateSuccess": "Subscription reactivated successfully", "reactivateError": "Error reactivating subscription", "fetchPackagesError": "Error fetching packages", "addonInfo": "Add-on Package Information", "connectedToPackage": "Connected to Package", "originalPrice": "Original Price", "proRatedPrice": "Pro-rated Price", "standardPackageInfo": "This is a standard package. Purchasing this will replace your current package."}, "features": {"emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "languageIntro": "Language Introduction Rights", "basicMessaging": "Basic Messaging", "unlimitedMessaging": "Unlimited Messaging", "homepageAd": "Homepage Advertisement", "sectorReport": "Sector Report", "additionalViews": "Additional View Requests", "additionalCreates": "Additional Create Requests"}, "store": {"createStore": "Create Company", "updateStore": "Update Company", "name": "Company Name", "description": "Company Description", "logo": "Company Logo", "coverImage": "Cover Image", "address": "Company Address", "phone": "Company Phone", "email": "Company Email", "website": "Company Website", "socialMedia": "Social Media Links", "type": "Company Type", "company": "Company", "broker": "Broker"}, "cards": {"savedCards": "Saved Cards", "addNewCard": "Add New Card", "noSavedCards": "No saved cards found", "unnamedCard": "Unnamed Card", "cardAlias": "<PERSON>", "cardAliasPlaceholder": "e.g. My Personal Card", "cardHolderName": "Card Holder Name", "cardHolderNamePlaceholder": "Name on Card", "cardNumber": "Card Number", "expireMonth": "Month", "expireYear": "Year", "cvc": "CVC", "addCard": "Add Card"}, "success": {"profileUpdated": "Profile Updated", "profileUpdatedDesc": "Your profile has been updated successfully", "storeCreated": "Store Created", "storeCreatedDesc": "Your store has been created successfully", "storeUpdated": "Store Updated", "storeUpdatedDesc": "Your store has been updated successfully", "cardAdded": "Card Added", "cardRemoved": "Card Removed", "defaultCardSet": "Default Card Set"}, "errors": {"failedToLoadProfile": {"title": "Failed to Load Profile", "description": "Please try again later"}, "failedToUpdateProfile": {"title": "Failed to Update Profile", "description": "Please check your information and try again"}, "failedToLoadStore": {"title": "Failed to Load Store", "description": "Please try again later"}, "failedToCreateStore": {"title": "Failed to Create Store", "description": "Please check your information and try again"}, "failedToUpdateStore": {"title": "Failed to Update Store", "description": "Please check your information and try again"}, "failedToLoadCards": {"title": "Failed to Load Cards", "description": "Could not load your saved cards. Please try again later"}, "failedToAddCard": {"title": "Failed to Add Card", "description": "Could not save your card. Please check the details and try again"}, "failedToRemoveCard": {"title": "Failed to Remove Card", "description": "Could not remove the card. Please try again later"}, "failedToSetDefaultCard": {"title": "Failed to Set Default Card", "description": "Could not set the default card. Please try again later"}}, "loading": "Loading...", "common": {"cancel": "Cancel", "confirm": "Confirm", "open_menu": "Open menu", "close": "Close", "navigation": "Navigation"}, "dashboard": {"welcome": "Welcome to Your Dashboard", "latest": "Latest Homepage Ads"}, "roles": {"user": "User", "admin": "Administrator", "representative": "Representative"}, "status": {"active": "Active", "inactive": "Inactive"}, "homepageAd": {"manage": "Manage Homepage Ads", "latest": "Your Latest Advertisements"}}