{"notFound": "Company not found", "notFoundDesc": "The company you are looking for does not exist.", "products": "Products", "noProducts": "No products found", "details": {"title": "Company Details", "description": "Company Description", "contact": "Contact Information", "address": "Company Address", "items": "Company Products", "noItems": "No products found for this company"}, "contact": {"sendMessage": "Send Message", "messageSent": "Message Sent", "messageDelivered": "Your message has been delivered to the company representatives.", "messageFailed": "Message Failed", "messageError": "An error occurred while sending your message. Please try again later.", "form": {"name": "Your Name", "namePlaceholder": "Enter your full name", "email": "Your Email", "emailPlaceholder": "Enter your email address", "subject": "Subject", "subjectPlaceholder": "Message subject", "message": "Message", "messagePlaceholder": "Enter your message here...", "send": "Send"}}, "fetchFailed": {"title": "Data Loading Failed", "description": "Failed to fetch company data. Please try again later."}, "title": "Companies", "description": "Your trusted business partners in global trade", "viewStore": "View Company Profile", "viewProfile": "View Company Profile", "noCoverImage": "No cover image", "storeOwner": "{{firstName}} {{lastName}}", "coverImage": "{{storeName}} cover image", "storeProducts": "Products and Services", "productImage": "{{productName}} product image", "itemTypes": {"product": "Product", "service": "Service"}, "storeType": "Company Type", "types": {"company": "Company", "broker": "Broker"}, "status": {"approved": "Approved", "pending": "Pending Approval"}, "stats": {"employees": "Employees", "founded": "Founded", "products": "Products", "visitors": "Visitors", "productViews": "Product Views", "storeViews": "Store Views"}, "categories": "Categories", "share": {"title": "Share", "copied": "<PERSON>d", "linkCopied": "Page link has been copied to clipboard"}, "common": {"cancel": "Cancel"}, "storeCard": {"noLocation": "Location not specified", "noDescription": "No description available", "views": "views", "viewProfile": "View Profile", "productViews": "Total product views"}, "search": {"placeholder": "Search companies..."}, "filters": {"button": "Filters", "sectorLabel": "Sector", "countryLabel": "Country", "cityLabel": "City", "locationLabel": "Location", "clear": "Clear Filters", "sectors": {"all": "All Sectors", "technology": "Technology", "trade": "Trade", "health": "Healthcare", "construction": "Construction", "agriculture": "Agriculture"}, "locations": {"all": "All Locations", "istanbul": "Istanbul", "izmir": "Izmir", "ankara": "Ankara", "bursa": "Bursa", "antalya": "<PERSON><PERSON><PERSON>"}, "cities": {"all": "All Cities"}, "sort": {"newest": "Newest", "oldest": "Oldest", "mostViewed": "Most Viewed", "leastViewed": "Least Viewed"}}, "noStores": {"title": "No Companies Found", "message": "No companies match your search criteria. Try adjusting your filters or search term."}, "errors": {"title": "Error Loading Companies", "fetchFailed": "Failed to load companies. Please try again later.", "retry": "Try Again", "storeNotFound": {"title": "Company Not Found", "description": "The company you are looking for cannot be found or may have been deleted."}, "accessDenied": {"title": "Access Denied", "description": "You do not have permission to view this company."}, "viewFailed": {"title": "View Failed", "description": "Failed to view company details. Please try again later."}, "noViewRequests": {"title": "No View Requests Remaining", "description": "You have no remaining view requests. Please upgrade your package."}, "upgradeRequired": {"title": "Upgrade Required", "description": "Please upgrade your subscription to view more companies."}, "tryAgain": {"description": "Please try again later."}}, "list": {"title": "Companies", "noStores": "No companies found", "search": "Search Companies", "filter": "Filter Companies"}}