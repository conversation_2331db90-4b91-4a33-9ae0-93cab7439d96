{"header": {"dashboard": "Dashboard", "users": "Users", "stores": "Stores", "productsServices": "Products & Services", "packages": "Packages", "representatives": "Representatives", "tickets": "Support Tickets", "settings": "Settings", "logout": "Logout", "profile": "Profile", "homepageAds": "Homepage Ads", "liveChat": "Live Chat"}, "navigation": {"menu": "<PERSON><PERSON>"}, "panel": {"adminPanel": "Admin Panel", "dashboard": "Dashboard", "users": "Users", "stores": "Stores", "productsServices": "Products & Services", "packages": "Packages", "designPackages": "Design Packages", "tickets": "Support Tickets", "homeAds": "Homepage Ads", "representatives": "Representatives", "liveChat": "Live Chat"}, "dashboard": {"title": "Admin Dashboard", "totalUsers": "Total Users", "activeUsers": "Active Users", "totalProducts": "Total Products", "totalServices": "Total Services", "revenue": "Revenue", "newUsers": "New Users", "popularProducts": "Popular Products", "recentOrders": "Recent Orders", "stats": {"users": "Total Users", "products": "Total Products", "services": "Total Services", "activeUsers": "Active Users", "totalUsers": "Total Users", "newUsers": "New Users", "last30Days": "Last 30 days", "topReferrers": "Top Referrers", "referralsCount": "Referrals Count"}, "referrals": {"name": "Name", "email": "Email", "total": "Total Referrals", "withSubscription": "With Package", "conversionRate": "Conversion Rate", "actions": "Actions", "viewDetails": "View Referral Details", "details": "Referral Details", "totalReferrals": "Total Referrals", "withPackage": "With Package", "withoutPackage": "Without Package"}, "charts": {"userActivity": "User Activity", "userRecruitment": "User Recruitment", "userDistribution": "User Distribution"}, "error": {"fetchStats": "Failed to fetch dashboard statistics", "unknown": "An unknown error occurred"}}, "users": {"title": "User Management", "createUser": "Create User", "editUser": "Edit User", "deleteUser": "Delete User", "messages": {"actionSuccess": "User action completed successfully", "actionFailed": "Failed to perform user action", "fetchFailed": "Failed to fetch users", "packageApplied": "Package applied to user successfully", "packageApplyFailed": "Failed to apply package to user", "subscriptionsFetchFailed": "Failed to fetch user subscriptions", "userDeleted": "User deleted successfully", "userActivated": "User activated successfully", "userDeactivated": "User deactivated successfully", "deleteFailed": "Failed to delete user", "activateFailed": "Failed to activate user", "deactivateFailed": "Failed to deactivate user"}, "deleteConfirmation": {"title": "Delete User", "message": "Are you sure to delete?"}, "table": {"id": "ID", "name": "Name", "user": "User", "email": "Email", "role": "Role", "status": "Status", "joinDate": "Join Date", "actions": "Actions"}, "form": {"name": "Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "role": "Role", "status": "Status"}, "roles": {"admin": "Admin", "user": "User", "vendor": "<PERSON><PERSON><PERSON>"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"activate": "Activate", "deactivate": "Deactivate", "delete": "Delete", "applyPackage": "Apply Package"}, "applyPackage": {"title": "Apply Package to User", "package": "Package", "selectPackage": "Select a package", "duration": "Duration (months)", "apply": "Apply Package", "currentSubscriptions": "Current Subscriptions", "newPackage": "Apply New Package", "packageName": "Package", "status": "Status", "expiresOn": "Expires On", "noSubscriptions": "User has no active subscriptions"}}, "productsServices": {"title": "Products & Services Management", "tabs": {"list": "List", "create": "Create New"}, "table": {"name": "Name", "description": "Description", "type": "Type", "category": "Category", "price": "Price", "status": "Status", "actions": "Actions"}, "status": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected"}, "types": {"product": "Product", "service": "Service"}, "listingTypes": {"demand": "Wanted to Buy", "sale": "For Sale"}, "modal": {"type": "Type", "listingType": "Listing Type", "category": "Category", "status": "Status", "description": "Description", "store": "Company", "owner": "Owner", "contactInfo": "Contact Information", "createdDate": "Created Date", "noImage": "No image available", "viewAllImages": "View All Images", "viewStore": "View Company Profile", "additionalInfo": "Additional Information", "htmlDescription": "Formatted Description", "basicInfo": "Basic Information", "storeDescription": "Company Description", "createdBy": "Created By", "noAdditionalInfo": "No additional information available"}, "actions": {"view": "View", "approve": "Approve", "activate": "Activate", "reject": "Reject", "delete": "Delete", "edit": "Edit", "approveSuccess": "Item approved successfully", "approveFailed": "Failed to approve item", "rejectSuccess": "Item rejected successfully", "rejectFailed": "Failed to reject item", "deleteSuccess": "Item deleted successfully", "deleteFailed": "Failed to delete item"}, "reject": {"title": "Reject Item", "selectReason": "Please select a reason for rejection:", "reasons": {"inappropriateContent": "Inappropriate Content", "missingInformation": "Missing Information", "poorQualityImages": "Poor Quality Images", "prohibitedItem": "Prohibited Item", "other": "Other"}, "customReason": "Please specify the reason", "customReasonPlaceholder": "Enter your reason here"}, "form": {"name": "Name", "description": "Description", "price": "Price", "category": "Category", "type": "Type", "create": "Create", "createSuccess": "Item created successfully", "createFailed": "Failed to create item", "stock": "Stock", "status": "Status"}}, "services": {"title": "Service Management", "createService": "Create Service", "editService": "Edit Service", "deleteService": "Delete Service", "table": {"id": "ID", "name": "Name", "category": "Category", "price": "Price", "duration": "Duration", "status": "Status", "actions": "Actions"}, "form": {"name": "Name", "description": "Description", "category": "Category", "price": "Price", "duration": "Duration", "status": "Status"}}, "packages": {"notFound": "Package not found", "title": "Package Management", "createPackage": "Create Package", "editPackage": "Edit Package", "deletePackage": "Delete Package", "deleteConfirmation": {"title": "Delete Package", "message": "Are you sure you want to delete this package? This action cannot be undone."}, "table": {"id": "ID", "name": "Name", "description": "Description", "price": "Price", "duration": "Duration", "features": "Features", "viewRequests": "View Requests", "createRequests": "Create Requests", "status": "Status", "actions": "Actions", "type": "Type"}, "form": {"name": "Name", "nameEn": "Name (English)", "description": "Description", "price": "Price", "duration": "Duration", "months": "months", "features": "Features", "status": "Status", "type": "Type", "maxListings": "<PERSON> Listings", "maxMessages": "Max Messages", "create": "Create", "viewRequests": "View Requests", "createRequests": "Create Requests", "viewRequestLimit": "View Request Limit", "createRequestLimit": "Create Request Limit", "emailNotification": "Email Notification", "smsNotification": "SMS Notification", "languageIntroRights": "Language Intro Rights", "messagingAllowed": "Messaging Allowed", "homepageAdDuration": "Homepage Ad Duration", "homepageAd": "Homepage Advertisement", "yearEndSectorReport": "Year-End Sector Report", "updateSuccess": "Package updated successfully", "updateFailed": "Failed to update package", "isActive": "Active", "createSuccess": "Package created successfully", "createFailed": "Failed to create package", "order": "Order", "basicInfo": "Basic Information", "pricingSection": "Pricing & Duration", "limitsSection": "Limits & Restrictions", "featuresSection": "Features", "editDescription": "Edit the package details below. Changes will be reflected for all users."}, "duration": {"monthly": "Monthly", "quarterly": "Quarterly", "semiannual": "Semi-Annual", "annual": "Annual"}, "type": {"messaging": "Messaging", "listing": "Listing", "standard": "Standard", "addon": "Add-on"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"activate": "Activate", "deactivate": "Deactivate", "delete": "Delete", "edit": "Edit", "toggleStatus": "Toggle Status", "deleteSuccess": "Package deleted successfully", "deleteFailed": "Failed to delete package", "toggleStatusSuccess": "Package status updated successfully", "toggleStatusFailed": "Failed to update package status"}, "tabs": {"list": "List", "create": "Create New"}, "fetchFailed": "Failed to fetch packages"}, "settings": {"title": "<PERSON><PERSON>s", "general": "General Settings", "security": "Security Settings", "notifications": "Notification Settings", "apiKeys": "API Keys"}, "representatives": {"title": "Representative Management", "add_new": "Add New Representative", "edit": "Edit Representative", "createRepresentative": "Create Representative", "table": {"name": "Full Name", "email": "Email", "phone": "Phone", "country": "Country", "city": "City", "status": "Status", "actions": "Actions"}, "form": {"first_name": "First Name", "last_name": "Last Name", "email": "Email", "phone": "Phone", "country": "Country", "city": "City", "select_country": "Select country", "select_city": "Select city", "is_active": "Active", "profile_picture": "Profile Picture", "upload_picture": "Upload Picture", "picture_size_limit": "Maximum file size: 5MB", "picture_formats": "Supported formats: JPG, PNG", "title": "Title/Position", "title_placeholder": "E.g., Export Manager", "company": "Company", "company_placeholder": "E.g., Global Trade Ltd.", "experience": "Experience", "experience_placeholder": "E.g., 10 years", "region": "Region", "region_placeholder": "E.g., Europe, Middle East", "languages": "Languages", "languages_placeholder": "E.g., English", "expertise": "Expertise", "expertise_placeholder": "E.g., Market Analysis", "verified": "Verified Representative", "remove_picture": "Remove picture", "add_button": "Add", "remove_language": "Remove {{language}}", "remove_expertise": "Remove {{expertise}}", "password": "Password", "password_placeholder": "Enter password", "new_password": "New Password (optional)"}, "status": {"active": "Active", "inactive": "Inactive"}, "actions": {"edit": "Edit", "delete": "Delete", "save": "Save Changes", "cancel": "Cancel", "confirm": "Confirm"}, "messages": {"fetch_error": "Failed to fetch representatives", "fetch_error_desc": "An error occurred while fetching representatives. Please try again.", "delete_confirm": "Are you sure you want to delete this representative?", "delete_success": "Representative deleted successfully", "delete_success_desc": "The representative has been deleted successfully.", "delete_error": "Failed to delete representative", "delete_error_desc": "An error occurred while deleting the representative. Please try again.", "create_success": "Representative created successfully", "create_success_desc": "The representative has been created successfully.", "create_error": "Failed to create representative", "create_error_desc": "An error occurred while creating the representative. Please try again.", "update_success": "Representative updated successfully", "update_success_desc": "The representative has been updated successfully.", "update_error": "Failed to update representative", "update_error_desc": "An error occurred while updating the representative. Please try again.", "invalid_image": "Invalid image format or size", "upload_error": "Failed to upload image"}}, "tickets": {"title": "Support Tickets", "table": {"title": "Title", "type": "Type", "category": "Category", "status": "Status", "created_at": "Created At", "actions": "Actions", "view": "View"}, "types": {"product": "Product", "service": "Service"}, "status": {"pending": "Pending", "in_progress": "In Progress", "resolved": "Resolved", "closed": "Closed"}, "form": {"description": "Description", "type": "Type", "category": "Category", "country": "Country", "city": "City", "amount": "Amount", "images": "Images", "status": "Status"}, "responses": "Responses", "admin": "Admin", "user": "User", "add_response": "Add Response", "response_placeholder": "Write your response here...", "actions": {"view": "View Details", "update": "Update Ticket", "close": "Close Ticket"}, "messages": {"update_success": "Ticket updated successfully", "update_error": "Failed to update ticket", "fetch_error": "Failed to fetch tickets", "response_added": "Response added successfully", "response_error": "Failed to add response", "ticket_closed": "Ticket closed successfully"}}, "auth": {"unauthorized": "Unauthorized", "pleaseLogin": "Please login to continue"}, "stores": {"title": "Store Management", "createStore": "Create Store", "editStore": "Edit Store", "deleteStore": "Delete Store", "viewStore": "View Store", "messages": {"actionSuccess": "Store action completed successfully", "actionFailed": "Failed to perform store action", "fetchFailed": "Failed to fetch stores", "approveSuccess": "Store approved successfully", "approveFailed": "Failed to approve store", "rejectSuccess": "Store rejected successfully", "rejectFailed": "Failed to reject store", "toggleSuccess": "Store status updated successfully", "toggleFailed": "Failed to update store status", "deleteSuccess": "Store deleted successfully", "deleteFailed": "Failed to delete store"}, "table": {"id": "ID", "name": "Store Name", "owner": "Owner", "approvalStatus": "Approval Status", "active": "Active", "createdAt": "Created Date", "actions": "Actions"}, "status": {"approved": "Approved", "pending": "Pending", "rejected": "Rejected", "active": "Active", "inactive": "Inactive", "title": "Status"}, "actions": {"view": "View", "approve": "Approve", "reject": "Reject", "activate": "Activate", "deactivate": "Deactivate", "delete": "Delete", "edit": "Edit"}, "reject": {"title": "Reject Store", "selectReason": "Please select a reason for rejection:", "reasons": {"inappropriateName": "Inappropriate Name", "missingInformation": "Missing Information", "poorQualityLogo": "Poor Quality Logo", "prohibitedBusiness": "Prohibited Business", "other": "Other"}, "customReason": "Please specify the reason", "customReasonPlaceholder": "Enter your reason here"}, "modal": {"viewStore": "View Store Details", "storeId": "Store ID", "storeName": "Store Name", "description": "Description", "owner": "Owner", "approvalStatus": "Approval Status", "activeStatus": "Active Status", "location": "Location", "contact": "Contact", "createdAt": "Created At", "updatedAt": "Updated At", "close": "Close"}, "confirmations": {"approve": "Are you sure you want to approve this store?", "reject": "Are you sure you want to reject this store?", "activate": "Are you sure you want to activate this store?", "deactivate": "Are you sure you want to deactivate this store?", "delete": "Are you sure you want to delete this store? This action cannot be undone."}}, "homepageAd": {"title": "Homepage Ads", "pending_title": "Pending Advertisements", "table": {"image": "Image", "title": "Title", "user": "User", "clicks": "<PERSON>licks", "status": "Status", "created_at": "Created At", "expires_at": "Expires At", "preview": "Preview", "actions": "Actions", "iconTitle": "Icon"}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "expired": "Expired", "active": "Active", "deleted": "Deleted"}, "actions": {"approve": "Approve", "reject": "Reject", "confirm_reject": "Confirm Rejection", "view": "View Image", "deactivate": "Deactivate", "reactivate": "Reactivate"}, "messages": {"fetch_error": "Failed to fetch homepage ads", "approve_success": "Advertisement approved successfully", "approve_error": "Failed to approve advertisement", "reject_success": "Advertisement rejected successfully", "reject_error": "Failed to reject advertisement", "rejected_by_admin": "Rejected by admin", "no_pending": "No pending advertisements", "no_ads": "No advertisements found"}, "modal": {"reject": "Reject Advertisement", "preview": "Image Preview"}, "form": {"rejection_reason": "Rejection Reason", "rejection_reason_placeholder": "Enter reason for rejection..."}}, "common": {"loading": "Loading...", "cancel": "Cancel", "delete": "Delete", "creationSuccess": "Created successfully", "creationFailed": "Creation failed", "actionSuccess": "Action completed successfully", "actionFailed": "Action failed", "fetchFailed": "Failed to fetch data", "save": "Save", "edit": "Edit", "view": "View", "confirm": "Confirm", "reject": "Reject", "approve": "Approve", "search": "Search", "filter": "Filter", "reset": "Reset", "noData": "No data found", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "submit": "Submit", "close": "Close", "refresh": "Refresh", "loadMore": "Load More"}, "sliders": {"title": "Slider Management", "addNew": "Add New Slider", "edit": "<PERSON>", "previewTable": "Preview", "link": "Link", "order": "Order", "actions": "Actions", "form": {"webImage": "Web Image", "mobileImage": "Mobile Image", "header": "Header Text", "headerPlaceholder": "Enter header text", "description": "Description", "descriptionPlaceholder": "Enter description text", "link": "Link", "linkPlaceholder": "Enter slider link (e.g., https://...)", "linkText": "Link Text", "linkTextPlaceholder": "Learn More", "order": "Display Order", "imageSection": "Images", "contentSection": "Content", "linkSection": "Link Options", "displaySection": "Display Settings", "noImageSelected": "No image selected"}, "preview": {"web": "Web Preview", "mobile": "Mobile Preview"}, "success": {"create": "Slider created successfully", "update": "Slider updated successfully", "delete": "Slider deleted successfully"}, "error": {"create": "Failed to create slider", "update": "Failed to update slider", "delete": "Failed to delete slider", "fetch": "Failed to fetch sliders", "invalidFileType": "Invalid file type", "onlyJpgPng": "Only JPG and PNG files are allowed", "onlyImageFiles": "Only image files are allowed", "fileTooLarge": "File too large", "maxFileSize": "Maximum file size is {{size}}MB", "genericError": "An unexpected error occurred. Please try again"}}, "products": {"title": "Product Management", "createProduct": "Create Product", "editProduct": "Edit Product", "deleteProduct": "Delete Product", "table": {"id": "ID", "name": "Name", "category": "Category", "price": "Price", "stock": "Stock", "status": "Status", "actions": "Actions"}, "form": {"name": "Name", "description": "Description", "category": "Category", "price": "Price", "stock": "Stock", "status": "Status"}}, "liveChat": {"title": "Live Chat Management", "send": "Send", "typeMessage": "Type a message...", "selectChat": "Select a chat to start conversation", "archiveChat": "Archive Chat", "noMessages": "No messages in this chat yet", "noActiveChats": "No active chats", "noClosedChats": "No closed chats", "noArchivedChats": "No archived chats", "connectionFailed": "Connection failed", "tryAgain": "Try again", "tryPolling": "Try with polling", "fallbackMode": "Fallback mode", "tabs": {"active": "Active", "closed": "Closed", "archived": "Archived"}, "statistics": {"activeChats": "Active Chats", "totalChats": "Total Chats", "totalMessages": "Total Messages", "avgResponseTime": "Average Response Time", "withUnread": "with unread messages", "closedChats": "closed"}}, "applyPackage": {"title": "Apply Package to User", "package": "Package", "selectPackage": "Select a package", "duration": "Duration (months)", "apply": "Apply Package"}, "designPackages": {"activePackages": "Active Packages", "inactivePackages": "Inactive Packages", "manageTitle": "Manage Design Packages", "manageDescription": "Manage design packages", "totalPackages": "Total Design Packages", "averagePrice": "Average Price", "categories": "Categories", "searchPlaceholder": "Search design packages...", "statusFilter": {"all": "All Status", "active": "Active", "inactive": "Inactive"}, "addNewButton": "Add New Design Package", "addNewTitle": "Add New Design Package", "editTitle": "Edit Design Package", "noPackagesFound": "No design packages found.", "fetchFailedMessage": "Failed to load design packages. Please try again.", "deleteSuccessMessage": "Design package deleted successfully.", "deleteFailedMessage": "Failed to delete design package.", "createSuccessMessage": "Design package created successfully.", "createFailedMessage": "Failed to create design package.", "updateSuccessMessage": "Design package updated successfully.", "updateFailedMessage": "Failed to update design package.", "notFoundMessage": "Design package not found.", "table": {"name": "Name", "iconTitle": "Icon", "category": "Category", "price": "Price", "status": "Status", "order": "Order", "actions": "Actions"}, "createDescription": "Create a new design package", "editDescription": "Edit existing design package details", "sections": {"basicInformation": "Basic Information", "pricingDetails": "Pricing & Details", "features": "Features", "settings": "Settings"}, "form": {"icon": "Icon", "iconPlaceholder": "e.g., Globe, Layers, PenTool", "deliveryTime": "Delivery Time (days)", "revisionCount": "Revision Count", "markAsPopular": "<PERSON> as Popular Package", "nameTr": "Name (Turkish)", "nameEn": "Name (English)", "descriptionTr": "Description (Turkish)", "descriptionEn": "Description (English)", "price": "Price", "currency": "<PERSON><PERSON><PERSON><PERSON>", "category": "Category", "categoryPlaceholder": "e.g., Logo Design, Web Development", "turnaroundTime": "Turnaround Time", "turnaroundTimePlaceholder": "e.g., 3-5 business days", "revisions": "Revisions", "featuresTr": "Features (Turkish)", "featuresEn": "Features (English)", "addFeaturePlaceholder": "Add a feature", "order": "Display Order", "slug": "URL Slug (Optional)", "slugPlaceholder": "auto-generated if-left-empty", "isActive": "Active"}}}