import { terms as enTerms } from './en';
import { terms as trTerms } from './tr';
import { terms as arTerms } from './ar';
import { terms as deTerms } from './de';
import { terms as esTerms } from './es';
import { terms as frTerms } from './fr';
import { terms as itTerms } from './it';
import { terms as ruTerms } from './ru';
import { terms as zhTerms } from './zh';

export const terms = {
  en: enTerms,
  tr: trTerms,
  ar: arTerms,
  de: deTerms,
  es: esTerms,
  fr: frTerms,
  it: itTerms,
  ru: ruTerms,
  zh: zhTerms
};