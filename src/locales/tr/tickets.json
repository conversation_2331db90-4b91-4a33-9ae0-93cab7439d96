{"title": "<PERSON><PERSON><PERSON>", "create_ticket": "Yeni Talep Oluştur", "noTickets": "Talep bulunamadı", "table": {"id": "ID", "title": "Başlık", "type": "<PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "status": "Durum", "created_at": "<PERSON><PERSON><PERSON>", "actions": "İşlemler"}, "actions": {"view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "form": {"title": "Başlık", "title_placeholder": "Talep başlığını girin", "type": "<PERSON><PERSON><PERSON>", "select_type": "<PERSON><PERSON><PERSON>", "type_product": "<PERSON><PERSON><PERSON><PERSON>", "type_service": "Hizmet", "category": "<PERSON><PERSON><PERSON>", "select_category": "<PERSON><PERSON><PERSON>", "subcategory": "Alt Kategori (İsteğe Bağlı)", "select_subcategory": "Alt Kategori <PERSON>", "country": "<PERSON><PERSON><PERSON>", "select_country": "<PERSON><PERSON><PERSON>", "city": "Şehir", "select_city": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "amount_placeholder": "<PERSON><PERSON><PERSON> girin", "description": "<PERSON><PERSON>ı<PERSON><PERSON>", "description_placeholder": "Açıklamanızı girin", "images": "<PERSON><PERSON><PERSON><PERSON>", "upload_images": "<PERSON><PERSON><PERSON><PERSON>", "max_files": "Ma<PERSON><PERSON><PERSON> {{count}} <PERSON><PERSON>", "max_size": "<PERSON><PERSON><PERSON><PERSON> dosya boyutu: {{size}}MB", "allowed_types": "İzin verilen dosya türleri: {{types}}", "status": "Durum", "submit": "<PERSON><PERSON><PERSON>", "submitting": "Gönderiliyor...", "create_header": "Yeni Talep Oluştur", "update_header": "<PERSON><PERSON>", "cancel": "Vazgeç", "product": "<PERSON><PERSON><PERSON><PERSON>", "service": "Hizmet"}, "validation": {"title_required": "Başlık zorunludur", "type_required": "<PERSON><PERSON><PERSON>", "category_required": "<PERSON><PERSON><PERSON>", "country_required": "<PERSON><PERSON><PERSON>", "city_required": "<PERSON><PERSON><PERSON>", "amount_required": "<PERSON><PERSON><PERSON>", "amount_min": "Miktar 0'dan büyük olmalıdır", "description_required": "Açıklama zorunludur", "description_min_length": "Açıklama en az 20 karakter olmalıdır", "file_required": "En az bir görsel z<PERSON>unludur", "file_type": "Geçersiz dosya türü. Sadece görsel dosyaları yüklenebilir", "file_size": "<PERSON><PERSON><PERSON> boyutu {{size}}MB limitini aşıyor", "max_files": "Ma<PERSON><PERSON><PERSON> {{count}} <PERSON><PERSON>", "required_fields": "Lütfen tüm zorunlu alanları doldurun"}, "messages": {"create_success": {"title": "Talep <PERSON>uldu", "description": "Talep başarıyla oluşturuldu"}, "create_error": {"title": "Oluşturma Başarısız", "description": "Talep oluşturulurken hata oluştu"}, "update_success": {"title": "<PERSON><PERSON>", "description": "Talep başarı<PERSON>"}, "update_error": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>ı<PERSON>", "description": "<PERSON><PERSON> hata o<PERSON>"}, "fetch_error": {"title": "Yükleme Hatası", "description": "Tale<PERSON> alınırken hata olu<PERSON>"}, "response_added": {"title": "Yanıt Eklendi", "description": "Yanıt başarıyla eklendi"}, "response_error": {"title": "<PERSON><PERSON>t <PERSON>", "description": "Yanıt eklenirken hata oluştu"}, "file_upload_error": {"title": "Yükleme Hatası", "description": "<PERSON><PERSON><PERSON> hata o<PERSON>"}}, "status": {"pending": "Beklemede", "in_progress": "İşlemde", "resolved": "Çözüldü", "closed": "Kapatıldı"}, "types": {"product": "<PERSON><PERSON><PERSON><PERSON>", "service": "Hizmet"}, "responses": "Yanıtlar", "add_response": "<PERSON><PERSON><PERSON>", "response_placeholder": "Yanıtınızı buraya yazın...", "admin": "Yönetici", "you": "Siz", "user": "Kullanıcı", "send": "<PERSON><PERSON><PERSON>", "cancellation_request": "Abonelik İptal Talebi", "cancellation_ticket_created": "İptal talebiniz destek bileti olarak iletildi."}