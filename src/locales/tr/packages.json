{"packages": {"headerTag": "Ticaret Ağınızı Genişletin!", "title": "İhtiyacınıza Uygun Paketlerle E-exportcity Yanınızda!", "description": "İster ilk adımınızı atıyor olun, ister küresel pazarda büyümeyi hedefleyin e-exportcity'nin esnek ve hedef odaklı paketleriyle ticaretinizi dijitalleştirin.", "mostPopular": "<PERSON>", "current": "Mev<PERSON>", "buyNow": "Satın Al", "addonHeader": "<PERSON><PERSON>", "addonTitle": "İhtiyacınıza Göre Ek Paketler", "addonDescription": "Mevcut paketinizi g<PERSON>, daha fazla fırsata er<PERSON>", "footer": {"title": "E-exportcity ile yeni nesil ticaret platformunda ağını büyüt!", "description": "E-exportcity, Türkiye'nin önde gelen dijital ihracat platformu olarak, işletmelerin global pazarlara açılmasını kolaylaştırıyor. Gelişmiş teknoloji altyapımız, uzman ihracat danışmanlarımız ve kapsamlı hizmet ağımızla, ihracatçılarımızın uluslararası ticaretteki rekabet gücünü artırıyoruz. Siz de e-exportcity ailesine katılarak, dijital dünyanın sunduğu sınırsız fırsatlardan yararlanın ve işletmenizi global arenada büyütün.", "features": {"global": "Global Pazarlara Erişim", "experts": "İhracat Danışmanları", "support": "7/24 Müşteri Desteği", "technology": "Gelişmiş Teknoloji Altyapısı"}}}, "titles": {"choosePackage": "<PERSON><PERSON>", "upgradePackage": "Paketinizi <PERSON>", "standardPackages": "<PERSON><PERSON>", "addonPackages": "<PERSON><PERSON>", "packageSelection": "<PERSON><PERSON>", "allPackages": "<PERSON><PERSON><PERSON>"}, "filters": {"all": "<PERSON><PERSON><PERSON>", "standard": "<PERSON><PERSON>", "addon": "<PERSON><PERSON>", "allItems": "<PERSON><PERSON><PERSON>"}, "package": {"name": "{{name}}", "price": "${{price}}", "type": {"standard": "<PERSON><PERSON>", "addon": "Ek <PERSON>"}, "features": {"viewRequests": {"title": "{{count}} Görüntüleme <PERSON>", "description": "Görüntüleyebileceğiniz istek sayısı"}, "createRequests": {"title": "{{count}} Oluşturma Hakkı", "description": "Oluşturabileceğiniz istek sayısı"}, "emailNotification": {"title": "E-posta Bildirimleri", "description": "E-posta ile bildirim alma"}, "smsNotification": {"title": "SMS Bildirimleri", "description": "SMS ile bildirim alma"}, "messagingAllowed": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>"}, "yearEndSectorReport": {"title": "<PERSON><PERSON><PERSON> Sonu Sektör <PERSON>oru", "description": "Detaylı sektör analizine erişim"}, "homePageAd": {"title": "Anasayfa Reklamı", "description": "Anasayfada reklam g<PERSON>imi"}, "languageIntroRights": {"title": "Çoklu Dil Desteği", "description": "<PERSON>en fazla dil seç<PERSON><PERSON><PERSON>"}}, "status": {"current": "Mev<PERSON>"}, "action": {"select": "Paketi Seç", "upgrade": "Paketi Yükselt", "addToPackage": "<PERSON><PERSON>", "requiresStandard": "<PERSON><PERSON>"}, "alreadyPurchased": "Bu pakete zaten sa<PERSON>iniz", "cannotDowngrade": "Daha düşük bir pakete geçiş yapamazsınız", "tooltips": {"viewRequests": "Görüntüleyebileceğiniz istek sayısı", "createRequests": "Oluşturabileceğiniz istek sayısı"}, "compare": "Karş<PERSON>laştır", "purchase": "Satın Al", "perMonth": "/ay", "loading": "Paketler yükleniyor..."}, "payment": {"modal": {"title": "Ö<PERSON>me <PERSON>", "packageDetails": "Paket: {{name}}"}, "options": {"useSavedCard": "Kayıtlı Kartı Kullan", "useNewCard": "<PERSON><PERSON>"}, "form": {"paymentMethod": "<PERSON><PERSON><PERSON>", "cardHolderName": "<PERSON><PERSON>", "cardHolderNamePlaceholder": "<PERSON><PERSON>", "cardNumber": "<PERSON><PERSON>", "cardNumberPlaceholder": "XXXX XXXX XXXX XXXX", "expiryMonth": "<PERSON>", "expiryYear": "<PERSON>", "cvv": "CVV", "cvvPlaceholder": "123", "saveCard": "Kartı gelecekteki ödemeler için kaydet", "cardAlias": "<PERSON><PERSON> Takma <PERSON>ı", "cardAliasPlaceholder": "Bu kart için bir isim girin", "selectCard": "Kayıtlı Kart Seç", "select": "Seç", "cardOption": "{{alias}} ({{type}}, **** {{lastFour}})", "processing": "Ödeme işleniyor...", "payButton": "<PERSON><PERSON><PERSON>", "cvc": "CVV", "cvcPlaceholder": "123", "required": "<PERSON><PERSON> <PERSON><PERSON>", "invalidCardNumber": "Geçersiz kart numarası", "invalidCvc": "Geçersiz CVV kodu", "errors": {"selectCard": "Lütfen bir kart seçin.", "cardHolderNameRequired": "Kart sahibinin adı zorunludur.", "invalidCardNumber": "Lütfen geçerli bir kart numarası girin.", "expiryDateRequired": "<PERSON> kullanma tarihi z<PERSON>.", "cardExpired": "Kartın süresi dolmuş.", "invalidCVC": "Lütfen geçerli bir güvenlik kodu (CVV) girin.", "cardAliasRequired": "Lütfen kartınız için bir isim belirtin.", "cardSaveError": {"description": "Kart kaydedilemedi. Lütfen tekrar deneyin."}}}, "errors": {"selectPackage": {"title": "<PERSON><PERSON><PERSON>", "description": "Lütfen bir paket seçin."}, "selectSavedCard": {"title": "<PERSON><PERSON>", "description": "Lütfen kayıtlı bir kart seçin."}, "userOrPackageNotFound": {"title": "Bulunamadı", "description": "Kullanıcı veya paket bulunamadı."}, "noActiveStandardPackage": {"title": "<PERSON><PERSON>", "description": "Aktif bir standart paket olmadan ek paket satın alınamaz."}}, "messages": {"3dSecureInit": {"title": "3D Secure Hatası", "description": "3D Secure başlatma hatası. Lütfen tekrar deneyin."}, "3dSecureError": {"title": "3D Secure Hatası", "description": "3D Secure ödeme işlemi hatası. Lütfen tekrar deneyin veya farklı bir ödeme yöntemi kullanın."}, "error": {"title": "Ödeme <PERSON>", "description": "Ödeme işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin."}, "success": {"title": "Ödeme Başarılı", "description": "Ödeme başarılı! Artık paket özelliklerine erişebilirsiniz."}, "addonSuccess": {"title": "Ek Paket Satın Alındı", "description": "Ek paket başarıyla satın alındı! Paket özellikleriniz güncellendi."}, "standardSuccess": {"title": "Paket Satın Alındı", "description": "Paket başarıyla satın alındı! Artık tüm özelliklere erişebilirsiniz."}, "processing": {"title": "İşleniyor", "description": "Ödemeniz işleniyor..."}, "unexpectedError": {"title": "Beklenmeyen Hata", "description": "Ödeme işlemi sırasında beklenmeyen bir hata oluştu. Lütfen tekrar deneyin."}, "missingToken": {"title": "Eksik Belirteç", "description": "Ödeme doğrulama belirteci eksik. Lütfen tekrar deneyin."}, "verificationFailed": {"title": "Doğrulama Başarısız", "description": "Ödeme doğrulaması başarısız oldu. Lütfen tekrar deneyin veya destek ile iletişime geçin."}, "paymentNotFound": {"title": "Ödeme Bulunamadı", "description": "Ödeme kaydı bulunamadı. Lütfen tekrar deneyin veya destek ile iletişime geçin."}, "failed": {"title": "Ödeme Başarısız", "description": "Ödeme başarısız. Lütfen kart bilgilerinizi kontrol edip tekrar deneyin."}}, "actions": {"close": "Ka<PERSON><PERSON>"}, "info": "<PERSON><PERSON><PERSON>", "processing": "Ödemeniz işleniyor...", "pay": "Ö<PERSON>", "payAmount": "${{amount}} Öde", "details": {"title": "<PERSON><PERSON>"}, "initiationError": "Ödeme başlatılırken hata oluş<PERSON>"}, "subscription": {"addonInfo": "Ek Paket Bilgisi", "connectedToPackage": "Bağlı Olduğu Paket", "remainingDays": "<PERSON><PERSON>", "days": "g<PERSON>n", "originalPrice": "Orijinal Fiyat", "proRatedPrice": "Orantılı Fiyat", "currentValue": "<PERSON><PERSON><PERSON>", "newPackagePrice": "Yeni Paket Fiyatı", "upgradePrice": "Yükseltme Fiyatı", "currentPackage": "Mev<PERSON>"}, "upgradeDetails": {"title": "Yükseltme Detayları", "description": "İhtiyaçlarınıza en uygun paketi seçin", "loading": "Paketler yükleniyor...", "error": "<PERSON><PERSON>", "fetchError": "Paketler yüklenirken hata o<PERSON>ş<PERSON>", "package": {"notAvailable": "<PERSON><PERSON><PERSON>", "cannotDowngrade": "Daha düşük bir pakete geçiş yapamazsınız", "requiresStandard": "<PERSON><PERSON>", "select": "Paketi Seç", "upgrade": "Paketi Yükselt", "addToPackage": "<PERSON><PERSON>", "alreadyPurchased": "Bu pakete zaten sa<PERSON>iniz", "features": "<PERSON><PERSON><PERSON><PERSON>", "current": "Mev<PERSON>"}}, "designPackages": {"badge": "Profesyonel Tasarım <PERSON>i", "title": "Markanızı Öne Çıkaracak Tasarım Paketleri", "subtitle": "İhracat yolculuğunuzda markanızı global pazarda güçlendirecek profesyonel tasarım çözümleriyle yanınızdayız.", "mostPopular": "En Çok Tercih Edilen", "oneTime": "/tek seferlik", "purchase": "Satın Al", "notes": "Notlar", "notesTitle": "Notlar", "buyNow": "Satın Al", "notesPlaceholder": "Projenizle ilgili özel isteklerinizi belirtebilirsiniz", "whyUs": {"title": "Neden Bizimle Çalışmalısınız?", "subtitle": "Profesyonel tasarım ekibimiz ve deneyimimizle markanızı bir adım öne taşıyoruz"}, "features": {"original": {"title": "<PERSON>zg<PERSON><PERSON>", "description": "Her marka i<PERSON>in <PERSON> o<PERSON>, <PERSON>zgün ve akılda kalıcı çözümler"}, "fast": {"title": "Hızlı Teslimat", "description": "Projeleriniz için özenle belirlenen zaman planına uygun teslimat"}, "source": {"title": "<PERSON><PERSON><PERSON>", "description": "Tüm tasarımların kaynak dosyaları ile birlikte teslimat"}, "responsive": {"title": "Responsive Tasarım", "description": "Tüm cihazlarda kusursuz görüntülenen modern tasarımlar"}}}, "error": {"fetchPackages": "Paketler yüklenirken hata o<PERSON>ş<PERSON>", "tryAgain": "<PERSON><PERSON><PERSON><PERSON> daha sonra tekrar deneyin"}}