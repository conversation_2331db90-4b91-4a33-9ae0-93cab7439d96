{"title": "<PERSON><PERSON>o", "welcome": "Bienvenido a la plataforma E-Exportcity", "search": {"placeholder": "Buscar productos y servicios...", "noResults": "No se encontraron resultados"}, "filters": {"title": "<PERSON><PERSON><PERSON>", "categories": "Categorías", "type": "Tipo", "allItems": "Todos los artículos", "types": "Tipos", "price": "Precio", "minPrice": "<PERSON><PERSON>", "maxPrice": "<PERSON><PERSON>xi<PERSON>", "apply": "Aplicar filtros", "clear": "Limpiar filtros", "showMore": "Mostrar más", "showLess": "<PERSON><PERSON> menos"}, "types": {"product": "Producto", "service": "<PERSON><PERSON><PERSON>", "products": "Productos", "services": "<PERSON><PERSON><PERSON>", "all": "Todos"}, "sort": {"title": "Ordenar por", "newest": "Más recientes", "oldest": "<PERSON>ás anti<PERSON>", "priceAsc": "Precio: menor a mayor", "priceDesc": "Precio: mayor a menor"}, "item": {"viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "price": "${{price}}", "type": "Tipo: {{type}}", "category": "Categoría: {{category}}"}, "errors": {"noViewRequests": "No hay solicitudes de visualización disponibles", "upgradeRequired": "Por favor, actualiza tu paquete para ver más artículos.", "checkFailed": "Error al verificar las solicitudes de visualización", "viewFailed": "Error al usar la solicitud de visualización", "tryAgain": "Por favor, inténtalo de nuevo más tarde.", "loadItems": "Error al cargar los artículos", "loadFilters": "Error al cargar los filtros", "loadAds": "Error al cargar los anuncios", "loadingFailed": "Error al cargar los anuncios", "loadData": "Error al cargar los datos. Por favor, inténtalo de nuevo más tarde.", "clickTrackingFailed": "Error al procesar tu clic"}, "dialogs": {"viewRequest": {"title": "<PERSON><PERSON> artículo", "message": "¿Deseas usar una de tus solicitudes de visualización para ver este artículo?", "remaining": "{{count}} solicitudes de visualización restantes.", "confirm": "S<PERSON>, ver artí<PERSON>lo", "cancel": "No, cancelar", "noRemaining": "No quedan solicitudes de visualización", "upgradeRequired": "Por favor, actualiza tu paquete para ver más artículos"}}, "pagination": {"previous": "Anterior", "next": "Siguient<PERSON>", "page": "Página {{current}} / {{total}}"}, "featured": {"stores": "Empresas Destacadas", "companies": "Empresas Destacadas", "noStores": "No hay empresas destacadas", "noCompanies": "No hay empresas destacadas", "viewAll": "<PERSON>er to<PERSON>"}, "homepageAds": {"title": "An<PERSON><PERSON>s Destacados", "subtitle": "Descubre el mejor contenido promocional de nuestras empresas", "noAds": "No hay anuncios", "manage": "Administrar anuncios", "latest": "Últimos anun<PERSON>", "featured": "Destacados", "product": "Producto", "service": "<PERSON><PERSON><PERSON>", "clickToView": "Haz clic para ver", "view": "<PERSON>er", "viewProduct": "Ver producto", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "visitStore": "Visitar empresa", "featuredAd": "<PERSON><PERSON><PERSON>", "clickToViewAdDetails": "Haz clic para ver los detalles del anuncio", "viewAll": "Ver todos"}, "navigation": {"menu": "Menú"}, "noItems": "No se encontraron artículos", "loading": "Cargando...", "representatives": {"title": "Nuestros representantes", "contact": "Contacto", "location": "Ubicación"}, "storeCount": "Número de Empresas", "featuredStores": "Empresas Destacadas", "viewAllStores": "Ver Todas las Empresas", "storeDetails": "Detalles de la Empresa", "noProductImage": "Sin Imagen de Producto", "sale": "<PERSON><PERSON><PERSON>", "demand": "<PERSON><PERSON><PERSON>", "ads": {"title": "Anun<PERSON>s des<PERSON>dos", "noAds": "No hay anuncios"}, "upgradeRequired": "Actualización requerida", "upgradeForAdsView": "Debes actualizar tu paquete para ver los detalles de los anuncios. Por favor, consulta nuestros paquetes para más funciones.", "viewPackages": "<PERSON><PERSON> paque<PERSON>", "close": "<PERSON><PERSON><PERSON>", "featuredProducts": {"title": "Productos y Servicios Destacados", "subtitle": "Los productos y servicios más populares subidos por las empresas", "viewAll": "<PERSON><PERSON>", "viewDetails": "<PERSON><PERSON>"}}