{"header": {"dashboard": "Panel de Control", "users": "Usuarios", "productsServices": "Productos y Servicios", "packages": "<PERSON><PERSON><PERSON>", "representatives": "Representantes", "tickets": "Tickets de Soporte", "settings": "Configuración", "logout": "<PERSON><PERSON><PERSON>", "profile": "Perfil", "homepageAds": "Anuncios de Inicio", "liveChat": "Chat en Vivo"}, "navigation": {"menu": "Menú"}, "panel": {"adminPanel": "Panel de Administrador", "dashboard": "Panel de Control", "users": "Usuarios", "stores": "Tiendas", "productsServices": "Productos y Servicios", "packages": "<PERSON><PERSON><PERSON>", "tickets": "Tickets de Soporte", "homeAds": "Anuncios de Inicio", "representatives": "Representantes", "liveChat": "Chat en Vivo"}, "dashboard": {"title": "Panel de Administrador", "totalUsers": "Total de Usuarios", "activeUsers": "Usuarios Activos", "totalProducts": "Total de Productos", "totalServices": "Total de Servicios", "revenue": "Ingresos", "newUsers": "Nuevos Usuarios", "popularProducts": "Productos Populares", "recentOrders": "Pedidos Recientes", "stats": {"users": "Total de Usuarios", "products": "Total de Productos", "services": "Total de Servicios", "activeUsers": "Usuarios Activos", "totalUsers": "Total de Usuarios", "newUsers": "Nuevos Usuarios"}, "charts": {"userActivity": "Actividad de Usuarios", "userRecruitment": "Reclutamiento de Usuarios", "userDistribution": "Distribución de Usuarios"}, "error": {"fetchStats": "Error al obtener estadísticas", "unknown": "Ha ocurrido un error desconocido"}}, "representatives": {"title": "Gestión de Representantes", "add_new": "Añadir Nuevo Representante", "edit": "<PERSON><PERSON>", "createRepresentative": "<PERSON><PERSON><PERSON>", "table": {"name": "Nombre Completo", "email": "Correo Electrónico", "phone": "Teléfono", "country": "<PERSON><PERSON>", "city": "Ciudad", "status": "Estado", "actions": "Acciones"}, "form": {"first_name": "Nombre", "last_name": "Apellido", "email": "Correo Electrónico", "phone": "Teléfono", "country": "<PERSON><PERSON>", "city": "Ciudad", "select_country": "Seleccionar país", "select_city": "Seleccionar ciudad", "is_active": "Activo", "profile_picture": "Foto de Perfil", "upload_picture": "Subir Foto", "picture_size_limit": "Tamaño máximo del archivo: 5MB", "picture_formats": "Formatos soportados: JPG, PNG", "title": "Título/Posición", "title_placeholder": "Ej: Gerente de Exportación", "company": "Empresa", "company_placeholder": "Ej: Comercio Global S.L.", "experience": "Experiencia", "experience_placeholder": "Ej: 10 años", "region": "Región", "region_placeholder": "Ej: Europa, Oriente Medio", "languages": "Idiomas", "languages_placeholder": "Ej: <PERSON><PERSON><PERSON><PERSON>", "expertise": "Especialización", "expertise_placeholder": "Ej: <PERSON><PERSON><PERSON><PERSON>", "verified": "Representante Verificado", "remove_picture": "Eliminar imagen", "add_button": "<PERSON><PERSON><PERSON>", "remove_language": "Eliminar {{language}}", "remove_expertise": "Eliminar {{expertise}}", "password": "Contraseña", "password_placeholder": "Ingrese contraseña", "new_password": "Nueva Contraseña (opcional)"}, "status": {"active": "Activo", "inactive": "Inactivo"}, "actions": {"edit": "<PERSON><PERSON>", "delete": "Eliminar", "save": "Guardar Cambios", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar"}, "messages": {"fetch_error": "Error al obtener representantes", "fetch_error_desc": "Ocurrió un error al obtener los representantes. Por favor, inténtelo de nuevo.", "delete_confirm": "¿Está seguro de que desea eliminar este representante?", "delete_success": "Representante eliminado con éxito", "delete_success_desc": "El representante ha sido eliminado con éxito.", "delete_error": "Error al eliminar el representante", "delete_error_desc": "Ocurrió un error al eliminar el representante. Por favor, inténtelo de nuevo.", "create_success": "Representante creado con éxito", "create_success_desc": "El representante ha sido creado con éxito.", "create_error": "Error al crear el representante", "create_error_desc": "Ocurrió un error al crear el representante. Por favor, inténtelo de nuevo.", "update_success": "Representante actualizado con éxito", "update_success_desc": "El representante ha sido actualizado con éxito.", "update_error": "Error al actualizar el representante", "update_error_desc": "Ocurrió un error al actualizar el representante. Por favor, inténtelo de nuevo.", "invalid_image": "Formato o tamaño de imagen inválido", "upload_error": "Error al subir la imagen"}}, "users": {"title": "Gestión de Usuarios", "createUser": "<PERSON><PERSON><PERSON>", "editUser": "<PERSON><PERSON>", "deleteUser": "Eliminar Usua<PERSON>", "table": {"id": "ID", "name": "Nombre", "user": "Usuario", "email": "Correo Electrónico", "role": "Rol", "status": "Estado", "joinDate": "<PERSON><PERSON>", "actions": "Acciones"}, "form": {"name": "Nombre", "email": "Correo Electrónico", "password": "Contraseña", "confirmPassword": "Con<PERSON><PERSON><PERSON>", "role": "Rol", "status": "Estado"}, "roles": {"admin": "Administrador", "user": "Usuario", "vendor": "<PERSON><PERSON><PERSON>"}, "status": {"active": "Activo", "inactive": "Inactivo", "banned": "Bloqueado", "pending": "Pendiente"}, "messages": {"actionSuccess": "Acción de usuario completada con éxito", "actionFailed": "Error al realizar la acción de usuario", "fetchFailed": "Error al obtener usuarios", "packageApplied": "Paquete aplicado al usuario con éxito", "packageApplyFailed": "Error al aplicar el paquete al usuario", "subscriptionsFetchFailed": "Error al obtener las suscripciones del usuario", "userDeleted": "Usuario eliminado con éxito", "userActivated": "Usuario activado con éxito", "userDeactivated": "Usuario desactivado con éxito", "deleteFailed": "Error al eliminar el usuario", "activateFailed": "Error al activar el usuario", "deactivateFailed": "Error al desactivar el usuario"}, "deleteConfirmation": {"title": "Eliminar usuario", "message": "¿Está seguro de eliminar?"}, "actions": {"activate": "Activar", "deactivate": "Desactivar", "delete": "Eliminar", "applyPackage": "Aplica<PERSON>"}, "applyPackage": {"title": "Aplicar paquete al usuario", "package": "<PERSON><PERSON><PERSON>", "selectPackage": "Seleccionar un paquete", "duration": "Duración (meses)", "apply": "Aplicar paquete", "currentSubscriptions": "Suscripciones actuales", "newPackage": "Aplicar nuevo paquete", "packageName": "<PERSON><PERSON><PERSON>", "status": "Estado", "expiresOn": "<PERSON><PERSON> el", "noSubscriptions": "El usuario no tiene suscripciones activas"}}, "tickets": {"title": "Tickets de Soporte", "table": {"title": "<PERSON><PERSON><PERSON><PERSON>", "type": "Tipo", "category": "Categoría", "status": "Estado", "created_at": "Fecha de Creación", "actions": "Acciones", "view": "<PERSON>er"}, "types": {"product": "Producto", "service": "<PERSON><PERSON><PERSON>"}, "status": {"pending": "Pendiente", "in_progress": "En Proceso", "resolved": "<PERSON><PERSON><PERSON><PERSON>", "closed": "<PERSON><PERSON><PERSON>"}, "form": {"description": "Descripción", "type": "Tipo", "category": "Categoría", "country": "<PERSON><PERSON>", "city": "Ciudad", "amount": "Cantidad", "images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Estado"}, "responses": "Respuestas", "admin": "Administrador", "user": "Usuario", "add_response": "Agregar <PERSON>", "response_placeholder": "Escribe tu respuesta aquí...", "actions": {"view": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON> Ticket"}, "messages": {"update_success": "Ticket actualizado exitosamente", "update_error": "Error al actualizar el ticket", "fetch_error": "Error al obtener tickets", "response_added": "Respuesta agregada exitosamente", "response_error": "Error al agregar respuesta", "ticket_closed": "Ticket cerrado exitosamente"}}, "productsServices": {"modal": {"basicInfo": "Información Básica", "storeDescription": "Descripción de la Empresa", "createdBy": "<PERSON><PERSON>o por", "noAdditionalInfo": "No hay información adicional disponible", "type": "Tipo", "listingType": "Tipo de Listado", "category": "Categoría", "status": "Estado", "description": "Descripción", "store": "Empresa", "owner": "Propietario", "contactInfo": "Información de Contacto", "createdDate": "Fecha de Creación", "noImage": "No hay imagen disponible", "viewAllImages": "Ver Todas las Imágenes", "viewStore": "Ver Perfil de la Empresa", "additionalInfo": "Información Adicional", "htmlDescription": "Descripción Formateada"}, "title": "Gestión de Productos y Servicios", "tabs": {"list": "Lista", "create": "<PERSON><PERSON>r <PERSON>"}, "table": {"name": "Nombre", "type": "Tipo", "category": "Categoría", "price": "Precio", "description": "Descripción", "status": "Estado", "actions": "Acciones"}, "status": {"approved": "Aprobado", "pending": "Pendiente", "rejected": "<PERSON><PERSON><PERSON><PERSON>"}, "types": {"product": "Producto", "service": "<PERSON><PERSON><PERSON>"}, "listingTypes": {"demand": "Se Busca Comprar", "sale": "En Venta"}, "actions": {"view": "<PERSON>er", "approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "approveSuccess": "Elemento aprobado con éxito", "approveFailed": "Error al aprobar el elemento", "rejectSuccess": "Elemento rechazado con éxito", "rejectFailed": "Error al rechazar el elemento", "deleteSuccess": "Elemento eliminado con éxito", "deleteFailed": "Error al eliminar el elemento"}, "form": {"name": "Nombre", "description": "Descripción", "price": "Precio", "category": "Categoría", "type": "Tipo", "create": "<PERSON><PERSON><PERSON>", "createSuccess": "Elemento creado con éxito", "createFailed": "Error al crear el elemento", "stock": "Stock", "status": "Estado"}}, "services": {"title": "Gestión de Servicios", "createService": "<PERSON><PERSON><PERSON>", "editService": "<PERSON><PERSON>", "deleteService": "Eliminar <PERSON><PERSON>", "table": {"id": "ID", "name": "Nombre", "category": "Categoría", "price": "Precio", "duration": "Duración", "status": "Estado", "actions": "Acciones"}, "form": {"name": "Nombre", "description": "Descripción", "category": "Categoría", "price": "Precio", "duration": "Duración", "status": "Estado"}}, "packages": {"notFound": "Paquete no encontrado", "title": "Gestión de Paquetes", "createPackage": "<PERSON><PERSON><PERSON>", "editPackage": "<PERSON><PERSON>", "deletePackage": "<PERSON><PERSON><PERSON>", "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "message": "¿Está seguro de que desea eliminar este paquete? Esta acción no se puede deshacer."}, "table": {"id": "ID", "name": "Nombre", "description": "Descripción", "price": "Precio", "duration": "Duración", "features": "Características", "viewRequests": "Ver Solicitudes", "createRequests": "<PERSON><PERSON><PERSON>", "status": "Estado", "actions": "Acciones", "type": "Tipo"}, "form": {"name": "Nombre", "nameEn": "Nombre (Inglés)", "description": "Descripción", "price": "Precio", "duration": "Duración", "months": "meses", "features": "Características", "status": "Estado", "type": "Tipo", "maxListings": "Máximo de Listados", "maxMessages": "Máximo de Mensajes", "create": "<PERSON><PERSON><PERSON>", "viewRequests": "Ver Solicitudes", "createRequests": "<PERSON><PERSON><PERSON>", "viewRequestLimit": "Límite de Solicitudes de Visualización", "createRequestLimit": "Límite de Solicitudes de Creación", "emailNotification": "Notificación por Correo", "smsNotification": "Notificación por SMS", "languageIntroRights": "Derechos de Introducción de Idioma", "messagingAllowed": "Mensajería Permitida", "homepageAdDuration": "Duración del Anuncio en Página Principal", "homepageAd": "<PERSON><PERSON><PERSON> en Página Principal", "yearEndSectorReport": "Informe Sectorial de Fin de Año", "updateSuccess": "Paquete actualizado con éxito", "updateFailed": "Error al actualizar el paquete", "isActive": "Activo", "createSuccess": "Paquete creado con éxito", "createFailed": "Error al crear el paquete", "order": "Orden", "basicInfo": "Información Básica", "pricingSection": "Precios y Duración", "limitsSection": "Límites y Restricciones", "featuresSection": "Características", "editDescription": "Edite los detalles del paquete a continuación. Los cambios se reflejarán para todos los usuarios."}, "duration": {"monthly": "<PERSON><PERSON><PERSON>", "quarterly": "Trimestral", "semiannual": "Semestral", "annual": "<PERSON><PERSON>"}, "type": {"messaging": "Mensajería", "listing": "Listado", "standard": "<PERSON><PERSON><PERSON><PERSON>", "addon": "Complemento"}, "status": {"active": "Activo", "inactive": "Inactivo"}, "actions": {"activate": "Activar", "deactivate": "Desactivar", "delete": "Eliminar", "edit": "<PERSON><PERSON>", "toggleStatus": "<PERSON><PERSON><PERSON>", "deleteSuccess": "Paquete eliminado con éxito", "deleteFailed": "Error al eliminar el paquete", "toggleStatusSuccess": "Estado del paquete actualizado con éxito", "toggleStatusFailed": "Error al actualizar el estado del paquete"}, "tabs": {"list": "Lista", "create": "<PERSON><PERSON>r <PERSON>"}, "fetchFailed": "Error al obtener paquetes"}, "settings": {"title": "Configuración de Administrador", "general": "Configuración General", "security": "Configuración de Seguridad", "notifications": "Configuración de Notificaciones", "apiKeys": "Claves API", "profile": "Perfil", "appearance": "Apariencia", "language": "Idioma", "permissions": "<PERSON><PERSON><PERSON>", "backup": "Copia de Seguridad", "advanced": "Configuración Avanzada"}, "auth": {"unauthorized": "No autorizado", "pleaseLogin": "Por favor, inicie sesión para continuar", "login": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON><PERSON>", "register": "Registrarse", "forgotPassword": "Olvid<PERSON> mi contraseña", "resetPassword": "Restable<PERSON>", "emailAddress": "Dirección de Correo Electrónico", "password": "Contraseña", "rememberMe": "Recordarme", "loginSuccess": "Inicio de sesión exitoso", "loginFailed": "Error al iniciar sesión", "logoutSuccess": "Cierre de sesión exitoso"}, "homepageAd": {"title": "<PERSON><PERSON><PERSON><PERSON> Página Principal", "table": {"image": "Imagen", "title": "<PERSON><PERSON><PERSON><PERSON>", "user": "Usuario", "clicks": "C<PERSON>s", "status": "Estado", "created_at": "Creado el", "expires_at": "Expira el", "preview": "Vista previa", "actions": "Acciones"}, "status": {"pending": "Pendiente", "approved": "Aprobado", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "expired": "<PERSON><PERSON><PERSON>", "active": "Activo", "deleted": "Eliminado"}, "actions": {"approve": "<PERSON><PERSON><PERSON>", "reject": "<PERSON><PERSON><PERSON>", "confirm_reject": "Confirmar <PERSON>", "view": "Ver Imagen", "deactivate": "Desactivar", "reactivate": "Reactivar"}, "modal": {"reject": "<PERSON><PERSON><PERSON>", "preview": "Vista Previa de Imagen"}, "form": {"rejection_reason": "Motivo de Rechazo", "rejection_reason_placeholder": "Ingrese el motivo del rechazo..."}, "messages": {"fetch_error": "Error al obtener anuncios de página principal", "approve_success": "<PERSON><PERSON><PERSON> aprobado con éxito", "approve_error": "Error al aprobar el anuncio", "reject_success": "<PERSON><PERSON><PERSON> rechazado con éxito", "reject_error": "Error al rechazar el anuncio", "no_ads": "No se encontraron anuncios", "rejected_by_admin": "Rechazado por el administrador", "no_pending": "No hay anuncios pendientes"}, "pending_title": "Anuncios Pendientes"}, "common": {"loading": "Cargando...", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Eliminar", "creationSuccess": "Creado con éxito", "creationFailed": "Error en la creación", "actionSuccess": "Acción completada con éxito", "actionFailed": "Error en la acción", "fetchFailed": "Error al obtener datos", "save": "Guardar", "edit": "<PERSON><PERSON>", "update": "Actualizar", "create": "<PERSON><PERSON><PERSON>", "search": "Buscar", "filter": "Filtrar", "reset": "Restablecer", "confirm": "Confirmar", "back": "Volver", "next": "Siguient<PERSON>", "previous": "Anterior", "yes": "Sí", "no": "No", "submit": "Enviar", "close": "<PERSON><PERSON><PERSON>", "view": "<PERSON>er", "details": "Detalles", "more": "Más", "less": "<PERSON><PERSON>", "all": "Todos", "none": "<PERSON><PERSON><PERSON>", "active": "Activo", "inactive": "Inactivo", "enabled": "Habilitado", "disabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "required": "Requerido", "optional": "Opcional"}, "sliders": {"title": "Gestión de Sliders", "addNew": "<PERSON><PERSON><PERSON> Slider", "edit": "<PERSON><PERSON>", "previewTable": "Vista Previa", "link": "Enlace", "order": "Orden", "actions": "Acciones", "form": {"webImage": "Imagen Web", "mobileImage": "<PERSON><PERSON>", "header": "Texto de Encabezado", "headerPlaceholder": "Ingrese texto de encabezado", "description": "Descripción", "descriptionPlaceholder": "Ingrese texto de descripción", "link": "Enlace", "linkPlaceholder": "Ingrese enlace del slider (ej: https://...)", "linkText": "Texto del Enlace", "linkTextPlaceholder": "Saber más", "order": "Orden de Visualización", "imageSection": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "contentSection": "Contenido", "linkSection": "Opciones de Enlace", "displaySection": "Configuración de Visualización", "noImageSelected": "Ninguna imagen seleccionada"}, "preview": {"web": "Vista Previa Web", "mobile": "Vista Previa Móvil"}, "success": {"create": "<PERSON><PERSON><PERSON> creado con éxito", "update": "Slider actualizado con éxito", "delete": "<PERSON><PERSON><PERSON> eliminado con éxito"}, "error": {"create": "Error al crear el slider", "update": "Error al actualizar el slider", "delete": "Error al eliminar el slider", "fetch": "Error al obtener sliders"}}, "products": {"title": "Gestión de Productos", "createProduct": "<PERSON><PERSON><PERSON>", "editProduct": "<PERSON><PERSON>", "deleteProduct": "Eliminar Producto", "table": {"id": "ID", "name": "Nombre", "category": "Categoría", "price": "Precio", "stock": "Stock", "status": "Estado", "actions": "Acciones"}, "form": {"name": "Nombre", "description": "Descripción", "category": "Categoría", "price": "Precio", "stock": "Stock", "status": "Estado"}}, "profile": {"title": "Perfil de Administrador", "personalInfo": "Información Personal", "changePassword": "Cambiar <PERSON>", "accountSettings": "Configuración de Cuenta", "notifications": "Notificaciones", "activity": "Actividad Reciente", "form": {"firstName": "Nombre", "lastName": "Apellido", "email": "Correo Electrónico", "phone": "Teléfono", "currentPassword": "Contraseña Actual", "newPassword": "Nueva Contraseña", "confirmPassword": "Con<PERSON><PERSON><PERSON>"}, "messages": {"updateSuccess": "Perfil actualizado con éxito", "updateFailed": "Error al actualizar el perfil", "passwordChanged": "Contraseña cambiada con éxito", "passwordChangeFailed": "Error al cambiar la contraseña"}}, "notifications": {"title": "Notificaciones", "markAllRead": "<PERSON><PERSON> como Leídas", "clearAll": "<PERSON><PERSON><PERSON>", "noNotifications": "No hay notificaciones", "types": {"system": "Sistema", "user": "Usuario", "product": "Producto", "service": "<PERSON><PERSON><PERSON>", "order": "Pedido", "payment": "Pago"}}, "applyPackage": {"title": "Aplicar Pa<PERSON>e al Usuario", "package": "<PERSON><PERSON><PERSON>", "selectPackage": "Seleccionar un paquete", "duration": "Duración (meses)", "apply": "Aplica<PERSON>"}, "liveChat": {"title": "Gestión de Chat en Vivo", "send": "Enviar", "typeMessage": "Escriba un mensaje...", "selectChat": "Seleccione un chat para iniciar la conversación", "archiveChat": "Archivar <PERSON>", "noMessages": "Aún no hay mensajes en este chat", "noActiveChats": "No hay chats activos", "noClosedChats": "No hay chats cerrados", "noArchivedChats": "No hay chats archivados", "connectionFailed": "Conexión fallida", "tryAgain": "Intentar de nuevo", "tryPolling": "Probar con sondeo", "fallbackMode": "Modo de reserva", "tabs": {"active": "Activos", "closed": "<PERSON><PERSON><PERSON>", "archived": "Archivados"}, "statistics": {"activeChats": "Chats Activos", "totalChats": "Total de Chats", "totalMessages": "Total de Mensajes", "avgResponseTime": "Tiempo Promedio de Respuesta", "withUnread": "con mensajes sin leer", "closedChats": "cerrados"}}, "designPackages": {"activePackages": "Paquetes Activos", "inactivePackages": "Paquetes Inactivos", "manageTitle": "Administrar <PERSON>ño", "manageDescription": "Administrar paque<PERSON> de di<PERSON>ño", "totalPackages": "Total de Paquetes de Diseño", "averagePrice": "<PERSON><PERSON> Promedio", "categories": "Categorías", "searchPlaceholder": "Buscar paquetes de diseño...", "statusFilter": {"all": "Todos los Estados", "active": "Activo", "inactive": "Inactivo"}, "addNewButton": "Añadir Nuevo Paquete de Diseño", "addNewTitle": "Añadir Nuevo Paquete de Diseño", "editTitle": "<PERSON><PERSON> Diseño", "noPackagesFound": "No se encontraron paquetes de diseño.", "fetchFailedMessage": "Error al cargar los paquetes de diseño. Por favor, inténtelo de nuevo.", "deleteSuccessMessage": "Paquete de diseño eliminado con éxito.", "deleteFailedMessage": "Error al eliminar el paquete de diseño.", "createSuccessMessage": "Paquete de diseño creado con éxito.", "createFailedMessage": "Error al crear el paquete de diseño.", "updateSuccessMessage": "Paquete de diseño actualizado con éxito.", "updateFailedMessage": "Error al actualizar el paquete de diseño.", "notFoundMessage": "Paquete de diseño no encontrado.", "table": {"name": "Nombre", "iconTitle": "Icono", "category": "Categoría", "price": "Precio", "status": "Estado", "order": "Orden", "actions": "Acciones"}, "form": {"nameTr": "Nombre (Turco)", "nameEn": "Nombre (Inglés)", "descriptionTr": "Descripción (Turco)", "descriptionEn": "Descripción (Inglés)", "price": "Precio", "currency": "Moneda", "category": "Categoría", "categoryPlaceholder": "ej., <PERSON><PERSON><PERSON>go, Desarrollo Web", "turnaroundTime": "Tiempo de Entrega", "turnaroundTimePlaceholder": "ej., 3-5 días laborables", "revisions": "Revisiones", "featuresTr": "Características (Turco)", "featuresEn": "Características (Inglés)", "addFeaturePlaceholder": "Añadir una característica", "order": "Orden de Visualización", "slug": "Slug URL (Opcional)", "slugPlaceholder": "se genera automáticamente si se deja vacío", "isActive": "Activo"}}}