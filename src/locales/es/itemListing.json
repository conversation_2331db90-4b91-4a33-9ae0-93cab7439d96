{"selectTypeAndMode": "Seleccionar <PERSON> y Modo", "search": {"placeholder": "Buscar productos o servicios..."}, "sort": {"newest": "Más recientes primero", "oldest": "Más antiguos primero", "mostViewed": "Más vistos", "leastViewed": "Menos vistos"}, "filters": {"button": "<PERSON><PERSON><PERSON>"}, "clearFilters": "<PERSON><PERSON><PERSON> filtros", "all": "Todos", "location": {"unknown": "Ubicación desconocida", "label": "Ubicación"}, "mode": "Modo", "store": "Tienda", "categories": "Categorías", "page": {"description": "Productos y servicios globales de tendencia en el comercio de exportación"}, "description": "Descripción", "addProduct": "<PERSON><PERSON><PERSON>", "addService": "<PERSON><PERSON><PERSON>", "requestProduct": "Solicitar Producto", "requestService": "Solicitar Servicio", "addProductDesc": "Crear un nuevo listado de productos para vender", "addServiceDesc": "Crear un nuevo listado de servicios para ofrecer", "requestProductDesc": "Solicitar un producto que estás buscando", "requestServiceDesc": "Solicitar un servicio que necesitas", "basicInfo": "Información Básica", "title": "Productos y Servicios", "createItemRequest": "<PERSON><PERSON><PERSON> solicit<PERSON> de artículo", "editItemRequest": "Editar solicitud de artículo", "editItem": "<PERSON><PERSON>", "name": "Nombre", "enterName": "Ingrese nombre", "type": "Tipo", "product": "Producto", "service": "<PERSON><PERSON><PERSON>", "category": "Categoría", "selectCategory": "Seleccionar categoría", "listingType": "Tipo de listado", "sale": "<PERSON><PERSON><PERSON>", "demand": "<PERSON><PERSON><PERSON>", "enterDescription": "Ingrese descripción", "htmlDescription": "Descripción HTML", "images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uploadImages": "Subir imágenes", "image": "Imagen", "preview": "Vista previa", "removeImage": "Eliminar imagen", "noImagesUploaded": "No hay imágenes subidas", "cancel": "<PERSON><PERSON><PERSON>", "update": "Actualizar", "create": "<PERSON><PERSON><PERSON>", "updating": "Actualizando...", "creating": "Creando...", "errorFetchingCategories": "Error al obtener categorías", "pleaseCompleteAllFields": "Por favor complete todos los campos requeridos", "errorUploadingFiles": "Error al subir archivos", "placeholders": {"productName": "Ingrese nombre del producto", "serviceName": "Ingrese nombre del servicio", "productDescription": "Describa su producto en detalle", "serviceDescription": "Describa su servicio en detalle", "productRequestDescription": "Describa el producto que está buscando", "serviceRequestDescription": "Describa el servicio que necesita", "requestTitle": "Ingrese título de la solicitud"}, "successMessages": {"itemRequestUpdated": "Solicitud de artículo actualizada con éxito", "itemRequestCreated": "Solicitud de artículo creada con éxito"}, "errorSavingItemRequest": {"title": "Error al guardar la solicitud de artículo", "description": "Por favor verifique la información e intente nuevamente"}, "categoryLevels": {"main": "Principal", "level": "Nivel {{level}}", "select": "Seleccionar categoría {{level}}"}, "types": {"product": "Producto", "service": "<PERSON><PERSON><PERSON>"}, "item": {"price": "${{price}}", "category": "Categoría", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "noItems": "No se encontraron artículos"}, "errors": {"loadData": {"title": "<PERSON><PERSON>r de carga", "description": "Error al cargar los artículos. Por favor, intente nuevamente más tarde."}, "loadCategoryItems": {"title": "<PERSON><PERSON>r de carga", "description": "Error al cargar los artículos de la categoría. Por favor, intente nuevamente más tarde."}, "loadSubcategories": {"title": "<PERSON><PERSON>r de carga", "description": "Error al cargar las subcategorías. Por favor, intente nuevamente más tarde."}, "noCreateRequests": {"title": "Sin solicitudes restantes", "description": "No le quedan solicitudes de creación. Por favor, actualice su plan."}, "upgradeRequired": {"title": "Actualización requerida", "description": "Por favor, actualice su plan para crear más artículos."}, "createFailed": {"title": "Error de creación", "description": "Error al crear el artículo. Por favor, intente nuevamente más tarde."}, "updateFailed": {"title": "Error de actualización", "description": "Error al actualizar el artículo. Por favor, intente nuevamente más tarde."}, "deleteFailed": {"title": "Error de eliminación", "description": "Error al eliminar el artículo. Por favor, intente nuevamente más tarde."}, "adCreateFailed": {"title": "Error de creación de anuncio", "description": "Error al crear el anuncio. Por favor, intente nuevamente más tarde."}, "tooManyFiles": {"title": "Demasiados archivos", "description": "Ha excedido el número máximo de archivos permitidos. Por favor, seleccione menos archivos."}, "maxFiveFiles": {"title": "Límite de archivos excedido", "description": "Máximo 5 archivos permitidos."}, "validation": {"title": "Error de validación", "description": "Por favor, corrija los errores en el formulario e intente nuevamente."}, "categoryRequired": {"title": "Categoría requerida", "description": "Por favor, seleccione una categoría."}, "subcategoryLoad": {"title": "<PERSON><PERSON>r de carga", "description": "Error al cargar las subcategorías. Por favor, intente nuevamente más tarde."}, "categoryLoad": {"title": "<PERSON><PERSON>r de carga", "description": "Error al cargar las categorías. Por favor, intente nuevamente más tarde."}, "tryAgain": {"title": "Error", "description": "Por favor, intente nuevamente."}, "imageUpload": {"title": "Error de carga de imagen", "description": "Error al subir imágenes. <PERSON>r favor, intente nuevamente más tarde."}, "missingImage": {"title": "<PERSON><PERSON> re<PERSON>", "description": "Por favor, suba al menos una imagen."}, "pleaseSelectImage": {"title": "<PERSON><PERSON> re<PERSON>", "description": "Por favor, seleccione una imagen para el anuncio."}, "itemLoadFailed": "No se pudieron cargar los datos del artículo", "itemSaveFailed": "No se pudieron guardar los datos del artículo", "itemCreationFailed": {"title": "Error de creación de artículo", "description": "No se pudo crear el artículo. Por favor, intente nuevamente más tarde."}}, "success": {"itemCreated": "<PERSON><PERSON><PERSON><PERSON> creado exitosamente", "itemUpdated": "Artículo actualizado exitosa<PERSON>e", "itemDeleted": "Artí<PERSON>lo eliminado exitosamente", "adCreated": {"title": "Éxito", "description": "<PERSON><PERSON><PERSON> creado exitosamente."}}, "buttons": {"createItem": "Crear nuevo artículo", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "create": "<PERSON><PERSON><PERSON>", "updateItem": "Actualizar <PERSON>", "publishProduct": "Publicar producto", "publishService": "Publicar servicio", "publishRequest": "Publicar solicitud", "uploadImages": "Subir imágenes", "save": "Guardar", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "promote": "Promocionar", "upgrade": "<PERSON><PERSON> paque<PERSON>", "message": "Men<PERSON><PERSON>", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "fields": {"name": "Nombre", "description": "Descripción", "productName": "Nombre del producto", "serviceName": "Nombre del servicio", "productDescription": "Descripción del producto", "serviceDescription": "Descripción del servicio", "requestDetails": "Detalles de la solicitud", "requestTitle": "<PERSON><PERSON><PERSON><PERSON> solicitud", "price": "Precio", "images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "imageHelp": "Suba hasta 5 imágenes. La primera imagen será usada como imagen principal.", "category": "Categoría", "adTitle": "<PERSON><PERSON><PERSON><PERSON> del anuncio", "adTitleHelper": "Ingrese un título atractivo para su anuncio", "adImage": "Imagen del anuncio", "adImageHelper": "La imagen debe ser menor a 2MB y en formato JPG, PNG o GIF"}, "modals": {"create": {"title": "Crear nuevo artículo", "success": "<PERSON><PERSON><PERSON><PERSON> creado exitosamente"}, "edit": {"title": "<PERSON><PERSON>", "success": "Artículo actualizado exitosa<PERSON>e"}, "message": {"title": "<PERSON><PERSON><PERSON> men<PERSON>"}}, "dialogs": {"delete": {"title": "Eliminar artículo", "message": "¿Está seguro de que desea eliminar este artículo? Esta acción no se puede deshacer.", "success": "Artí<PERSON>lo eliminado exitosamente"}, "viewRequest": {"title": "Usar solicitud de visualización", "message": "¿Desea usar una de sus solicitudes de visualización para ver este artículo?", "confirm": "S<PERSON>, ver artí<PERSON>lo", "cancel": "No, cancelar", "remaining": "restante"}, "homeAd": {"title": "<PERSON><PERSON><PERSON> anuncio en la página principal", "message": "¿Desea promocionar este artículo en la página principal durante {{duration}} días?", "success": "<PERSON><PERSON><PERSON> c<PERSON>o exitosamente"}, "upgrade": {"title": "Actualizar su paquete", "message": "¡Promocione sus artículos en la página principal y llegue a más clientes actualizando a un paquete con función de anuncio en la página principal!", "benefits": {"title": "Beneficios del anuncio en la página principal:", "visibility": "Mayor visibilidad para sus productos", "traffic": "Más tráfico a sus listados", "sales": "Mayor probabilidad de realizar ventas"}}}, "seeDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "myItemsTitle": "<PERSON><PERSON>í<PERSON>", "products": "Productos", "services": "<PERSON><PERSON><PERSON>", "allCategories": "Todas las categorías", "pendingRequests": "Solicitudes pendientes", "approvedItems": "<PERSON><PERSON><PERSON><PERSON>", "cannotEditApprovedItem": "No se puede editar el artículo aprobado", "itemRequestDeleted": "Solicitud de artículo eliminada", "itemDeleted": "<PERSON><PERSON><PERSON><PERSON> eliminado", "errorDeletingItemRequest": "Error al eliminar la solicitud de artículo", "deleteItemRequestConfirmation": "¿Está seguro de que desea eliminar esta solicitud de artículo?", "selectType": "Seleccionar tipo", "mainCategory": "Categoría principal", "subCategory": "Subcategoría {{level}}", "noItemsFound": "No se encontraron artículos", "loading": "Cargando...", "errorFetchingItems": "Error al obtener artículos", "createNewRequest": "Crear nueva solicitud", "deleteItemRequest": "Eliminar solicitud de artículo", "whatsapp": {"contact": "Contactar vía WhatsApp", "message": "<PERSON><PERSON>, estoy interesado en su artículo: {{itemName}}"}, "language": {"english": "Inglés", "turkish": "<PERSON><PERSON><PERSON>", "german": "Alemán", "select": "Seleccionar idioma"}}