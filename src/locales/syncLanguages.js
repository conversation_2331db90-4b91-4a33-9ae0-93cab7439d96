import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Read a JSON file
 * @param {string} filePath - The path to the JSON file
 * @returns {Object} The parsed JSON object
 */
function readJsonFile(filePath) {
  try {
    const data = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error.message);
    process.exit(1);
  }
}

/**
 * Write a JSON file
 * @param {string} filePath - The path to write the JSON file
 * @param {Object} data - The data to write to the file
 */
function writeJsonFile(filePath, data) {
  try {
    const jsonString = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonString);
    console.log(`File ${filePath} written successfully`);
  } catch (error) {
    console.error(`<PERSON>rror writing file ${filePath}:`, error.message);
    process.exit(1);
  }
}

/**
 * Find missing keys in target and add them
 * @param {Object} source - Source object
 * @param {Object} target - Target object to update
 * @param {string} prefix - Current key prefix (for nested objects)
 * @returns {Object} Updated target
 */
function addMissingKeys(source, target, prefix = '') {
  Object.keys(source).forEach(key => {
    const currentKey = prefix ? `${prefix}.${key}` : key;
    
    if (!(key in target)) {
      // Key doesn't exist in target, add it with placeholder value
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        target[key] = {};
      } else {
        // For strings, add the English text with a "TRANSLATE: " prefix
        target[key] = `TRANSLATE: ${source[key]}`;
      }
      console.log(`Added missing key: ${currentKey}`);
    } else if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
      // It's a nested object, recursively check it
      if (typeof target[key] !== 'object' || target[key] === null || Array.isArray(target[key])) {
        target[key] = {};
      }
      target[key] = addMissingKeys(source[key], target[key], currentKey);
    }
  });
  
  return target;
}

/**
 * Remove extra keys in target
 * @param {Object} source - Source object
 * @param {Object} target - Target object to update
 * @param {string} prefix - Current key prefix (for nested objects)
 * @returns {Object} Updated target with extra keys removed
 */
function removeExtraKeys(source, target, prefix = '') {
  const keysToDelete = [];
  
  Object.keys(target).forEach(key => {
    const currentKey = prefix ? `${prefix}.${key}` : key;
    
    if (!(key in source)) {
      // Key exists in target but not in source, mark for deletion
      keysToDelete.push(key);
      console.log(`Marked extra key for removal: ${currentKey}`);
    } else if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
      // It's a nested object, recursively check it
      if (typeof target[key] === 'object' && target[key] !== null && !Array.isArray(target[key])) {
        target[key] = removeExtraKeys(source[key], target[key], currentKey);
      }
    }
  });
  
  // Remove the keys marked for deletion
  keysToDelete.forEach(key => {
    delete target[key];
  });
  
  return target;
}

/**
 * Synchronize language files
 * @param {string} sourceFile - Path to source language file
 * @param {string} targetFile - Path to target language file to update
 * @param {boolean} removeExtra - Whether to remove extra keys
 */
function synchronizeLanguageFiles(sourceFile, targetFile, removeExtra = false) {
  console.log(`Synchronizing ${targetFile} with ${sourceFile}`);
  
  // Read the JSON files
  const source = readJsonFile(sourceFile);
  const target = readJsonFile(targetFile);
  
  // Add missing keys
  const updatedTarget = addMissingKeys(source, {...target});
  
  // Remove extra keys if specified
  const finalTarget = removeExtra ? removeExtraKeys(source, updatedTarget) : updatedTarget;
  
  // Write the updated file
  writeJsonFile(targetFile, finalTarget);
}

/**
 * Synchronize all language files in a directory
 * @param {string} baseLanguage - Base language code (e.g., 'en')
 * @param {string[]} targetLanguages - List of target language codes (e.g., ['ar', 'fr'])
 * @param {boolean} removeExtra - Whether to remove extra keys
 */
function synchronizeAllLanguageFiles(baseLanguage = 'en', targetLanguages = ['ar'], removeExtra = false) {
  // Get all JSON files in the base language directory
  const baseDir = path.join(__dirname, baseLanguage);
  const files = fs.readdirSync(baseDir).filter(file => file.endsWith('.json'));
  
  console.log(`Found ${files.length} JSON files in ${baseDir}`);
  
  // Process each target language
  targetLanguages.forEach(language => {
    const targetDir = path.join(__dirname, language);
    
    // Create the target language directory if it doesn't exist
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log(`Created directory: ${targetDir}`);
    }
    
    // Process each file
    files.forEach(file => {
      const sourceFile = path.join(baseDir, file);
      const targetFile = path.join(targetDir, file);
      
      // Create the target file with empty content if it doesn't exist
      if (!fs.existsSync(targetFile)) {
        writeJsonFile(targetFile, {});
        console.log(`Created empty file: ${targetFile}`);
      }
      
      // Synchronize the file
      synchronizeLanguageFiles(sourceFile, targetFile, removeExtra);
    });
  });
}

// Function to synchronize a specific file
function synchronizeSpecificFile(file, baseLanguage = 'en', targetLanguage = 'ar', removeExtra = false) {
  const sourceFile = path.join(__dirname, baseLanguage, file);
  const targetFile = path.join(__dirname, targetLanguage, file);
  
  // Create the target file with empty content if it doesn't exist
  if (!fs.existsSync(targetFile)) {
    writeJsonFile(targetFile, {});
    console.log(`Created empty file: ${targetFile}`);
  }
  
  // Synchronize the file
  synchronizeLanguageFiles(sourceFile, targetFile, removeExtra);
}

// Examples of how to use the functions

// Synchronize admin.json - add this line for admin.json only
synchronizeSpecificFile('admin.json', 'en', 'ar', true);

// Uncomment this to synchronize all language files
// synchronizeAllLanguageFiles('en', ['ar'], false);
