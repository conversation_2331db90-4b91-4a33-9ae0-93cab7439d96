{"packages": {"headerTag": "扩展您的贸易网络！", "title": "E-exportcity 为您提供量身定制的套餐！", "description": "无论您是踏出第一步，还是希望在全球市场上发展，都可以使用 e-exportcity 灵活且目标明确的套餐实现业务数字化。", "mostPopular": "最受欢迎", "current": "当前套餐", "buyNow": "立即购买", "addonHeader": "附加套餐", "addonTitle": "根据您的需求提供的附加套餐", "addonDescription": "加强您当前的套餐，获得更多机会", "footer": {"title": "通过 E-exportcity 在新一代贸易平台上发展您的网络！", "description": "E-exportcity 作为土耳其领先的数字出口平台，为企业进入全球市场提供便利。凭借我们先进的技术基础设施、专业的出口顾问和全面的服务网络，我们提升了出口商在国际贸易中的竞争力。加入 e-exportcity 大家庭，利用数字世界提供的无限机会，在全球舞台上发展您的业务。", "features": {"global": "进入全球市场", "experts": "出口顾问", "support": "全天候客户支持", "technology": "先进技术基础设施"}}}, "titles": {"choosePackage": "选择套餐", "upgradePackage": "升级套餐", "standardPackages": "标准套餐", "addonPackages": "附加套餐", "packageSelection": "套餐选择", "allPackages": "所有套餐"}, "filters": {"all": "所有套餐", "standard": "标准套餐", "addon": "附加套餐", "allItems": "所有套餐"}, "package": {"name": "{{name}}", "price": "¥{{price}}", "type": {"standard": "标准套餐", "addon": "附加套餐"}, "features": {"viewRequests": {"title": "{{count}}次查看请求", "description": "您可以查看的请求数量"}, "createRequests": {"title": "{{count}}次创建请求", "description": "您可以创建的请求数量"}, "emailNotification": {"title": "电子邮件通知", "description": "接收电子邮件通知"}, "smsNotification": {"title": "短信通知", "description": "接收短信通知"}, "messagingAllowed": {"title": "消息功能", "description": "发送和接收消息"}, "yearEndSectorReport": {"title": "年终行业报告", "description": "访问详细的行业分析"}, "homePageAd": {"title": "首页广告", "description": "在首页显示广告"}, "languageIntroRights": {"title": "多语言支持", "description": "访问多语言选项"}}, "status": {"current": "当前套餐"}, "action": {"requiresStandard": "需要标准套餐", "select": "选择套餐", "upgrade": "升级套餐", "addToPackage": "添加到套餐"}, "alreadyPurchased": "您已拥有此套餐", "cannotDowngrade": "无法降级到较低套餐", "tooltips": {"viewRequests": "您可以查看的请求数量", "createRequests": "您可以创建的请求数量"}, "compare": "比较", "purchase": "立即购买", "perMonth": "/月", "loading": "正在加载套餐..."}, "payment": {"modal": {"title": "支付详情", "packageDetails": "套餐：{{name}}"}, "options": {"useSavedCard": "使用已保存的卡", "useNewCard": "使用新卡"}, "form": {"paymentMethod": "支付方式", "cardHolderName": "持卡人姓名", "cardHolderNamePlaceholder": "张三", "cardNumber": "卡号", "cardNumberPlaceholder": "XXXX XXXX XXXX XXXX", "expiryMonth": "到期月份", "expiryYear": "到期年份", "cvv": "CVV", "cvvPlaceholder": "123", "saveCard": "保存卡用于将来付款", "selectCard": "选择已保存的卡", "cardOption": "{{alias}} ({{type}}, **** {{lastFour}})", "cvc": "CVV", "cvcPlaceholder": "123", "cardAlias": "卡别名", "cardAliasPlaceholder": "为此卡输入名称", "select": "选择", "processing": "正在处理支付...", "payButton": "确认支付", "required": "<PERSON><PERSON> <PERSON><PERSON>", "invalidCardNumber": "Geçersiz kart numarası", "invalidCvc": "Geçersiz CVV kodu", "errors": {"selectCard": "Lütfen bir kart seçin.", "cardHolderNameRequired": "Kart sahibinin adı zorunludur.", "invalidCardNumber": "Lütfen geçerli bir kart numarası girin.", "expiryDateRequired": "<PERSON> kullanma tarihi z<PERSON>.", "cardExpired": "Kartın süresi dolmuş.", "invalidCVC": "Lütfen geçerli bir güvenlik kodu (CVV) girin.", "cardAliasRequired": "Lütfen kartınız için bir isim belirtin.", "cardSaveError": {"description": "Kart kaydedilemedi. Lütfen tekrar deneyin."}}}, "errors": {"selectPackage": "请选择套餐", "selectSavedCard": "请选择已保存的卡", "userOrPackageNotFound": "未找到用户或套餐", "noActiveStandardPackage": "没有有效的标准套餐，无法购买附加套餐"}, "messages": {"3dSecureInit": "3D安全初始化错误", "3dSecureError": "3D安全支付错误", "error": "支付错误", "success": "支付成功！您现在可以访问套餐功能。", "addonSuccess": "附加套餐购买成功！您的套餐功能已更新。", "standardSuccess": "套餐购买成功！您现在可以访问所有功能。", "processing": "正在处理您的付款...", "unexpectedError": "支付过程中发生意外错误", "missingToken": "支付验证令牌缺失", "verificationFailed": "支付验证失败。请重试或联系客服", "paymentNotFound": "未找到支付记录。请重试或联系客服", "failed": "支付失败。请检查您的卡信息并重试"}, "actions": {"close": "关闭"}, "info": "支付信息", "processing": "正在处理您的付款...", "pay": "支付", "payAmount": "支付¥{{amount}}", "details": {"title": "套餐详情"}, "initiationError": "启动支付时出错"}, "subscription": {"addonInfo": "附加套餐信息", "connectedToPackage": "连接到套餐", "remainingDays": "剩余天数", "days": "天", "originalPrice": "原价", "proRatedPrice": "按比例计算价格", "currentValue": "当前套餐价值", "newPackagePrice": "新套餐价格", "upgradePrice": "升级价格", "currentPackage": "当前套餐", "noSubscription": "无订阅", "noSubscriptionMessage": "您目前没有活跃的订阅"}, "upgradeDetails": {"title": "升级详情", "description": "选择最适合您需求的套餐", "loading": "正在加载套餐...", "error": "错误", "fetchError": "加载套餐时出错", "package": {"notAvailable": "<PERSON><PERSON><PERSON>", "cannotDowngrade": "Daha düşük bir pakete geçiş yapamazsınız", "requiresStandard": "<PERSON><PERSON>", "select": "Paketi Seç", "upgrade": "Paketi Yükselt", "addToPackage": "<PERSON><PERSON>", "alreadyPurchased": "Bu pakete zaten sa<PERSON>iniz", "features": "<PERSON><PERSON><PERSON><PERSON>", "current": "Mev<PERSON>"}, "payment": {"modal": {"title": "支付详情", "packageDetails": "套餐：{{name}}"}, "options": {"useSavedCard": "使用已保存的卡", "useNewCard": "使用新卡"}, "form": {"cardHolderName": "持卡人姓名", "cardHolderNamePlaceholder": "张三", "cardNumber": "卡号", "cardNumberPlaceholder": "XXXX XXXX XXXX XXXX", "expiryMonth": "到期月份", "expiryYear": "到期年份", "cvv": "CVV", "cvvPlaceholder": "123", "saveCard": "保存卡用于将来付款", "selectCard": "选择已保存的卡", "cardOption": "{{alias}} ({{type}}, **** {{lastFour}})"}, "processing": "处理支付中...", "pay": "支付", "payAmount": "支付¥{{amount}}"}, "subscription": {"currentPackage": "当前套餐", "addonInfo": "附加套餐信息", "connectedToPackage": "连接到套餐", "remainingDays": "剩余天数", "days": "天", "originalPrice": "原价", "proRatedPrice": "按比例计算价格", "currentValue": "当前套餐价值", "newPackagePrice": "新套餐价格", "upgradePrice": "升级价格"}}, "designPackages": {"badge": "Profesyonel Tasarım <PERSON>i", "title": "Markanızı Öne Çıkaracak Tasarım Paketleri", "subtitle": "İhracat yolculuğunuzda markanızı global pazarda güçlendirecek profesyonel tasarım çözümleriyle yanınızdayız.", "mostPopular": "En Çok Tercih Edilen", "oneTime": "/tek seferlik", "purchase": "Satın Al", "notes": "Notlar", "notesTitle": "Notlar", "buyNow": "Satın Al", "notesPlaceholder": "Projenizle ilgili özel isteklerinizi belirtebilirsiniz", "whyUs": {"title": "Neden Bizimle Çalışmalısınız?", "subtitle": "Profesyonel tasarım ekibimiz ve deneyimimizle markanızı bir adım öne taşıyoruz"}, "features": {"original": {"title": "<PERSON>zg<PERSON><PERSON>", "description": "Her marka i<PERSON>in <PERSON> o<PERSON>, <PERSON>zgün ve akılda kalıcı çözümler"}, "fast": {"title": "Hızlı Teslimat", "description": "Projeleriniz için özenle belirlenen zaman planına uygun teslimat"}, "source": {"title": "<PERSON><PERSON><PERSON>", "description": "Tüm tasarımların kaynak dosyaları ile birlikte teslimat"}, "responsive": {"title": "Responsive Tasarım", "description": "Tüm cihazlarda kusursuz görüntülenen modern tasarımlar"}}}, "system": {"error": "系统错误", "backToDashboard": "返回仪表板", "backToPackages": "返回套餐"}, "error": {"fetchPackages": "Paketler yüklenirken hata o<PERSON>ş<PERSON>", "tryAgain": "<PERSON><PERSON><PERSON><PERSON> daha sonra tekrar deneyin"}, "fetchError": "获取订阅详情失败", "fetchPackagesError": "获取套餐失败", "paymentSuccess": "支付成功", "paymentFailed": "支付失败", "loading": "正在加载套餐...", "checkSubscriptionError": "检查订阅状态时出错"}