{"selectTypeAndMode": "选择类型和模式", "search": {"placeholder": "搜索产品或服务..."}, "sort": {"newest": "最新优先", "oldest": "最早优先", "mostViewed": "浏览最多", "leastViewed": "浏览最少"}, "filters": {"button": "筛选"}, "clearFilters": "清除筛选", "all": "全部", "location": {"unknown": "未知位置", "label": "位置"}, "mode": "模式", "store": "商店", "categories": "类别", "page": {"description": "全球出口贸易中的热门产品和服务"}, "description": "描述", "addProduct": "添加产品", "addService": "添加服务", "requestProduct": "请求产品", "requestService": "请求服务", "addProductDesc": "创建新产品列表以供销售", "addServiceDesc": "创建新服务列表以供提供", "requestProductDesc": "请求您正在寻找的产品", "requestServiceDesc": "请求您需要的服务", "basicInfo": "基本信息", "title": "产品和服务", "createItemRequest": "创建商品请求", "editItemRequest": "编辑商品请求", "editItem": "编辑商品", "name": "名称", "enterName": "输入名称", "type": "类型", "product": "产品", "service": "服务", "category": "类别", "selectCategory": "选择类别", "listingType": "清单类型", "sale": "销售", "demand": "需求", "enterDescription": "输入描述", "htmlDescription": "HTML描述", "images": "图片", "uploadImages": "上传图片", "image": "图片", "preview": "预览", "removeImage": "删除图片", "noImagesUploaded": "未上传图片", "cancel": "取消", "update": "更新", "create": "创建", "updating": "更新中...", "creating": "创建中...", "errorFetchingCategories": "获取类别时出错", "pleaseCompleteAllFields": "请填写所有必填字段", "errorUploadingFiles": "上传文件时出错", "successMessages": {"itemRequestUpdated": "商品请求已成功更新", "itemRequestCreated": "商品请求已成功创建"}, "errorSavingItemRequest": "保存商品请求时出错", "placeholders": {"productName": "输入产品名称", "serviceName": "输入服务名称", "productDescription": "详细描述您的产品", "serviceDescription": "详细描述您的服务", "productRequestDescription": "描述您正在寻找的产品", "serviceRequestDescription": "描述您需要的服务", "requestTitle": "输入请求标题"}, "categoryLevels": {"main": "主要", "level": "级别 {{level}}", "select": "选择{{level}}类别"}, "types": {"product": "产品", "service": "服务"}, "item": {"price": "${{price}}", "category": "类别", "viewDetails": "查看详情", "noItems": "未找到商品"}, "errors": {"loadData": {"title": "加载错误", "description": "加载商品失败。请稍后重试。"}, "loadCategoryItems": {"title": "加载错误", "description": "加载类别商品失败。请稍后重试。"}, "loadSubcategories": {"title": "加载错误", "description": "加载子类别失败。请稍后重试。"}, "noCreateRequests": {"title": "没有剩余请求", "description": "您没有剩余的创建请求。请升级您的套餐。"}, "upgradeRequired": {"title": "需要升级", "description": "请升级您的套餐以创建更多商品。"}, "createFailed": {"title": "创建错误", "description": "创建商品失败。请稍后重试。"}, "updateFailed": {"title": "更新错误", "description": "更新商品失败。请稍后重试。"}, "deleteFailed": {"title": "删除错误", "description": "删除商品失败。请稍后重试。"}, "adCreateFailed": {"title": "广告创建错误", "description": "创建广告失败。请稍后重试。"}, "tooManyFiles": {"title": "文件过多", "description": "您已超过允许的最大文件数量。请选择较少的文件。"}, "maxFiveFiles": {"title": "超出文件限制", "description": "最多允许5个文件。"}, "validation": {"title": "验证错误", "description": "请修复表单中的错误并重试。"}, "categoryRequired": {"title": "需要类别", "description": "请选择一个类别。"}, "subcategoryLoad": {"title": "加载错误", "description": "加载子类别失败。请稍后重试。"}, "categoryLoad": {"title": "加载错误", "description": "加载类别失败。请稍后重试。"}, "tryAgain": {"title": "错误", "description": "请重试。"}, "imageUpload": {"title": "图片上传错误", "description": "上传图片失败。请稍后重试。"}, "missingImage": {"title": "需要图片", "description": "请至少上传一张图片。"}, "pleaseSelectImage": {"title": "需要图片", "description": "请为广告选择一张图片。"}, "itemLoadFailed": {"title": "加载错误", "description": "无法加载商品数据。"}, "itemSaveFailed": {"title": "保存错误", "description": "无法保存商品数据。"}}, "success": {"itemCreated": "商品已成功创建", "itemUpdated": "商品已成功更新", "itemDeleted": "商品已成功删除", "adCreated": {"title": "成功", "description": "广告已成功创建。"}}, "buttons": {"createItem": "创建新商品", "edit": "编辑", "delete": "删除", "create": "创建", "updateItem": "更新商品", "publishProduct": "发布产品", "publishService": "发布服务", "publishRequest": "发布请求", "uploadImages": "上传图片", "save": "保存", "cancel": "取消", "confirm": "确认", "promote": "推广", "upgrade": "查看套餐", "message": "消息"}, "fields": {"name": "名称", "description": "描述", "productName": "产品名称", "serviceName": "服务名称", "productDescription": "产品描述", "serviceDescription": "服务描述", "requestDetails": "请求详情", "requestTitle": "请求标题", "price": "价格", "images": "图片", "imageHelp": "", "category": "类别", "adTitle": "广告标题", "adTitleHelper": "输入一个吸引人的广告标题", "adImage": "广告图片", "adImageHelper": "图片必须小于2MB，格式为JPG、PNG或GIF"}, "modals": {"create": {"title": "创建新商品", "success": "商品创建成功"}, "edit": {"title": "编辑商品", "success": "商品更新成功"}, "message": {"title": "发送消息"}}, "dialogs": {"delete": {"title": "删除商品", "message": "您确定要删除此商品吗？此操作无法撤消。", "success": "商品删除成功"}, "viewRequest": {"title": "使用查看请求", "message": "您想使用一个查看请求来查看此商品吗？", "confirm": "是的，查看商品", "cancel": "否，取消", "remaining": "剩余"}, "homeAd": {"title": "创建首页广告", "message": "您想在首页推广此商品{{duration}}天吗？", "success": "广告创建成功"}, "upgrade": {"title": "升级您的套餐", "message": "升级到带有首页广告功能的套餐，在首页推广您的商品并接触更多客户！", "benefits": {"title": "首页广告的优势：", "visibility": "提高产品可见度", "traffic": "增加您的商品流量", "sales": "提高销售机会"}}}, "seeDetails": "查看详情", "myItemsTitle": "我的商品", "products": "产品", "services": "服务", "allCategories": "所有类别", "pendingRequests": "待处理请求", "approvedItems": "已批准商品", "cannotEditApprovedItem": "无法编辑已批准的商品", "itemRequestDeleted": "商品请求已删除", "itemDeleted": "商品已删除", "errorDeletingItemRequest": "删除商品请求时出错", "deleteItemRequestConfirmation": "您确定要删除此商品请求吗？", "selectType": "选择类型", "mainCategory": "主类别", "subCategory": "子类别 {{level}}", "noItemsFound": "未找到商品", "loading": "加载中...", "errorFetchingItems": "获取商品时出错", "createNewRequest": "创建新请求", "deleteItemRequest": "删除商品请求", "whatsapp": {"contact": "通过WhatsApp联系", "message": "您好，我对您的商品感兴趣：{{itemName}}"}, "language": {"english": "英语", "turkish": "土耳其语", "german": "德语", "select": "选择语言"}}