{"selectTypeAndMode": "Выберите тип и режим", "search": {"placeholder": "Поиск товаров или услуг..."}, "sort": {"newest": "Сначала новые", "oldest": "Сначала старые", "mostViewed": "Самые просматриваемые", "leastViewed": "Наименее просматриваемые"}, "filters": {"button": "Фильтры"}, "clearFilters": "Очистить фильтры", "all": "Все", "location": {"unknown": "Неизвестное местоположение", "label": "Местоположение"}, "mode": "Режим", "store": "Мага<PERSON>ин", "categories": "Категории", "page": {"description": "Мировые трендовые продукты и услуги в экспортной торговле"}, "addProduct": "Добавить продукт", "addService": "Добавить услугу", "requestProduct": "Запросить продукт", "requestService": "Запросить услугу", "addProductDesc": "Создайте новый список продуктов для продажи", "addServiceDesc": "Создайте новый список услуг для предложения", "requestProductDesc": "Запросить продукт, который вы ищете", "requestServiceDesc": "Запросить услугу, которая вам нужна", "basicInfo": "Основная информация", "title": "Продукты и Услуги", "description": "Описание", "categoryLevels": {"main": "Основной", "level": "Уровень {{level}}", "select": "Выберите категорию {{level}}"}, "types": {"product": "<PERSON><PERSON><PERSON><PERSON>", "service": "Услуга"}, "item": {"price": "${{price}}", "category": "Категория", "viewDetails": "Подробнее", "noItems": "Товары не найдены"}, "errors": {"loadData": {"title": "Ошибка загрузки", "description": "Не удалось загрузить товары. Пожалуйста, попробуйте позже."}, "loadCategoryItems": {"title": "Ошибка загрузки", "description": "Не удалось загрузить товары категории. Пожалуйста, попробуйте позже."}, "loadSubcategories": {"title": "Ошибка загрузки", "description": "Не удалось загрузить подкатегории. Пожалуйста, попробуйте позже."}, "noCreateRequests": {"title": "Нет запро<PERSON>ов", "description": "У вас не осталось запросов на создание. Пожалуйста, обновите ваш план."}, "upgradeRequired": {"title": "Требуется обновление", "description": "Пожалуйста, обновите пакет для создания большего количества товаров."}, "createFailed": {"title": "Ошибка создания", "description": "Не удалось создать товар. Пожалуйста, попробуйте позже."}, "updateFailed": {"title": "Ошибка обновления", "description": "Не удалось обновить товар. Пожалуйста, попробуйте позже."}, "deleteFailed": {"title": "Ошибка удаления", "description": "Не удалось удалить товар. Пожалуйста, попробуйте позже."}, "adCreateFailed": {"title": "Ошибка создания рекламы", "description": "Не удалось создать рекламу. Пожалуйста, попробуйте позже."}, "tooManyFiles": {"title": "Слишком много файлов", "description": "Вы превысили максимальное количество разрешенных файлов. Пожалуйста, выберите меньше файлов."}, "maxFiveFiles": {"title": "Превышен лимит файлов", "description": "Максимум 5 файлов разрешено."}, "validation": {"title": "Ошибка валидации", "description": "Пожалуйста, исправьте ошибки в форме и попробуйте снова."}, "categoryRequired": {"title": "Требуется категория", "description": "Пожалуйста, выберите категорию."}, "subcategoryLoad": {"title": "Ошибка загрузки", "description": "Не удалось загрузить подкатегории. Пожалуйста, попробуйте позже."}, "categoryLoad": {"title": "Ошибка загрузки", "description": "Не удалось загрузить категории. Пожалуйста, попробуйте позже."}, "tryAgain": {"title": "Ошибка", "description": "Пожалуйста, попробуйте снова."}, "imageUpload": {"title": "Ошибка загрузки изображений", "description": "Не удалось загрузить изображения. Пожалуйста, попробуйте позже."}, "missingImage": {"title": "Требуется изображение", "description": "Пожалуйста, загрузите хотя бы одно изображение."}, "pleaseSelectImage": {"title": "Требуется изображение", "description": "Пожалуйста, выберите изображение для рекламы."}, "itemLoadFailed": {"title": "Ошибка загрузки", "description": "Не удалось загрузить данные товара."}, "itemSaveFailed": {"title": "Ошибка сохранения", "description": "Не удалось сохранить данные товара."}}, "success": {"itemCreated": "Товар успешно создан", "itemUpdated": "Товар успешно обновлен", "itemDeleted": "Товар успешно удален", "adCreated": {"title": "Успех", "description": "Реклама успешно создана."}}, "buttons": {"createItem": "Создать новый товар", "edit": "Редактировать", "delete": "Удалить", "create": "Создать", "updateItem": "Обновить товар", "publishProduct": "Опубликовать продукт", "publishService": "Опубликовать услугу", "publishRequest": "Опубликовать запрос", "uploadImages": "Загрузить изображения", "save": "Сохранить", "cancel": "Отмена", "confirm": "Подтвердить", "promote": "Продвигать", "upgrade": "Просмотр пакетов", "message": "Сообщение"}, "fields": {"name": "Название", "description": "Описание", "productName": "Название продукта", "serviceName": "Название услуги", "productDescription": "Описание продукта", "serviceDescription": "Описание услуги", "requestDetails": "Детали запроса", "requestTitle": "Заголовок запроса", "price": "Цена", "images": "Изображения", "imageHelp": "", "category": "Категория", "adTitle": "Заголовок рекламы", "adTitleHelper": "Введите привлекательный заголовок для вашей рекламы", "adImage": "Изображение рекламы", "adImageHelper": "Изображение должно быть менее 2МБ и в формате JPG, PNG или GIF"}, "modals": {"create": {"title": "Создать новый товар", "success": "Товар успешно создан"}, "edit": {"title": "Редактировать товар", "success": "Товар успешно обновлен"}, "message": {"title": "Отправить сообщение"}}, "dialogs": {"delete": {"title": "Удалить товар", "message": "Вы уверены, что хотите удалить этот товар? Это действие нельзя отменить.", "success": "Товар успешно удален"}, "viewRequest": {"title": "Использовать запрос на просмотр", "message": "Хотите использовать один из ваших запросов на просмотр для этого товара?", "confirm": "Да, посмотреть товар", "cancel": "Нет, отмена", "remaining": "осталось"}, "homeAd": {"title": "Создать рекламу на главной странице", "message": "Хотите продвигать этот товар на главной странице в течение {{duration}} дней?", "success": "Реклама успешно создана"}, "upgrade": {"title": "Обновить ваш пакет", "message": "Продвигайте свои товары на главной странице и получайте больше клиентов, обновив пакет с функцией рекламы на главной странице!", "benefits": {"title": "Преимущества рекламы на главной странице:", "visibility": "Повышенная видимость ваших товаров", "traffic": "Больше трафика на ваши объявления", "sales": "Выше шанс продаж"}}}, "type": "Тип", "selectType": "Выберите тип", "product": "<PERSON><PERSON><PERSON><PERSON>", "service": "Услуга", "placeholders": {"productName": "Введите название продукта", "serviceName": "Введите название услуги", "productDescription": "Опишите ваш продукт подробно", "serviceDescription": "Опишите вашу услугу подробно", "productRequestDescription": "Опишите продукт, который вы ищете", "serviceRequestDescription": "Опишите услугу, которая вам нужна", "requestTitle": "Введите заголовок запроса"}, "mainCategory": "Основная категория", "subCategory": "Подкатегория {{level}}", "selectCategory": "Выберите категорию", "noItemsFound": "Товары не найдены", "loading": "Загрузка...", "createItemRequest": "Создать запрос товара", "editItemRequest": "Изменить запрос товара", "editItem": "Редактировать товар", "enterName": "Введите название", "enterDescription": "Введите описание", "htmlDescription": "HTML-описание", "uploadImages": "Загрузить изображения", "image": "Изображение", "images": "Изображения", "preview": "Предпросмотр", "removeImage": "Удалить изображение", "noImagesUploaded": "Нет загруженных изображений", "update": "Обновить", "updating": "Обновление...", "creating": "Создание...", "errorFetchingCategories": "Ошибка при получении категорий", "pleaseCompleteAllFields": "Пожалуйста, заполните все обязательные поля", "errorUploadingFiles": "Ошибка при загрузке файлов", "successMessages": {"itemRequestUpdated": "Запрос на товар успешно обновлен", "itemRequestCreated": "Запрос на товар успешно создан"}, "errorSavingItemRequest": "Ошибка при сохранении запроса на товар", "listingType": "Тип листинга", "sale": "Продажа", "demand": "Спрос", "name": "Название", "category": "Категория", "cancel": "Отмена", "create": "Создать", "seeDetails": "Посмотреть детали", "myItemsTitle": "Мои товары", "products": "Товары", "services": "Услуги", "allCategories": "Все категории", "pendingRequests": "Ожидающие запросы", "approvedItems": "Одобренные товары", "cannotEditApprovedItem": "Невозможно редактировать одобренный товар", "itemRequestDeleted": "Запрос на товар удален", "itemDeleted": "Товар удален", "errorDeletingItemRequest": "Ошибка при удалении запроса на товар", "deleteItemRequestConfirmation": "Вы уверены, что хотите удалить этот запрос на товар?", "errorFetchingItems": "Ошибка при получении товаров", "itemLoadFailed": "Не удалось загрузить данные товара", "itemSaveFailed": "Не удалось сохранить данные товара", "createNewRequest": "Создать новый запрос", "deleteItemRequest": "Удалить запрос на товар", "whatsapp": {"contact": "Связаться через WhatsApp", "message": "Здравствуйте, я заинтересован в вашем товаре: {{itemName}}"}, "language": {"english": "Английский", "turkish": "Турецкий", "german": "Немецкий", "select": "Выбрать язык"}}