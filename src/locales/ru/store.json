{"notFound": "Компания не найдена", "notFoundDesc": "Искомая компания не существует.", "products": "Продукты", "noProducts": "Продукты не найдены", "details": {"title": "Информация о Компании", "description": "Описание Компании", "contact": "Контактная Информация", "address": "Ад<PERSON><PERSON>с Компании", "items": "Продукты Компании", "noItems": "Продукты для этой компании не найдены"}, "contact": "Контактная информация", "fetchFailed": {"title": "Ошибка Загрузки Данных", "description": "Не удалось получить данные компании. Пожалуйста, попробуйте позже."}, "title": "Компании", "description": "Ваши надежные деловые партнеры в мировой торговле", "viewStore": "Просмотреть профиль компании", "viewProfile": "Просмотреть профиль компании", "noCoverImage": "Нет изображения обложки", "storeOwner": "{{firstName}} {{lastName}}", "coverImage": "Изображение обложки {{storeName}}", "storeProducts": "Продукты компании", "productImage": "Изображение продукта {{productName}}", "itemTypes": {"product": "<PERSON><PERSON><PERSON><PERSON>", "service": "Услуга"}, "storeType": "Тип Компании", "types": {"company": "Компания", "broker": "Брокер"}, "storeCard": {"noLocation": "Местоположение не указано", "noDescription": "Описание отсутствует", "views": "просмотров", "viewProfile": "Просмотреть профиль"}, "search": {"placeholder": "Поиск компаний..."}, "filters": {"button": "Фильтры", "sectorLabel": "Сектор", "locationLabel": "Местоположение", "clear": "Очистить фильтры", "sectors": {"all": "Все секторы", "technology": "Технологии", "trade": "Торговля", "health": "Здравоохранение", "construction": "Строительство", "agriculture": "Сельское хозяйство"}, "locations": {"all": "Все местоположения", "istanbul": "Стамбул", "izmir": "Измир", "ankara": "Анкара", "bursa": "Бурса", "antalya": "Анталья"}, "sort": {"newest": "Новейшие", "oldest": "Старейшие", "mostViewed": "Наиболее просматриваемые", "leastViewed": "Наименее просматриваемые"}}, "noStores": {"title": "Компании не найдены", "message": "Нет компаний, соответствующих вашим критериям поиска. Попробуйте изменить фильтры или поисковый запрос."}, "errors": {"title": "Ошибка загрузки компаний", "fetchFailed": "Не удалось загрузить компании. Пожалуйста, попробуйте позже.", "retry": "Повторить попытку", "storeNotFound": {"title": "Компания не найдена", "description": "Компания, которую вы ищете, не может быть найдена или была удалена."}}, "list": {"title": "Компании", "noStores": "Компании не найдены", "search": "Поиск Компаний", "filter": "Фильтр Компаний"}}