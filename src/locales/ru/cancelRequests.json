{"title": "Запрос на отмену подписки", "reason": "Причина отмены", "submit": "Отправить запрос", "cancel": "Отмена", "success": {"title": "Успешно", "description": "Запрос на отмену успешно отправлен"}, "error": {"title": "Ошибка", "description": "Ошибка при отправке запроса на отмену. Пожалуйста, попробуйте позже."}, "reasonPlaceholder": "Пожалуйста, объясните, почему вы хотите отменить подписку", "confirmationTitle": "Подтверждение запроса на отмену", "confirmationMessage": "Вы уверены, что хотите запросить отмену подписки? Этот запрос будет рассмотрен нашей командой администраторов.", "revertRequest": "Отменить запрос на отмену", "revertSuccess": {"title": "Успешно", "description": "Запрос на отмену успешно отменен"}, "revertError": {"title": "Ошибка", "description": "Ошибка при отмене запроса. Пожалуйста, попробуйте позже."}, "status": {"pending": "На рассмотрении", "approved": "Одобрено", "rejected": "Отклонено", "reverted": "Отменено пользователем"}, "admin": {"title": "Запросы на отмену подписки", "noRequests": "Запросов на отмену не найдено", "approve": "Одобрить", "reject": "Отклонить", "requestDate": "Дата запроса", "status": "Статус", "user": "Пользователь", "package": "Пак<PERSON>т", "actions": "Действия", "viewReason": "Просмотреть причину"}}