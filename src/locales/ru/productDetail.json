{"title": "Детали продукта", "types": {"product": "<PERSON><PERSON><PERSON><PERSON>", "service": "Услуга"}, "detailedDescription": "Подробное описание", "buttons": {"contactSeller": "Связаться с продавцом", "back": "Вернуться к списку", "message": "Отправить сообщение", "cancel": "Отмена", "upgrade": "Улучшить"}, "modals": {"message": {"title": "Отправить сообщение"}}, "dialogs": {"viewRequest": {"title": "Просмотр товара", "message": "Хотите использовать запрос на просмотр для этого товара?", "remaining": "осталось просмотров", "confirm": "Просмотреть товар", "cancel": "Отмена"}, "upgrade": {"title": "Требуется улучшение", "message": "Для отправки сообщений необходимо улучшить подписку.", "confirm": "Улучшить сейчас", "cancel": "Отмена", "messageFeature": "Функция сообщений"}}, "errors": {"accessDenied": {"title": "Доступ Запрещен", "description": "У вас нет разрешения на доступ к этому элементу."}, "requiresViewRequest": {"title": "Требуется Запрос на Просмотр", "description": "Для просмотра этого товара необходимо использовать запрос на просмотр."}, "noViewRequests": {"title": "Нет Запросов на Просмотр", "description": "У вас не осталось запросов на просмотр. Пожалуйста, улучшите свой пакет."}, "fetchFailed": {"title": "Ошибка Загрузки", "description": "Не удалось загрузить продукт. Пожалуйста, попробуйте позже."}, "viewFailed": {"title": "Ошибка Просмотра", "description": "Не удалось просмотреть продукт. Пожалуйста, попробуйте позже."}, "itemNotFound": {"title": "Продукт не Найден", "description": "Продукт, который вы ищете, не существует или был удален."}, "tryAgain": {"title": "Ошибка", "description": "Пожалуйста, попробуйте позже."}, "upgradeRequired": {"title": "Требуется Улучшение", "description": "Пожалуйста, улучшите подписку для просмотра большего количества продуктов."}, "noSubscription": {"title": "Нет Активной Подписки", "description": "У вас нет активной подписки. Пожалуйста, приобретите пакет для доступа к этой функции."}}, "loading": "Загрузка...", "noImage": "Изображение отсутствует", "details": "Детали продукта", "item": {"category": "Категория", "type": "Тип", "listingType": "Тип объявления"}, "notSpecified": "Не указано", "sale": "Продажа", "demand": "Спрос", "contact": {"title": "Контактная информация", "seller": "Продавец", "phone": "Телефон", "email": "Электронная почта"}}