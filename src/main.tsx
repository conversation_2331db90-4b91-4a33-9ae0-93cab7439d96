import './i18n'; // Initialize i18next
import React from "react";
import ReactDOM from "react-dom/client";
import { ChakraProvider } from "@chakra-ui/react";
import { Provider } from "react-redux";
import { BrowserRouter, useLocation } from "react-router-dom";
import App from "./App";
import store from "./redux/store";
import theme from "./theme";
import { SocketProvider } from "./context/SocketContext";
import { NotificationProvider } from "./context/NotificationContext";
import { AuthProvider } from "./context/AuthContext";

// Import global styles
import "./globals.css";

// Define ConditionalNotificationProvider as a proper component
const ConditionalNotificationProvider: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith("/admin");

  if (isAdminRoute) {
    return <>{children}</>;
  }

  return <NotificationProvider>{children}</NotificationProvider>;
};

// Create a root component that combines everything
const AppWithProviders: React.FC = () => {
  return (
    <ChakraProvider theme={theme} resetCSS={false}>
      <Provider store={store}>
        <SocketProvider>
          <BrowserRouter>
            <AuthProvider>
              <ConditionalNotificationProvider>
                <App />
              </ConditionalNotificationProvider>
            </AuthProvider>
          </BrowserRouter>
        </SocketProvider>
      </Provider>
    </ChakraProvider>
  );
};

const root = ReactDOM.createRoot(
  document.getElementById("root") as HTMLElement,
);

root.render(
  <React.StrictMode>
    <AppWithProviders />
  </React.StrictMode>,
);
