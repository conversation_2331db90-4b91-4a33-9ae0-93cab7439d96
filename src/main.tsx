import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON>rowserRouter } from 'react-router-dom';
import App from './App';
import './index.css';

const root = document.getElementById('root');

if (!root) {
  throw new Error('Root element not found');
}

createRoot(root).render(
  <StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </StrictMode>
);