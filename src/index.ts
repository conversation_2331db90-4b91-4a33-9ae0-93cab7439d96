import express from 'express';
import http from 'http';
import dotenv from 'dotenv';
dotenv.config();
import cors from 'cors';
import cookieParser from 'cookie-parser';
import morgan from 'morgan';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import authRoutes from './routes/authRoutes';
import userRoutes from './routes/userRoutes';
import itemRoutes from './routes/itemRoutes';
import publicCategoryRoutes from './routes/publicCategoryRoutes';
import adminRoutes from './routes/adminRoutes';
import messageRoutes from './routes/messageRoutes';
import reviewRoutes from './routes/reviewRoutes';
import subscriptionRoutes from './routes/subscriptionRoutes';
import packageRoutes from './routes/packageRoutes';
import fs from 'fs';
import path from 'path';
import { startCronJobs, initCronJobs } from './services/cronService';
import { initAutoRenewalJob } from './services/autoRenewalService';
import uploadRoutes from './routes/uploadRoutes';

// Import Socket Handler
import { handleSocketConnection } from './socketHandler';
import notificationRoutes from './routes/notificationRoutes';
import homeAdRoutes from './routes/homeAdRoutes';
import cardRoutes from './routes/cardRoutes';
import storeRoutes from './routes/storeRoutes';
import representativeRoutes from './routes/representativeRoutes';
import ticketRoutes from './routes/ticketRoutes';
import sliderRoutes from './routes/sliderRoutes';
import liveChatRoutes from './routes/liveChatRoutes';
import designPackageRoutes from './routes/designPackageRoutes';
import connectDB from './config/database';
connectDB();

const app = express();

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir);
}

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir);
}

// Create write streams for logs
const accessLogStream = fs.createWriteStream(path.join(logsDir, 'access.log'), { flags: 'a' });
const errorLogStream = fs.createWriteStream(path.join(logsDir, 'error.log'), { flags: 'a' });

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Define custom token first
morgan.token('status-colored', (req: any, res: any) => {
  const status = res.statusCode;
  const color = status >= 500 ? 31 // red
    : status >= 400 ? 33 // yellow
    : status >= 300 ? 36 // cyan
    : 32; // green
  return `\x1b[${color}m${status}\x1b[0m`;
});

app.use(morgan((tokens:any, req, res) => {
  return [
    tokens.method(req, res),
    tokens.url(req, res),
    tokens['status-colored'](req, res),
    `${tokens['response-time'](req, res)}ms`
  ].join(' ');
}, {
  skip: (req) => req.url.startsWith('/socket.io'),
  stream: process.stdout
}));

// Serve static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// Request and Response logging middleware
app.use((req: express.Request, res: express.Response, next: express.NextFunction) => {
  const requestLog = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    headers: req.headers,
    body: req.body,
    query: req.query,
    params: req.params
  };

  fs.appendFile(
    path.join(logsDir, 'requests.log'),
    JSON.stringify(requestLog) + '\n',
    (err) => {
      if (err) console.error('Error writing to requests log:', err);
    }
  );

  // Capture and log response
  const oldSend = res.send;
  res.send = function (data) {
    const responseLog = {
      timestamp: new Date().toISOString(),
      requestUrl: req.url,
      requestMethod: req.method,
      statusCode: res.statusCode,
      response: data
    };

    fs.appendFile(
      path.join(logsDir, 'responses.log'),
      JSON.stringify(responseLog) + '\n',
      (err) => {
        if (err) console.error('Error writing to responses log:', err);
      }
    );

    return oldSend.apply(res, arguments as any);
  };

  next();
});

// Error logging middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errorLog = {
    timestamp: new Date().toISOString(),
    error: {
      message: err.message,
      stack: err.stack
    },
    request: {
      path: req.path,
      method: req.method,
      body: req.body,
      query: req.query,
      params: req.params
    }
  };

  fs.appendFile(
    path.join(logsDir, 'error.log'),
    JSON.stringify(errorLog) + '\n',
    (err) => {
      if (err) console.error('Error writing to error log:', err);
    }
  );

  next(err);
});

// CORS setup
app.use(
  cors({
    origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000', 'https://e-exportcity.com', 'https://www.e-exportcity.com'], // Allow specific origins including www subdomain
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Language'],
    optionsSuccessStatus: 200 // Some legacy browsers (IE11) choke on 204
  })
);

// Add explicit preflight handler for Socket.IO
app.options('/socket.io/*', cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000', 'https://e-exportcity.com', 'https://www.e-exportcity.com'],
  credentials: true,
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept-Language']
}));

// Swagger setup
const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'E-Exportcity Platform API',
      version: '1.0.0',
      description: 'API documentation for the E-Exportcity Platform',
    },
    servers: [
      {
        url: `http://localhost:${process.env.PORT || 5000}`,
      },
    ],
  },
  apis: ['./src/routes/*.ts'],
};

// Conditionally enable Swagger in development
if (process.env.NODE_ENV === 'development') {
  const swaggerSpec = swaggerJsdoc(options);
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
}

// Create HTTP server
const server = http.createServer(app);

// Initialize Socket.io
console.log('Initializing Socket.io server...');

// Create a single Socket.io instance with enhanced configuration
import { initializeSocket } from './services/socketService';
export const io = initializeSocket(server);

// Set socket configuration once
io.engine.on("connection_error", (err: any) => {
  console.log("Socket connection error:", err.req?.url, err.code, err.message, err.context);
});

// Socket.IO setup
handleSocketConnection(io);

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/items', itemRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/messages', messageRoutes);
app.use('/api/reviews', reviewRoutes);
app.use('/api/packages', packageRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/home-ads', homeAdRoutes);
app.use('/api/cards', cardRoutes);
app.use('/api/stores', storeRoutes);
app.use('/api/tickets', ticketRoutes);
app.use('/api/representatives', representativeRoutes)
app.use('/api/upload', uploadRoutes);
app.use('/api/public/categories', publicCategoryRoutes);
app.use('/api/sliders', sliderRoutes);
app.use('/api/live-chat', liveChatRoutes);
app.use('/api', designPackageRoutes);

// Start cron jobs for the application
startCronJobs();
initCronJobs();
// Start auto-renewal job to handle subscription renewals
initAutoRenewalJob();

// Start the server
const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Socket.io server available at ${process.env.CLIENT_URL || '*'}`);
  console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
});