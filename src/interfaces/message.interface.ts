import { Types } from 'mongoose';

export interface IMessage {
  _id?: string | Types.ObjectId;
  content: string;
  senderId: string | Types.ObjectId;
  recipientId: string | Types.ObjectId;
  productId: string | Types.ObjectId;
  roomId: string;
  read?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface IMessageResponse extends IMessage {
  sender?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  recipient?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  product?: {
    _id: string;
    name: string;
    images: string[];
  };
}

export interface IMessageSocketData {
  content: string;
  senderId: string;
  recipientId: string;
  roomId: string;
  productId: string;
  senderName?: string;
}

export interface ITypingData {
  userId: string;
  roomId: string;
  userName: string;
}
