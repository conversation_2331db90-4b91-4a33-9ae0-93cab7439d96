import { DefaultEventsMap, Server, Socket } from 'socket.io';
import { LiveChat } from '../models/LiveChat';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

interface ChatJoinData {
  chatId: string;
  anonymousId?: string;
}

interface ChatMessageData {
  chatId: string;
  content: string;
  anonymousId?: string;
}

export const setupLiveChatHandlers = (io: Server<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap>, socket: Socket) => {
  const userId = socket.data.user?._id || socket.data.user?.id;
  const isAdmin = socket.data?.user?.isAdmin || socket.data?.user?.role === 'admin';

  console.log('Live chat handlers setup', { 
    socketId: socket.id, 
    userId,
    userData: JSON.stringify(socket.data?.user || {}),
    isAdmin,
    authToken: socket.handshake?.auth?.token ? 'Token present' : 'No token'
  });

  // Handle admin:join event
  socket.on('admin:join', () => {
    console.log('admin:join event received', { socketId: socket.id, userId });
    
    // Check if token contains admin role
    if (socket.handshake?.auth?.token) {
      try {
        const token = socket.handshake.auth.token;
        const tokenData = JSON.parse(Buffer.from(token.split('.')[1], 'base64').toString());
        console.log('Token data:', tokenData);
        
        if (tokenData.role === 'admin') {
          console.log(`Verified admin ${userId || tokenData.id} joined admin room`);
          socket.data.user = {
            ...socket.data.user,
            role: 'admin',
            isAdmin: true
          };
          socket.join('admin');
        }
      } catch (error) {
        console.error('Error parsing token:', error);
      }
    }
  });

  // Join chat room
  socket.on('livechat:join', async (data: ChatJoinData) => {
    try {
      const { chatId, anonymousId } = data;
      // Recheck admin status here
      const currentAdmin = socket.data?.user?.isAdmin || socket.data?.user?.role === 'admin';

      console.log('livechat:join attempt', { 
        socketId: socket.id, 
        chatId, 
        userId, 
        isAdmin: currentAdmin,
        userData: JSON.stringify(socket.data?.user || {})
      });

      // Find the chat and validate access
      const chat = await LiveChat.findById(chatId);

      if (!chat) {
        console.log(`Chat not found: ${chatId}`);
        socket.emit('livechat:error', { message: 'Chat not found' });
        return;
      }

      let hasAccess = false;

      // Admin always has access - check both local and socket data
      if (currentAdmin) {
        console.log(`Admin access granted to chat ${chatId} for user ${userId}`);
        hasAccess = true;
        // If admin, join the admin room as well
        socket.join('admin');
      }
      // Authenticated user
      else if (userId) {
        hasAccess = chat.userId?.toString() === userId.toString();
        console.log(`User ${userId} access to chat ${chatId}: ${hasAccess}`);
      }
      // Anonymous user
      else if (anonymousId) {
        hasAccess = chat.anonymousId === anonymousId;
        console.log(`Anonymous user ${anonymousId} access to chat ${chatId}: ${hasAccess}`);
      }

      if (!hasAccess) {
        console.log(`Access denied to chat ${chatId} for user:`, { 
          userId, 
          isAdmin: currentAdmin, 
          socketId: socket.id,
          userRole: socket.data?.user?.role,
          chatUserId: chat.userId?.toString()
        });
        socket.emit('livechat:error', { message: 'Access denied to this chat' });
        return;
      }

      // Join the chat room
      const roomId = `chat_${chatId}`;
      socket.join(roomId);

      // Log room joining info
      console.log(`Socket ${socket.id} joined chat room ${roomId}. User type: ${isAdmin ? 'admin' : (userId ? 'user' : 'anonymous')}, ChatId: ${chatId}`);

      // Log all sockets in this room after joining
      io.in(roomId).fetchSockets().then(sockets => {
        console.log(`Sockets in room ${roomId} after joining:`, sockets.map(s => s.id));
      });

      // Notify client
      socket.emit('livechat:joined', {
        chatId,
        status: chat.status
      });
    } catch (error) {
      console.error('Error in livechat:join:', error);
      socket.emit('livechat:error', { message: 'Failed to join chat room' });
    }
  });

  // Leave chat room
  socket.on('livechat:leave', (data: { chatId: string }) => {
    try {
      const { chatId } = data;
      const roomId = `chat_${chatId}`;

      socket.leave(roomId);
      socket.emit('livechat:left', { chatId });
    } catch (error) {
      console.error('Error in livechat:leave:', error);
    }
  });

  // Send chat message via socket
  socket.on('livechat:message', async (data: ChatMessageData) => {
    try {
      const { chatId, content, anonymousId } = data;

      if (!content || !chatId) {
        socket.emit('livechat:error', { message: 'Content and chatId are required' });
        return;
      }

      // Find the chat
      const chat = await LiveChat.findById(chatId);

      if (!chat) {
        socket.emit('livechat:error', { message: 'Chat not found' });
        return;
      }

      if (chat.status !== 'active') {
        socket.emit('livechat:error', { message: 'This chat session is no longer active' });
        return;
      }

      // Determine sender type and ID
      let senderId: string;
      let senderType: 'user' | 'admin' | 'anonymous';

      if (isAdmin) {
        senderId = userId;
        senderType = 'admin';
      } else if (userId) {
        // Ensure the user owns this chat
        if (chat.userId?.toString() !== userId) {
          socket.emit('livechat:error', { message: 'You do not have access to this chat' });
          return;
        }

        senderId = userId;
        senderType = 'user';
      } else {
        // Anonymous user
        if (!anonymousId || chat.anonymousId !== anonymousId) {
          socket.emit('livechat:error', { message: 'Invalid anonymous ID' });
          return;
        }

        senderId = anonymousId;
        senderType = 'anonymous';
      }

      // Create the message
      const message = {
        content,
        senderId,
        senderType,
        timestamp: new Date(),
        read: false
      };

      // Add message to chat and update lastMessage and unreadCount
      chat.messages.push(message);
      chat.lastMessage = message;

      // Increment unreadCount if the sender is not admin
      if (senderType !== 'admin') {
        chat.unreadCount = (chat.unreadCount || 0) + 1;
      }

      // Save to database
      await chat.save();

      console.log(`Message saved to database for chat ${chatId}. Sender type: ${senderType}, Content: ${content.substring(0, 20)}...`);

      // Emit to chat room
      const roomId = `chat_${chatId}`;

      // Log socket room info for debugging
      const roomSockets = await io.in(roomId).fetchSockets();
      const socketIds = roomSockets.map(s => s.id);
      console.log(`Emitting message to chat room: ${roomId}. Connected sockets in room: ${socketIds.length}`, socketIds);

      // Emit to all sockets in the room including the sender
      io.to(roomId).emit('livechat:message', {
        chatId,
        message
      });

      console.log(`Message emitted to room ${roomId}`);

      // If sender is not admin, notify admins
      if (senderType !== 'admin') {
        io.to('admin').emit('livechat:new_message', {
          chatId,
          message,
          name: chat.name,
          timestamp: new Date()
        });
      }
    } catch (error) {
      console.error('Error in livechat:message:', error);
      socket.emit('livechat:error', { message: 'Failed to send message' });
    }
  });

  // Admin typing indicator
  socket.on('livechat:typing', (data: { chatId: string; isTyping: boolean }) => {
    try {
      const { chatId, isTyping } = data;
      const roomId = `chat_${chatId}`;

      // Emit typing status to chat room (include sender's socket ID)
      io.to(roomId).emit('livechat:typing', {
        chatId,
        isAdmin: isAdmin,
        isTyping,
        socketId: socket.id // Add socket ID to help clients filter their own typing indicators
      });
    } catch (error) {
      console.error('Error in livechat:typing:', error);
    }
  });

  // Request admin support
  socket.on('livechat:request_admin', async (data: { name: string; email?: string; subject?: string }) => {
    try {
      const { name, email, subject } = data;

      if (!name) {
        socket.emit('livechat:error', { message: 'Name is required' });
        return;
      }

      // Generate anonymous ID if user is not logged in
      let anonymousId: string | undefined;
      if (!userId) {
        anonymousId = uuidv4();
      }

      // Create a new chat session
      const chat = new LiveChat({
        userId: userId ? new mongoose.Types.ObjectId(userId) : undefined,
        anonymousId,
        name,
        email,
        subject,
        status: 'active',
        messages: [],
        unreadCount: 0
      });

      await chat.save();

      // Automatically join the chat room
      const roomId = `chat_${chat._id}`;
      socket.join(roomId);

      // Notify admins
      io.to('admin').emit('livechat:new_chat', {
        chatId: chat._id,
        name: chat.name,
        email: chat.email,
        subject: chat.subject,
        timestamp: new Date()
      });

      // Notify requester
      socket.emit('livechat:chat_created', {
        chatId: chat._id,
        anonymousId: chat.anonymousId
      });
    } catch (error) {
      console.error('Error in livechat:request_admin:', error);
      socket.emit('livechat:error', { message: 'Failed to create chat session' });
    }
  });

  // Close chat
  socket.on('livechat:close', async (data: { chatId: string; anonymousId?: string }) => {
    try {
      const { chatId, anonymousId } = data;

      // Find the chat
      const chat = await LiveChat.findById(chatId);

      if (!chat) {
        socket.emit('livechat:error', { message: 'Chat not found' });
        return;
      }

      // Check if user has permission to close
      let hasPermission = false;

      if (isAdmin) {
        hasPermission = true;
      } else if (userId) {
        hasPermission = chat.userId?.toString() === userId;
      } else if (anonymousId) {
        hasPermission = chat.anonymousId === anonymousId;
      }

      if (!hasPermission) {
        socket.emit('livechat:error', { message: 'You do not have permission to close this chat' });
        return;
      }

      // Close the chat
      chat.status = 'closed';
      await chat.save();

      // Notify chat room
      const roomId = `chat_${chatId}`;
      io.to(roomId).emit('livechat:closed', {
        chatId,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Error in livechat:close:', error);
      socket.emit('livechat:error', { message: 'Failed to close chat' });
    }
  });

  // Handle disconnect
  socket.on('disconnect', () => {
    const rooms = Array.from(socket.rooms);

    // Leave all chat rooms
    rooms.forEach(room => {
      if (room.startsWith('chat_')) {
        const chatId = room.replace('chat_', '');
        io.to(room).emit('livechat:user_disconnect', {
          chatId,
          isAdmin
        });
      }
    });
  });
};