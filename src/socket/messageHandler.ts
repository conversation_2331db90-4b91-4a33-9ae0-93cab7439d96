import { DefaultEventsMap, Server, Socket } from 'socket.io';
import { createClient } from 'redis';
import { Message } from '../models/Message';
import mongoose from 'mongoose';

// Check if Redis is enabled
const redisEnabled = process.env.REDIS_ENABLED === 'true';

// Create Redis client only if enabled
const redis = redisEnabled
  ? createClient({
      url: process.env.REDIS_URL || `redis://${process.env.REDIS_HOST || 'localhost'}:${process.env.REDIS_PORT || '6379'}`
    })
  : null;

// Connect to Redis only if enabled
if (redisEnabled && redis) {
  redis.on('error', (err: Error) => console.error('Redis Client Error', err));
  redis.connect().catch(console.error);
} else {
  console.log('Redis is disabled in message handler. Skipping connection setup.')
}

interface IUser {
  _id: string;
  firstName: string;
  lastName: string;
}

interface IProduct {
  _id: string;
  name: string;
  price: number;
}

interface IMessageData {
  _id?: string;
  content: string;
  senderId: string | IUser;
  recipientId: string | IUser;
  roomId: string;
  productId?: string | IProduct;
  senderName?: string;
  source?: 'socket' | 'api';
}

interface ITypingData {
  userId: string;
  recipientId: string;
  roomId: string;
  userName: string;
}

export const setupMessageHandlers = (io: Server<DefaultEventsMap, DefaultEventsMap, DefaultEventsMap>, socket: Socket) => {
  const userId = socket.data.user?._id || socket.data.user?.id;

  const updateNotificationCount = async (userId: string) => {
    if (redisEnabled && redis) {
      const count = await redis.get(`notifications:${userId}`);
      io.to(userId).emit('notifications:count', parseInt(count || '0', 10));
    } else {
      io.to(userId).emit('notifications:count', 0);
    }
  };

  if (userId) {
    socket.join(userId);
  }

  const handleRoomJoin = async (roomId: string) => {
    if (!socket.rooms.has(roomId)) {
      await socket.join(roomId);
    }
  };

  socket.on('message:send', async (messageData: any) => {
    try {
      let message;

      if (messageData.source !== 'api') {
        const senderId = new mongoose.Types.ObjectId(messageData.senderId);
        const recipientId = new mongoose.Types.ObjectId(messageData.recipientId);
        const productId = messageData.productId ? new mongoose.Types.ObjectId(messageData.productId) : undefined;

        message = new Message({
          content: messageData.content,
          senderId,
          recipientId,
          productId,
          roomId: messageData.roomId,
          createdAt: new Date(),
          timestamp: new Date(),
          read: false
        });

        await message.save();
      } else {
        const senderId = typeof messageData.senderId === 'object' ? messageData.senderId._id : messageData.senderId;
        const recipientId = typeof messageData.recipientId === 'object' ? messageData.recipientId._id : messageData.recipientId;

        message = await Message.findOne({
          content: messageData.content,
          senderId,
          recipientId,
          roomId: messageData.roomId
        }).sort({ createdAt: -1 });
      }

      if (!message) {
        throw new Error('Failed to save or find message');
      }

      const populatedMessage = await Message.findById(message._id)
        .populate('senderId', 'firstName lastName avatar')
        .populate('recipientId', 'firstName lastName avatar')
        .populate('productId', 'name images')
        .lean();

      if (!populatedMessage) {
        throw new Error('Failed to populate message');
      }

      const messageToSend = {
        ...populatedMessage,
        _id: message._id.toString(),
        source: messageData.source || 'socket'
      };

      const recipientId = typeof messageData.recipientId === 'object'
        ? messageData.recipientId._id
        : messageData.recipientId;

      const allSockets = await io.fetchSockets();

      const recipientSockets = allSockets.filter(s => {
        const socketUserId = s.data.user?._id || s.data.user?.id;
        return socketUserId && socketUserId.toString() === recipientId.toString();
      });

      recipientSockets.forEach(recipientSocket => {
        recipientSocket.emit('message:received', messageToSend);
      });

      if (recipientSockets.length > 0) {
        const recipientInMessageCenter = recipientSockets.some(s => s.data.currentPage === '/messages');

        if (!recipientInMessageCenter) {
          let newCount = 1;

          if (redisEnabled && redis) {
            const currentCount = await redis.get(`notifications:${recipientId}`) || '0';
            newCount = parseInt(currentCount) + 1;
            await redis.set(`notifications:${recipientId}`, newCount.toString());
          }

          recipientSockets.forEach(recipientSocket => {
            recipientSocket.emit('notifications:count', newCount);
          });

          const notification = {
            type: 'message',
            sender: typeof messageData.senderId === 'object' ?
              `${messageData.senderId.firstName} ${messageData.senderId.lastName}` :
              messageData.senderName || 'Unknown',
            preview: messageData.content,
            timestamp: new Date(),
            data: {
              senderId: messageData.senderId,
              recipientId: messageData.recipientId,
              productId: messageData.productId,
              roomId: messageData.roomId
            }
          };

          recipientSockets.forEach(recipientSocket => {
            recipientSocket.emit('new_notification', notification);
          });
        }
      } else if (redisEnabled && redis) {
        const currentCount = await redis.get(`notifications:${recipientId}`) || '0';
        const newCount = parseInt(currentCount) + 1;
        await redis.set(`notifications:${recipientId}`, newCount.toString());
      }

    } catch (error) {
      socket.emit('message:error', { message: 'Failed to send message' });
    }
  });

  socket.on('join:room', async (roomId: string) => {
    await handleRoomJoin(roomId);
  });

  socket.on('leave:room', (roomId: string) => {
    socket.leave(roomId);
  });

  socket.on('typing:start', async (data: ITypingData) => {
    if (!data.roomId || !data.recipientId) return;

    try {
      await handleRoomJoin(data.roomId);
      socket.to(data.roomId).emit('typing:start', {
        userId: data.userId,
        roomId: data.roomId,
        userName: data.userName
      });
    } catch (error) {
      // Error handling
    }
  });

  socket.on('typing:stop', async (data: ITypingData) => {
    if (!data.roomId || !data.recipientId) return;

    try {
      await handleRoomJoin(data.roomId);
      socket.to(data.roomId).emit('typing:stop', {
        userId: data.userId,
        roomId: data.roomId,
        userName: data.userName
      });
    } catch (error) {
      // Error handling
    }
  });

  socket.on('notifications:get', async () => {
    if (userId) {
      await updateNotificationCount(userId);
    }
  });

  socket.on('notifications:clear', async () => {
    if (userId) {
      if (redisEnabled && redis) {
        await redis.set(`notifications:${userId}`, '0');
      }
      socket.emit('notifications:count', 0);
    }
  });

  socket.on('disconnect', () => {
    const rooms = socket.rooms;
    rooms.forEach(room => {
      if (room !== socket.id && userId) {
        socket.to(room).emit('typing:stop', {
          userId,
          roomId: room
        });
      }
    });
  });
};
