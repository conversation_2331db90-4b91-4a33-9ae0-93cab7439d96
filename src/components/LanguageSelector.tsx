import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { ChevronDownIcon } from "@chakra-ui/icons";

interface Language {
  code: string;
  name: string;
  nativeName: string;
  dir?: "ltr" | "rtl";
}

interface LanguageSelectorProps {
  display?: "code" | "native";
  className?: string;
  textColor?: "black" | "white";
}

const languages: Language[] = [
  { code: "tr", name: "Turkish", nativeName: "Türkçe", dir: "ltr" },
  { code: "en", name: "English", nativeName: "English", dir: "ltr" },
  { code: "ar", name: "Arabic", nativeName: "العربية", dir: "rtl" },
  { code: "ru", name: "Russian", nativeName: "Русский", dir: "ltr" },
  { code: "fr", name: "French", nativeName: "Français", dir: "ltr" },
  { code: "es", name: "Spanish", nativeName: "Español", dir: "ltr" },
  { code: "de", name: "German", nativeName: "Deutsch", dir: "ltr" },
  { code: "it", name: "Italian", nativeName: "Italiano", dir: "ltr" },
  { code: "zh", name: "Chinese", nativeName: "中文", dir: "ltr" },
];

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  display = "native",
  className = "",
  textColor = "black",
}) => {
  const { i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";
  const currentLanguage =
    languages.find((lang) => lang.code === i18n.language.split("-")[0]) ||
    languages[0];

  const getDisplayText = (lang: Language) => {
    return display === "code" ? lang.code.toUpperCase() : lang.nativeName;
  };

  const handleLanguageChange = async (langCode: string) => {
    const newLang = languages.find((lang) => lang.code === langCode);
    if (newLang) {
      // Update the HTML dir attribute for RTL support
      document.documentElement.dir = newLang.dir || "ltr";
      await i18n.changeLanguage(langCode);
    }
  };

  return (
    <Menu placement="bottom-end" autoSelect={false} gutter={0}>
      <MenuButton
        as={Button}
        variant="ghost"
        rightIcon={<ChevronDownIcon />}
        fontWeight="normal"
        fontSize="sm"
        textColor={textColor}
        size="sm"
        className={className}
      >
        {getDisplayText(currentLanguage)}
      </MenuButton>
      <MenuList
        minW="150px"
        py={2}
        shadow="md"
        borderRadius="md"
        borderWidth="1px"
        borderColor="gray.100"
        transform={isRTL ? 'translateX(calc(0% - 8px))' : 'translateX(-8px)'}
      >
        {languages.map((lang) => (
          <MenuItem
            key={lang.code}
            onClick={() => handleLanguageChange(lang.code)}
            dir={lang.dir || "ltr"}
            fontSize="sm"
            fontWeight={currentLanguage.code === lang.code ? "medium" : "normal"}
            color="gray.700"
            _hover={{ bg: 'gray.50' }}
            py={2}
          >
            {getDisplayText(lang)}
          </MenuItem>
        ))}
      </MenuList>
    </Menu>
  );
};

export default LanguageSelector;
