import React, { useState } from 'react';
import * as Dialog from '@radix-ui/react-dialog';
import { 
  HeadphonesIcon, 
  X, 
  Send, 
  User, 
  Bot,
  Loader2
} from 'lucide-react';

const LiveChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Array<{type: 'user' | 'agent', content: string}>>([
    {
      type: 'agent',
      content: 'Merhaba! Size nasıl yardımcı olabilirim?'
    }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    // Add user message
    setMessages(prev => [...prev, { type: 'user', content: newMessage }]);
    setNewMessage('');
    
    // Simulate agent typing
    setIsTyping(true);
    setTimeout(() => {
      setMessages(prev => [...prev, { 
        type: 'agent', 
        content: 'Teşekkürler mesajınız için. Müşteri temsilcimiz en kısa sürede size dönüş yapacaktır.' 
      }]);
      setIsTyping(false);
    }, 1000);
  };

  return (
    <>
      {/* Chat Button */}
      <Dialog.Root open={isOpen} onOpenChange={setIsOpen}>
        <Dialog.Trigger asChild>
          <button 
            className="fixed bottom-6 right-6 w-14 h-14 bg-primary rounded-full flex items-center justify-center shadow-lg hover:bg-[#0A9996] transition-colors duration-200 group"
            aria-label="Canlı Destek"
          >
            <HeadphonesIcon className="w-6 h-6 text-white" />
            <span className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 rounded-full text-white text-xs flex items-center justify-center">
              1
            </span>
          </button>
        </Dialog.Trigger>

        <Dialog.Portal>
          <Dialog.Overlay className="fixed inset-0 bg-black/50 backdrop-blur-sm" />
          <Dialog.Content className="fixed bottom-6 right-6 w-[400px] max-h-[600px] bg-white rounded-2xl shadow-2xl flex flex-col">
            {/* Header */}
            <div className="p-4 border-b flex items-center justify-between bg-primary rounded-t-2xl">
              <div className="flex items-center space-x-3">
                <HeadphonesIcon className="w-6 h-6 text-white" />
                <div>
                  <h3 className="font-semibold text-white">Canlı Destek</h3>
                  <p className="text-sm text-white/80">Size nasıl yardımcı olabiliriz?</p>
                </div>
              </div>
              <Dialog.Close asChild>
                <button className="text-white/80 hover:text-white transition-colors">
                  <X className="w-5 h-5" />
                </button>
              </Dialog.Close>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message, index) => (
                <div
                  key={index}
                  className={`flex items-start space-x-2 ${
                    message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                  }`}
                >
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.type === 'user' ? 'bg-primary' : 'bg-gray-100'
                  }`}>
                    {message.type === 'user' ? (
                      <User className="w-5 h-5 text-white" />
                    ) : (
                      <Bot className="w-5 h-5 text-primary" />
                    )}
                  </div>
                  <div className={`rounded-2xl px-4 py-2 max-w-[80%] ${
                    message.type === 'user' 
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    {message.content}
                  </div>
                </div>
              ))}
              {isTyping && (
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0">
                    <Bot className="w-5 h-5 text-primary" />
                  </div>
                  <div className="bg-gray-100 rounded-2xl px-4 py-2">
                    <Loader2 className="w-5 h-5 animate-spin text-primary" />
                  </div>
                </div>
              )}
            </div>

            {/* Input */}
            <form onSubmit={handleSendMessage} className="p-4 border-t">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Mesajınızı yazın..."
                  className="flex-1 px-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <button 
                  type="submit"
                  className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center text-white hover:bg-[#0A9996] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!newMessage.trim()}
                >
                  <Send className="w-5 h-5" />
                </button>
              </div>
            </form>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    </>
  );
};

export default LiveChat;