import React, { useState, useEffect, useRef } from 'react';
import { useSocket } from '../context/SocketContext';
import { io, Socket } from 'socket.io-client';
import { useAuthCheck } from '../hooks/useAuthCheck';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import {
  HeadphonesIcon,
  X,
  Send,
  MinusIcon,
  Loader,
  ChevronDown,
  Check
} from 'lucide-react';
import {
  startLiveChat,
  getLiveChatHistory,
  sendLiveChatMessage,
  closeLiveChat,
  markLiveChatMessagesAsRead
} from '../api';
import { v4 as uuidv4 } from 'uuid';

interface Message {
  content: string;
  senderId: string;
  senderType: 'user' | 'admin' | 'anonymous';
  timestamp: Date;
  read: boolean;
}

interface LiveChatProps {
  initiallyOpen?: boolean;
}

const LiveChat: React.FC<LiveChatProps> = ({ initiallyOpen = false }) => {
  const { t } = useTranslation('common');
  const contextSocket = useSocket().socket;
  const { user, isLoggedIn } = useAuthCheck();
  const location = useLocation();
  const [isOpen, setIsOpen] = useState(initiallyOpen);
  const [isMinimized, setIsMinimized] = useState(false);

  // Check if current path is an admin path
  const isAdminPath = location.pathname.startsWith('/admin');

  // Don't render component if on admin path
  if (isAdminPath) {
    return null;
  }
  const [chatId, setChatId] = useState<string | null>(null);
  const [anonymousId, setAnonymousId] = useState<string | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [subject, setSubject] = useState('');
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [adminTyping, setAdminTyping] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isChatStarted, setIsChatStarted] = useState(false);
  const [isChatEnded, setIsChatEnded] = useState(false);
  const [directSocket, setDirectSocket] = useState<Socket | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'disconnected' | 'reconnecting' | 'failed'>('connected');
  const reconnectAttemptsRef = useRef<number>(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<any>(null);

  // Use the appropriate socket - either from context (for logged in users) or direct (for anonymous users)
  // If contextSocket isn't connected for logged-in users, fall back to directSocket
  const socket = isLoggedIn
    ? (contextSocket && contextSocket.connected ? contextSocket : directSocket)
    : directSocket;

  // Initialize direct socket for anonymous users
  useEffect(() => {
    if (!isLoggedIn && !directSocket) {
      console.log('LiveChat: Creating direct socket connection for anonymous user');

      // Always use production API URL for socket connections
      // Local server connection attempts consistently fail, so we're bypassing them completely
      const effectiveSocketUrl = import.meta.env.VITE_SOCKET_URL;

      console.log('LiveChat: Using production socket URL:', effectiveSocketUrl);

      const newSocket: any = io(effectiveSocketUrl, {
        path: '/socket.io',
        transports: ['websocket', 'polling'], // Match backend transport order
        reconnectionAttempts: 5,   // Allow more reconnection attempts
        reconnectionDelay: 1000,   // Start with a shorter delay
        timeout: 20000,            // Longer timeout to establish connection
        reconnection: true,
        reconnectionDelayMax: 5000, // Maximum delay between reconnections
        autoConnect: true,
        forceNew: true,
        withCredentials: true      // Enable CORS credentials
      });

      // Log transport change events
      newSocket.io.engine.on('transport', function (transport: any) {
        console.log('LiveChat: Socket transport selected:', transport.name);
      });

      // Log detailed connection process
      console.log('LiveChat: Socket connection attempt started');

      newSocket.on('connect', () => {
        console.log('LiveChat: Anonymous socket connected with ID:', newSocket.id);
        setConnectionStatus('connected');
        reconnectAttemptsRef.current = 0; // Reset counter on successful connection

        // If we have a chatId, rejoin the room after reconnection
        if (chatId) {
          newSocket.emit('livechat:join', {
            chatId,
            anonymousId: anonymousId
          });
          console.log('Rejoined chat room after connection:', chatId);
        }
      });

      newSocket.on('connect_error', (error: { message: string | string[]; }) => {
        console.error('LiveChat: Anonymous socket connection error:', error);
        setConnectionStatus('disconnected');

        // Check for CORS error
        const isCorsError = error.message?.includes('CORS') ||
          error.message?.includes('xhr poll error') ||
          error.message?.includes('xhr polling error');

        // If we have a chat ID, start using polling immediately
        if (chatId) {
          console.log('LiveChat: Connection error, starting REST API polling');
          startPollingMessages(chatId, !isLoggedIn ? anonymousId || undefined : undefined);
        }

        if (isCorsError) {
          console.log('LiveChat: CORS error detected, falling back to REST API');
          setError('Network connectivity issue detected. Using fallback mode.');
          // Don't retry more than once with CORS errors
          reconnectAttemptsRef.current = 4;
          setConnectionStatus('failed');
          // Don't disconnect as we might want to try again later with different options
        } else {
          // Increment reconnect attempts
          reconnectAttemptsRef.current += 1;

          // After MAX_ATTEMPTS, consider the connection failed permanently
          if (reconnectAttemptsRef.current > 3) {
            console.log('LiveChat: Max reconnection attempts reached');
            setConnectionStatus('failed');
            newSocket.disconnect(); // Stop trying to reconnect
          }
        }
      });

      newSocket.on('reconnect_attempt', (attemptNumber: any) => {
        console.log(`LiveChat: Socket reconnection attempt ${attemptNumber}`);
        setConnectionStatus('reconnecting');
      });

      newSocket.on('reconnect_failed', () => {
        console.log('LiveChat: Socket reconnection failed');
        setConnectionStatus('failed');
      });

      newSocket.on('reconnect', (attemptNumber: any) => {
        console.log(`LiveChat: Socket reconnected after ${attemptNumber} attempts`);
        setConnectionStatus('connected');
        reconnectAttemptsRef.current = 0; // Reset counter
      });

      newSocket.on('disconnect', (reason: any) => {
        console.log('LiveChat: Anonymous socket disconnected. Reason:', reason);
        setConnectionStatus('disconnected');

        // Activate REST API polling fallback immediately on disconnect
        if (chatId) {
          console.log('LiveChat: Starting REST API polling fallback due to socket disconnect');
          startPollingMessages(chatId, !isLoggedIn ? anonymousId || undefined : undefined);
        }

        // Only attempt manual reconnect for forceful disconnects
        if (reason === 'io server disconnect') {
          console.log("Server forcefully disconnected. Attempting to reconnect...");
          // Add delay before reconnecting to prevent rapid reconnect loop
          setTimeout(() => {
            if (reconnectAttemptsRef.current <= 3) {
              newSocket.connect();
            }
          }, 3000);
        }
      });

      setDirectSocket(newSocket);
      return () => {
        console.log('LiveChat: Cleaning up anonymous socket connection');
        // Remove all listeners explicitly to prevent memory leaks
        newSocket.off('connect');
        newSocket.off('connect_error');
        newSocket.off('reconnect_attempt');
        newSocket.off('reconnect_failed');
        newSocket.off('reconnect');
        newSocket.off('disconnect');
        newSocket.off('livechat:message');
        newSocket.off('livechat:typing');
        newSocket.off('livechat:closed');
        newSocket.off('livechat:error');
        newSocket.off('livechat:chat_created');
        newSocket.off('livechat:joined');

        // Close the connection
        newSocket.disconnect();
        setDirectSocket(null);
      };
    }
  }, [isLoggedIn]);

  // Initialize chat and anonymousId from localStorage
  useEffect(() => {
    // Check if there's a stored chat
    const storedChatId = localStorage.getItem('liveChatId');
    const storedAnonymousId = localStorage.getItem('liveChatAnonymousId');

    if (storedChatId) {
      setChatId(storedChatId);

      // If user is not logged in, make sure we have an anonymousId
      if (!isLoggedIn && storedAnonymousId) {
        setAnonymousId(storedAnonymousId);
      } else if (!isLoggedIn) {
        // Generate a new anonymousId
        const newAnonymousId = uuidv4();
        setAnonymousId(newAnonymousId);
        localStorage.setItem('liveChatAnonymousId', newAnonymousId);
      }

      setIsChatStarted(true);

      // Load chat history
      loadChatHistory(storedChatId, storedAnonymousId || undefined);
    } else if (!isLoggedIn) {
      // If no stored chat and not logged in, generate anonymousId
      const newAnonymousId = uuidv4();
      setAnonymousId(newAnonymousId);
      localStorage.setItem('liveChatAnonymousId', newAnonymousId);
    }

    setIsInitialized(true);
  }, [isLoggedIn]);

  // Set up socket listeners
  useEffect(() => {
    if (!socket || !isInitialized) {
      console.log('LiveChat: Skipping socket setup - socket or initialization not ready', {
        socketExists: !!socket,
        isInitialized
      });

      // If we have a chat ID but no socket, set up polling fallback
      if (chatId && isInitialized) {
        console.log('LiveChat: Setting up polling fallback for chat ID:', chatId);

        // Set up fallback polling for messages
        const pollInterval = setInterval(async () => {
          try {
            // Only poll if socket is not connected
            if (!socket || !socket.connected) {
              console.log('LiveChat: Polling for messages via REST API');
              const history = await getLiveChatHistory(chatId, !isLoggedIn ? anonymousId || undefined : undefined);

              if (history.messages && Array.isArray(history.messages)) {
                // Format dates properly
                const formattedMessages = history.messages.map((msg: any) => ({
                  ...msg,
                  timestamp: new Date(msg.timestamp)
                }));

                // Update state with latest messages
                setMessages(formattedMessages);
              }
            }
          } catch (error) {
            console.error('LiveChat: Error polling for messages:', error);
          }
        }, 5000); // Poll every 5 seconds

        return () => {
          clearInterval(pollInterval);
        };
      }

      return;
    }

    console.log('LiveChat: Setting up socket listeners with socket ID:', socket.id, 'Transport:', socket.io.engine.transport.name);

    // Join the chat room if we have a chatId
    if (chatId) {
      console.log('Joining chat room:', chatId, 'anonymousId:', !isLoggedIn ? anonymousId : undefined);

      // Leave any previously joined chat room first
      if (socket.connected) {
        socket.emit('livechat:leave', { chatId });
      }

      // Join the chat room
      socket.emit('livechat:join', {
        chatId,
        anonymousId: !isLoggedIn ? anonymousId : undefined
      });

      console.log(`LiveChat component: Socket ${socket.id} joined chat room for chatId ${chatId}`);

      // Mark messages as read
      if (isOpen && chatId) {
        markLiveChatMessagesAsRead(chatId, !isLoggedIn ? anonymousId || undefined : undefined)
          .catch(err => console.error('Error marking messages as read:', err));
      }
    }

    // Listen for new messages
    socket.on('livechat:message', (data: { chatId: string; message: Message }) => {
      console.log('Received message via socket:', data);
      if (data.chatId === chatId) {
        // Add notification sound when the user receives a message from an admin
        if (data.message.senderType === 'admin') {
          console.log('Got admin message, playing notification sound');

          // Play notification sound
          try {
            const audio = new Audio('/notification-sound.mp3');
            audio.play().catch(err => console.error('Error playing sound:', err));
          } catch (error) {
            console.error('Error creating Audio object:', error);
          }

          // Mark message as read if chat is open
          if (isOpen) {
            markLiveChatMessagesAsRead(chatId, !isLoggedIn ? anonymousId || undefined : undefined)
              .catch(err => console.error('Error marking messages as read:', err));
          }
        }

        // Add message to state
        setMessages(prev => [...prev, data.message]);
        scrollToBottom();
      }
    });

    // Listen for typing indicators
    socket.on('livechat:typing', (data: { chatId: string; isAdmin: boolean; isTyping: boolean; socketId: string }) => {
      // Only show typing indicator if it's from admin and not from self
      if (data.chatId === chatId && data.isAdmin && data.socketId !== socket.id) {
        setAdminTyping(data.isTyping);
      }
    });

    // Listen for chat closed
    socket.on('livechat:closed', (data: { chatId: string }) => {
      if (data.chatId === chatId) {
        setIsChatEnded(true);
      }
    });

    // Listen for error messages
    socket.on('livechat:error', (data: { message: string }) => {
      setError(data.message);
      // Errors will now remain visible until user dismisses them
    });

    // Listen for chat creation success
    socket.on('livechat:chat_created', (data: { chatId: string; anonymousId?: string }) => {
      setChatId(data.chatId);
      localStorage.setItem('liveChatId', data.chatId);

      if (data.anonymousId && !isLoggedIn) {
        setAnonymousId(data.anonymousId);
        localStorage.setItem('liveChatAnonymousId', data.anonymousId);
      }

      setIsChatStarted(true);
      scrollToBottom();
    });

    return () => {
      // Leave the chat room when component unmounts
      if (chatId) {
        socket.emit('livechat:leave', { chatId });
      }

      socket.off('livechat:message');
      socket.off('livechat:typing');
      socket.off('livechat:closed');
      socket.off('livechat:error');
      socket.off('livechat:chat_created');
    };
  }, [socket, chatId, anonymousId, isInitialized, isOpen, isLoggedIn, directSocket]);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [messages, isOpen, adminTyping]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized, isChatStarted]);

  // Load chat history
  const loadChatHistory = async (id: string, anonId?: string) => {
    try {
      setIsLoading(true);
      const history = await getLiveChatHistory(id, anonId);
      console.log('Loaded chat history:', history);

      if (history.messages && Array.isArray(history.messages)) {
        // Format dates properly
        const formattedMessages = history.messages.map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));

        setMessages(formattedMessages);

        // If chat is no longer active, mark it as ended
        if (history.status !== 'active') {
          setIsChatEnded(true);
        }
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
      setError('Failed to load chat history');
    } finally {
      setIsLoading(false);
    }
  };

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  // Start a new chat
  const handleStartChat = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name.trim()) {
      setError('Please enter your name');
      return;
    }

    try {
      setIsLoading(true);

      if (isLoggedIn && user) {
        // For logged in users
        setName(user.firstName + ' ' + user.lastName);
        setEmail(user.email);
      }

      const chatData = {
        name: name.trim(),
        email: email.trim() || undefined,
        subject: subject.trim() || undefined,
        anonymousId: !isLoggedIn ? anonymousId || undefined : undefined
      };

      // If socket is connected, use socket to start chat
      if (socket && socket.connected) {
        console.log('LiveChat: Starting chat via socket with data:', {
          name: chatData.name,
          email: chatData.email ? 'provided' : 'not provided',
          subject: chatData.subject ? 'provided' : 'not provided',
          anonymousId: chatData.anonymousId ? 'provided' : 'not provided'
        });
        socket.emit('livechat:request_admin', chatData);
      } else {
        // Fallback to REST API
        console.log('LiveChat: Socket not connected, using REST API to start chat');
        const response = await startLiveChat(chatData);
        console.log('LiveChat: REST API response for start chat:', response);

        if (response.chatId) {
          setChatId(response.chatId);
          localStorage.setItem('liveChatId', response.chatId);

          if (response.anonymousId && !isLoggedIn) {
            setAnonymousId(response.anonymousId);
            localStorage.setItem('liveChatAnonymousId', response.anonymousId);
          }

          setIsChatStarted(true);

          // Load initial messages if any
          if (response.messages && Array.isArray(response.messages)) {
            setMessages(response.messages);
          }
        }
      }
    } catch (error: any) {
      console.error('Error starting chat:', error);
      setError(error.message || 'Failed to start chat');
    } finally {
      setIsLoading(false);
    }
  };

  // Send a message
  const handleSendMessage = async () => {
    if (!input.trim() || !chatId || isChatEnded) return;

    try {
      const messageData = {
        content: input.trim(),
        anonymousId: !isLoggedIn ? anonymousId || undefined : undefined
      };

      // Clear input right away for better UX
      setInput('');

      // Create a temporary message object to show immediately
      const tempMessage: Message = {
        content: messageData.content,
        senderId: isLoggedIn ? (user?._id as string) : (anonymousId as string),
        senderType: isLoggedIn ? 'user' : 'anonymous',
        timestamp: new Date(),
        read: false
      };

      // Add the message to the local state immediately
      setMessages(prev => [...prev, tempMessage]);

      // Use socket if connected
      if (socket && socket.connected) {
        socket.emit('livechat:message', {
          chatId,
          ...messageData
        });
      } else {
        // Fallback to REST API
        const response = await sendLiveChatMessage(chatId, messageData);

        // If REST API was used, update the message in state with the server response
        if (response && response.message) {
          setMessages(prev => {
            // Create a unique identifier for the message

            // Filter out the temporary message and any potential duplicates
            const filtered = prev.filter(msg => {
              // Skip messages that match our temporary message or possible duplicates with same content and similar timestamp
              const msgTime = new Date(msg.timestamp).getTime();
              const tempTime = new Date(tempMessage.timestamp).getTime();
              const timeWindow = 2000; // 2 second window for potential duplicates

              return !(
                // Either it's our exact temp message
                (msg.content === tempMessage.content &&
                  Math.abs(msgTime - tempTime) < 100) ||
                // Or it's a potential duplicate from socket
                (msg.content === response.message.content &&
                  msg.senderId === response.message.senderId &&
                  Math.abs(msgTime - new Date(response.message.timestamp).getTime()) < timeWindow)
              );
            });

            return [...filtered, response.message];
          });
        }
      }

      // Stop typing indicator
      handleTypingStop();

      // Scroll to the bottom to show the new message
      scrollToBottom();
    } catch (error: any) {
      console.error('Error sending message:', error);
      setError(error.message || 'Failed to send message');
    }
  };

  // Handle typing start
  const handleTypingStart = () => {
    if (!socket || !chatId || isChatEnded) return;

    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Emit typing start event
    socket.emit('livechat:typing', {
      chatId,
      isTyping: true
    });

    // Set timeout to stop typing after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(handleTypingStop, 2000);
  };

  // Handle typing stop
  const handleTypingStop = () => {
    if (!socket || !chatId) return;

    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }

    // Emit typing stop event
    socket.emit('livechat:typing', {
      chatId,
      isTyping: false
    });
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);

    // Trigger typing indicator if input is not empty
    if (e.target.value.trim() && !isChatEnded) {
      handleTypingStart();
    } else {
      handleTypingStop();
    }
  };

  // Handle key press (Enter to send)
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // End the chat
  const handleEndChat = async () => {
    if (!chatId) return;

    try {
      setIsLoading(true);

      // Use socket if connected
      if (socket && socket.connected) {
        socket.emit('livechat:close', {
          chatId,
          anonymousId: !isLoggedIn ? anonymousId || undefined : undefined
        });
      } else {
        // Fallback to REST API
        await closeLiveChat(chatId, !isLoggedIn ? anonymousId || undefined : undefined);
      }

      setIsChatEnded(true);
    } catch (error: any) {
      console.error('Error ending chat:', error);
      setError(error.message || 'Failed to end chat');
    } finally {
      setIsLoading(false);
    }
  };

  // Start a new chat after ending previous one
  const handleNewChat = () => {
    // Clear previous chat data
    setChatId(null);
    setMessages([]);
    setIsChatEnded(false);
    setIsChatStarted(false);

    // Remove from localStorage
    localStorage.removeItem('liveChatId');

    // If not logged in, generate a new anonymousId
    if (!isLoggedIn) {
      const newAnonymousId = uuidv4();
      setAnonymousId(newAnonymousId);
      localStorage.setItem('liveChatAnonymousId', newAnonymousId);
    }
  };

  // Toggle chat open/closed
  const toggleChat = () => {
    setIsOpen(!isOpen);
    setIsMinimized(false);

    // If opening chat and we have a chatId, mark messages as read
    if (!isOpen && chatId) {
      markLiveChatMessagesAsRead(chatId, !isLoggedIn ? anonymousId || undefined : undefined)
        .catch(err => console.error('Error marking messages as read:', err));
    }
  };

  // Toggle chat minimized
  const toggleMinimize = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsMinimized(!isMinimized);
  };

  // Start polling for messages via REST API
  const startPollingMessages = (chatId: string, anonId?: string) => {
    console.log('LiveChat: Setting up message polling for chatId', chatId);

    // Clear any existing interval
    if ((window as any).liveChatPollInterval) {
      clearInterval((window as any).liveChatPollInterval);
    }

    // Create new polling interval
    const pollInterval = setInterval(async () => {
      try {
        if (!socket?.connected) {
          console.log('LiveChat: Polling for messages via REST API');
          const history = await getLiveChatHistory(chatId, anonId);

          if (history.messages && Array.isArray(history.messages)) {
            // Format dates properly
            const formattedMessages = history.messages.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }));

            // Update state with latest messages
            setMessages(formattedMessages);
          }
        } else {
          // If socket reconnects, stop polling
          console.log('LiveChat: Socket reconnected, stopping polling');
          clearInterval((window as any).liveChatPollInterval);
          (window as any).liveChatPollInterval = null;
        }
      } catch (error) {
        console.error('LiveChat: Error polling for messages:', error);
      }
    }, 3000); // Poll every 3 seconds

    // Store interval ID for cleanup
    (window as any).liveChatPollInterval = pollInterval;
  };

  // Global cleanup for polling interval
  useEffect(() => {
    return () => {
      // Clean up polling interval if it exists
      if ((window as any).liveChatPollInterval) {
        console.log('LiveChat: Cleaning up polling interval');
        clearInterval((window as any).liveChatPollInterval);
        (window as any).liveChatPollInterval = null;
      }
    };
  }, []);

  // Format date
  const formatTime = (date: Date | string) => {
    const d = new Date(date);
    return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="fixed bottom-6 right-6 z-50 flex flex-col items-end">
      {/* Chat button - only shown when chat is closed */}
      {!isOpen && (
        <button
          onClick={toggleChat}
          className="flex items-center justify-center w-16 h-16 rounded-full bg-teal-500 text-white shadow-lg hover:bg-teal-600 transition-colors focus:outline-none focus:ring-2 focus:ring-teal-300"
          aria-label="Open chat"
        >
          <HeadphonesIcon size={24} />
        </button>
      )}

      {/* Chat window */}
      {isOpen && (
        <div className="bg-white rounded-lg shadow-xl w-80 md:w-96 overflow-hidden mt-4 border border-gray-200 flex flex-col transition-all duration-300"
          style={{ height: isMinimized ? '60px' : '450px' }}>
          {/* Chat header */}
          <div className="bg-teal-500 text-white px-4 py-3 flex justify-between items-center cursor-pointer"
            onClick={toggleMinimize}>
            <div className="flex items-center space-x-3">
              <HeadphonesIcon className="w-5 h-5 text-white" />
              <h3 className="font-medium">{t('liveChat.title')}</h3>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={toggleMinimize}
                className="text-white hover:bg-teal-600 rounded p-1 transition-colors"
                aria-label={isMinimized ? 'Expand chat' : 'Minimize chat'}
              >
                {isMinimized ? <ChevronDown size={18} /> : <MinusIcon size={18} />}
              </button>
              <button
                onClick={(e) => { e.stopPropagation(); toggleChat(); }}
                className="text-white hover:bg-teal-600 rounded p-1 transition-colors"
                aria-label="Close chat"
              >
                <X size={18} />
              </button>
            </div>
          </div>

          {/* Chat content - hidden when minimized */}
          {!isMinimized && (
            <>
              {!isChatStarted ? (
                // Chat start form
                <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
                  <p className="text-gray-600 mb-4">{t('liveChat.welcome')}</p>
                  {error && (
                    <div className="bg-red-100 border border-red-200 text-red-700 px-4 py-2 rounded mb-4 flex justify-between items-center">
                      <span>{error}</span>
                      <button
                        onClick={() => setError(null)}
                        className="text-red-500 hover:text-red-700"
                        aria-label="Dismiss error"
                      >
                        <X size={16} />
                      </button>
                    </div>
                  )}
                  <form onSubmit={handleStartChat} className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('liveChat.nameLabel')} *
                      </label>
                      <input
                        type="text"
                        value={isLoggedIn && user ? `${user.firstName} ${user.lastName}` : name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder={t('liveChat.namePlaceholder')}
                        required
                        disabled={isLoggedIn || isLoading}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all disabled:bg-gray-100"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('liveChat.emailLabel')}
                      </label>
                      <input
                        type="email"
                        value={isLoggedIn && user ? user.email : email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder={t('liveChat.emailPlaceholder')}
                        disabled={isLoggedIn || isLoading}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all disabled:bg-gray-100"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('liveChat.subjectLabel')}
                      </label>
                      <input
                        type="text"
                        value={subject}
                        onChange={(e) => setSubject(e.target.value)}
                        placeholder={t('liveChat.subjectPlaceholder')}
                        disabled={isLoading}
                        className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all disabled:bg-gray-100"
                      />
                    </div>
                    <button
                      type="submit"
                      disabled={isLoading || !name.trim()}
                      className="w-full py-2 px-4 bg-teal-500 text-white font-medium rounded hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center">
                          <Loader className="animate-spin mr-2" size={16} />
                          {t('liveChat.starting')}
                        </div>
                      ) : (
                        t('liveChat.startChat')
                      )}
                    </button>
                  </form>
                </div>
              ) : (
                // Chat messages
                <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
                  {isLoading && messages.length === 0 && (
                    <div className="flex justify-center items-center h-full">
                      <Loader className="animate-spin" />
                    </div>
                  )}

                  {messages.length === 0 && !isLoading && (
                    <div className="text-center text-gray-500 my-4">
                      {t('liveChat.startConversation')}
                    </div>
                  )}

                  {messages.map((msg, index) => (
                    <div
                      key={index}
                      className={`mb-4 flex ${msg.senderType === 'admin' ? 'justify-start' : 'justify-end'}`}
                    >
                      <div
                        className={`max-w-[75%] px-3 py-2 rounded-lg ${msg.senderType === 'admin'
                          ? 'bg-gray-200 text-gray-800 rounded-tl-none'
                          : 'bg-teal-500 text-white rounded-tr-none'
                          }`}
                      >
                        <p className="text-sm">{msg.content}</p>
                        <div className="flex items-center justify-end mt-1 space-x-1">
                          <span className="text-xs opacity-70">
                            {formatTime(msg.timestamp)}
                          </span>
                          {msg.senderType !== 'admin' && msg.read && (
                            <Check size={12} className="opacity-70" />
                          )}
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Admin typing indicator */}
                  {adminTyping && (
                    <div className="mb-4 flex justify-start">
                      <div className="bg-gray-100 text-gray-500 px-3 py-2 rounded-lg rounded-tl-none">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse"></div>
                          <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse delay-100"></div>
                          <div className="w-2 h-2 rounded-full bg-gray-400 animate-pulse delay-200"></div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Chat ended message */}
                  {isChatEnded && (
                    <div className="bg-gray-100 text-gray-700 py-2 px-4 rounded text-center text-sm my-4">
                      {t('liveChat.chatEnded')}
                      <button
                        onClick={handleNewChat}
                        className="ml-2 text-teal-500 underline hover:text-teal-700"
                      >
                        {t('liveChat.startNewChat')}
                      </button>
                    </div>
                  )}

                  {/* Scroll to bottom helper div */}
                  <div ref={messagesEndRef} />
                </div>
              )}

              {/* Connection status indicator */}
              {isChatStarted && connectionStatus !== 'connected' && (
                <div className={`px-3 py-1 text-sm text-center text-white ${connectionStatus === 'reconnecting'
                  ? 'bg-yellow-500'
                  : connectionStatus === 'failed'
                    ? 'bg-red-700'
                    : 'bg-red-500'
                  }`}>
                  {connectionStatus === 'reconnecting' ? (
                    t('liveChat.reconnecting') || 'Reconnecting...'
                  ) : connectionStatus === 'failed' ? (
                    <div className="flex flex-col items-center justify-center gap-2">
                      <span>{t('liveChat.connectionFailed') || 'Connection failed.'}</span>
                      <div className="flex gap-2 flex-wrap justify-center">
                        <button
                          className="bg-white text-red-700 px-2 py-0.5 text-xs rounded hover:bg-gray-100"
                          onClick={() => {
                            if (directSocket) {
                              reconnectAttemptsRef.current = 0;
                              setConnectionStatus('reconnecting');
                              directSocket.connect();
                            }
                          }}
                        >
                          {t('liveChat.tryAgain') || 'Try Again'}
                        </button>
                        <button
                          className="bg-white text-blue-700 px-2 py-0.5 text-xs rounded hover:bg-gray-100"
                          onClick={() => {
                            // Clean up existing socket
                            if (directSocket) {
                              directSocket.disconnect();
                            }

                            // Try connecting to production with polling transport only
                            console.log('LiveChat: Trying production socket with polling only');
                            const productionUrl = import.meta.env.VITE_SOCKET_URL || 'https://api.e-exportcity.com';

                            const newSocket = io(productionUrl, {
                              path: '/socket.io',
                              transports: ['polling'], // Polling only for compatibility
                              reconnectionAttempts: 3,
                              reconnectionDelay: 1000,
                              timeout: 20000,
                              reconnection: true,
                              forceNew: true
                            });

                            setConnectionStatus('reconnecting');
                            setDirectSocket(newSocket);
                          }}
                        >
                          {t('liveChat.tryPolling') || 'Try Polling Only'}
                        </button>
                        <button
                          className="bg-white text-green-700 px-2 py-0.5 text-xs rounded hover:bg-gray-100 mt-1"
                          onClick={() => {
                            // Clean up existing socket
                            if (directSocket) {
                              directSocket.disconnect();
                              setDirectSocket(null);
                            }

                            // Set a flag to use REST API only mode
                            console.log('LiveChat: Switching to REST API only mode');
                            setConnectionStatus('connected'); // Pretend we're connected to prevent further reconnection attempts

                            // Start polling for messages using our polling function
                            if (chatId) {
                              startPollingMessages(chatId, !isLoggedIn ? anonymousId || undefined : undefined);
                            }
                          }}
                        >
                          {t('liveChat.fallbackMode') || 'Use No-Socket Mode'}
                        </button>
                      </div>
                    </div>
                  ) : (
                    t('liveChat.disconnected') || 'Disconnected from server'
                  )}
                </div>
              )}

              {/* Chat input */}
              {isChatStarted && !isChatEnded && (
                <div className="border-t border-gray-200 p-3 bg-white">
                  <div className="flex items-center">
                    <input
                      ref={inputRef}
                      type="text"
                      value={input}
                      onChange={handleInputChange}
                      onKeyPress={handleKeyPress}
                      placeholder={t('liveChat.typePlaceholder')}
                      disabled={isLoading}
                      className="flex-1 p-2 border border-gray-300 rounded-l focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all disabled:bg-gray-100"
                    />
                    <button
                      onClick={handleSendMessage}
                      disabled={!input.trim() || isLoading}
                      className="p-2 bg-teal-500 text-white rounded-r hover:bg-teal-600 focus:outline-none focus:ring-2 focus:ring-teal-300 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <Send size={20} />
                    </button>
                  </div>

                  {/* Chat actions */}
                  <div className="flex justify-between mt-2">
                    <span className="text-xs text-gray-500">
                      {t('liveChat.supportAvailable')}
                    </span>
                    <button
                      onClick={handleEndChat}
                      className="text-xs text-red-500 hover:underline focus:outline-none"
                    >
                      {t('liveChat.endChat')}
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default LiveChat;