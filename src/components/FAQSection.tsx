import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const FAQSection = () => {
  const navigate = useNavigate();
  const [openQuestion, setOpenQuestion] = useState<number | null>(null);

  const faqs = [
    {
      question: "E-exportcity nedir?",
      answer: "E-exportcity, Türkiye'nin önde gelen dijital ihracat platformudur. İşletmelerin global pazarlara açılmasını kolaylaştıran, güvenli ve yenilikçi bir ticaret ekosistemidir."
    },
    {
      question: "Nasıl üye olabilirim?",
      answer: "Platformumuza üye olmak için ana sayfadaki 'Üye Ol' butonuna tıklayarak kayıt formunu doldurmanız yeterlidir. Üyelik işleminiz tamamlandıktan sonra hesabınızı hemen kullanmaya başlayabilirsiniz."
    },
    {
      question: "Hangi hizmetleri sunuyorsunuz?",
      answer: "E-exportcity üzerinden firma profili oluşturma, ürün/hizmet listeleme, potansiyel müşterilerle iletişim kurma, ihracat danışmanlığı ve daha birçok hizmetten yararlanabilirsiniz."
    },
    {
      question: "Verilerimiz nasıl korunuyor?",
      answer: "En son güvenlik teknolojilerini kullanarak verilerinizi SSL şifreleme ve güvenlik duvarları ile koruyoruz. Düzenli güvenlik denetimleri gerçekleştiriyor ve veri güvenliği konusunda uluslararası standartlara uyuyoruz."
    }
  ];

  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Sıkça Sorulan Sorular
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            E-exportcity hakkında merak edilenler
          </p>
        </motion.div>

        <motion.div 
          className="max-w-3xl mx-auto space-y-4"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {faqs.map((faq, index) => (
            <motion.div
              key={index}
              className="bg-white rounded-xl shadow-sm overflow-hidden"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <button
                onClick={() => setOpenQuestion(openQuestion === index ? null : index)}
                className="w-full flex items-center justify-between p-6"
              >
                <span className="text-lg font-medium text-gray-900">
                  {faq.question}
                </span>
                {openQuestion === index ? (
                  <ChevronUp className="w-5 h-5 text-gray-500" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-500" />
                )}
              </button>
              {openQuestion === index && (
                <div className="px-6 pb-6">
                  <div className="prose max-w-none">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                </div>
              )}
            </motion.div>
          ))}
        </motion.div>

        <motion.div 
          className="text-center mt-8"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <button
            onClick={() => navigate('/faq')}
            className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors group"
          >
            <span>Tüm Soruları Görüntüle</span>
            <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default FAQSection;