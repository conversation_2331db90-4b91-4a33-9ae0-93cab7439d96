import React from "react";
import { Box, Text, VStack, useColorModeValue } from "@chakra-ui/react";
import { ResponsiveValue } from "@chakra-ui/react";

interface ImagePlaceholderProps {
  width: ResponsiveValue<string | number>;
  height: ResponsiveValue<string | number>;
  text?: string;
}

const ImagePlaceholder: React.FC<ImagePlaceholderProps> = ({
  width,
  height,
  text = "No Image Available",
}: any) => {
  const bgColor = useColorModeValue("gray.100", "gray.700");
  const textColor = useColorModeValue("gray.500", "gray.400");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  return (
    <Box
      width={width}
      height={height}
      bg={bgColor}
      borderWidth="1px"
      borderColor={borderColor}
      borderStyle="dashed"
      borderRadius="md"
      display="flex"
      alignItems="center"
      justifyContent="center"
    >
      <VStack spacing={2}>
        <Text color={textColor} fontSize="sm" fontWeight="medium">
          {text}
        </Text>
        <Text color={textColor} fontSize="xs">
          {typeof width === "number" ? `${width}px` : width} ×{" "}
          {typeof height === "number" ? `${height}px` : height}
        </Text>
      </VStack>
    </Box>
  );
};

export default ImagePlaceholder;
