import React from "react";
import {
  Box,
  Flex,
  Button,
  Menu,
  Menu<PERSON>utton,
  MenuList,
  MenuItem,
  useColorModeValue,
  Avatar,
  HStack,
  Text,
  Container,
  Image,
} from "@chakra-ui/react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ChevronDownIcon } from "@chakra-ui/icons";
import LanguageSelector from "./LanguageSelector";
import { useAuth } from "@/context/AuthContext";
// Navigation icons moved to AdminPanel component

const AdminHeader: React.FC = () => {
  const { t, i18n } = useTranslation(["admin", "common"]);
  const { logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const bg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  const isRTL = i18n.dir() === "rtl";
  const isAdminPath = location.pathname.startsWith('/admin');

  // Admin navigation has been moved to the sidebar within AdminPanel

  return (
    <Box
      as="header"
      position="sticky"
      top={0}
      left={0}
      bg={bg}
      borderBottomWidth="1px"
      borderColor={borderColor}
      display={"flex"}
      justifyContent={"center"}
      width="100%"
      zIndex={1000}
      px={4}
      boxShadow="sm"
    >
      <Container margin={"0"} maxW={isAdminPath ? "100%" : "container.xl"}>
        <Flex
          h={16}
          alignItems="center"
          justifyContent="space-between"
          direction={isRTL ? "row-reverse" : "row"}
        >
          <Box
            as="a"
            href="/admin/dashboard"
            style={{
              cursor: "pointer",
              flexShrink: 0,
              marginRight: isRTL ? 0 : "20px",
              marginLeft: isRTL ? "20px" : 0,
            }}
            onClick={(e: any) => {
              e.preventDefault();
              navigate("/admin/dashboard");
            }}
          >
            <Image
              src="/logo.png"
              alt="Admin Logo"
              h="40px"
              maxW="140px"
              objectFit="contain"
            />
          </Box>

          <HStack spacing={3} width="100%" justifyContent="flex-end">
            <LanguageSelector />
            <Menu>
              <MenuButton
                as={Button}
                rightIcon={<ChevronDownIcon />}
                variant="ghost"
                size="sm"
              >
                <HStack>
                  <Avatar
                    size="xs"
                    name="Admin User"
                    src="/default-avatar.png"
                  />

                  <Text display={{ base: "none", md: "block" }}>
                    {t("common:admin")}
                  </Text>
                </HStack>
              </MenuButton>
              <MenuList>
                <MenuItem
                  onClick={() => {
                    logout();
                    navigate("/admin/login");
                  }}
                >
                  {t("header.logout")}
                </MenuItem>
              </MenuList>
            </Menu>
          </HStack>
        </Flex>
      </Container>

      {/* Drawer removed as navigation is now in the sidebar */}
    </Box>
  );
};

export default AdminHeader;
