import React, { createContext, useContext, useEffect, useState } from "react";
import { useSocket } from "../../context/SocketContext";
import { useToast } from "@chakra-ui/react";
import { getUnreadNotifications, markNotificationAsRead } from "../../api";

interface Notification {
  _id: string;
  type: string;
  title: string;
  message: string;
  data?: Record<string, any>;
  timestamp: Date;
  read: boolean;
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  clearNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined,
);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const socket = useSocket();
  const toast = useToast();

  useEffect(() => {
    // Fetch initial unread notifications
    const fetchNotifications = async () => {
      try {
        const response = await getUnreadNotifications();
        setNotifications(response.data);
      } catch (error: any) {
        console.error("Error fetching notifications:", error);
      }
    };

    fetchNotifications();
  }, []);

  useEffect(() => {
    if (!socket) return;

    (socket as any).on("notification", (newNotification: Notification) => {
      setNotifications((prev) => [newNotification, ...prev]);

      // Show toast notification if the window is not focused
      if (document.hidden) {
        toast({
          title: newNotification.title,
          description: newNotification.message,
          status: "info",
          duration: 5000,
          isClosable: true,
          position: "top-right",
          onCloseComplete: () => {
            handleNotificationClick(newNotification);
          },
        });

        // Also show browser notification if permitted
        if (Notification.permission === "granted") {
          new Notification(newNotification.title, {
            body: newNotification.message,
          });
        }
      }
    });

    (socket as any).on(
      "notificationRead",
      ({ notificationId }: { notificationId: string }) => {
        setNotifications((prev) =>
          prev.map((notification) =>
            notification._id === notificationId
              ? { ...notification, read: true }
              : notification,
          ),
        );
      },
    );

    (socket as any).on(
      "notificationDeleted",
      ({ notificationId }: { notificationId: string }) => {
        setNotifications((prev) =>
          prev.filter((notification) => notification._id !== notificationId),
        );
      },
    );

    return () => {
      (socket as any).off("notification");
      (socket as any).off("notificationRead");
      (socket as any).off("notificationDeleted");
    };
  }, [socket, toast]);

  // Request notification permission on mount
  useEffect(() => {
    if (Notification.permission === "default") {
      Notification.requestPermission();
    }
  }, []);

  const handleNotificationClick = (notification: Notification) => {
    // Mark as read
    markAsRead(notification._id);

    // Handle different notification types
    switch (notification.type) {
      case "newMessage":
        // Navigate to the message
        if (notification.data?.messageId) {
          // You can implement navigation logic here
          window.location.href = `/messages?messageId=${notification.data.messageId}`;
        }
        break;
      case "productUpdate":
        // Navigate to the product
        if (notification.data?.productId) {
          window.location.href = `/products/${notification.data.productId}`;
        }
        break;
      // Add more cases as needed
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      await markNotificationAsRead(notificationId);
      setNotifications((prev) =>
        prev.map((notification) =>
          notification._id === notificationId
            ? { ...notification, read: true }
            : notification,
        ),
      );
    } catch (error: any) {
      console.error("Error marking notification as read:", error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const unreadIds = notifications.filter((n) => !n.read).map((n) => n._id);

      await Promise.all(unreadIds.map((id) => markNotificationAsRead(id)));

      setNotifications((prev) =>
        prev.map((notification) => ({ ...notification, read: true })),
      );
    } catch (error: any) {
      console.error("Error marking all notifications as read:", error);
    }
  };

  const clearNotification = (notificationId: string) => {
    setNotifications((prev) =>
      prev.filter((notification) => notification._id !== notificationId),
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  const value = {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotifications must be used within a NotificationProvider",
    );
  }
  return context;
};
