import React, { createContext, useContext, useEffect, useState } from "react";
import { useSocket } from "../../context/SocketContext";
import { getUnreadNotifications } from "@/api";

interface NotificationContextProps {
  count: number;
  notifications: string[];
  markAsRead: () => void;
}

const NotificationContext = createContext<NotificationContextProps>({
  count: 0,
  notifications: [],
  markAsRead: () => {},
});

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const socket = useSocket();
  const [count, setCount] = useState(0);
  const [notifications, setNotifications] = useState<string[]>([]);

  useEffect(() => {
    // Fetch unread notifications on mount
    getUnreadNotifications().then((unread) => {
      setCount(unread.data.length);
      setNotifications(unread.data);
    });

    if (socket) {
      (socket as any).on(
        "notification",
        (data: { message: string; timestamp: string }) => {
          setCount((prev) => prev + 1);
          setNotifications((prev) => [...prev, data.message]);
        },
      );
    }

    return () => {
      if (socket) {
        (socket as any).off("notification");
      }
    };
  }, [socket]);

  const markAsRead = () => {
    setCount(0);
    // Optionally, make an API call to mark notifications as read in the backend
  };

  return (
    <NotificationContext.Provider value={{ count, notifications, markAsRead }}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  return useContext(NotificationContext);
};
