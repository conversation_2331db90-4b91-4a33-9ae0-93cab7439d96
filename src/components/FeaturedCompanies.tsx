import React from 'react';
import { useNavigate } from 'react-router-dom';
import { BadgeCheck, MapPin, ExternalLink, Eye } from 'lucide-react';
import { isLoggedIn } from '../data/testUser';

const FeaturedCompanies = () => {
  const navigate = useNavigate();
  const loggedIn = isLoggedIn();

  const companies = [
    {
      id: 'techglobal-solutions',
      name: "TechGlobal Solutions",
      location: "İstanbul, Türkiye",
      image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&q=80",
      logo: "https://images.unsplash.com/photo-1599305445671-ac291c95aaa9?auto=format&fit=crop&q=80",
      views: "1.2K",
      verified: true
    },
    {
      id: 'ecotrade-international',
      name: "EcoTrade International",
      location: "İzmir, Türkiye",
      image: "https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?auto=format&fit=crop&q=80",
      logo: "https://images.unsplash.com/photo-1542744094-24638eff58bb?auto=format&fit=crop&q=80",
      views: "980",
      verified: true
    },
    {
      id: 'medicare-exports',
      name: "MediCare Exports",
      location: "Ankara, Türkiye",
      image: "https://images.unsplash.com/photo-1538108149393-fbbd81895907?auto=format&fit=crop&q=80",
      logo: "https://images.unsplash.com/photo-1505751172876-fa1923c5c528?auto=format&fit=crop&q=80",
      views: "850",
      verified: false
    },
    {
      id: 'buildpro-construction',
      name: "BuildPro Construction",
      location: "Bursa, Türkiye",
      image: "https://images.unsplash.com/photo-1541976590-713941681591?auto=format&fit=crop&q=80",
      logo: "https://images.unsplash.com/photo-1507207611509-ec012433ff52?auto=format&fit=crop&q=80",
      views: "1.1K",
      verified: true
    }
  ];

  const handleCompanyClick = (companyId: string) => {
    if (loggedIn) {
      navigate(`/companies/${companyId}`);
    } else {
      navigate('/register');
    }
  };

  return (
    <div className="bg-gradient-to-b from-gray-50 to-white py-16">
      <div className="max-w-[1920px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-12 px-4 md:px-12">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">Öne Çıkan Firmalar</h2>
            <p className="mt-3 text-lg text-gray-600">Global ticarette öne çıkan güvenilir partnerler</p>
          </div>
          <button 
            onClick={() => navigate('/companies')}
            className="hidden md:inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
          >
            <span>Tüm Firmaları Gör</span>
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-4 md:px-12">
          {companies.map((company, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
              <div className="aspect-[4/3] relative overflow-hidden">
                <img 
                  src={company.image}
                  alt={company.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                <div className="absolute bottom-4 left-4 flex items-center space-x-2">
                  <div className="w-12 h-12 rounded-lg overflow-hidden border-2 border-white">
                    <img 
                      src={company.logo}
                      alt={`${company.name} logo`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  {company.verified && (
                    <BadgeCheck className="h-6 w-6 text-primary bg-white rounded-full p-1" />
                  )}
                </div>
              </div>

              <div className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{company.name}</h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{company.location}</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2 mb-4">
                  <Eye className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">{company.views} görüntülenme</span>
                </div>

                <button 
                  onClick={() => handleCompanyClick(company.id)}
                  className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
                >
                  <span>Detayları Gör</span>
                  <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 text-center md:hidden">
          <button 
            onClick={() => navigate('/companies')}
            className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
          >
            <span>Tüm Firmaları Gör</span>
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeaturedCompanies;