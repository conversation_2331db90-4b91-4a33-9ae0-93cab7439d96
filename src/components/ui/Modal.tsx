import React from "react";
import {
  Modal as <PERSON><PERSON>Modal,
  <PERSON>dal<PERSON><PERSON>lay,
  Modal<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dal<PERSON>ody,
  <PERSON>dal<PERSON>ooter,
  ModalCloseButton,
  But<PERSON>,
} from "@chakra-ui/react";

type ModalProps = {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
};

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  return (
    <ChakraModal isOpen={isOpen} onClose={onClose}>
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>{title}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>{children}</ModalBody>
        <ModalFooter>
          <Button colorScheme="blue" mr={3} onClick={onClose}>
            Close
          </Button>
        </ModalFooter>
      </ModalContent>
    </ChakraModal>
  );
};

export default Modal;
