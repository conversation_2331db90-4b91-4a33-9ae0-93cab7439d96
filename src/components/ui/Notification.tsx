import React from "react";
import { useToast } from "@chakra-ui/react";

type NotificationProps = {
  title: string;
  description: string;
  status: "success" | "error" | "warning" | "info";
};

const Notification: React.FC<NotificationProps> = ({
  title,
  description,
  status,
}) => {
  const toast = useToast();

  React.useEffect(() => {
    toast({
      title,
      description,
      status,
      duration: 5000,
      isClosable: true,
    });
  }, [toast, title, description, status]);

  return null;
};

export default Notification;
