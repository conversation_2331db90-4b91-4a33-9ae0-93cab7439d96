import React from "react";
import {
  Select as ChakraSelect,
  SelectProps as ChakraSelectProps,
} from "@chakra-ui/react";

type SelectProps = ChakraSelectProps & {
  options: { label: string; value: string }[];
};

const Select: React.FC<SelectProps> = ({ options, ...props }) => {
  return (
    <ChakraSelect {...props}>
      {options.map((option, index) => (
        <option key={index} value={option.value}>
          {option.label}
        </option>
      ))}
    </ChakraSelect>
  );
};

export default Select;
