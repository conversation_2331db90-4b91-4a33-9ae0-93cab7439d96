import React from "react";
import {
  Box,
  Heading,
  Text,
  useColorModeValue,
  VStack,
  Icon,
  SimpleGrid,
  Button,
  Flex,
  useDisclosure,
} from "@chakra-ui/react";
import {
  FiUser,
  FiMail,
  FiSettings,
  FiGlobe,
  FiTrendingUp,
  FiHelpCircle,
} from "react-icons/fi";
import { IconType } from "react-icons/lib";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";

const UserDashboard: React.FC = () => {
  const { t, i18n } = useTranslation();
  const bgColor = useColorModeValue("gray.50", "gray.900");
  const textColor = useColorModeValue("gray.700", "gray.200");
  const { onToggle } = useDisclosure();

  const toggleLanguage = () => {
    i18n.changeLanguage(i18n.language === "en" ? "tr" : "en");
    onToggle();
  };

  return (
    <Box p={8} bg={bgColor} minHeight="100vh">
      <Flex justifyContent="space-between" alignItems="center" mb={8}>
        <Heading as="h1" size="2xl" color="primary.600" fontWeight="bold">
          {t("userDashboard.title")}
        </Heading>
        <Button
          leftIcon={<FiGlobe />}
          onClick={toggleLanguage}
          colorScheme="teal"
        >
          {i18n.language === "en" ? "Türkçe" : "English"}
        </Button>
      </Flex>
      <Text fontSize="xl" color={textColor} mb={8}>
        {t("userDashboard.welcome")}
      </Text>
      <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={8}>
        <DashboardCard
          icon={FiUser}
          title={t("userDashboard.profile.title")}
          description={t("userDashboard.profile.description")}
        />

        <DashboardCard
          icon={FiMail}
          title={t("userDashboard.messages.title")}
          description={t("userDashboard.messages.description")}
        />

        <DashboardCard
          icon={FiSettings}
          title={t("userDashboard.settings.title")}
          description={t("userDashboard.settings.description")}
        />

        <DashboardCard
          icon={FiTrendingUp}
          title={t("userDashboard.analytics.title")}
          description={t("userDashboard.analytics.description")}
        />

        <DashboardCard
          icon={FiHelpCircle}
          title={t("userDashboard.help.title")}
          description={t("userDashboard.help.description")}
        />
      </SimpleGrid>
    </Box>
  );
};

const DashboardCard: React.FC<{
  icon: IconType;
  title: string;
  description: string;
}> = ({ icon, title, description }) => {
  const cardBgColor = useColorModeValue("white", "gray.800");
  const hoverBgColor = useColorModeValue("gray.100", "gray.700");

  return (
    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
      <Box
        p={6}
        bg={cardBgColor}
        shadow="xl"
        borderRadius="xl"
        transition="all 0.3s"
        _hover={{ bg: hoverBgColor, transform: "translateY(-5px)" }}
      >
        <VStack align="start" spacing={4}>
          <Icon as={icon} boxSize={10} color="primary.500" />
          <Heading size="lg" fontWeight="semibold">
            {title}
          </Heading>
          <Text color="gray.500" fontSize="md">
            {description}
          </Text>
        </VStack>
      </Box>
    </motion.div>
  );
};

export default UserDashboard;
