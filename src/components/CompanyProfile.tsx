{/* Previous imports remain unchanged */}

const CompanyProfile = () => {
  const [companyData, setCompanyData] = useState({
    // Previous data remains unchanged
    type: 'company', // 'company', 'individual', 'broker'
    approvalStatus: 'pending', // 'pending', 'approved', 'rejected'
    // Add other fields...
  });

  // Add approval status badge
  const getApprovalBadge = () => {
    switch (companyData.approvalStatus) {
      case 'approved':
        return (
          <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
            Onaylandı
          </span>
        );
      case 'rejected':
        return (
          <span className="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm">
            Reddedildi
          </span>
        );
      default:
        return (
          <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">
            Onay Bekliyor
          </span>
        );
    }
  };

  // Add company type badge
  const getCompanyTypeBadge = () => {
    switch (companyData.type) {
      case 'company':
        return (
          <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
            Kurumsal Firma
          </span>
        );
      case 'individual':
        return (
          <span className="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm">
            Şahıs Firması
          </span>
        );
      case 'broker':
        return (
          <span className="px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-sm">
            Broker
          </span>
        );
    }
  };

  return (
    <Layout>
      <div className="bg-gray-50 min-h-screen">
        {/* Hero Section */}
        <div className="relative h-[300px] md:h-[400px]">
          {/* Previous hero content remains unchanged */}
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 w-full max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                {/* Previous profile content */}
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    {getCompanyTypeBadge()}
                    {getApprovalBadge()}
                  </div>
                  {/* Previous content */}
                </div>
              </div>
              {/* Previous buttons */}
            </div>
          </div>
        </div>
        {/* Rest of the component remains unchanged */}
      </div>
    </Layout>
  );
};

export default CompanyProfile;