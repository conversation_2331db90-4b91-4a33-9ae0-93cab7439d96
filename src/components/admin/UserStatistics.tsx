import React from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  <PERSON>,
  Re<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from "recharts";
import {
  Box,
  Grid,
  Heading,
  useColorModeValue,
  Card,
  CardHeader,
  CardBody,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  SimpleGrid,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  Flex,
  Badge,
  IconButton,
  Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  PopoverArrow,
  PopoverCloseButton,
  List,
  ListItem,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { FiUsers } from "react-icons/fi";

interface ReferrerType {
  _id: string;
  count: number;
  usersWithSubscription: number;
  referrer: {
    firstName: string;
    lastName: string;
    email: string;
  }
}

interface UserStatisticsProps {
  activeUsers: number;
  totalUsers: number;
  newUsers: number;
  userGrowth: number;
  topReferrers?: ReferrerType[];
  userActivityData: Array<{
    date: string;
    active: number;
    inactive: number;
  }>;
  userRecruitmentData: Array<{
    month: string;
    users: number;
  }>;
  userTypeDistribution: Array<{
    type: string;
    value: number;
  }>;
}

const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042"];

export const UserStatistics: React.FC<UserStatisticsProps> = ({
  activeUsers = 0,
  totalUsers = 0,
  newUsers = 0,
  userGrowth = 0,
  topReferrers = [],
  userActivityData = [],
  userRecruitmentData = [],
  userTypeDistribution = [],
}) => {
  const { t } = useTranslation("admin");
  const cardBg = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  return (
    <Box>
      <Heading size="lg" mb={6}>
        {t("dashboard.title")}
      </Heading>

      {/* Key stats */}
      <SimpleGrid minChildWidth="200px" spacing={6} mb={8}>
        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor}>
          <CardBody>
            <Stat>
              <StatLabel>{t("dashboard.stats.activeUsers")}</StatLabel>
              <StatNumber>{activeUsers.toLocaleString()}</StatNumber>
              <StatHelpText>
                <StatArrow
                  type={userGrowth >= 0 ? "increase" : "decrease"}
                />
                {userGrowth}%
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor}>
          <CardBody>
            <Stat>
              <StatLabel>{t("dashboard.stats.totalUsers")}</StatLabel>
              <StatNumber>{totalUsers.toLocaleString()}</StatNumber>
            </Stat>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor}>
          <CardBody>
            <Stat>
              <StatLabel>{t("dashboard.stats.newUsers")}</StatLabel>
              <StatNumber>{newUsers.toLocaleString()}</StatNumber>
              <StatHelpText>
                {t("dashboard.stats.last30Days")}
              </StatHelpText>
            </Stat>
          </CardBody>
        </Card>
      </SimpleGrid>

      {/* Referral Stats Section */}
      {topReferrers && topReferrers.length > 0 && (
        <Card bg={cardBg} borderWidth="1px" borderColor={borderColor} mb={8}>
          <CardHeader>
            <Heading size="md">{t("dashboard.stats.topReferrers")}</Heading>
          </CardHeader>
          <CardBody>
            <Table variant="simple">
              <Thead>
                <Tr>
                  <Th>{t("dashboard.referrals.name")}</Th>
                  <Th>{t("dashboard.referrals.email")}</Th>
                  <Th isNumeric>{t("dashboard.referrals.total")}</Th>
                  <Th isNumeric>{t("dashboard.referrals.withSubscription")}</Th>
                  <Th>{t("dashboard.referrals.conversionRate")}</Th>
                  <Th>{t("dashboard.referrals.actions")}</Th>
                </Tr>
              </Thead>
              <Tbody>
                {topReferrers.map((ref) => (
                  <Tr key={ref._id}>
                    <Td>
                      {ref.referrer.firstName} {ref.referrer.lastName}
                    </Td>
                    <Td>{ref.referrer.email}</Td>
                    <Td isNumeric>{ref.count}</Td>
                    <Td isNumeric>{ref.usersWithSubscription}</Td>
                    <Td>
                      <Badge
                        colorScheme={
                          ref.usersWithSubscription / ref.count > 0.5
                            ? "green"
                            : ref.usersWithSubscription / ref.count > 0.2
                              ? "yellow"
                              : "red"
                        }
                      >
                        {((ref.usersWithSubscription / ref.count) * 100).toFixed(1)}%
                      </Badge>
                    </Td>
                    <Td>
                      <Popover>
                        <PopoverTrigger>
                          <IconButton
                            aria-label={t("dashboard.referrals.viewDetails")}
                            icon={<FiUsers />}
                            size="sm"
                            variant="outline"
                          />
                        </PopoverTrigger>
                        <PopoverContent>
                          <PopoverArrow />
                          <PopoverCloseButton />
                          <PopoverHeader>
                            {t("dashboard.referrals.details")}
                          </PopoverHeader>
                          <PopoverBody>
                            <List spacing={2}>
                              <ListItem>
                                <Flex justify="space-between">
                                  <Text>{t("dashboard.referrals.totalReferrals")}</Text>
                                  <Text fontWeight="bold">{ref.count}</Text>
                                </Flex>
                              </ListItem>
                              <ListItem>
                                <Flex justify="space-between">
                                  <Text>{t("dashboard.referrals.withPackage")}</Text>
                                  <Text fontWeight="bold">{ref.usersWithSubscription}</Text>
                                </Flex>
                              </ListItem>
                              <ListItem>
                                <Flex justify="space-between">
                                  <Text>{t("dashboard.referrals.withoutPackage")}</Text>
                                  <Text fontWeight="bold">{ref.count - ref.usersWithSubscription}</Text>
                                </Flex>
                              </ListItem>
                              <ListItem>
                                <Flex justify="space-between">
                                  <Text>{t("dashboard.referrals.conversionRate")}</Text>
                                  <Text fontWeight="bold">
                                    {((ref.usersWithSubscription / ref.count) * 100).toFixed(1)}%
                                  </Text>
                                </Flex>
                              </ListItem>
                            </List>
                          </PopoverBody>
                        </PopoverContent>
                      </Popover>
                    </Td>
                  </Tr>
                ))}
              </Tbody>
            </Table>
          </CardBody>
        </Card>
      )}

      {/* Graphs */}
      <Grid templateColumns={{ base: "1fr", lg: "1fr 1fr" }} gap={6}>
        {/* User Activity */}
        <Card bg={cardBg} borderColor={borderColor} borderWidth="1px">
          <CardHeader>
            <Heading size="md">{t("dashboard.charts.userActivity")}</Heading>
          </CardHeader>
          <CardBody>
            <Box h="300px">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={userActivityData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="active"
                    stroke="#0088FE"
                    strokeWidth={2}
                  />

                  <Line
                    type="monotone"
                    dataKey="inactive"
                    stroke="#FF8042"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </Box>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderColor={borderColor} borderWidth="1px">
          <CardHeader>
            <Heading size="md">{t("dashboard.charts.userRecruitment")}</Heading>
          </CardHeader>
          <CardBody>
            <Box h="300px">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={userRecruitmentData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Bar dataKey="users" fill="#0088FE" />
                </BarChart>
              </ResponsiveContainer>
            </Box>
          </CardBody>
        </Card>

        <Card bg={cardBg} borderColor={borderColor} borderWidth="1px">
          <CardHeader>
            <Heading size="md">
              {t("dashboard.charts.userDistribution")}
            </Heading>
          </CardHeader>
          <CardBody>
            <Box h="300px">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={userTypeDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${name} ${(percent * 100).toFixed(0)}%`
                    }
                  >
                    {userTypeDistribution.map((_: any, index: number) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={COLORS[index % COLORS.length]}
                      />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Box>
          </CardBody>
        </Card>
      </Grid>
    </Box>
  );
};
