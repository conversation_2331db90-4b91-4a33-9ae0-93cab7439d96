import React, { useState, useEffect } from "react";
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  useToast,
  Badge,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  Text,
  useDisclosure,
  Stack,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import {
  getCancellationRequests,
  approveCancellation,
  rejectCancellation,
} from "../../api/adminApi";

interface CancellationRequest {
  _id: string;
  userId: {
    _id: string;
    email: string;
    fullName: string;
  };
  subscriptionId: string;
  packageId: {
    _id: string;
    name: string;
    nameEn: string;
  };
  reason: string;
  status: "pending" | "approved" | "rejected";
  createdAt: string;
}

const CancellationRequests: React.FC = () => {
  const { t } = useTranslation("cancelRequests");
  const toast = useToast();
  const [requests, setRequests] = useState<CancellationRequest[]>([]);
  const [selectedRequest, setSelectedRequest] =
    useState<CancellationRequest | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const fetchRequests = async () => {
    try {
      const response = await getCancellationRequests();
      setRequests(response.data);
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  useEffect(() => {
    fetchRequests();
  }, []);

  const handleViewReason = (request: CancellationRequest) => {
    setSelectedRequest(request);
    onOpen();
  };

  const handleAction = async (
    requestId: string,
    action: "approve" | "reject",
  ) => {
    try {
      setIsLoading(true);
      if (action === "approve") {
        await approveCancellation(requestId);
      } else {
        await rejectCancellation(requestId);
      }
      await fetchRequests();
      toast({
        title: t(`admin.${action}Success`),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: t(`admin.${action}Error`),
        description: error.message,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "yellow";
      case "approved":
        return "green";
      case "rejected":
        return "red";
      default:
        return "gray";
    }
  };

  return (
    <Box p={4}>
      <Stack spacing={4}>
        <Text fontSize="2xl" fontWeight="bold">
          {t("admin.title")}
        </Text>

        {requests.length === 0 ? (
          <Text>{t("admin.noRequests")}</Text>
        ) : (
          <Table variant="simple">
            <Thead>
              <Tr>
                <Th>{t("admin.requestDate")}</Th>
                <Th>{t("admin.user")}</Th>
                <Th>{t("admin.package")}</Th>
                <Th>{t("admin.status")}</Th>
                <Th>{t("admin.actions")}</Th>
              </Tr>
            </Thead>
            <Tbody>
              {requests.map((request) => (
                <Tr key={request._id}>
                  <Td>{new Date(request.createdAt).toLocaleDateString()}</Td>
                  <Td>{request.userId.fullName}</Td>
                  <Td>{request.packageId.name}</Td>
                  <Td>
                    <Badge colorScheme={getStatusColor(request.status)}>
                      {t(`status.${request.status}`)}
                    </Badge>
                  </Td>
                  <Td>
                    <Stack direction="row" spacing={2}>
                      <Button
                        size="sm"
                        onClick={() => handleViewReason(request)}
                      >
                        {t("admin.viewReason")}
                      </Button>
                      {request.status === "pending" && (
                        <>
                          <Button
                            size="sm"
                            colorScheme="green"
                            onClick={() => handleAction(request._id, "approve")}
                            isLoading={isLoading}
                          >
                            {t("admin.approve")}
                          </Button>
                          <Button
                            size="sm"
                            colorScheme="red"
                            onClick={() => handleAction(request._id, "reject")}
                            isLoading={isLoading}
                          >
                            {t("admin.reject")}
                          </Button>
                        </>
                      )}
                    </Stack>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        )}
      </Stack>

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("reason")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <Text>{selectedRequest?.reason}</Text>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default CancellationRequests;
