import React, { useState, useEffect } from "react";
import { Box, VStack, useToast } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import ImagePreview from "./ImagePreview";

interface ImageValidatorProps {
  imageUrl: string;
  onValidationComplete?: (
    status: "approved" | "rejected",
    message: string,
  ) => void;
}

const ImageValidator: React.FC<ImageValidatorProps> = ({
  imageUrl,
  onValidationComplete,
}) => {
  const { t } = useTranslation("admin");
  const toast = useToast();
  const [validationStatus, setValidationStatus] = useState<
    "pending" | "approved" | "rejected"
  >("pending");
  const [validationMessage, setValidationMessage] = useState("");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const validateImage = async () => {
      try {
        setIsLoading(true);

        // Simulated image validation API call
        // Replace this with actual API call to your image validation service
        const response = await new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              status: "approved",
              message: "Image content is appropriate",
            });
          }, 2000);
        });

        const result = response as {
          status: "approved" | "rejected";
          message: string;
        };

        setValidationStatus(result.status);
        setValidationMessage(result.message);

        if (onValidationComplete) {
          onValidationComplete(result.status, result.message);
        }

        if (result.status === "rejected") {
          toast({
            title: t("validation.rejected"),
            description: result.message,
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        }
      } catch (error) {
        setValidationStatus("rejected");
        setValidationMessage(t("validation.error"));
        toast({
          title: t("validation.error"),
          description: t("validation.errorMessage"),
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (imageUrl) {
      validateImage();
    }
  }, [imageUrl, onValidationComplete, t, toast]);

  return (
    <VStack spacing={4} w="full">
      <Box w="full">
        <ImagePreview
          src={imageUrl}
          validationStatus={validationStatus}
          validationMessage={validationMessage}
          isLoading={isLoading}
        />
      </Box>
    </VStack>
  );
};

export default ImageValidator;
