import React from "react";
import {
  <PERSON>,
  <PERSON>,
  Spinner,
  Badge,
  VStack,
  Text,
  Icon,
  Tooltip,
} from "@chakra-ui/react";
import { FaCheck, FaTimes, FaExclamationTriangle } from "react-icons/fa";
import { useTranslation } from "react-i18next";

interface ImagePreviewProps {
  src: string;
  alt?: string;
  validationStatus?: "pending" | "approved" | "rejected";
  validationMessage?: string;
  isLoading?: boolean;
}

const ImagePreview: React.FC<ImagePreviewProps> = ({
  src,
  alt = "Preview image",
  validationStatus = "pending",
  validationMessage = "",
  isLoading = false,
}) => {
  const { t } = useTranslation("admin");

  const getStatusColor = () => {
    switch (validationStatus) {
      case "approved":
        return "green";
      case "rejected":
        return "red";
      default:
        return "yellow";
    }
  };

  const getStatusIcon = () => {
    switch (validationStatus) {
      case "approved":
        return FaCheck;
      case "rejected":
        return FaTimes;
      default:
        return FaExclamationTriangle;
    }
  };

  return (
    <VStack spacing={2} align="center" w="full">
      <Box position="relative" w="full">
        {isLoading ? (
          <Spinner size="xl" />
        ) : (
          <>
            <Image
              src={src}
              alt={alt}
              objectFit="cover"
              w="full"
              h="200px"
              borderRadius="md"
              opacity={validationStatus === "rejected" ? 0.7 : 1}
            />

            <Tooltip label={validationMessage} placement="top">
              <Badge
                position="absolute"
                top={2}
                right={2}
                colorScheme={getStatusColor()}
                display="flex"
                alignItems="center"
                gap={1}
                px={2}
                py={1}
                borderRadius="full"
              >
                <Icon as={getStatusIcon()} />
                <Text fontSize="sm" textTransform="capitalize">
                  {t(`validation.${validationStatus}`)}
                </Text>
              </Badge>
            </Tooltip>
          </>
        )}
      </Box>
      {validationMessage && (
        <Text fontSize="sm" color={`${getStatusColor()}.500`}>
          {validationMessage}
        </Text>
      )}
    </VStack>
  );
};

export default ImagePreview;
