import React, { useEffect, useState } from "react";
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Button,
  useToast,
  Text,
  Image,
  HStack,
  Spinner,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  VStack,
  FormControl,
  FormLabel,
  Textarea,
  useDisclosure,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  IconButton,
  Tooltip,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import { adminApi } from "@/adminApi";
import { FiCheck, FiX, FiEye, FiTrash2 } from "react-icons/fi";

interface IHomepageAd {
  _id: string;
  userId: string;
  userName: string;
  image: string;
  title: string;
  clicks: number;
  status: "pending" | "approved" | "rejected";
  createdAt: string;
  expiresAt: string;
  rejectionReason?: string;
}

const HomepageAdList: React.FC = () => {
  const { t } = useTranslation(["admin", "common"]);
  const toast = useToast();
  const [ads, setAds] = useState<IHomepageAd[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAd, setSelectedAd] = useState<IHomepageAd | null>(null);
  const [rejectionReason, setRejectionReason] = useState("");
  const { isOpen, onOpen, onClose } = useDisclosure();

  // For delete confirmation
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);
  const [adToDelete, setAdToDelete] = useState<string | null>(null);
  const cancelRef = React.useRef<HTMLButtonElement>(null);

  useEffect(() => {
    fetchAds();
  }, []);

  const fetchAds = async () => {
    try {
      setIsLoading(true);
      const response = await adminApi.get("/admin/home-ads");
      setAds(response.data);
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.fetch_error", "Failed to fetch homepage ads"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      console.error("Error fetching homepage ads:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleApprove = async (adId: string) => {
    try {
      const response = await adminApi.put(`/admin/home-ads/${adId}/approve`);

      if (response.status === 200) {
        toast({
          title: t("common:success"),
          description: t("admin:homepageAd.messages.approve_success", "Ad approved successfully"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        await fetchAds();
      } else {
        throw new Error("Failed to approve ad");
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.approve_error", "Failed to approve ad"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      console.error("Error approving homepage ad:", error);
    }
  };

  const handleReject = async () => {
    if (!selectedAd || !rejectionReason.trim()) return;

    try {
      const response = await adminApi.put(
        `/admin/home-ads/${selectedAd._id}/reject`,
        {
          reason: rejectionReason,
        }
      );

      if (response.status === 200) {
        toast({
          title: t("common:success"),
          description: t("admin:homepageAd.messages.reject_success", "Ad rejected successfully"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        onClose();
        setRejectionReason("");
        setSelectedAd(null);
        await fetchAds();
      } else {
        throw new Error("Failed to reject ad");
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.reject_error", "Failed to reject ad"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      console.error("Error rejecting homepage ad:", error);
    }
  };

  const handleDelete = async (adId: string) => {
    try {
      const response = await adminApi.delete(`/admin/home-ads/${adId}`);

      if (response.status === 200 || response.status === 204) {
        toast({
          title: t("common:success"),
          description: t("admin:homepageAd.messages.delete_success", "Ad deleted successfully"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
        await fetchAds();
      } else {
        throw new Error("Failed to delete ad");
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("admin:homepageAd.messages.delete_error", "Failed to delete ad"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      console.error("Error deleting homepage ad:", error);
    } finally {
      setIsDeleteAlertOpen(false);
      setAdToDelete(null);
    }
  };

  const openRejectModal = (ad: IHomepageAd) => {
    setSelectedAd(ad);
    setRejectionReason("");
    onOpen();
  };

  const openDeleteConfirmation = (adId: string) => {
    setAdToDelete(adId);
    setIsDeleteAlertOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "green";
      case "pending":
        return "yellow";
      case "rejected":
        return "red";
      default:
        return "gray";
    }
  };

  return (
    <Box>
      {isLoading ? (
        <Box py={10} textAlign="center">
          <Spinner size="xl" />
          <Text mt={4}>{t("common:loading")}</Text>
        </Box>
      ) : ads.length === 0 ? (
        <Box py={10} textAlign="center">
          <Text>{t("admin:homepageAd.messages.no_ads", "No homepage ads found")}</Text>
        </Box>
      ) : (
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>{t("admin:homepageAd.table.image", "Image")}</Th>
              <Th>{t("admin:homepageAd.table.title", "Title")}</Th>
              <Th>{t("admin:homepageAd.table.user", "User")}</Th>
              <Th>{t("admin:homepageAd.table.clicks", "Clicks")}</Th>
              <Th>{t("admin:homepageAd.table.status", "Status")}</Th>
              <Th>{t("admin:homepageAd.table.created_at", "Created At")}</Th>
              <Th>{t("admin:homepageAd.table.expires_at", "Expires At")}</Th>
              <Th>{t("common:actions", "Actions")}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {ads.map((ad) => (
              <Tr key={ad._id}>
                <Td>
                  <Image
                    src={import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image}
                    alt={ad.title}
                    boxSize="50px"
                    objectFit="cover"
                    borderRadius="md"
                    cursor="pointer"
                    onClick={() => window.open(import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image, "_blank")}
                  />
                </Td>
                <Td>{ad.title}</Td>
                <Td>{ad.userName}</Td>
                <Td>{ad.clicks}</Td>
                <Td>
                  <Badge colorScheme={getStatusColor(ad.status)}>
                    {t(`admin:homepageAd.status.${ad.status}`)}
                  </Badge>
                </Td>
                <Td>
                  {ad.createdAt ?
                    (() => {
                      try {
                        return format(new Date(ad.createdAt), "PP");
                      } catch (error) {
                        console.error("Invalid createdAt date format:", ad.createdAt);
                        return "Invalid date";
                      }
                    })()
                    : "N/A"}
                </Td>
                <Td>
                  {ad.expiresAt ?
                    (() => {
                      try {
                        return format(new Date(ad.expiresAt), "PP");
                      } catch (error) {
                        console.error("Invalid expiresAt date format:", ad.expiresAt);
                        return "Invalid date";
                      }
                    })()
                    : "N/A"}
                </Td>
                <Td>
                  <HStack spacing={1}>
                    {ad.status === "pending" && (
                      <>
                        <Tooltip label={t("admin:homepageAd.actions.approve", "Approve")}>
                          <IconButton
                            aria-label={t("admin:homepageAd.actions.approve", "Approve")}
                            icon={<FiCheck />}
                            size="sm"
                            colorScheme="green"
                            onClick={() => handleApprove(ad._id)}
                          />
                        </Tooltip>
                        <Tooltip label={t("admin:homepageAd.actions.reject", "Reject")}>
                          <IconButton
                            aria-label={t("admin:homepageAd.actions.reject", "Reject")}
                            icon={<FiX />}
                            size="sm"
                            colorScheme="red"
                            onClick={() => openRejectModal(ad)}
                          />
                        </Tooltip>
                      </>
                    )}
                    {ad.status === "approved" && (
                      <Tooltip label={t("admin:homepageAd.actions.deactivate", "Deactivate")}>
                        <IconButton
                          aria-label={t("admin:homepageAd.actions.deactivate", "Deactivate")}
                          icon={<FiX />}
                          size="sm"
                          colorScheme="red"
                          onClick={() => openRejectModal(ad)}
                        />
                      </Tooltip>
                    )}
                    {ad.status === "rejected" && (
                      <Tooltip label={t("admin:homepageAd.actions.reactivate", "Reactivate")}>
                        <IconButton
                          aria-label={t("admin:homepageAd.actions.reactivate", "Reactivate")}
                          icon={<FiCheck />}
                          size="sm"
                          colorScheme="green"
                          onClick={() => handleApprove(ad._id)}
                        />
                      </Tooltip>
                    )}
                    <Tooltip label={t("admin:homepageAd.actions.view", "View")}>
                      <IconButton
                        aria-label={t("admin:homepageAd.actions.view", "View")}
                        icon={<FiEye />}
                        size="sm"
                        colorScheme="blue"
                        onClick={() => window.open(import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image, "_blank")}
                      />
                    </Tooltip>
                    <Tooltip label={t("admin:homepageAd.actions.delete", "Delete")}>
                      <IconButton
                        aria-label={t("admin:homepageAd.actions.delete", "Delete")}
                        icon={<FiTrash2 />}
                        size="sm"
                        colorScheme="red"
                        variant="outline"
                        onClick={() => openDeleteConfirmation(ad._id)}
                      />
                    </Tooltip>
                  </HStack>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      )}

      {/* Rejection Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            {t("admin:homepageAd.modal.reject", "Reject Ad")}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl isRequired>
                <FormLabel>
                  {t("admin:homepageAd.form.rejection_reason", "Rejection Reason")}
                </FormLabel>
                <Textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder={t(
                    "admin:homepageAd.form.rejection_reason_placeholder",
                    "Enter reason for rejection..."
                  )}
                />
              </FormControl>
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              {t("common:cancel", "Cancel")}
            </Button>
            <Button
              colorScheme="red"
              onClick={handleReject}
              isDisabled={!rejectionReason.trim()}
            >
              {t("admin:homepageAd.actions.confirm_reject", "Confirm Reject")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteAlertOpen}
        leastDestructiveRef={cancelRef as any}
        onClose={() => setIsDeleteAlertOpen(false)}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {t("admin:homepageAd.modal.delete", "Delete Ad")}
            </AlertDialogHeader>

            <AlertDialogBody>
              {t("admin:homepageAd.messages.delete_confirm", "Are you sure? This action cannot be undone.")}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={() => setIsDeleteAlertOpen(false)}>
                {t("common:cancel", "Cancel")}
              </Button>
              <Button
                colorScheme="red"
                onClick={() => adToDelete && handleDelete(adToDelete)}
                ml={3}
              >
                {t("admin:homepageAd.actions.confirm_delete", "Delete")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default HomepageAdList;
