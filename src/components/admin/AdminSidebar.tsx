import React from "react";
import {
  Box,
  Flex,
  VStack,
  Text,
  Icon,
  IconButton,
  useColorModeValue,
  <PERSON>er,
  <PERSON>er<PERSON><PERSON>nt,
  <PERSON>er<PERSON><PERSON><PERSON>,
  <PERSON>er<PERSON>lose<PERSON>utton,
  <PERSON>er<PERSON>eader,
  Drawer<PERSON><PERSON>,
  useDisclosure
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { 
  FiMenu, 
  FiHome, 
  FiUsers, 
  FiShoppingBag, 
  FiPackage, 
  FiMessageSquare,
  FiImage,
  FiUser,
  FiMessageCircle,
  FiLayers // Added for Design Packages
} from "react-icons/fi";
import { FaStore } from "react-icons/fa";
import { useNavigate, useLocation } from "react-router-dom";

// Define menu items with their paths and icons
const menuItems = [
  { id: 'dashboard', path: '/admin/dashboard', icon: FiHome },
  { id: 'users', path: '/admin/users', icon: FiUsers },
  { id: 'stores', path: '/admin/stores', icon: FaStore },
  { id: 'productsServices', path: '/admin/products', icon: FiShoppingBag },
  { id: 'packages', path: '/admin/packages', icon: FiPackage },
  { id: 'designPackages', path: '/admin/design-packages', icon: FiLayers },
  { id: 'tickets', path: '/admin/tickets', icon: FiMessageSquare },
  { id: 'liveChat', path: '/admin/live-chat', icon: FiMessageCircle },
  { id: 'homeAds', path: '/admin/homepage-ads', icon: FiImage },
  { id: 'representatives', path: '/admin/representatives', icon: FiUser }
];

interface SidebarItemProps {
  icon: any;
  children: React.ReactNode;
  isActive?: boolean;
  onClick: () => void;
}

// Sidebar Item Component
const SidebarItem: React.FC<SidebarItemProps> = ({ icon, children, isActive, onClick }) => {
  const activeColor = useColorModeValue("blue.500", "blue.300");
  const hoverBg = useColorModeValue("gray.100", "gray.700");
  const bg = isActive ? useColorModeValue("blue.50", "blue.900") : undefined;
  const textColor = isActive ? activeColor : undefined;

  return (
    <Flex
      align="center"
      p="4"
      mx="4"
      borderRadius="md"
      role="group"
      cursor="pointer"
      bg={bg}
      color={textColor}
      _hover={{ bg: hoverBg }}
      fontWeight={isActive ? "bold" : "normal"}
      onClick={onClick}
    >
      <Icon mr="4" fontSize="18" as={icon} color={isActive ? activeColor : undefined} />
      <Text fontSize="md">{children}</Text>
    </Flex>
  );
};

// Simple AdminSidebar component that can be used inside admin pages
const AdminSidebar: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { t } = useTranslation("admin");
  const navigate = useNavigate();
  const location = useLocation();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const bgColor = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");
  
  const isActive = (path: string) => location.pathname === path;

  // Mobile sidebar drawer
  const MobileSidebar = () => (
    <Drawer isOpen={isOpen} placement="left" onClose={onClose} size="full">
      <DrawerOverlay />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerHeader borderBottomWidth="1px">
          {t("panel.adminPanel")}
        </DrawerHeader>
        <DrawerBody>
          <VStack align="stretch" spacing="0">
            {menuItems.map((item) => (
              <SidebarItem
                key={item.id}
                icon={item.icon}
                isActive={isActive(item.path)}
                onClick={() => {
                  navigate(item.path);
                  onClose();
                }}
              >
                {t(`panel.${item.id}`)}
              </SidebarItem>
            ))}
          </VStack>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  );
  
  return (
    <Flex>
      {/* Desktop Sidebar */}
      <Box
        position="fixed"
        left={0}
        top="64px" // Adjust based on your header height
        bottom={0}
        w="250px"
        bg={bgColor}
        borderRight="1px"
        borderColor={borderColor}
        display={{ base: "none", md: "block" }}
        zIndex={2}
        overflowY="auto"
      >
        <VStack align="stretch" spacing="0" pt="4">
          {menuItems.map((item) => (
            <SidebarItem
              key={item.id}
              icon={item.icon}
              isActive={isActive(item.path)}
              onClick={() => navigate(item.path)}
            >
              {t(`panel.${item.id}`)}
            </SidebarItem>
          ))}
        </VStack>
      </Box>

      {/* Mobile menu button and content area */}
      <Box
        ml={{ base: 0, md: "250px" }}
        transition="margin-left 0.2s"
        width="100%"
      >
        <IconButton
          aria-label="Open menu"
          icon={<FiMenu />}
          display={{ base: "flex", md: "none" }}
          onClick={onOpen}
          mb={4}
          mt={2}
          variant="outline"
        />
        <MobileSidebar />
        
        <Box p={{ base: 4, md: 6 }}>
          {children}
        </Box>
      </Box>
    </Flex>
  );
};

export default AdminSidebar;
