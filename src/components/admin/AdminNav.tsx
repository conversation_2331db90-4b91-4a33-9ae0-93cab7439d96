import React from "react";
import {
  Flex,
  Link,
  Icon,
  Text,
  VStack,
  useColorModeValue,
} from "@chakra-ui/react";
import { Link as RouterLink, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  FiHome,
  FiUsers,
  FiPackage,
  FiMessageSquare,
  FiSettings,
  FiImage,
} from "react-icons/fi";

const AdminNav: React.FC = () => {
  const { t } = useTranslation("admin");
  const location = useLocation();
  const activeBg = useColorModeValue("blue.50", "blue.900");
  const activeColor = useColorModeValue("blue.600", "blue.200");
  const hoverBg = useColorModeValue("gray.100", "gray.700");

  const navItems = [
    { icon: FiHome, label: t("header.dashboard"), path: "/admin" },
    { icon: FiUsers, label: t("header.users"), path: "/admin/users" },
    { icon: FiPackage, label: t("header.packages"), path: "/admin/packages" },
    {
      icon: FiMessageSquare,
      label: t("header.tickets"),
      path: "/admin/tickets",
    },
    {
      icon: FiImage,
      label: t("header.homepageAds"),
      path: "/admin/homepage-ads",
    },
    { icon: FiSettings, label: t("header.settings"), path: "/admin/settings" },
  ];

  return (
    <VStack spacing={2} align="stretch" width="100%">
      {navItems.map((item) => {
        const isActive = location.pathname === item.path;
        return (
          <Link
            as={RouterLink}
            to={item.path}
            key={item.path}
            style={{ textDecoration: "none" }}
          >
            <Flex
              align="center"
              p="4"
              mx="4"
              borderRadius="lg"
              role="group"
              cursor="pointer"
              bg={isActive ? activeBg : "transparent"}
              color={isActive ? activeColor : undefined}
              _hover={{
                bg: isActive ? activeBg : hoverBg,
              }}
            >
              <Icon as={item.icon} mr="4" />
              <Text>{item.label}</Text>
            </Flex>
          </Link>
        );
      })}
    </VStack>
  );
};

export default AdminNav;
