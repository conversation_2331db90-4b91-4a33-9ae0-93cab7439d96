import React, { useEffect, useState } from "react";
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Button,
  useToast,
  Flex,
  IconButton,
  Avatar,
  Badge,
  Text,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Spinner,
  Center,
  Tooltip,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { FaEdit, FaTrash } from "react-icons/fa";
import { getRepresentatives, deleteRepresentative } from "@/adminApi";
import { IRepresentative } from "@/types/representative";
import RepresentativeForm from "./RepresentativeForm";
import { getCountries, getCities } from "@/api";
import { ICountry, ICity } from "@/types/user";

const RepresentativeList: React.FC = () => {
  const { t } = useTranslation("admin");
  const toast = useToast();
  const [representatives, setRepresentatives] = useState<IRepresentative[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedRepresentative, setSelectedRepresentative] = useState<IRepresentative | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [citiesByCountry, setCitiesByCountry] = useState<Record<string, ICity[]>>({});

  // Modal states
  const { isOpen: isEditOpen, onOpen: onEditOpen, onClose: onEditClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const cancelRef = React.useRef<any>(null);

  // Fetch representatives on component mount
  useEffect(() => {
    fetchRepresentatives();
    fetchCountries();
  }, []);

  // Function to fetch representatives
  const fetchRepresentatives = async () => {
    setLoading(true);
    try {
      const response = await getRepresentatives();
      setRepresentatives(response.data);

      // Fetch cities for each country
      const countriesToFetch = [...new Set(response.data.map((rep: IRepresentative) => rep.country))].filter(
        (code): code is string => typeof code === 'string'
      );
      fetchCitiesForCountries(countriesToFetch);
    } catch (error) {
      console.error("Error fetching representatives:", error);
      toast({
        title: t("representatives.messages.fetch_error"),
        description: t("representatives.messages.fetch_error_desc"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch all countries
  const fetchCountries = async () => {
    try {
      const data = await getCountries();
      setCountries(data);
    } catch (error) {
      console.error("Error fetching countries:", error);
    }
  };

  // Function to fetch cities for multiple countries
  const fetchCitiesForCountries = async (countryCodes: string[]) => {
    try {
      const newCitiesByCountry: Record<string, ICity[]> = { ...citiesByCountry };

      for (const code of countryCodes) {
        if (!newCitiesByCountry[code]) {
          const cities = await getCities(code);
          newCitiesByCountry[code] = cities;
        }
      }

      setCitiesByCountry(newCitiesByCountry);
    } catch (error) {
      console.error("Error fetching cities:", error);
    }
  };

  // Helper function to get country name from code
  const getCountryName = (countryCode: string) => {
    const country = countries.find(c => c.code === countryCode || c._id === countryCode);
    return country?.name || countryCode;
  };

  // Helper function to get city name from code
  const getCityName = (countryCode: string, cityCode: string) => {
    const cities = citiesByCountry[countryCode] || [];
    const city = cities.find(c => c.city_id === cityCode);
    return city?.name || cityCode;
  };

  // Function to handle edit button click
  const handleEdit = (representative: IRepresentative) => {
    setSelectedRepresentative(representative);
    onEditOpen();
  };

  // Function to handle delete button click
  const handleDeleteClick = (representative: IRepresentative) => {
    setSelectedRepresentative(representative);
    onDeleteOpen();
  };

  // Function to delete representative
  const handleDelete = async () => {
    if (!selectedRepresentative) return;

    try {
      setIsDeleting((selectedRepresentative.id || selectedRepresentative._id) as string);
      await deleteRepresentative((selectedRepresentative.id || selectedRepresentative._id) as string);

      toast({
        title: t("representatives.messages.delete_success"),
        description: t("representatives.messages.delete_success_desc"),
        status: "success",
        duration: 5000,
        isClosable: true,
      });

      // Refresh the list
      fetchRepresentatives();
      onDeleteClose();
    } catch (error) {
      console.error("Error deleting representative:", error);
      toast({
        title: t("representatives.messages.delete_error"),
        description: t("representatives.messages.delete_error_desc"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsDeleting(null);
    }
  };

  // Function to handle successful form submission
  const handleFormSuccess = () => {
    fetchRepresentatives();
    onEditClose();
  };

  if (loading) {
    return (
      <Center py={10}>
        <Spinner size="xl" />
      </Center>
    );
  }

  return (
    <Box overflowX="auto">
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>{t("representatives.table.name")}</Th>
            <Th>{t("representatives.table.email")}</Th>
            <Th>{t("representatives.table.phone")}</Th>
            <Th>{t("representatives.table.country")}</Th>
            <Th>{t("representatives.table.city")}</Th>
            <Th>{t("representatives.table.status")}</Th>
            <Th>{t("representatives.table.actions")}</Th>
          </Tr>
        </Thead>
        <Tbody>
          {representatives.length === 0 ? (
            <Tr>
              <Td colSpan={7}>
                <Text textAlign="center">{t("common.noData")}</Text>
              </Td>
            </Tr>
          ) : (
            representatives.map((representative) => (
              <Tr key={representative.id || representative._id}>
                <Td>
                  <Flex align="center">
                    <Avatar
                      size="sm"
                      name={`${representative.firstName} ${representative.lastName}`}
                      src={representative.profilePicture ?
                        `${import.meta.env.VITE_SOCKET_URL}/uploads/${representative.profilePicture.replace(/^\/+/, '')}` :
                        undefined}
                      mr={2}
                    />
                    {representative.firstName} {representative.lastName}
                  </Flex>
                </Td>
                <Td>{representative.email}</Td>
                <Td>{representative.phoneNumber}</Td>
                <Td>
                  <Tooltip label={representative.country} placement="top">
                    {representative.countryName || getCountryName(representative.country)}
                  </Tooltip>
                </Td>
                <Td>
                  <Tooltip label={representative.city} placement="top">
                    {representative.cityName || getCityName(representative.country, representative.city)}
                  </Tooltip>
                </Td>
                <Td>
                  <Badge colorScheme={representative.isActive ? "green" : "red"}>
                    {representative.isActive
                      ? t("representatives.status.active")
                      : t("representatives.status.inactive")}
                  </Badge>
                </Td>
                <Td>
                  <Flex>
                    <IconButton
                      aria-label="Edit representative"
                      icon={<FaEdit />}
                      size="sm"
                      colorScheme="blue"
                      mr={2}
                      onClick={() => handleEdit(representative)}
                    />
                    <IconButton
                      aria-label="Delete representative"
                      icon={<FaTrash />}
                      size="sm"
                      colorScheme="red"
                      isLoading={isDeleting === (representative.id || representative._id)}
                      onClick={() => handleDeleteClick(representative)}
                    />
                  </Flex>
                </Td>
              </Tr>
            ))
          )}
        </Tbody>
      </Table>

      {/* Edit Modal */}
      <Modal isOpen={isEditOpen} onClose={onEditClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("representatives.edit")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedRepresentative && (
              <RepresentativeForm
                representative={selectedRepresentative}
                onSuccess={handleFormSuccess}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteOpen}
        leastDestructiveRef={cancelRef}
        onClose={onDeleteClose}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {t("representatives.actions.delete")}
            </AlertDialogHeader>

            <AlertDialogBody>
              {t("representatives.messages.delete_confirm")}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onDeleteClose}>
                {t("representatives.actions.cancel")}
              </Button>
              <Button
                colorScheme="red"
                onClick={handleDelete}
                ml={3}
                isLoading={isDeleting === (selectedRepresentative?.id || selectedRepresentative?._id)}
              >
                {t("representatives.actions.confirm")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default RepresentativeList;
