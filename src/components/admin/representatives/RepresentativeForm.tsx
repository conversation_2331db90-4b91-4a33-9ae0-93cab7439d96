import React, { useState, useEffect } from "react";
import {
  FormControl,
  FormLabel,
  Input,
  Button,
  VStack,
  useToast,
  FormErrorMessage,
  Checkbox,
  HStack,
  Select,
  Box,
  Image,
  Text,
  Flex,
  IconButton,
  InputGroup,
  InputRightElement,
  Tag,
  TagLabel,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useForm } from "react-hook-form";
import { FaEye, FaEyeSlash, FaImage, FaTrash } from "react-icons/fa";
import { X } from "lucide-react";
import { createRepresentative, updateRepresentative, uploadAdminFile } from "@/adminApi";
import { IRepresentative, ICreateRepresentative, IUpdateRepresentative } from "@/types/representative";
import { useCountries } from "@/hooks/useCountries";

interface RepresentativeFormProps {
  representative?: IRepresentative;
  onSuccess?: () => void;
}

const RepresentativeForm: React.FC<RepresentativeFormProps> = ({
  representative,
  onSuccess,
}) => {
  const { t } = useTranslation("admin");
  const toast = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [profilePicture, setProfilePicture] = useState<File | null>(null);
  const [profilePicturePreview, setProfilePicturePreview] = useState<string | null>(
    representative?.profilePicture
      ? `${import.meta.env.VITE_SOCKET_URL}/uploads/${representative.profilePicture}`
      : null
  );
  const [showPassword, setShowPassword] = useState(false);
  const { countries, cities, getCities } = useCountries();

  const isEditing = !!representative;

  // Create local state for country and city to avoid React Hook Form conflicts
  const [selectedCountryState, setSelectedCountryState] = useState<string>(representative?.country || "");
  const [selectedCityState, setSelectedCityState] = useState<string>(representative?.city || "");

  // New state variables for handling arrays
  const [languages, setLanguages] = useState<string[]>(representative?.languages || []);
  const [expertise, setExpertise] = useState<string[]>(representative?.expertise || []);
  const [tempLanguage, setTempLanguage] = useState<string>("");
  const [tempExpertise, setTempExpertise] = useState<string>("");
  const [verified, setVerified] = useState<boolean>(representative?.verified || false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<ICreateRepresentative & { isActive?: boolean }>({
    defaultValues: {
      firstName: representative?.firstName || "",
      lastName: representative?.lastName || "",
      email: representative?.email || "",
      phoneNumber: representative?.phoneNumber || "",
      country: "", // Will be managed by local state
      city: "", // Will be managed by local state
      password: "",
      isActive: representative?.isActive || true,

      // New fields
      title: representative?.title || "",
      company: representative?.company || "",
      experience: representative?.experience || "",
      region: representative?.region || "",
      verified: representative?.verified || false,
      // languages and expertise will be handled separately
    },
  });

  // Use the local state value for country
  const selectedCountry = selectedCountryState;

  // Load cities when country changes
  useEffect(() => {
    if (selectedCountry) {
      getCities(selectedCountry);
    }
  }, [selectedCountry, getCities]);

  // Helper methods for languages and expertise
  const addLanguage = () => {
    if (tempLanguage && !languages.includes(tempLanguage)) {
      setLanguages([...languages, tempLanguage]);
      setTempLanguage("");
    }
  };

  const removeLanguage = (language: string) => {
    setLanguages(languages.filter(lang => lang !== language));
  };

  const addExpertise = () => {
    if (tempExpertise && !expertise.includes(tempExpertise)) {
      setExpertise([...expertise, tempExpertise]);
      setTempExpertise("");
    }
  };

  const removeExpertise = (exp: string) => {
    setExpertise(expertise.filter(e => e !== exp));
  };

  // Initialize cities when editing a representative
  useEffect(() => {
    if (isEditing && representative) {
      // Explicitly set the form values to ensure they stick
      setValue("firstName", representative.firstName || "");
      setValue("lastName", representative.lastName || "");
      setValue("email", representative.email || "");
      setValue("phoneNumber", representative.phoneNumber || "");
      setValue("isActive", representative.isActive !== false);

      if (representative.country) {
        setValue("country", representative.country);
        getCities(representative.country);

        // Set city after a small delay to ensure cities are loaded
        if (representative.city) {
          // Using setTimeout to ensure the cities list is loaded before setting the city
          setTimeout(() => {
            setValue("city", representative.city || "");
          }, 100);
        }
      }
    }
  }, [isEditing, representative, getCities, setValue]);

  // Handle profile picture change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: t("representatives.messages.invalid_image"),
          description: t("representatives.form.picture_size_limit"),
          status: "error",
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      // Validate file type
      if (!file.type.includes("image/jpeg") && !file.type.includes("image/png")) {
        toast({
          title: t("representatives.messages.invalid_image"),
          description: t("representatives.form.picture_formats"),
          status: "error",
          duration: 5000,
          isClosable: true,
        });
        return;
      }

      setProfilePicture(file);
      const reader = new FileReader();
      reader.onload = () => {
        setProfilePicturePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove profile picture
  const handleRemovePicture = () => {
    setProfilePicture(null);
    setProfilePicturePreview(null);
  };

  // Handle form submission
  const onSubmit = async (data: ICreateRepresentative & { isActive?: boolean }) => {
    // Add country and city from local state
    const formData = {
      ...data,
      country: selectedCountryState,
      city: selectedCityState,
    };
    setIsSubmitting(true);

    try {
      let profilePictureUrl = representative?.profilePicture;

      // Upload profile picture if there's a new one
      if (profilePicture) {
        try {
          const uploadResponse = await uploadAdminFile(profilePicture);
          profilePictureUrl = uploadResponse.files[0];
        } catch (error) {
          console.error("Error uploading profile picture:", error);
          toast({
            title: t("representatives.messages.upload_error"),
            status: "error",
            duration: 5000,
            isClosable: true,
          });
          setIsSubmitting(false);
          return;
        }
      }

      if (isEditing && representative) {
        // Prepare update data
        const updateData: IUpdateRepresentative = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phoneNumber: formData.phoneNumber,
          country: formData.country,
          city: formData.city,
          isActive: formData.isActive,

          // New fields
          title: formData.title,
          company: formData.company,
          experience: formData.experience,
          region: formData.region,
          verified: verified,
          languages: languages,
          expertise: expertise
        };

        // Include password only if it's provided
        if (formData.password) {
          updateData.password = formData.password;
        }

        // Handle profile picture changes
        if (profilePictureUrl && profilePictureUrl !== representative.profilePicture && updateData) {
          // New profile picture uploaded
          (updateData as any).profilePicture = profilePictureUrl;
        } else if (profilePicturePreview === null && representative.profilePicture) {
          // Profile picture removed
          (updateData as any).profilePicture = null;
        }

        // Update representative
        await updateRepresentative(
          representative.id || representative._id || "",
          updateData
        );

        toast({
          title: t("representatives.messages.update_success"),
          description: t("representatives.messages.update_success_desc"),
          status: "success",
          duration: 5000,
          isClosable: true,
        });
      } else {
        // Create new representative
        const createData: ICreateRepresentative & { profilePicture?: string } = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phoneNumber: formData.phoneNumber,
          country: formData.country,
          city: formData.city,
          password: formData.password,

          // New fields
          title: formData.title,
          company: formData.company,
          experience: formData.experience,
          region: formData.region,
          verified: verified,
          languages: languages,
          expertise: expertise
        };

        // Include profile picture if uploaded
        if (profilePictureUrl) {
          createData.profilePicture = profilePictureUrl;
        }

        await createRepresentative(createData);

        toast({
          title: t("representatives.messages.create_success"),
          description: t("representatives.messages.create_success_desc"),
          status: "success",
          duration: 5000,
          isClosable: true,
        });

        // Reset form after successful creation
        reset();
        setProfilePicture(null);
        setProfilePicturePreview(null);
      }

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error saving representative:", error);
      toast({
        title: isEditing
          ? t("representatives.messages.update_error")
          : t("representatives.messages.create_error"),
        description: isEditing
          ? t("representatives.messages.update_error_desc")
          : t("representatives.messages.create_error_desc"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <VStack spacing={6} align="stretch">
        {/* Profile Picture - At the top */}
        <FormControl>
          <FormLabel>{t("representatives.form.profile_picture")}</FormLabel>
          <Box p={4} borderWidth={1} borderRadius="md" borderStyle="dashed">
            {profilePicturePreview ? (
              <Box position="relative" maxW="300px" mx="auto">
                <Image
                  src={profilePicturePreview}
                  alt="Profile Preview"
                  maxH="200px"
                  borderRadius="md"
                  mx="auto"
                />
                <IconButton
                  aria-label={t("representatives.form.remove_picture", "Remove picture")}
                  icon={<FaTrash />}
                  size="sm"
                  colorScheme="red"
                  position="absolute"
                  top={2}
                  right={2}
                  onClick={handleRemovePicture}
                />
              </Box>
            ) : (
              <Flex
                direction="column"
                align="center"
                justify="center"
                py={6}
                as="label"
                htmlFor="profile-picture"
                cursor="pointer"
                h="150px"
              >
                <FaImage size={40} />
                <Text mt={2}>{t("representatives.form.upload_picture")}</Text>
              </Flex>
            )}
            <Input
              id="profile-picture"
              type="file"
              accept="image/jpeg, image/png"
              onChange={handleFileChange}
              hidden
            />
          </Box>
          <HStack spacing={2} mt={1}>
            <Text fontSize="xs" color="gray.500">
              {t("representatives.form.picture_size_limit")}
            </Text>
            <Text fontSize="xs" color="gray.500">
              {t("representatives.form.picture_formats")}
            </Text>
          </HStack>
        </FormControl>

        {/* Basic Information */}
        <HStack spacing={4}>
          <FormControl isInvalid={!!errors.firstName} isRequired>
            <FormLabel>{t("representatives.form.first_name")}</FormLabel>
            <Input
              {...register("firstName", {
                required: "First name is required",
              })}
            />
            <FormErrorMessage>
              {errors.firstName && errors.firstName.message}
            </FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.lastName} isRequired>
            <FormLabel>{t("representatives.form.last_name")}</FormLabel>
            <Input
              {...register("lastName", {
                required: "Last name is required",
              })}
            />
            <FormErrorMessage>
              {errors.lastName && errors.lastName.message}
            </FormErrorMessage>
          </FormControl>
        </HStack>

        <HStack spacing={4} align="flex-start">
          <FormControl isInvalid={!!errors.email} isRequired>
            <FormLabel>{t("representatives.form.email")}</FormLabel>
            <Input
              type="email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              })}
            />
            <FormErrorMessage>
              {errors.email && errors.email.message}
            </FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.phoneNumber} isRequired>
            <FormLabel>{t("representatives.form.phone")}</FormLabel>
            <Input
              {...register("phoneNumber", {
                required: "Phone number is required",
              })}
            />
            <FormErrorMessage>
              {errors.phoneNumber && errors.phoneNumber.message}
            </FormErrorMessage>
          </FormControl>
        </HStack>

        {/* Title and Company */}
        <HStack spacing={4}>
          <FormControl>
            <FormLabel>{t("representatives.form.title", "Title/Position")}</FormLabel>
            <Input
              {...register("title")}
              placeholder={t("representatives.form.title_placeholder", "E.g., Export Manager")}
            />
          </FormControl>

          <FormControl>
            <FormLabel>{t("representatives.form.company", "Company")}</FormLabel>
            <Input
              {...register("company")}
              placeholder={t("representatives.form.company_placeholder", "E.g., Global Trade Ltd.")}
            />
          </FormControl>
        </HStack>

        {/* Experience and Region */}
        <HStack spacing={4}>
          <FormControl>
            <FormLabel>{t("representatives.form.experience", "Experience")}</FormLabel>
            <Input
              {...register("experience")}
              placeholder={t("representatives.form.experience_placeholder", "E.g., 10 years")}
            />
          </FormControl>

          <FormControl>
            <FormLabel>{t("representatives.form.region", "Region")}</FormLabel>
            <Input
              {...register("region")}
              placeholder={t("representatives.form.region_placeholder", "E.g., Europe, Middle East")}
            />
          </FormControl>
        </HStack>

        {/* Languages and Expertise in one row */}
        <HStack spacing={4} align="flex-start">
          {/* Languages */}
          <FormControl>
            <FormLabel>{t("representatives.form.languages", "Languages")}</FormLabel>
            <HStack>
              <Input
                value={tempLanguage}
                onChange={(e) => setTempLanguage(e.target.value)}
                placeholder={t("representatives.form.languages_placeholder", "E.g., English")}
              />
              <Button onClick={addLanguage} size="sm">
                {t("representatives.form.add_button", "Add")}
              </Button>
            </HStack>
            {languages.length > 0 && (
              <Box mt={2}>
                <HStack spacing={2} flexWrap="wrap">
                  {languages.map((lang, index) => (
                    <Tag key={index} size="md" borderRadius="full" variant="solid" colorScheme="blue">
                      <TagLabel>{lang}</TagLabel>
                      <IconButton
                        size="xs"
                        colorScheme="blue"
                        variant="ghost"
                        icon={<X size={12} />}
                        aria-label={t("representatives.form.remove_language", "Remove language").replace("{{language}}", lang)}
                        onClick={() => removeLanguage(lang)}
                      />
                    </Tag>
                  ))}
                </HStack>
              </Box>
            )}
          </FormControl>

          {/* Expertise */}
          <FormControl>
            <FormLabel>{t("representatives.form.expertise", "Expertise")}</FormLabel>
            <HStack>
              <Input
                value={tempExpertise}
                onChange={(e) => setTempExpertise(e.target.value)}
                placeholder={t("representatives.form.expertise_placeholder", "E.g., Market Analysis")}
              />
              <Button onClick={addExpertise} size="sm">
                {t("representatives.form.add_button", "Add")}
              </Button>
            </HStack>
            {expertise.length > 0 && (
              <Box mt={2}>
                <HStack spacing={2} flexWrap="wrap">
                  {expertise.map((exp, index) => (
                    <Tag key={index} size="md" borderRadius="full" variant="solid" colorScheme="green">
                      <TagLabel>{exp}</TagLabel>
                      <IconButton
                        size="xs"
                        colorScheme="green"
                        variant="ghost"
                        icon={<X size={12} />}
                        aria-label={t("representatives.form.remove_expertise", "Remove expertise").replace("{{expertise}}", exp)}
                        onClick={() => removeExpertise(exp)}
                      />
                    </Tag>
                  ))}
                </HStack>
              </Box>
            )}
          </FormControl>
        </HStack>

        {/* Verification status */}
        <FormControl>
          <Checkbox
            isChecked={verified}
            onChange={(e) => setVerified(e.target.checked)}
          >
            {t("representatives.form.verified", "Verified Representative")}
          </Checkbox>
        </FormControl>

        <HStack spacing={4}>
          <FormControl isInvalid={!!errors.country} isRequired>
            <FormLabel>{t("representatives.form.country")}</FormLabel>
            <Select
              placeholder={t("representatives.form.select_country")}
              value={selectedCountryState}
              onChange={(e) => {
                setSelectedCountryState(e.target.value);
                setSelectedCityState(""); // Reset city when country changes
              }}
            >
              {countries.map((country) => (
                <option key={country.code || country._id} value={country.code || country._id}>
                  {country.name}
                </option>
              ))}
            </Select>
            <FormErrorMessage>
              {!selectedCountryState && "Country is required"}
            </FormErrorMessage>
          </FormControl>

          <FormControl isInvalid={!!errors.city} isRequired>
            <FormLabel>{t("representatives.form.city")}</FormLabel>
            <Select
              placeholder={t("representatives.form.select_city")}
              value={selectedCityState}
              onChange={(e) => setSelectedCityState(e.target.value)}
              isDisabled={!selectedCountry || cities.length === 0}
            >
              {cities.map((city) => (
                <option key={city.city_id} value={city.city_id}>
                  {city.name}
                </option>
              ))}
            </Select>
            <FormErrorMessage>
              {!selectedCityState && selectedCountry && "City is required"}
            </FormErrorMessage>
          </FormControl>
        </HStack>

        <FormControl isInvalid={!!errors.password} isRequired={!isEditing}>
          <FormLabel>
            {isEditing
              ? t("representatives.form.new_password", "New Password (optional)")
              : t("representatives.form.password", "Password")
            }
          </FormLabel>
          <InputGroup>
            <Input
              type={showPassword ? "text" : "password"}
              {...register("password", {
                required: !isEditing ? t("common.validation.required", "Password is required") : false,
                minLength: {
                  value: 6,
                  message: t("common.validation.password_min_length", "Password must be at least 6 characters"),
                },
              })}
              placeholder={t("representatives.form.password_placeholder", "Enter password")}
            />
            <InputRightElement>
              <IconButton
                aria-label={showPassword
                  ? t("common.hide_password", "Hide password")
                  : t("common.show_password", "Show password")
                }
                icon={showPassword ? <FaEyeSlash /> : <FaEye />}
                h="1.75rem"
                size="sm"
                onClick={() => setShowPassword(!showPassword)}
                variant="ghost"
              />
            </InputRightElement>
          </InputGroup>
          <FormErrorMessage>
            {errors.password && errors.password.message}
          </FormErrorMessage>
        </FormControl>

        {isEditing && (
          <FormControl>
            <Checkbox
              {...register("isActive")}
              defaultChecked={representative?.isActive}
            >
              {t("representatives.form.is_active")}
            </Checkbox>
          </FormControl>
        )}

        <Box textAlign="center" mt={4}>
          <Button
            colorScheme="blue"
            isLoading={isSubmitting}
            type="submit"
            width="200px"
          >
            {isEditing
              ? t("representatives.actions.save")
              : t("representatives.createRepresentative")}
          </Button>
        </Box>
      </VStack>
    </form>
  );
};

export default RepresentativeForm;