import React from "react";
import {
  Box,
  Flex,
  IconButton,
  useDisclosure,
  useColorModeValue,
  Drawer,
  DrawerBody,
  DrawerHeader,
  DrawerOverlay,
  DrawerContent,
  DrawerCloseButton,
} from "@chakra-ui/react";
import { HamburgerIcon } from "@chakra-ui/icons";
import { useTranslation } from "react-i18next";
import AdminNav from "./AdminNav";

const AdminHeader: React.FC = () => {
  const { t } = useTranslation("admin");
  const { isOpen, onOpen, onClose } = useDisclosure();
  const bgColor = useColorModeValue("white", "gray.900");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  return (
    <Box
      bg={bgColor}
      borderBottom="1px"
      borderColor={borderColor}
      position="sticky"
      top={0}
      left={0}
      margin={"0 auto"}
      zIndex={1000}
      width="100%"
    >
      <Flex
        h={16}
        alignItems="center"
        justifyContent="space-between"
        px={4}
        width="100%"
      >
        {/* Mobile Menu Button */}
        <IconButton
          display={{ base: "flex", md: "none" }}
          onClick={onOpen}
          variant="ghost"
          aria-label="Open menu"
          icon={<HamburgerIcon />}
        />

        {/* Desktop Navigation */}
        <Box display={{ base: "none", md: "block" }} width="100%">
          <AdminNav />
        </Box>

        {/* Mobile Navigation Drawer */}
        <Drawer isOpen={isOpen} placement="left" onClose={onClose}>
          <DrawerOverlay />
          <DrawerContent>
            <DrawerCloseButton />
            <DrawerHeader borderBottomWidth="1px">
              {t("header.dashboard")}
            </DrawerHeader>
            <DrawerBody>
              <AdminNav />
            </DrawerBody>
          </DrawerContent>
        </Drawer>
      </Flex>
    </Box>
  );
};

export default AdminHeader;
