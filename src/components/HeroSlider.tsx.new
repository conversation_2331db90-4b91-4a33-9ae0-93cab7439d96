import React from "react";
import { Link } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import { useMediaQuery } from "@chakra-ui/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

interface SliderImage {
  web: string;
  mobile: string;
  link?: string;
}

interface HeroSliderProps {
  images: SliderImage[];
}

const HeroSlider: React.FC<HeroSliderProps> = ({ images }) => {
  const [isMobile] = useMediaQuery("(max-width: 768px)");

  if (!images || images.length === 0) return null;

  return (
    <div className="w-full overflow-hidden">
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        navigation={!isMobile}
        pagination={{ clickable: true }}
        autoplay={{ delay: 5000 }}
        loop={true}
        className="w-full"
        style={{
          height: isMobile ? "300px" : "600px",
        }}
      >
        {images.map((image, index) => (
          <SwiperSlide key={index}>
            {image.link ? (
              <Link
                to={image.link}
                target="_blank"
                className="flex h-full w-full items-center justify-center hover:opacity-90 transition-opacity duration-200"
              >
                <img
                  src={
                    import.meta.env.VITE_SOCKET_URL +
                    (isMobile ? image.mobile : image.web)
                  }
                  alt={`Slide ${index + 1}`}
                  className="w-full h-full object-cover"
                  loading={index === 0 ? "eager" : "lazy"}
                />
              </Link>
            ) : (
              <div className="h-full w-full flex items-center justify-center">
                <img
                  src={
                    import.meta.env.VITE_SOCKET_URL +
                    (isMobile ? image.mobile : image.web)
                  }
                  alt={`Slide ${index + 1}`}
                  className="w-full h-full object-cover"
                  loading={index === 0 ? "eager" : "lazy"}
                />
              </div>
            )}
          </SwiperSlide>
        ))}
      </Swiper>
    </div>
  );
};

export default HeroSlider;