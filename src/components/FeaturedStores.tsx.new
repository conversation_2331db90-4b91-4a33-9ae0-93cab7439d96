import React from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import { IStore } from "../types/store";
import StoreCard from "./common/StoreCard";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

interface FeaturedStoresProps {
  stores: IStore[];
  isAuthenticated: boolean;
  onStoreClick: (storeId: string) => void;
}

const FeaturedStores: React.FC<FeaturedStoresProps> = ({
  stores,
  isAuthenticated,
  onStoreClick,
}) => {
  const { t } = useTranslation("featuredStores");
  const navigate = useNavigate();

  if (!stores || stores.length === 0) {
    return null;
  }

  const breakpoints = {
    320: { slidesPerView: 2, spaceBetween: 10 },
    640: { slidesPerView: 3, spaceBetween: 15 },
    768: { slidesPerView: 4, spaceBetween: 20 },
    1024: { slidesPerView: 5, spaceBetween: 20 },
  };

  return (
    <section className="py-12 bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">{t("title")}</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            {t("subtitle")}
          </p>
        </div>

        <div className="relative">
          <Swiper
            modules={[Navigation, Pagination, Autoplay]}
            spaceBetween={20}
            breakpoints={breakpoints}
            navigation
            pagination={{ clickable: true }}
            autoplay={{ delay: 5000 }}
            loop={true}
            className="py-4 pb-12"
          >
            {stores.map((store) => (
              <SwiperSlide key={store._id} className="px-1">
                <div 
                  className="bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden cursor-pointer"
                  onClick={() => {
                    if (isAuthenticated) {
                      navigate(`/stores/${store._id}`);
                    } else {
                      onStoreClick(store._id);
                    }
                  }}
                >
                  <div className="h-40 overflow-hidden">
                    <img
                      src={`${import.meta.env.VITE_SOCKET_URL}/${store.coverImage || ''}`}
                      alt={store.name}
                      className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-500"
                      onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                        e.currentTarget.src = `${import.meta.env.PUBLIC_URL}/placeholders/store-cover.jpg`;
                      }}
                    />
                  </div>
                  
                  <div className="relative px-4 pt-10 pb-4">
                    <div className="absolute -top-8 left-4 w-16 h-16 rounded-full border-4 border-white overflow-hidden bg-white shadow-sm">
                      <img
                        src={`${import.meta.env.VITE_SOCKET_URL}/${store.logo || ''}`}
                        alt={`${store.name} logo`}
                        className="w-full h-full object-cover"
                        onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                          e.currentTarget.src = `${import.meta.env.PUBLIC_URL}/placeholders/store-logo.jpg`;
                        }}
                      />
                    </div>
                    
                    <h3 className="text-lg font-semibold text-gray-900 mb-1 truncate">
                      {store.name}
                    </h3>
                    
                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      <span className="truncate">
                        {store.city}, {store.country}
                      </span>
                    </div>
                    
                    {store.verified && (
                      <div className="absolute top-2 right-2 bg-blue-50 px-2 py-1 rounded-full">
                        <span className="text-xs font-medium text-blue-600">
                          {t("verified")}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        <div className="text-center mt-6">
          <button
            onClick={() => navigate('/stores')}
            className="inline-flex items-center space-x-2 text-primary hover:text-blue-600 transition-colors font-medium"
          >
            <span>{t("viewAll")}</span>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default FeaturedStores;