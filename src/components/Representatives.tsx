import React from 'react';
import { useNavigate } from 'react-router-dom';
import { BadgeCheck, MapPin, ExternalLink, Globe2, Building2, Briefcase } from 'lucide-react';
import { isLoggedIn } from '../data/testUser';

const Representatives = () => {
  const navigate = useNavigate();
  const loggedIn = isLoggedIn();

  const representatives = [
    {
      name: "Ahmet Yılmaz",
      title: "Kıdemli İhracat Uzmanı",
      company: "TechGlobal Solutions",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80",
      languages: ["Türkçe", "İngilizce", "Almanca"],
      experience: "12 yıl",
      expertise: ["Teknoloji İhracatı", "Pazar Analizi"],
      country: "Türkiye",
      region: "Avrupa",
      verified: true
    },
    {
      name: "Ayşe Demir",
      title: "Uluslararası Ticaret Danışmanı",
      company: "EcoTrade International",
      image: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80",
      languages: ["Türkçe", "İngilizce", "Fransızca"],
      experience: "8 yıl",
      expertise: ["Sürdürülebilir İhracat", "Yeşil Ticaret"],
      country: "Almanya",
      region: "Avrupa",
      verified: true
    },
    {
      name: "Mehmet Kaya",
      title: "İhracat Geliştirme Müdürü",
      company: "MediCare Exports",
      image: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?auto=format&fit=crop&q=80",
      languages: ["Türkçe", "İngilizce", "Arapça"],
      experience: "15 yıl",
      expertise: ["Medikal İhracat", "Regülasyon"],
      country: "Birleşik Arap Emirlikleri",
      region: "Orta Doğu",
      verified: false
    },
    {
      name: "Zeynep Aydın",
      title: "Uluslararası İş Geliştirme",
      company: "BuildPro Construction",
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?auto=format&fit=crop&q=80",
      languages: ["Türkçe", "İngilizce", "Rusça"],
      experience: "10 yıl",
      expertise: ["İnşaat İhracatı", "Proje Yönetimi"],
      country: "Rusya",
      region: "Avrasya",
      verified: true
    }
  ];

  const handleContactClick = () => {
    if (!loggedIn) {
      navigate('/register');
    }
  };

  return (
    <div className="bg-gradient-to-b from-gray-50 to-white py-16">
      <div className="max-w-[1920px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-12 px-4 md:px-12">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">İhracat Temsilcileri</h2>
            <p className="mt-3 text-lg text-gray-600">Global ticarette deneyimli uzmanlar</p>
          </div>
          <button 
            onClick={() => navigate('/representatives')}
            className="hidden md:inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
          >
            <span>Tüm Temsilcileri Gör</span>
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-4 md:px-12">
          {representatives.map((rep, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300">
              <div className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <img 
                    src={rep.image}
                    alt={rep.name}
                    className="w-16 h-16 rounded-xl object-cover"
                  />
                  <div>
                    <div className="flex items-center space-x-2">
                      <h3 className="text-lg font-semibold text-gray-900">{rep.name}</h3>
                      {rep.verified && <BadgeCheck className="h-5 w-5 text-primary" />}
                    </div>
                    <p className="text-primary">{rep.title}</p>
                    <p className="text-sm text-gray-600">{rep.company}</p>
                  </div>
                </div>

                <div className="space-y-3 mb-6">
                  <div className="flex items-center space-x-2">
                    <Globe2 className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{rep.country} ({rep.region})</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Building2 className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{rep.languages.join(", ")}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Briefcase className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">{rep.experience} deneyim</span>
                  </div>
                </div>

                <button 
                  onClick={handleContactClick}
                  className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
                >
                  <span>İletişime Geç</span>
                  <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 text-center md:hidden">
          <button 
            onClick={() => navigate('/representatives')}
            className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
          >
            <span>Tüm Temsilcileri Gör</span>
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default Representatives;