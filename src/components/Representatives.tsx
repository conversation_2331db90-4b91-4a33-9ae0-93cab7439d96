import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { BadgeCheck, ExternalLink, Globe2, Building2, Briefcase, Mail, Phone } from 'lucide-react';
import { getActiveRepresentatives } from "../api/representativeApi";
import { useAuth } from "@/context/AuthContext";

interface Representative {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  countryName?: string;
  cityName?: string;
  profilePicture?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  company?: string;
  title?: string;
  languages?: string[];
  expertise?: string[];
  experience?: string;
  verified?: boolean;
  region?: string;
}

const Representatives: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('representatives');
  const { isAuthenticated } = useAuth();
  const [representatives, setRepresentatives] = useState<Representative[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showPhoneMap, setShowPhoneMap] = useState<{ [key: string]: boolean }>({});

  useEffect(() => {
    const fetchRepresentatives = async () => {
      try {
        setIsLoading(true);
        const response: any = await getActiveRepresentatives();
        if (response.success && response.data) {
          setRepresentatives(response.data);
        } else {
          console.error('Failed to fetch representatives:', response.error);
        }
      } catch (error) {
        console.error('Error fetching representatives:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRepresentatives();
  }, []);

  const togglePhone = (repId: string) => {
    setShowPhoneMap(prev => ({
      ...prev,
      [repId]: !prev[repId]
    }));
  };

  if (isLoading) {
    return (
      <div className="w-full bg-gradient-to-b from-gray-50 to-white py-16">
        <div className="full-width-container">
          <div className="w-full max-w-[1920px] mx-auto bg-white">
            <div className="flex justify-center items-center min-h-[300px]">
              <div className="spinner-border animate-spin inline-block w-8 h-8 border-4 rounded-full text-blue-600" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (representatives.length === 0) {
    return null;
  }

  return (
    <div className="w-full bg-gradient-to-b from-gray-50 to-white py-16">
      <div className="full-width-container">
        <div className="w-full max-w-[1920px] mx-auto bg-white">
          <div className="flex items-center justify-between mb-12 px-4 md:px-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900">{t('title')}</h2>
              <p className="mt-3 text-lg text-gray-600">{t('subtitle')}</p>
            </div>
            <button
              onClick={() => navigate('/representatives')}
              className="hidden md:inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
            >
              <span>{t('viewAll')}</span>
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-4 md:px-12">
            {representatives.map((rep) => (
              <div key={rep._id} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300">
                <div className="p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    {rep.profilePicture ? (
                      <img
                        src={
                          rep.profilePicture.startsWith("data:") || rep.profilePicture.startsWith("http")
                            ? rep.profilePicture
                            : `${import.meta.env.VITE_SOCKET_URL}/uploads/${rep.profilePicture.replace(/^\/+/, '')}`
                        }
                        alt={`${rep.firstName} ${rep.lastName}`}
                        className="w-16 h-16 rounded-xl object-cover"
                        onError={(e) => {
                          e.currentTarget.onerror = null;
                          // Create a container for the initials
                          const initialsDiv = document.createElement('div');
                          initialsDiv.className = "w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center text-primary font-bold";
                          initialsDiv.innerText = `${rep.firstName.charAt(0)}${rep.lastName.charAt(0)}`;

                          // Replace the image with the initials div
                          const parent = e.currentTarget.parentElement;
                          if (parent) {
                            parent.replaceChild(initialsDiv, e.currentTarget);
                          }
                        }}
                      />
                    ) : (
                      <div className="w-16 h-16 rounded-xl bg-primary/10 flex items-center justify-center text-primary font-bold">
                        {rep.firstName.charAt(0)}{rep.lastName.charAt(0)}
                      </div>
                    )}
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-semibold text-gray-900">{rep.firstName} {rep.lastName}</h3>
                        {rep.verified && <BadgeCheck className="h-5 w-5 text-primary" />}
                      </div>
                      {rep.title && <p className="text-primary">{rep.title}</p>}
                      {rep.company && <p className="text-sm text-gray-600">{rep.company}</p>}
                    </div>
                  </div>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-2">
                      <Globe2 className="h-4 w-4 text-gray-400" />
                      <span className="text-sm text-gray-600">
                        {rep.cityName && rep.countryName ?
                          `${rep.cityName}, ${rep.countryName}` :
                          (rep.city && rep.country ? `${rep.city}, ${rep.country}` : '')
                        }
                        {rep.region ? `(${rep.region})` : ''}
                      </span>
                    </div>
                    {rep.languages && rep.languages.length > 0 && (
                      <div className="flex items-center space-x-2">
                        <Building2 className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">
                          {rep.languages.join(", ")}
                        </span>
                      </div>
                    )}
                    {rep.experience && (
                      <div className="flex items-center space-x-2">
                        <Briefcase className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-600">{rep.experience} {t('experience')}</span>
                      </div>
                    )}
                    {showPhoneMap[rep._id] && (
                      <div className="flex items-center space-x-2 text-primary">
                        <Phone className="h-4 w-4" />
                        <span className="text-sm">{rep.phoneNumber}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <a
                      href={`mailto:${rep.email}`}
                      className="flex-1 py-2 px-3 bg-primary/10 text-primary rounded-lg text-sm font-medium hover:bg-primary hover:text-white transition-colors duration-200 flex items-center justify-center space-x-2"
                    >
                      <Mail className="h-4 w-4" />
                      <span>{t('emailButton')}</span>
                    </a>
                    <button
                      onClick={() => togglePhone(rep._id)}
                      className="flex-1 py-2 px-3 bg-primary/10 text-primary rounded-lg text-sm font-medium hover:bg-primary hover:text-white transition-colors duration-200 flex items-center justify-center space-x-2"
                    >
                      <Phone className="h-4 w-4" />
                      <span>{t('phoneButton')}</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 text-center md:hidden">
            <button
              onClick={() => navigate('/representatives')}
              className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
            >
              <span>{t('viewAll')}</span>
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Representatives;