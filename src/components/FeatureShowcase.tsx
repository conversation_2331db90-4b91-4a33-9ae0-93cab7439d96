import React from 'react';
import { motion } from 'framer-motion';
import { 
  Globe2, 
  Shield, 
  TrendingUp, 
  Users, 
  Search,
  MessageSquare,
  CheckCircle,
  Building2,
  Package,
  ArrowRight
} from 'lucide-react';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};

const FeatureShowcase = () => {
  const features = [
    {
      icon: Search,
      title: "Akıllı Eşleştirme",
      description: "Yapay zeka destekli eşleştirme sistemi ile size en uygun iş ortaklarını bulun",
      color: "bg-primary/10",
      iconColor: "text-primary"
    },
    {
      icon: MessageSquare,
      title: "Anlık İletişim",
      description: "Potansiyel iş ortaklarınızla platform üzerinden güvenli bir şekilde iletişim kurun",
      color: "bg-primary/10",
      iconColor: "text-primary"
    },
    {
      icon: Shield,
      title: "Güvenli Altyapı",
      description: "SSL şifreleme ve gelişmiş güvenlik önlemleri ile verileriniz güvende",
      color: "bg-primary/10",
      iconColor: "text-primary"
    },
    {
      icon: Building2,
      title: "Firma Doğrulama",
      description: "Doğrulanmış firmalar ile güvenli ticaret yapın",
      color: "bg-primary/10",
      iconColor: "text-primary"
    },
    {
      icon: Package,
      title: "Ürün Yönetimi",
      description: "Ürün ve hizmetlerinizi kolayca yönetin, global pazara sunun",
      color: "bg-primary/10",
      iconColor: "text-primary"
    },
    {
      icon: TrendingUp,
      title: "Analitik Raporlar",
      description: "Detaylı analitik raporlar ile performansınızı takip edin",
      color: "bg-primary/10",
      iconColor: "text-primary"
    }
  ];

  const benefits = [
    {
      title: "Zaman Tasarrufu",
      description: "Geleneksel yöntemlere göre %70 daha hızlı iş ortağı bulun",
      stats: "%70",
      color: "text-primary"
    },
    {
      title: "Maliyet Avantajı",
      description: "Fuar ve seyahat maliyetlerinden %80'e varan tasarruf",
      stats: "%80",
      color: "text-primary"
    },
    {
      title: "Geniş Network",
      description: "150+ ülkede 10.000+ aktif üye ile networking fırsatı",
      stats: "10K+",
      color: "text-primary"
    }
  ];

  return (
    <div className="space-y-32 py-16">
      {/* Features Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Platform Özellikleri
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            İşinizi büyütmeniz için ihtiyacınız olan tüm özellikler tek platformda
          </p>
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className={`${feature.color} rounded-xl p-6 hover:shadow-lg transition-shadow`}
            >
              <feature.icon className={`h-12 w-12 ${feature.iconColor} mb-4`} />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Benefits Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Avantajlar
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            E-exportcity ile ticaretin geleceğine adım atın
          </p>
        </motion.div>

        <motion.div 
          className="grid grid-cols-1 md:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {benefits.map((benefit, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-shadow text-center"
            >
              <div className={`text-5xl font-bold mb-4 ${benefit.color}`}>
                {benefit.stats}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {benefit.title}
              </h3>
              <p className="text-gray-600">
                {benefit.description}
              </p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default FeatureShowcase;