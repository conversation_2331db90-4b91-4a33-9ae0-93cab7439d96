import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Search,
  MessageSquare,
  Shield,
  Building2,
  Package,
  TrendingUp
} from 'lucide-react';

const FeatureShowcase: React.FC = () => {
  const { t } = useTranslation('introSections');

  const featureIcons: any = {
    'Smart Matching': Search,
    'Instant Communication': MessageSquare,
    'Secure Infrastructure': Shield,
    'Company Verification': Building2,
    'Product Management': Package,
    'Analytical Reports': TrendingUp
  };

  const features = (t('featuresList', { returnObjects: true }) as any).map((feature: any) => ({
    ...feature,
    icon: featureIcons[feature.title] || Search,
    color: "bg-primary/10",
    iconColor: "text-primary"
  }));

  const advantages = (t('advantagesList', { returnObjects: true }) as any);

  return (
    <div className="space-y-32 py-16">
      {/* Features Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {t('platformFeatures.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('platformFeatures.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature: any, index: any) => (
            <div
              key={index}
              className={`${feature.color} rounded-xl p-6 hover:shadow-lg transition-shadow`}
            >
              <feature.icon className={`h-12 w-12 ${feature.iconColor} mb-4`} />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Benefits Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t('advantages.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {t('advantages.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {advantages.map((benefit: any, index: number) => (
            <div
              key={index}
              className="bg-white rounded-xl p-8 shadow-sm hover:shadow-lg transition-shadow text-center"
            >
              <div className="text-5xl font-bold mb-4 text-primary">
                {benefit.stats}
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {benefit.title}
              </h3>
              <p className="text-gray-600">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeatureShowcase;