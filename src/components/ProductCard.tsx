import React from "react";
import { Box, Image, Text, VStack, Badge, Button } from "@chakra-ui/react";
import { FiShoppingCart } from "react-icons/fi";
import { useTranslation } from "react-i18next";

interface ProductCardProps {
  title: string;
  description: string;
  imageUrl: string;
}

const ProductCard: React.FC<ProductCardProps> = ({
  title,
  description,
  imageUrl,
}) => {
  const { t } = useTranslation("productCard");

  return (
    <Box
      maxW="sm"
      borderWidth="1px"
      borderRadius="lg"
      overflow="hidden"
      shadow="md"
    >
      <Image
        src={imageUrl}
        alt={t("image.alt", { title })}
        height="200px"
        width="100%"
        objectFit="cover"
      />

      <VStack p="6" spacing={3} align="start">
        <VStack align="start" spacing={1}>
          <Text fontWeight="bold" fontSize="xl">
            {title}
          </Text>
          <Badge colorScheme="primary" fontSize="0.8em">
            {t("badge.new")}
          </Badge>
        </VStack>
        <Text color="gray.500" fontSize="sm">
          {description}
        </Text>
        <Button
          leftIcon={<FiShoppingCart />}
          colorScheme="primary"
          variant="solid"
          width="full"
        >
          {t("button.addToCart")}
        </Button>
      </VStack>
    </Box>
  );
};

export default ProductCard;
