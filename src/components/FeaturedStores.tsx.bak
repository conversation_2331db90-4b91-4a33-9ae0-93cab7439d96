import React from "react";
import {
  Box,
  Heading,
  useBreakpointValue,
} from "@chakra-ui/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import { IStore } from "../types/store";
import { useTranslation } from "react-i18next";
import StoreCard from "./common/StoreCard";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

interface FeaturedStoresProps {
  stores: IStore[];
  isAuthenticated: boolean;
  onStoreClick: (storeId: string) => void;
}

const FeaturedStores: React.FC<FeaturedStoresProps> = ({
  stores,
  isAuthenticated,
}) => {
  const { t } = useTranslation("featuredStores");
  const slidesPerView =
    useBreakpointValue({ base: 2, sm: 3, md: 4, lg: 5 }) || 5;

  return (
    <Box py={8}>
      <Heading size="lg" mb={6} textAlign="center">
        {t("title")}
      </Heading>
      <Box px={{ base: 4, md: 8 }}>
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          spaceBetween={20}
          slidesPerView={slidesPerView}
          navigation
          style={{
            paddingBottom: "40px",
          }}
          pagination={{ clickable: true }}
          autoplay={{ delay: 5000 }}
        >
          {stores.map((store) => (
            <SwiperSlide key={store._id}>
              <StoreCard
                store={store}
                isAuthenticated={isAuthenticated}
                isFeatured={true}
              />
            </SwiperSlide>
          ))}
        </Swiper>
      </Box>
    </Box>
  );
};

export default FeaturedStores;
