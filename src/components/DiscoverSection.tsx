import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Building, Users, Globe, Building2, TrendingUp } from 'lucide-react';

const DiscoverSection: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('discover');

  // Card icons mapping
  const cardIcons: any = {
    'Active Companies': Building,
    'Export Representatives': Users,
    'Global Access': Globe,
    'Successful Transactions': Building2,
    'Verified Members': TrendingUp
  };

  // Card gradients
  const cardGradients: string[] = [
    "linear-gradient(135deg, #FF6B6B 0%, #FFE66D 100%)",
    "linear-gradient(135deg, #4ECDC4 0%, #556270 100%)",
    "linear-gradient(135deg, #6C5B7B 0%, #C06C84 100%)",
    "linear-gradient(135deg, #FF8008 0%, #FFC837 100%)",
    "linear-gradient(135deg, #45B649 0%, #DCE35B 100%)"
  ];

  // Get cards from translation
  const cards: any[] = (t('cards', { returnObjects: true }) as any).map((card: any, index: number) => ({
    ...card,
    icon: cardIcons[card.title] || Building,
    gradient: cardGradients[index % cardGradients.length]
  }));

  return (
    <div className="w-full bg-white py-16">
      <div className="full-width-container">
        <div className="w-full max-w-[1920px] mx-auto bg-white">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-8 px-4 md:px-12">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              <h2 className="text-[22px] md:text-[24px] font-bold text-black">
                {t('title')}
              </h2>
              <span className="inline-flex px-[10px] py-[6px] bg-[#E6F6F6] text-[#1CB3AF] text-[13px] rounded">
                {t('subtitle')}
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 px-4 md:px-12">
            {cards.map((card, index) => (
              <button
                key={index}
                onClick={() => navigate('/stores')}
                className="group relative rounded-lg p-5 transition-all duration-300 hover:scale-[1.02] hover:shadow-lg cursor-pointer"
                style={{
                  background: card.gradient,
                  backgroundSize: '200% 200%',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundPosition = '100% 100%';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundPosition = '0% 0%';
                }}
              >
                <div className="flex flex-col h-full">
                  <card.icon className="w-6 h-6 text-white/90 mb-4" />
                  <h3 className="text-[17px] font-semibold text-white mb-auto">
                    {card.title}
                  </h3>
                  <div className="flex items-center justify-between mt-4">
                    <span className="text-sm text-white/70">{card.info}</span>
                    <span className="text-sm font-bold text-white">{card.count}</span>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DiscoverSection;