import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import { useMediaQuery } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/context/AuthContext";
import { Package } from "lucide-react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/bundle";

interface SliderImage {
  web: string;
  mobile: string;
  link?: string;
  header?: string;
  description?: string;
  linkText?: string;
}

interface HeroSliderProps {
  images: SliderImage[];
}

const HeroSlider: React.FC<HeroSliderProps> = ({ images }) => {
  const [isMobile] = useMediaQuery("(max-width: 768px)");
  const navigate = useNavigate();
  const { t } = useTranslation("slider");
  const { isAuthenticated } = useAuth();

  if (!images || images.length === 0) return null;

  return (
    <div className="w-full">
      <div className="full-width-container">
        <Swiper
          modules={[Navigation, Pagination, Autoplay]}
          navigation={!isMobile}
          pagination={{
            clickable: true,
            bulletClass: 'swiper-pagination-bullet !bg-white/50 !opacity-100',
            bulletActiveClass: 'swiper-pagination-bullet-active !bg-white',
          }}
          autoplay={{ delay: 5000, disableOnInteraction: false }}
          loop={true}
          className="h-[300px] md:h-[500px]"
        >
          {images.map((image, index) => (
            <SwiperSlide key={index}>
              <div className="relative h-full w-full">
                <img
                  src={
                    import.meta.env.VITE_SOCKET_URL +
                    (isMobile ? image.mobile : image.web)
                  }
                  alt={image.header || `Slide ${index + 1}`}
                  className="w-full h-full object-cover"
                  loading={index === 0 ? "eager" : "lazy"}
                />
                <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-black/50" />

                {/* Text overlay */}
                {(image.header || image.description) && (
                  <div className="absolute inset-0 flex items-center">
                    <div className="max-w-3xl mx-auto px-4 md:px-6 lg:px-8 w-full">
                      <div className="max-w-3xl">
                        {image.header && (
                          <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold text-white mb-4 md:mb-6">
                            {image.header}
                          </h2>
                        )}
                        {image.description && (
                          <p className="text-base md:text-lg lg:text-xl text-white/90 mb-6 md:mb-8">
                            {image.description}
                          </p>
                        )}
                        <div className="flex flex-col md:flex-row gap-4">
                          {image.link && (
                            <Link
                              to={image.link}
                              target="_blank"
                              className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors group"
                            >
                              <span>{image.linkText || t('learnMore')}</span>
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform"
                              >
                                <path d="M5 12h14M12 5l7 7-7 7" />
                              </svg>
                            </Link>
                          )}

                          {!isAuthenticated && (
                            <button
                              onClick={() => navigate('/register')}
                              className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-white text-primary rounded-lg font-medium hover:bg-gray-100 transition-colors group"
                            >
                              <Package className="h-5 w-5 mr-2" />
                              <span>{t('signUpFree')}</span>
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
};

export default HeroSlider;