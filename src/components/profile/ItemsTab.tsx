import React, { useState, useEffect, useRef } from "react";
import {
  Box,
  Select,
  SimpleGrid,
  Spinner,
  Alert,
  AlertIcon,
  VStack,
  useToast,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Button,
  Heading,
  HStack,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { IItem, IItemRequest } from "../../types/item";
import { getOwnedItemsByType, deleteItem } from "../../api";
import { ItemCard } from "./ItemCard";
import ItemRequestModal from "./ItemRequestModal";

const ItemsTab: React.FC = () => {
  const { t } = useTranslation("itemListing");
  const [items, setItems] = useState<IItem[]>([]);
  const [selectedType, setSelectedType] = useState<"product" | "service">(
    "product",
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedItemRequest, setSelectedItemRequest] = useState<
    IItemRequest | undefined
  >(undefined);
  const cancelRef = useRef<any>(null);
  const toast = useToast();

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const itemsData = await getOwnedItemsByType(selectedType);
      setItems(itemsData);
    } catch (err) {
      console.error("Error fetching data:", err);
      setError(t("errorFetchingItems"));
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [selectedType, t]);

  const handleEdit = (item: IItem) => {
    setSelectedItemRequest(item as any);
    setIsModalOpen(true);
  };

  const handleDelete = (item: IItem) => {
    setDeleteItemId(item._id);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!deleteItemId) return;

    try {
      await deleteItem(deleteItemId);
      await fetchData(); // Refresh the data
      toast({
        title: t("itemDeleted"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      toast({
        title: t("errorDeletingItem"),
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setDeleteItemId(null);
    }
  };

  const handleCreateNew = () => {
    setSelectedItemRequest(undefined);
    setIsModalOpen(true);
  };

  if (isLoading) {
    return (
      <Box textAlign="center" py={10}>
        <Spinner />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert status="error">
        <AlertIcon />
        {error}
      </Alert>
    );
  }

  return (
    <VStack spacing={6} align="stretch">
      <HStack justify="space-between" align="center">
        <Box>
          <Select
            value={selectedType}
            onChange={(e) =>
              setSelectedType(e.target.value as "product" | "service")
            }
            mb={4}
          >
            <option value="product">{t("products")}</option>
            <option value="service">{t("services")}</option>
          </Select>
        </Box>
        <Button colorScheme="blue" onClick={handleCreateNew}>
          {t("createNewRequest")}
        </Button>
      </HStack>

      <Box>
        <Heading size="md" mb={4}>
          {t("pendingRequests")}
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          {items
            .filter(
              (item: any) =>
                item.type === selectedType && item.status === "INACTIVE",
            )
            .map((item: any) => (
              <ItemCard
                key={item._id}
                item={item as any}
                onEdit={() => {
                  setSelectedItemRequest(item);
                  setIsModalOpen(true);
                }}
                onDelete={() => {
                  setDeleteItemId(item._id);
                  setIsDeleteDialogOpen(true);
                }}
              />
            ))}
        </SimpleGrid>
      </Box>

      <Box>
        <Heading size="md" mb={4}>
          {t("approvedItems")}
        </Heading>
        <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
          {items
            .filter(
              (item: any) =>
                item.type === selectedType && item.status === "ACTIVE",
            )
            .map((item: any) => (
              <ItemCard
                key={item._id}
                item={item}
                onEdit={() => handleEdit(item)}
                onDelete={() => handleDelete(item)}
              />
            ))}
        </SimpleGrid>
      </Box>

      <ItemRequestModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setSelectedItemRequest(undefined);
        }}
        itemRequest={selectedItemRequest}
        onSuccess={fetchData}
      />

      <AlertDialog
        isOpen={isDeleteDialogOpen}
        leastDestructiveRef={cancelRef}
        onClose={() => setIsDeleteDialogOpen(false)}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {t("deleteItemRequest")}
            </AlertDialogHeader>

            <AlertDialogBody>
              {t("deleteItemRequestConfirmation")}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button
                ref={cancelRef}
                onClick={() => setIsDeleteDialogOpen(false)}
              >
                {t("cancel")}
              </Button>
              <Button colorScheme="red" onClick={confirmDelete} ml={3}>
                {t("delete")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </VStack>
  );
};

export default ItemsTab;
