import React, { useEffect, useState, useRef } from "react";
import {
  Box,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  useDisclosure,
  VStack,
  HStack,
  Text,
  useToast,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Spinner,
  Select,
  Avatar,
  Divider,
  useColorModeValue,
  FormErrorMessage,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { ticketApi } from "@/api/ticketApi";
import { getActiveSubscription } from "@/api"; // Assuming this is correctly exported from /api/index.ts
import { ITicket } from "@/types/ticket";
import { ICity, ICountry } from "@/types/user";
import { ICategory } from "@/types/category";
import {
  getCountries,
  getCities,
  getCategories,
  getCategoriesByParentId,
} from "@/api";
import { formatDistance } from "date-fns";

interface ITicketFormData {
  title: string;
  type: string;
  category: string;
  subcategory: string;
  country: string;
  city: string;
  amount: number;
  description: string;
  images: FileList | [];
}

interface CurrentSubscription {
  _id: string;
  // Add other relevant fields if needed by other parts of TicketsTab, or keep minimal
}

const TicketsTab: React.FC = () => {
  const { t, i18n } = useTranslation(["tickets", "common", "userProfile", "cancelRequests"]);
  const toast = useToast();
  const [tickets, setTickets] = useState<ITicket[]>([]);
  const [selectedTicket, setSelectedTicket] = useState<ITicket | null>(null);
  const [newResponse, setNewResponse] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    isOpen: isCreateOpen,
    onOpen: onCreateOpen,
    onClose: onCreateClose,
  } = useDisclosure();
  const {
    isOpen: isDetailOpen,
    onOpen: onDetailOpen,
    onClose: onDetailClose,
  } = useDisclosure();
  const [formData, setFormData] = useState<ITicketFormData>({
    title: "",
    type: "product",
    category: "",
    subcategory: "",
    country: "",
    city: "",
    amount: 0,
    description: "",
    images: []
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [cities, setCities] = useState<ICity[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [subcategories, setSubcategories] = useState<ICategory[]>([]);
  // State for moved cancellation modal
  const [cancellationReason, setCancellationReason] = useState("");
  const [isCancellingSubscription, setIsCancellingSubscription] = useState(false);
  const [currentSubscriptionId, setCurrentSubscriptionId] = useState<string | null>(null);
  const { 
    isOpen: isCancelSubscriptionModalOpen, 
    onOpen: onOpenCancelSubscriptionModal, 
    onClose: onCloseCancelSubscriptionModal 
  } = useDisclosure();

  const bgColor = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  useEffect(() => {
    fetchTickets();
    fetchCountries();
    fetchCategories();

    const fetchUserSubscription = async () => {
      try {
        // Assuming getActiveSubscription might return null or an object with a subscription property
        const activeSubResponse = await getActiveSubscription(); 
        if (activeSubResponse && activeSubResponse.subscription && activeSubResponse.subscription._id) {
          setCurrentSubscriptionId(activeSubResponse.subscription._id);
        } else {
          setCurrentSubscriptionId(null);
        }
      } catch (error) {
        console.error("Error fetching active subscription for cancellation modal:", error);
        setCurrentSubscriptionId(null);
        // Optionally, show a toast, but it might be too noisy if the user doesn't intend to cancel
      }
    };
    fetchUserSubscription();
  }, []);

  const fetchTickets = async () => {
    try {
      const response = await ticketApi.getUserTickets();
      if (response.success) {
        setTickets(response.data || []);
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("tickets:messages.fetch_error"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewTicket = async (ticket: ITicket) => {
    try {
      const response = await ticketApi.getById(ticket._id);
      if (response.success && response.data) {
        setSelectedTicket(response.data);
        onDetailOpen();
        // Scroll to bottom after modal opens and content renders
        setTimeout(scrollToBottom, 200);
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("tickets:messages.fetch_error"),
        status: "error",
        duration: 3000,
      });
    }
  };

  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleAddResponse = async () => {
    if (!selectedTicket || !newResponse.trim()) return;

    setIsSubmitting(true);
    try {
      const response = await ticketApi.addResponse(
        selectedTicket._id,
        newResponse,
      );
      if (response.success && response.data) {
        setSelectedTicket(response.data);
        setNewResponse("");
        await fetchTickets();
        toast({
          title: t("common:success"),
          description: t("tickets:messages.response_added"),
          status: "success",
          duration: 3000,
        });
        // Scroll to bottom after the state updates and component re-renders
        setTimeout(scrollToBottom, 100);
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("tickets:messages.response_error"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    if (!formData.title) errors.title = t("tickets:validation.title_required");
    if (!formData.description)
      errors.description = t("tickets:validation.description_required");
    if (!formData.type) errors.type = t("tickets:validation.type_required");
    if (!formData.category)
      errors.category = t("tickets:validation.category_required");
    if (!formData.country)
      errors.country = t("tickets:validation.country_required");
    if (!formData.city) errors.city = t("tickets:validation.city_required");
    if (!formData.amount || formData.amount <= 0)
      errors.amount = t("tickets:validation.amount_min");
    if (formData.images && formData.images.length > 5)
      errors.images = t("tickets:validation.max_images");

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleConfirmMovedCancellation = async () => {
    if (!currentSubscriptionId) {
      toast({
        title: t("cancelRequests:error.title"),
        description: t("cancelRequests:error.noSubscriptionId", "Subscription ID not found."),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    setIsCancellingSubscription(true);
    
    // Create a ticket for cancellation request instead of direct cancellation
    const formDataToSend = new FormData();
    formDataToSend.append("title", t("tickets:cancellation_request", "Subscription Cancellation Request"));
    formDataToSend.append("type", "service");
    formDataToSend.append("description", cancellationReason);
    
    // Auto-fill other required fields with default values
    // Get first available category and country if they exist
    const defaultCategory = categories.length > 0 ? categories[0]._id : "";
    const defaultCountry = countries.length > 0 ? countries[0].code : "";
    const defaultCity = cities.length > 0 ? cities[0].city_id : "0"; // Default city ID if no cities
    
    formDataToSend.append("category", defaultCategory);
    formDataToSend.append("country", defaultCountry);
    formDataToSend.append("city", defaultCity);
    formDataToSend.append("amount", "0"); // Set amount to 0 for cancellation requests
    
    try {
      const response = await ticketApi.create(formDataToSend);
      if (response.success) {
        toast({
          title: t("common:success"),
          description: t("tickets:cancellation_ticket_created", "Your cancellation request has been submitted as a ticket."),
          status: "success",
          duration: 5000,
          isClosable: true,
        });
        setCancellationReason("");
        onCloseCancelSubscriptionModal();
        await fetchTickets(); // Refresh the tickets list
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: t("cancelRequests:error.title"),
        description: error.message || t("tickets:messages.create_error"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsCancellingSubscription(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    setIsSubmitting(true);
    const formDataToSend = new FormData();
    Object.keys(formData).forEach((key) => {
      if (key === "images") {
        Array.from(formData.images as FileList).forEach((file: File) => {
          formDataToSend.append("images", file);
        });
      } else {
        const value = formData[key as keyof ITicketFormData];
        formDataToSend.append(key, String(value));
      }
    });

    try {
      const response = await ticketApi.create(formDataToSend);
      if (response.success) {
        toast({
          title: t("common:success"),
          description: t("tickets:messages.create_success"),
          status: "success",
          duration: 3000,
        });
        onCreateClose();
        setFormData({
          title: "",
          type: "product",
          category: "",
          subcategory: "",
          country: "",
          city: "",
          amount: 0,
          description: "",
          images: [],
        });
        await fetchTickets();
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: t("tickets:messages.create_error"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const fetchCountries = async () => {
    try {
      const data = await getCountries();
      setCountries(data);
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: error.message,
        status: "error",
        duration: 3000,
      });
    }
  };

  const fetchCities = async (countryId: string) => {
    try {
      const data = await getCities(countryId);
      setCities(data);
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: error.message,
        status: "error",
        duration: 3000,
      });
    }
  };

  const fetchCategories = async () => {
    try {
      const data = await getCategories();
      setCategories(data);
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: error.message,
        status: "error",
        duration: 3000,
      });
    }
  };

  const fetchSubcategories = async (categoryId: string) => {
    try {
      const data = await getCategoriesByParentId(categoryId);
      setSubcategories(data);
    } catch (error: any) {
      toast({
        title: t("common:error"),
        description: error.message,
        status: "error",
        duration: 3000,
      });
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    const fieldName = name as keyof ITicketFormData;

    setFormData((prev: ITicketFormData) => ({
      ...prev,
      [fieldName]: value,
    }));
    setFormErrors((prev: Record<string, string>) => ({ ...prev, [fieldName]: "" }));

    if (fieldName === "country") {
      fetchCities(value);
      setFormData((prev: ITicketFormData) => ({ ...prev, city: "" }));
    } else if (fieldName === "category") {
      fetchSubcategories(value);
      setFormData((prev: ITicketFormData) => ({ ...prev, subcategory: "" }));
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 5) {
      setFormErrors((prev: any) => ({
        ...prev,
        images: t("tickets:validation.max_images"),
      }));
      return;
    }
    setFormData((prev: any) => ({ ...prev, images: files }));
    setFormErrors((prev: any) => ({ ...prev, images: "" }));
  };

  const getCategoryName = (categoryId: string) => {
    const category = categories.find((cat) => cat._id === categoryId);
    return category ? category.name : categoryId;
  };

  const getSubcategoryName = (subcategoryId: string) => {
    const subcategory = subcategories.find((cat) => cat._id === subcategoryId);
    return subcategory ? subcategory.name : subcategoryId;
  };

  return (
    <Box>
      <HStack justify="space-between" mb={4}>
        <Text fontSize="xl" fontWeight="bold">
          {t("tickets:title")}
        </Text>
        <HStack spacing={4}>
          <Button 
            colorScheme="red" 
            onClick={onOpenCancelSubscriptionModal} 
            mr={3}
            isDisabled={!currentSubscriptionId || isCancellingSubscription}
          >
            {t("userProfile:subscription.cancelButton", "Cancel Subscription")}
          </Button>
          <Button colorScheme="blue" onClick={() => {
            setFormData({ title: "", type: "product", category: "", subcategory: "", country: "", city: "", amount: 0, description: "", images: [] });
            setFormErrors({});
            onCreateOpen();
          }}>
            {t("tickets:create_ticket")}
          </Button>
        </HStack>
      </HStack>

      {isLoading ? (
        <Spinner />
      ) : (
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>{t("tickets:table.title")}</Th>
              <Th>{t("tickets:table.type")}</Th>
              <Th>{t("tickets:table.category")}</Th>
              <Th>{t("tickets:table.status")}</Th>
              <Th>{t("tickets:table.created_at")}</Th>
              <Th>{t("tickets:table.actions")}</Th>
            </Tr>
          </Thead>
          <Tbody>
            {tickets.map((ticket) => (
              <Tr key={ticket._id}>
                <Td>{ticket.title}</Td>
                <Td>{t(`tickets:types.${ticket.type}`)}</Td>
                <Td>
                  {getCategoryName(ticket.category)}
                  {ticket.subcategory && (
                    <> / {getSubcategoryName(ticket.subcategory)}</>
                  )}
                </Td>
                <Td>
                  <Badge
                    colorScheme={
                      ticket.status === "resolved"
                        ? "green"
                        : ticket.status === "in_progress"
                          ? "yellow"
                          : "red"
                    }
                  >
                    {t(`tickets:status.${ticket.status}`)}
                  </Badge>
                </Td>
                <Td>{new Date(ticket.createdAt).toLocaleDateString()}</Td>
                <Td>
                  <Button size="sm" onClick={() => handleViewTicket(ticket)}>
                    {t("tickets:actions.view")}
                  </Button>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      )}

      {/* Create Ticket Modal */}
      <Modal isOpen={isCreateOpen} onClose={onCreateClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <form onSubmit={handleSubmit}>
            <ModalHeader>{t("tickets:form.create_header")}</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              <VStack spacing={4}>
                <FormControl isInvalid={!!formErrors.title} isRequired>
                  <FormLabel>{t("tickets:form.title")}</FormLabel>
                  <Input
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                  />
                  <FormErrorMessage>{formErrors.title}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!formErrors.type} isRequired>
                  <FormLabel>{t("tickets:form.type")}</FormLabel>
                  <Select name="type" value={formData.type} onChange={handleInputChange}>
                    <option value="product">{t("tickets:form.product")}</option>
                    <option value="service">{t("tickets:form.service")}</option>
                  </Select>
                  <FormErrorMessage>{formErrors.type}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!formErrors.category} isRequired>
                  <FormLabel>{t("tickets:form.category")}</FormLabel>
                  <Select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    placeholder={t("tickets:form.select_category")}
                  >
                    {categories.map((category) => (
                      <option key={category._id} value={category._id}>
                        {category.name}
                      </option>
                    ))}
                  </Select>
                  <FormErrorMessage>{formErrors.category}</FormErrorMessage>
                </FormControl>

                {subcategories.length > 0 && (
                  <FormControl isInvalid={!!formErrors.subcategory}>
                    <FormLabel>{t("tickets:form.subcategory")}</FormLabel>
                    <Select
                      name="subcategory"
                      value={formData.subcategory}
                      onChange={handleInputChange}
                      placeholder={t("tickets:form.select_subcategory")}
                    >
                      {subcategories.map((subcategory) => (
                        <option key={subcategory._id} value={subcategory._id}>
                          {subcategory.name}
                        </option>
                      ))}
                    </Select>
                    <FormErrorMessage>{formErrors.subcategory}</FormErrorMessage>
                  </FormControl>
                )}

                <FormControl isInvalid={!!formErrors.country} isRequired>
                  <FormLabel>{t("tickets:form.country")}</FormLabel>
                  <Select
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    placeholder={t("tickets:form.select_country")}
                  >
                    {countries.map((country) => (
                      <option key={country.code} value={country.code}>
                        {country.name}
                      </option>
                    ))}
                  </Select>
                  <FormErrorMessage>{formErrors.country}</FormErrorMessage>
                </FormControl>

                {cities.length > 0 && (
                  <FormControl isInvalid={!!formErrors.city} isRequired>
                    <FormLabel>{t("tickets:form.city")}</FormLabel>
                    <Select
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      placeholder={t("tickets:form.select_city")}
                    >
                      {cities.map((city) => (
                        <option key={city.city_id} value={city.city_id}>
                          {city.name}
                        </option>
                      ))}
                    </Select>
                    <FormErrorMessage>{formErrors.city}</FormErrorMessage>
                  </FormControl>
                )}

                <FormControl isInvalid={!!formErrors.amount} isRequired>
                  <FormLabel>{t("tickets:form.amount")}</FormLabel>
                  <Input
                    name="amount"
                    type="number"
                    value={formData.amount}
                    onChange={handleInputChange}
                  />
                  <FormErrorMessage>{formErrors.amount}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!formErrors.description} isRequired>
                  <FormLabel>{t("tickets:form.description")}</FormLabel>
                  <Textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                  />
                  <FormErrorMessage>{formErrors.description}</FormErrorMessage>
                </FormControl>

                <FormControl isInvalid={!!formErrors.images}>
                  <FormLabel>{t("tickets:form.images")}</FormLabel>
                  <Input
                    type="file"
                    name="images"
                    multiple
                    onChange={handleFileChange}
                    accept="image/*"
                  />
                  <FormErrorMessage>{formErrors.images}</FormErrorMessage>
                </FormControl>
              </VStack>
            </ModalBody>

            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onCreateClose}>
                {t("common:cancel")}
              </Button>
              <Button
                type="submit"
                colorScheme="blue"
                isLoading={isSubmitting}
                loadingText={t("tickets:form.submitting")}
              >
                {t("tickets:form.submit")}
              </Button>
            </ModalFooter>
          </form>
        </ModalContent>
      </Modal>

      {/* Subscription Cancellation Modal */}
      <Modal isOpen={isCancelSubscriptionModalOpen} onClose={onCloseCancelSubscriptionModal}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("cancelRequests:title")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <Text>{t("cancelRequests:confirmationMessage")}</Text>
              <FormControl isRequired>
                <FormLabel>{t("cancelRequests:reasonLabel")}</FormLabel>
                <Textarea
                  value={cancellationReason}
                  onChange={(e) => setCancellationReason(e.target.value)}
                  placeholder={t("cancelRequests:reasonPlaceholder")}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onCloseCancelSubscriptionModal}>
              {t("common:cancel")}
            </Button>
            <Button
              colorScheme="red"
              onClick={handleConfirmMovedCancellation}
              isLoading={isCancellingSubscription}
              isDisabled={!cancellationReason.trim()}
            >
              {t("cancelRequests:confirmButton")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Ticket Detail Modal */}
      <Modal isOpen={isDetailOpen} onClose={onDetailClose} size="xl">
        <ModalOverlay />
        <ModalContent className="h-[80vh]">
          <ModalHeader>
            {selectedTicket?.title}
            <Badge
              ml={2}
              colorScheme={
                selectedTicket?.status === "resolved"
                  ? "green"
                  : selectedTicket?.status === "in_progress"
                    ? "yellow"
                    : "red"
              }
            >
              {selectedTicket?.status &&
                t(`tickets:status.${selectedTicket.status}`)}
            </Badge>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody className="overflow-y-scroll h-[80vh]">
            <VStack spacing={4} align="stretch">
              <Box>
                <Text fontWeight="bold">{t("tickets:form.description")}</Text>
                <Text>{selectedTicket?.description}</Text>
              </Box>

              {selectedTicket?.images && selectedTicket.images.length > 0 && (
                <Box>
                  <Text fontWeight="bold" mb={2}>
                    {t("tickets:form.images")}
                  </Text>
                  <HStack spacing={2} overflowX="auto" p={2}>
                    {selectedTicket.images.map((image, index) => (
                      <Box
                        key={index}
                        as="img"
                        src={image}
                        alt={`Ticket image ${index + 1}`}
                        maxH="200px"
                        objectFit="cover"
                        borderRadius="md"
                      />
                    ))}
                  </HStack>
                </Box>
              )}

              <Divider />

              <Box>
                <Text fontWeight="bold" mb={2}>
                  {t("tickets:responses")}
                </Text>
                <VStack spacing={4} align="stretch" mb={4}>
                  {selectedTicket?.responses?.map((response, index) => (
                    <Box
                      key={index}
                      p={4}
                      borderRadius="md"
                      bg={response.isAdmin ? "blue.50" : bgColor}
                      borderWidth="1px"
                      borderColor={borderColor}
                    >
                      <HStack spacing={3} mb={2}>
                        <Avatar
                          size="sm"
                          name={
                            response.isAdmin
                              ? t("tickets:admin")
                              : t("tickets:you")
                          }
                        />

                        <VStack align="start" spacing={0}>
                          <Text fontWeight="bold">
                            {response.isAdmin
                              ? t("tickets:admin")
                              : t("tickets:you")}
                          </Text>
                          <Text fontSize="sm" color="gray.500">
                            {formatDistance(
                              new Date(response.createdAt),
                              new Date(),
                              { addSuffix: true },
                            )}
                          </Text>
                        </VStack>
                      </HStack>
                      <Text>{response.message}</Text>
                    </Box>
                  ))}
                  <div ref={messagesEndRef} />
                </VStack>
              </Box>

              {selectedTicket?.status !== "resolved" && (
                <Box className="sticky bottom-0 bg-white p-4">
                  <FormControl>
                    <FormLabel>{t("tickets:add_response")}</FormLabel>
                    <Textarea
                      value={newResponse}
                      onChange={(e) => setNewResponse(e.target.value)}
                      placeholder={t("tickets:response_placeholder")}
                    />
                  </FormControl>
                  <Button
                    mt={2}
                    colorScheme="blue"
                    isLoading={isSubmitting}
                    onClick={handleAddResponse}
                    isDisabled={!newResponse.trim()}
                  >
                    {t("tickets:send")}
                  </Button>
                </Box>
              )}
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default TicketsTab;
