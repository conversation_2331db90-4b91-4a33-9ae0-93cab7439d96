import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  Text,
  VStack,
  HStack,
  useToast,
  Card,
  CardBody,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  useDisclosure,
} from "@chakra-ui/react";
import { DeleteIcon, StarIcon } from "@chakra-ui/icons";
import { useTranslation } from "react-i18next";
import {
  getStoredCards,
  removeStoredCard,
  setDefaultCard,
  addStoredCard,
} from "../../api/cardApi";

interface StoredCard {
  _id: string;
  cardAlias: string;
  lastFourDigits: string;
  cardType: string;
  isDefault: boolean;
}

const CardTab: React.FC = () => {
  const { t } = useTranslation("userProfile");
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [cards, setCards] = useState<StoredCard[]>([]);
  const [loading, setLoading] = useState(true);
  const [newCard, setNewCard] = useState({
    cardHolderName: "",
    cardNumber: "",
    expireMonth: "",
    expireYear: "",
    cvc: "",
    cardAlias: "",
  });

  const fetchCards = async () => {
    try {
      const storedCards = await getStoredCards();
      setCards(storedCards);
    } catch (error: any) {
      toast({
        title: t("errors.failedToLoadCards.title"),
        description: t("errors.failedToLoadCards.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCards();
  }, []);

  const handleRemoveCard = async (cardId: string) => {
    try {
      await removeStoredCard(cardId);
      await fetchCards();
      toast({
        title: t("success.cardRemoved"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: t("errors.failedToRemoveCard.title"),
        description: t("errors.failedToRemoveCard.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleSetDefaultCard = async (cardId: string) => {
    try {
      await setDefaultCard(cardId);
      await fetchCards();
      toast({
        title: t("success.defaultCardSet"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: t("errors.failedToSetDefaultCard.title"),
        description: t("errors.failedToSetDefaultCard.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleAddCard = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await addStoredCard(newCard);
      await fetchCards();
      onClose();
      setNewCard({
        cardHolderName: "",
        cardNumber: "",
        expireMonth: "",
        expireYear: "",
        cvc: "",
        cardAlias: "",
      });
      toast({
        title: t("success.cardAdded"),
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: t("errors.failedToAddCard.title"),
        description: t("errors.failedToAddCard.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  if (loading) {
    return <Text>{t("loading")}</Text>;
  }

  return (
    <Box>
      <VStack spacing={4} align="stretch">
        <HStack justify="space-between">
          <Text fontSize="xl" fontWeight="bold">
            {t("cards.savedCards")}
          </Text>
          <Button colorScheme="blue" onClick={onOpen}>
            {t("cards.addNewCard")}
          </Button>
        </HStack>

        {cards.length === 0 ? (
          <Text>{t("cards.noSavedCards")}</Text>
        ) : (
          cards.map((card) => (
            <Card key={card._id}>
              <CardBody>
                <HStack justify="space-between">
                  <VStack align="start" spacing={1}>
                    <Text fontWeight="bold">
                      {card.cardAlias || t("cards.unnamedCard")}
                    </Text>
                    <Text>**** **** **** {card.lastFourDigits}</Text>
                    <Text fontSize="sm" color="gray.500">
                      {card.cardType}
                    </Text>
                  </VStack>
                  <HStack>
                    <IconButton
                      aria-label="Set as default"
                      icon={<StarIcon />}
                      colorScheme={card.isDefault ? "yellow" : "gray"}
                      onClick={() => handleSetDefaultCard(card._id)}
                    />

                    <IconButton
                      aria-label="Remove card"
                      icon={<DeleteIcon />}
                      colorScheme="red"
                      onClick={() => handleRemoveCard(card._id)}
                    />
                  </HStack>
                </HStack>
              </CardBody>
            </Card>
          ))
        )}
      </VStack>

      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("cards.addNewCard")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <form onSubmit={handleAddCard}>
              <VStack spacing={4}>
                <FormControl isRequired>
                  <FormLabel>{t("cards.cardAlias")}</FormLabel>
                  <Input
                    value={newCard.cardAlias}
                    onChange={(e) =>
                      setNewCard({ ...newCard, cardAlias: e.target.value })
                    }
                    placeholder={t("cards.cardAliasPlaceholder")}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("cards.cardHolderName")}</FormLabel>
                  <Input
                    value={newCard.cardHolderName}
                    onChange={(e) =>
                      setNewCard({ ...newCard, cardHolderName: e.target.value })
                    }
                    placeholder={t("cards.cardHolderNamePlaceholder")}
                  />
                </FormControl>
                <FormControl isRequired>
                  <FormLabel>{t("cards.cardNumber")}</FormLabel>
                  <Input
                    value={newCard.cardNumber}
                    onChange={(e) =>
                      setNewCard({ ...newCard, cardNumber: e.target.value })
                    }
                    placeholder="**** **** **** ****"
                    maxLength={16}
                  />
                </FormControl>
                <HStack>
                  <FormControl isRequired>
                    <FormLabel>{t("cards.expireMonth")}</FormLabel>
                    <Input
                      value={newCard.expireMonth}
                      onChange={(e) =>
                        setNewCard({ ...newCard, expireMonth: e.target.value })
                      }
                      placeholder="MM"
                      maxLength={2}
                    />
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>{t("cards.expireYear")}</FormLabel>
                    <Input
                      value={newCard.expireYear}
                      onChange={(e) =>
                        setNewCard({ ...newCard, expireYear: e.target.value })
                      }
                      placeholder="YY"
                      maxLength={2}
                    />
                  </FormControl>
                  <FormControl isRequired>
                    <FormLabel>{t("cards.cvc")}</FormLabel>
                    <Input
                      value={newCard.cvc}
                      onChange={(e) =>
                        setNewCard({ ...newCard, cvc: e.target.value })
                      }
                      placeholder="***"
                      maxLength={3}
                      type="password"
                    />
                  </FormControl>
                </HStack>
                <Button type="submit" colorScheme="blue" width="100%">
                  {t("cards.addCard")}
                </Button>
              </VStack>
            </form>
          </ModalBody>
        </ModalContent>
      </Modal>
    </Box>
  );
};

export default CardTab;
