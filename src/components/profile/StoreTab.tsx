import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  useToast,
  Heading,
  Grid,
  Textarea,
  Image,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  useDisclosure,
  HStack,
  Text,
  IconButton,
  RadioGroup,
  Radio,
  Stack,
} from "@chakra-ui/react";
import { getUserStore, createStore, updateStore } from "../../api/storeApi";
import { useTranslation } from "react-i18next";
import { AddIcon, ViewIcon } from "@chakra-ui/icons";
import { IStore } from "@/types/store";

interface ImageUploadPreviewProps {
  label: string;
  currentImage?: string | null;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  accept?: string;
  required?: boolean;
}

const ImageUploadPreview: React.FC<ImageUploadPreviewProps> = ({
  label,
  currentImage,
  onChange,
  accept = "image/*",
  required = false,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [preview, setPreview] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const reader = new FileReader();
      reader.onload = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(e.target.files[0]);
    }
    onChange(e);
  };

  return (
    <FormControl isRequired={required}>
      <FormLabel>{label}</FormLabel>
      <VStack spacing={2} align="stretch">
        <HStack spacing={4}>
          <Box
            borderWidth="1px"
            borderRadius="lg"
            p={2}
            width="100%"
            position="relative"
          >
            <Input
              type="file"
              accept={accept}
              onChange={handleChange}
              opacity="0"
              position="absolute"
              top="0"
              left="0"
              width="100%"
              height="100%"
              cursor="pointer"
            />

            <HStack spacing={4} justify="space-between">
              <Text fontSize="sm" color="gray.500">
                {preview || currentImage ? "Change Image" : "Choose Image"}
              </Text>
              {(preview || currentImage) && (
                <IconButton
                  aria-label="View image"
                  icon={<ViewIcon />}
                  size="sm"
                  onClick={(e) => {
                    e.preventDefault();
                    onOpen();
                  }}
                />
              )}
            </HStack>
          </Box>
        </HStack>
        {(preview || currentImage) && (
          <Box
            borderWidth="1px"
            borderRadius="lg"
            overflow="hidden"
            width="150px"
            height="150px"
          >
            <Image
              src={preview || currentImage || ""}
              alt={label}
              objectFit="cover"
              width="100%"
              height="100%"
            />
          </Box>
        )}
      </VStack>

      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{label}</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Image
              src={preview || currentImage || ""}
              alt={label}
              maxW="100%"
              maxH="70vh"
              objectFit="contain"
            />
          </ModalBody>
        </ModalContent>
      </Modal>
    </FormControl>
  );
};

const StoreTab: React.FC = () => {
  const { t } = useTranslation("userProfile");
  const toast = useToast();

  const [store, setStore] = useState<IStore | null>(null);
  const [storeLoading, setStoreLoading] = useState<boolean>(true);
  const [storeName, setStoreName] = useState<string>("");
  const [storeDescription, setStoreDescription] = useState<string>("");
  const [storeAddress, setStoreAddress] = useState<string>("");
  const [storePhone, setStorePhone] = useState<string>("");
  const [storeEmail, setStoreEmail] = useState<string>("");
  const [storeWebsite, setStoreWebsite] = useState<string>("");
  const [storeLogo, setStoreLogo] = useState<File | null>(null);
  const [storeCover, setStoreCover] = useState<File | null>(null);
  const [storeType, setStoreType] = useState<string>("company");

  useEffect(() => {
    const fetchStore = async () => {
      try {
        const storeData = await getUserStore();
        setStore(storeData);
        setStoreName(storeData.name || "");
        setStoreDescription(storeData.description || "");
        setStoreAddress(storeData.address || "");
        setStorePhone(storeData.phone || "");
        setStoreEmail(storeData.email || "");
        setStoreWebsite(storeData.website || "");
        setStoreType(storeData.type || "company");
      } catch (error: any) {
        if (error.response?.status !== 404) {
          toast({
            title: t("errors.failedToLoadStore.title"),
            description: t("errors.failedToLoadStore.description"),
            status: "error",
            duration: 5000,
            isClosable: true,
          });
        }
      } finally {
        setStoreLoading(false);
      }
    };

    fetchStore();
  }, [toast, t]);

  const handleStoreSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const formData = new FormData();

      // Add basic store data
      formData.append("name", storeName);
      formData.append("description", storeDescription);
      formData.append("address", storeAddress);
      formData.append("phone", storePhone);
      formData.append("email", storeEmail);
      formData.append("website", storeWebsite);
      formData.append("type", storeType);

      // When updating an existing store
      if (store) {
        // Add files if they exist, otherwise preserve existing images
        if (storeLogo) {
          formData.append("logo", storeLogo);
        } else if (store.logo) {
          // Signal backend to preserve existing logo
          formData.append("preserveLogo", "true");
        }
        
        if (storeCover) {
          formData.append("coverImage", storeCover);
        } else if (store.coverImage) {
          // Signal backend to preserve existing cover image
          formData.append("preserveCoverImage", "true");
        }
      } else {
        // For new store creation
        if (storeLogo) {
          formData.append("logo", storeLogo);
        }
        if (storeCover) {
          formData.append("coverImage", storeCover);
        }
      }

      if (store) {
        const updatedStore = await updateStore(formData);
        setStore(updatedStore);
      } else {
        const newStore = await createStore(formData);
        setStore(newStore);
      }

      toast({
        title: store ? t("success.storeUpdated") : t("success.storeCreated"),
        description: store
          ? t("success.storeUpdatedDesc")
          : t("success.storeCreatedDesc"),
        status: "success",
        duration: 5000,
        isClosable: true,
      });
    } catch (error: any) {
      toast({
        title: store
          ? t("errors.failedToUpdateStore.title")
          : t("errors.failedToCreateStore.title"),
        description: store
          ? t("errors.failedToUpdateStore.description")
          : t("errors.failedToCreateStore.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleFileChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    type: "logo" | "cover",
  ) => {
    if (e.target.files && e.target.files[0]) {
      if (type === "logo") {
        setStoreLogo(e.target.files[0]);
      } else {
        setStoreCover(e.target.files[0]);
      }
    }
  };

  return (
    <VStack spacing={8} align="stretch">
      <Box>
        <Heading size="md" mb={4}>
          {store ? t("store.updateStore") : t("store.createStore")}
        </Heading>
        {!storeLoading && (
          <form onSubmit={handleStoreSubmit}>
            <VStack spacing={4} align="start">
              <FormControl id="storeName" isRequired>
                <FormLabel>{t("store.name")}</FormLabel>
                <Input
                  type="text"
                  value={storeName}
                  onChange={(e) => setStoreName(e.target.value)}
                />
              </FormControl>

              <FormControl id="storeDescription" isRequired>
                <FormLabel>{t("store.description")}</FormLabel>
                <Textarea
                  value={storeDescription}
                  onChange={(e) => setStoreDescription(e.target.value)}
                />
              </FormControl>

              <Grid templateColumns="repeat(2, 1fr)" gap={4} width="100%">
                <ImageUploadPreview
                  label={t("store.logo")}
                  currentImage={
                    store?.logo
                      ? `${import.meta.env.VITE_SOCKET_URL}/${store.logo}`
                      : undefined
                  }
                  onChange={(e) => handleFileChange(e, "logo")}
                />

                <ImageUploadPreview
                  label={t("store.coverImage")}
                  currentImage={
                    store?.coverImage
                      ? `${import.meta.env.VITE_SOCKET_URL}/${store.coverImage}`
                      : undefined
                  }
                  onChange={(e) => handleFileChange(e, "cover")}
                />
              </Grid>

              <FormControl id="storeType" isRequired>
                <FormLabel>{t("store.type")}</FormLabel>
                <RadioGroup value={storeType} onChange={setStoreType}>
                  <Stack direction="row">
                    <Radio value="company">{t("store.company")}</Radio>
                    <Radio value="broker">{t("store.broker")}</Radio>
                  </Stack>
                </RadioGroup>
              </FormControl>

              <FormControl id="storeAddress">
                <FormLabel>{t("store.address")}</FormLabel>
                <Input
                  type="text"
                  value={storeAddress}
                  onChange={(e) => setStoreAddress(e.target.value)}
                />
              </FormControl>

              <Grid templateColumns="repeat(2, 1fr)" gap={4} width="100%">
                <FormControl id="storePhone">
                  <FormLabel>{t("store.phone")}</FormLabel>
                  <Input
                    type="tel"
                    value={storePhone}
                    onChange={(e) => setStorePhone(e.target.value)}
                  />
                </FormControl>

                <FormControl id="storeEmail">
                  <FormLabel>{t("store.email")}</FormLabel>
                  <Input
                    type="email"
                    value={storeEmail}
                    onChange={(e) => setStoreEmail(e.target.value)}
                  />
                </FormControl>
              </Grid>

              <FormControl id="storeWebsite">
                <FormLabel>{t("store.website")}</FormLabel>
                <Input
                  type="url"
                  value={storeWebsite}
                  onChange={(e) => setStoreWebsite(e.target.value)}
                />
              </FormControl>

              <Button
                type="submit"
                colorScheme="blue"
                leftIcon={store ? undefined : <AddIcon />}
                width="100%"
              >
                {store ? t("store.updateStore") : t("store.createStore")}
              </Button>
            </VStack>
          </form>
        )}
      </Box>
    </VStack>
  );
};

export default StoreTab;
