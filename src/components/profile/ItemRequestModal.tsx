import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  VStack,
  useToast,
  Box,
  Image,
  SimpleGrid,
  IconButton,
  Spinner,
  Text,
  Grid,
  GridItem,
  Icon,
  Flex,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { DeleteIcon } from "@chakra-ui/icons";
import RichTextEditor from "../common/RichTextEditor";
import {
  createItemRequest,
  updateItemRequest,
  getCategoriesByType,
  uploadFiles,
} from "@/api";
import { FaSave, FaPlus } from "react-icons/fa";

interface ItemRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemRequest?: any;
  onSuccess: () => void;
}

// Add Category interface
interface Category {
  _id: string;
  name: string;
  description?: string;
}

// Update the form data interface
interface FormData {
  name: string;
  description: string;
  type: "product" | "service";
  listingType: "demand" | "sale";
  category: string;
  images: string[];
}

const ItemRequestModal: React.FC<ItemRequestModalProps> = ({
  isOpen,
  onClose,
  itemRequest,
  onSuccess,
}) => {
  const { t } = useTranslation("itemListing");
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isCategoriesLoading, setIsCategoriesLoading] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    type: "product",
    listingType: "sale",
    category: "",
    images: [],
  });
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);

  useEffect(() => {
    if (itemRequest) {
      // When editing an existing item, initialize with its data
      const existingImages = itemRequest.images || [];

      setFormData({
        name: itemRequest.name,
        description: itemRequest.description,
        type: itemRequest.type,
        listingType: itemRequest.listingType,
        category: itemRequest.category._id,
        images: existingImages,
      });

      // Set preview URLs for existing images with proper formatting
      const formattedExistingImages = existingImages.map((img: string) => {
        return { path: img, isExisting: true };
      });
      setPreviewUrls(formattedExistingImages);
      console.log('Editing item with', existingImages.length, 'images');
    } else {
      // Reset form when creating a new item
      setFormData({
        name: "",
        description: "",
        type: "product",
        listingType: "sale",
        category: "",
        images: [],
      });
      setPreviewUrls([]);
      setSelectedFiles([]);
    }
  }, [itemRequest]);

  useEffect(() => {
    const fetchMainCategories = async () => {
      try {
        setIsCategoriesLoading(true);
        const data = await getCategoriesByType(formData.type);
        setCategories(data);
      } catch (error) {
        console.error("Error fetching categories:", error);
        toast({
          title: t("errors.fetchCategoriesFailed.title"),
          description: t("errors.fetchCategoriesFailed.description"),
          status: "error",
          duration: 3000,
        });
      } finally {
        setIsCategoriesLoading(false);
      }
    };

    if (isOpen) {
      fetchMainCategories();
    }
  }, [isOpen, formData.type, t, toast]);

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = event.target.files;
    if (files) {
      const newFiles = Array.from(files);
      setSelectedFiles((prev) => [...prev, ...newFiles]);

      // Create preview URLs for new files
      newFiles.forEach((file) => {
        const reader = new FileReader();
        reader.onloadend = () => {
          // Add new file preview with dataURL and flag as not existing (new upload)
          setPreviewUrls((prev: any) => [...prev, {
            path: reader.result as string,
            isExisting: false,
            file: file
          }]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const handleSubmit = async () => {
    try {
      setIsLoading(true);

      // Validate required fields
      if (
        !formData.name ||
        !formData.description ||
        !formData.type ||
        !formData.category
      ) {
        toast({
          title: t("errors.validationFailed.title"),
          description: t("errors.validationFailed.description"),
          status: "error",
          duration: 3000,
        });
        return;
      }

      // First, upload any new files if they exist
      let uploadedImagePaths: string[] = [];
      if (selectedFiles.length > 0) {
        try {
          uploadedImagePaths = await uploadFiles(selectedFiles);
        } catch (error) {
          console.error("Error uploading files:", error);
          toast({
            title: t("errors.fileUploadFailed.title"),
            description: t("errors.fileUploadFailed.description"),
            status: "error",
            duration: 3000,
          });
          return;
        }
      }

      // Combine existing images from formData with newly uploaded ones
      // formData.images contains only the existing images that weren't removed
      const allImages = [
        ...formData.images, // These are the remaining existing images
        ...uploadedImagePaths, // Add newly uploaded image paths
      ];

      console.log('Submitting with images:', {
        existing: formData.images.length,
        new: uploadedImagePaths.length,
        total: allImages.length
      });

      // Prepare request data
      const requestData = {
        name: formData.name,
        description: formData.description,
        type: formData.type,
        listingType: formData.listingType,
        category: formData.category,
        images: allImages,
      };

      if (itemRequest) {
        await updateItemRequest(itemRequest._id, requestData);
        toast({
          title: t("successMessages.itemRequestUpdated"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      } else {
        await createItemRequest(requestData);
        toast({
          title: t("successMessages.itemRequestCreated"),
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      }
      onSuccess();
      onClose();
    } catch (error: any) {
      console.error("Form submission error:", error);
      toast({
        title: error.response?.data?.message || t("errorSavingItemRequest.title"),
        description: t("errorSavingItemRequest.description"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const removeImage = (index: number) => {
    const imageToRemove: any = previewUrls[index];

    // Remove from preview URLs
    setPreviewUrls((prev) => prev.filter((_, i) => i !== index));

    if (!imageToRemove.isExisting) {
      // If it's a new upload, remove from selectedFiles
      const fileIndex = selectedFiles.findIndex(file =>
        // Find the file by comparing with the file in preview URL
        imageToRemove.file === file
      );

      if (fileIndex >= 0) {
        setSelectedFiles(prev => prev.filter((_, i) => i !== fileIndex));
      }
    } else {
      // If it's an existing image, remove from formData.images
      const pathToRemove = imageToRemove.path;
      setFormData(prev => ({
        ...prev,
        images: prev.images.filter(imgPath => imgPath !== pathToRemove)
      }));
    }

    console.log('Image removed. Existing images:',
      formData.images.length, 'New files:', selectedFiles.length);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay />
      <ModalContent>
        <ModalHeader>
          {itemRequest ? t("editItemRequest") : t("createItemRequest")}
        </ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={6}>
            {/* Left Column */}
            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel fontWeight="medium">{t("name")}</FormLabel>
                <Input
                  value={formData.name}
                  onChange={(e) =>
                    setFormData({ ...formData, name: e.target.value })
                  }
                  placeholder={t("enterName")}
                  borderRadius="md"
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel fontWeight="medium">{t("type")}</FormLabel>
                <Select
                  value={formData.type}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      type: e.target.value as "product" | "service",
                    })
                  }
                  borderRadius="md"
                >
                  <option value="product">{t("product")}</option>
                  <option value="service">{t("service")}</option>
                </Select>
              </FormControl>
            </VStack>

            {/* Right Column */}
            <VStack spacing={4} align="stretch">
              <FormControl isRequired>
                <FormLabel fontWeight="medium">{t("category")}</FormLabel>
                {isCategoriesLoading ? (
                  <Spinner size="sm" />
                ) : (
                  <Select
                    value={formData.category}
                    onChange={(e) =>
                      setFormData({ ...formData, category: e.target.value })
                    }
                    placeholder={t("selectCategory")}
                    borderRadius="md"
                  >
                    {categories.map((category) => (
                      <option key={category._id} value={category._id}>
                        {category.name}
                      </option>
                    ))}
                  </Select>
                )}
              </FormControl>

              <FormControl isRequired>
                <FormLabel fontWeight="medium">{t("listingType")}</FormLabel>
                <Select
                  value={formData.listingType}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      listingType: e.target.value as "demand" | "sale",
                    })
                  }
                  borderRadius="md"
                >
                  <option value="sale">{t("sale")}</option>
                  <option value="demand">{t("demand")}</option>
                </Select>
              </FormControl>
            </VStack>

            {/* Full Width Elements */}
            <GridItem colSpan={{ base: 1, md: 2 }}>
              <FormControl isRequired mt={4}>
                <FormLabel fontWeight="medium">{t("description")}</FormLabel>
                <RichTextEditor
                  value={formData.description}
                  onChange={(value) =>
                    setFormData({ ...formData, description: value })
                  }
                />
              </FormControl>

              <FormControl mt={4}>
                <FormLabel fontWeight="medium">{t("images")}</FormLabel>
                <Flex alignItems="center" gap={4}>
                  <Input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleFileChange}
                    display="none"
                    id="file-upload"
                  />

                  <Button
                    as="label"
                    htmlFor="file-upload"
                    cursor="pointer"
                    colorScheme="blue"
                    variant="outline"
                    leftIcon={<Icon as={DeleteIcon} />}
                    borderRadius="md"
                    flex="1"
                  >
                    {t("uploadImages")}
                  </Button>

                  {previewUrls.length > 0 && (
                    <Text color="gray.600">
                      {previewUrls.length}{" "}
                      {previewUrls.length === 1 ? t("image") : t("images")}
                    </Text>
                  )}
                </Flex>

                <SimpleGrid columns={3} spacing={4} mt={4}>
                  {previewUrls.length > 0 ? (
                    previewUrls.map((imageData: any, index) => (
                      <Box
                        key={index}
                        position="relative"
                        borderRadius="md"
                        overflow="hidden"
                        boxShadow="sm"
                      >
                        <Image
                          src={imageData.isExisting
                            ? `${import.meta.env.VITE_SOCKET_URL}/${imageData.path.replace(/^\/+/, '')}`
                            : imageData.path /* Use dataURL directly for new uploads */
                          }
                          alt={`${t("preview")} ${index + 1}`}
                          objectFit="cover"
                          boxSize="100px"
                        />

                        <IconButton
                          aria-label={t("removeImage")}
                          icon={<DeleteIcon />}
                          size="sm"
                          position="absolute"
                          top={1}
                          right={1}
                          colorScheme="red"
                          onClick={() => removeImage(index)}
                        />
                      </Box>
                    ))
                  ) : (
                    <Text color="gray.500">{t("noImagesUploaded")}</Text>
                  )}
                </SimpleGrid>
              </FormControl>
            </GridItem>
          </Grid>
        </ModalBody>

        <ModalFooter
          borderTopWidth="1px"
          borderTopColor="gray.200"
          mt={4}
          pt={4}
        >
          <Button variant="outline" mr={3} onClick={onClose} borderRadius="md">
            {t("cancel")}
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={isLoading}
            loadingText={itemRequest ? t("updating") : t("creating")}
            leftIcon={itemRequest ? <FaSave /> : <FaPlus />}
            borderRadius="md"
          >
            {itemRequest ? t("update") : t("create")}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default ItemRequestModal;
