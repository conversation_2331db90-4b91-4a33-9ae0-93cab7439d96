import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>ta<PERSON>,
  Button,
  useToast,
  FormControl,
  FormLabel,
  Input,
  FormErrorMessage,
  Box,
  Text,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Image,
  Card,
  CardBody,
  SimpleGrid,
  useBreakpointValue,
  IconButton,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  Flex,
  Select,
  Heading,
  Divider,
  FormHelperText,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { useForm, Controller } from "react-hook-form";
import { format } from "date-fns";
import { RepeatIcon, DeleteIcon, InfoIcon } from "@chakra-ui/icons";
import { useAuth } from "@/context/AuthContext";
import { api } from "@/api";

interface IProduct {
  _id: string;
  name: string;
  images: string[];
}

interface IHomepageAd {
  _id: string;
  image: string;
  title: string;
  clicks: number;
  status: "pending" | "approved" | "rejected" | "expired";
  rejectionReason?: string;
  createdAt: Date;
  expiresAt: Date;
  itemId?: string;
}

interface ICreateHomepageAd {
  title: string;
  image: FileList;
  itemId?: string;
}

const HomepageAdTab: React.FC = () => {
  const { t } = useTranslation(["profile", "common"]);
  const toast = useToast();
  const { user } = useAuth();
  const [ads, setAds] = useState<IHomepageAd[]>([]);
  const [products, setProducts] = useState<IProduct[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const isMobile = useBreakpointValue({ base: true, md: false });


  // Modal state for viewing images
  const { isOpen: isImageModalOpen, onOpen: openImageModal, onClose: closeImageModal } = useDisclosure();
  const [selectedImage, setSelectedImage] = useState<string>("");

  // Alert dialog state for delete confirmation
  const { isOpen: isDeleteAlertOpen, onOpen: openDeleteAlert, onClose: closeDeleteAlert } = useDisclosure();
  const [adToDelete, setAdToDelete] = useState<string>("");
  const cancelRef = React.useRef<HTMLButtonElement>(null);

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm<ICreateHomepageAd>();

  const fetchAds = async () => {
    try {
      const response = await api.get("/home-ads/user");
      const data = await response.data;
      setAds(data);
    } catch (error) {
      toast({
        title: t("homepageAd.messages.fetch_error.title"),
        description: t("homepageAd.messages.fetch_error.description"),
        status: "error",
        duration: 3000,
      });
    }
  };

  // Fetch user's products
  const fetchProducts = async () => {
    try {
      setIsLoadingProducts(true);
      const response = await api.get("/items/owned/type/all");

      // Make sure we handle the response data correctly
      const items = Array.isArray(response.data) ? response.data : [];
      setProducts(items);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast({
        title: t("homepageAd.messages.products_fetch_error.title"),
        description: t("homepageAd.messages.products_fetch_error.description"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsLoadingProducts(false);
    }
  };

  const handleImageClick = (image: string) => {
    // Ensure we have a full URL by constructing it correctly
    const baseUrl = import.meta.env.VITE_SOCKET_URL || '';
    const fullImageUrl = image.startsWith('http') ? image : `${baseUrl}/uploads${image}`;
    setSelectedImage(fullImageUrl);
    openImageModal();
  };

  const handleDeleteClick = (adId: string) => {
    setAdToDelete(adId);
    openDeleteAlert();
  };

  const confirmDelete = async () => {
    if (!adToDelete) return;

    try {
      setIsDeleting(true);
      await api.delete(`/home-ads/${adToDelete}`);

      toast({
        title: t("homepageAd.messages.delete_success.title"),
        description: t("homepageAd.messages.delete_success.description"),
        status: "success",
        duration: 3000,
      });

      // Update the ads list
      setAds(ads.filter(ad => ad._id !== adToDelete));
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || t("homepageAd.messages.delete_error", "Failed to delete ad");

      toast({
        title: t("homepageAd.messages.delete_error.title"),
        description: errorMessage,
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsDeleting(false);
      closeDeleteAlert();
      setAdToDelete("");
    }
  };

  const onSubmit = async (data: ICreateHomepageAd) => {
    try {
      setIsSubmitting(true);

      // Check if there's a file selected
      if (!data.image || data.image.length === 0) {
        throw new Error(t("homepageAd.validation.image_required"));
      }

      const imageFile = data.image[0];
      console.log('Uploading image:', {
        name: imageFile.name,
        type: imageFile.type,
        size: imageFile.size
      });

      // Create form data
      const formData = new FormData();
      formData.append("title", data.title);
      formData.append("image", imageFile);

      // Add itemId if selected
      if (data.itemId) {
        formData.append("itemId", data.itemId);
      }

      // Log form data for debugging
      console.log('Form data contents:');
      for (let [key, value] of formData.entries()) {
        console.log(`${key}: ${value instanceof File ? `File: ${value.name} (${value.size} bytes)` : value}`);
      }

      // Get the auth token
      const token = localStorage.getItem('userToken');

      // Make the request using the Fetch API for more direct control
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/home-ads`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${token}`
        },
        // Include credentials
        credentials: 'include'
      });

      // Parse the response
      const result = await response.json();
      console.log('API response:', result);

      if (!response.ok) {
        throw new Error(result.message || t("homepageAd.messages.create_error"));
      }

      toast({
        title: t("homepageAd.messages.create_success.title"),
        description: t("homepageAd.messages.create_success.description"),
        status: "success",
        duration: 3000,
      });

      reset();
      fetchAds();
    } catch (error: any) {
      console.error('Error creating homepage ad:', error);

      // Display error message
      toast({
        title: t("homepageAd.messages.create_error.title"),
        description: error.message || t("homepageAd.messages.create_error.description"),
        status: "error",
        duration: 3000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReactivate = async (id: string) => {
    try {
      const response = await api.put(`/home-ads/${id}/activate`, {
        method: "PUT",
      });

      if (response.status !== 200) {
        throw new Error(t("homepageAd.messages.reactivate_error.description"));
      }

      toast({
        title: t("homepageAd.messages.reactivate_success.title"),
        description: t("homepageAd.messages.reactivate_success.description"),
        status: "success",
        duration: 3000,
      });

      fetchAds();
    } catch (error) {
      toast({
        title: t("homepageAd.messages.reactivate_error.title"),
        description: t("homepageAd.messages.reactivate_error.description"),
        status: "error",
        duration: 3000,
      });
    }
  };

  useEffect(() => {
    fetchAds();
    fetchProducts();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "green";
      case "rejected":
        return "red";
      case "expired":
        return "gray";
      default:
        return "yellow";
    }
  };

  const hasHomepageAdAddon =
    (user as any)?.subscriptionDetails?.addons?.some((addon: any) =>
      addon.packageId?.homepageAd === true
    ) ||
    (user as any)?.subscriptionDetails?.standard?.packageId?.homepageAd === true ||
    (user as any)?.subscriptionDetails?.standard?.features?.includes("homepage_ad");

  if (!hasHomepageAdAddon) {
    return null;
  }

  const DesktopView = () => (
    <Box overflowX="auto">
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>{t("homepageAd.table.image")}</Th>
            <Th>{t("homepageAd.table.title")}</Th>
            <Th>{t("homepageAd.table.clicks")}</Th>
            <Th>{t("homepageAd.table.status")}</Th>
            <Th>{t("homepageAd.table.created_at")}</Th>
            <Th>{t("homepageAd.table.expires_at")}</Th>
            <Th>{t("homepageAd.table.actions")}</Th>
          </Tr>
        </Thead>
        <Tbody>
          {ads.map((ad) => (
            <Tr key={ad._id}>
              <Td>
                <Image
                  src={import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image}
                  alt={ad.title}
                  boxSize="50px"
                  objectFit="cover"
                  borderRadius="md"
                  cursor="pointer"
                  onClick={() => handleImageClick(ad.image)}
                />
              </Td>
              <Td>{ad.title}</Td>
              <Td>{ad.clicks}</Td>
              <Td>
                <VStack align="start" spacing={1}>
                  <Badge colorScheme={getStatusColor(ad.status)}>
                    {t(`profile:homepageAd.status.${ad.status}`)}
                  </Badge>
                  {ad.rejectionReason && (
                    <Text fontSize="xs" color="red.500">
                      {ad.rejectionReason}
                    </Text>
                  )}
                </VStack>
              </Td>
              <Td>{ad?.createdAt && format(new Date(ad.createdAt), "PP")}</Td>
              <Td>{ad?.expiresAt ? format(new Date(ad.expiresAt), "PP") : t("profile:homepageAd.notAvailable")}</Td>
              <Td>
                <Flex>
                  {ad.status === "expired" && (
                    <IconButton
                      aria-label={t("homepageAd.actions.reactivate")}
                      icon={<RepeatIcon />}
                      size="sm"
                      mr={2}
                      onClick={() => handleReactivate(ad._id)}
                    />
                  )}
                  <IconButton
                    aria-label={t("common:delete")}
                    icon={<DeleteIcon />}
                    size="sm"
                    colorScheme="red"
                    onClick={() => handleDeleteClick(ad._id)}
                  />
                </Flex>
              </Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );

  const MobileView = () => (
    <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
      {ads.map((ad) => (
        <Card key={ad._id} shadow="md" borderRadius="lg" overflow="hidden">
          <CardBody>
            <VStack align="stretch" spacing={3}>
              <Image
                src={import.meta.env.VITE_SOCKET_URL + '/uploads' + ad.image}
                alt={ad.title}
                borderRadius="md"
                objectFit="cover"
                height="200px"
                cursor="pointer"
                onClick={() => handleImageClick(ad.image)}
              />

              <Text fontWeight="bold">{ad.title}</Text>
              <Text fontSize="sm">
                {t("homepageAd.table.clicks")}: {ad.clicks}
              </Text>
              <Badge colorScheme={getStatusColor(ad.status)}>
                {t(`profile:homepageAd.status.${ad.status}`)}
              </Badge>
              {ad.rejectionReason && (
                <Text fontSize="sm" color="red.500">
                  {t("homepageAd.table.rejection_reason")}: {ad.rejectionReason}
                </Text>
              )}
              <Text fontSize="sm">
                {t("homepageAd.table.created_at")}:{" "}
                {format(new Date(ad.createdAt), "PP")}
              </Text>
              <Text fontSize="sm">
                {t("homepageAd.table.expires_at")}:{" "}
                {ad?.expiresAt ? format(new Date(ad.expiresAt), "PP") : t("profile:homepageAd.notAvailable")}
              </Text>
              <Flex justifyContent="space-between" mt={2}>
                {ad.status === "expired" && (
                  <Button
                    size="sm"
                    leftIcon={<RepeatIcon />}
                    onClick={() => handleReactivate(ad._id)}
                  >
                    {t("homepageAd.actions.reactivate")}
                  </Button>
                )}
                <Button
                  size="sm"
                  colorScheme="red"
                  leftIcon={<DeleteIcon />}
                  onClick={() => handleDeleteClick(ad._id)}
                >
                  {t("common:delete")}
                </Button>
              </Flex>
            </VStack>
          </CardBody>
        </Card>
      ))}
    </SimpleGrid>
  );

  return (
    <VStack spacing={8} align="stretch">
      {/* Image View Modal */}
      <Modal isOpen={isImageModalOpen} onClose={closeImageModal} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>{t("homepageAd.modal.image_preview")}</ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <Image
              src={selectedImage}
              alt="Ad preview"
              width="100%"
              borderRadius="md"
            />
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={isDeleteAlertOpen}
        leastDestructiveRef={cancelRef as any}
        onClose={closeDeleteAlert}
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              {t("homepageAd.modal.delete_confirmation")}
            </AlertDialogHeader>

            <AlertDialogBody>
              {t("homepageAd.modal.delete_warning")}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={closeDeleteAlert}>
                {t("common:cancel")}
              </Button>
              <Button
                colorScheme="red"
                onClick={confirmDelete}
                ml={3}
                isLoading={isDeleting}
              >
                {t("common:delete")}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>

      <Card
        as="form"
        onSubmit={handleSubmit(onSubmit)}
        p={6}
        borderRadius="lg"
        shadow="md"
      >
        <CardBody>
          <VStack spacing={6} align="stretch">
            <Heading size="md">{t("homepageAd.form.title_header")}</Heading>
            <Divider />

            <FormControl isInvalid={!!errors.title}>
              <FormLabel>{t("homepageAd.form.title")}</FormLabel>
              <Input
                {...register("title", {
                  required: t("homepageAd.validation.title_required"),
                })}
                placeholder={t("homepageAd.form.title_placeholder")}
              />
              <FormErrorMessage>{errors.title?.message}</FormErrorMessage>
            </FormControl>

            <FormControl>
              <FormLabel>{t("homepageAd.form.product")}</FormLabel>
              <Controller
                name="itemId"
                control={control}
                render={({ field }) => (
                  <Select
                    placeholder={t("homepageAd.form.product_placeholder")}
                    {...field}
                    isDisabled={isLoadingProducts || products.length === 0}
                  >
                    {products.map(product => (
                      <option key={product._id} value={product._id}>
                        {product.name}
                      </option>
                    ))}
                  </Select>
                )}
              />
              <FormHelperText>
                <Flex alignItems="center">
                  <InfoIcon mr={1} color="blue.500" />
                  {t("homepageAd.form.product_help")}
                </Flex>
              </FormHelperText>
            </FormControl>

            <FormControl isInvalid={!!errors.image}>
              <FormLabel>{t("homepageAd.form.image")}</FormLabel>
              <Input
                type="file"
                multiple={false}
                accept="image/*"
                {...register("image", {
                  required: t("homepageAd.validation.image_required"),
                  validate: {
                    fileSize: (file: any) => {
                      return (
                        file[0].size <= 5 * 1024 * 1024 ||
                        t("homepageAd.validation.image_size")
                      );
                    },
                    fileType: (file: any) =>
                      [
                        "image/jpeg",
                        "image/jpg",
                        "image/png",
                        "image/gif",
                      ].includes(file[0]?.type) ||
                      t("homepageAd.validation.image_type"),
                  },
                })}
              />
              <FormHelperText>
                {t("homepageAd.form.image_help", "Maximum size: 5MB. Formats: JPG, PNG, GIF")}
              </FormHelperText>
              <FormErrorMessage>{errors.image?.message}</FormErrorMessage>
            </FormControl>

            <Button
              type="submit"
              colorScheme="blue"
              isLoading={isSubmitting}
              width={{ base: "full", md: "auto" }}
              alignSelf="flex-end"
              mt={4}
            >
              {t("homepageAd.actions.create")}
            </Button>
          </VStack>
        </CardBody>
      </Card>

      <Heading size="md" mt={6}>{t("homepageAd.section.your_ads")}</Heading>

      {isMobile ?
        <MobileView />
        :
        <DesktopView />

      }
    </VStack>
  );
};

export default HomepageAdTab;
