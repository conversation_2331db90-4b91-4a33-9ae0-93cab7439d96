import React from "react";
import {
  Card,
  VStack,
  Image,
  Heading,
  Badge,
  ButtonGroup,
  Button,
  useColorModeValue,
} from "@chakra-ui/react";
import { useNavigate } from "react-router-dom";
import { IItem } from "../../types/item";
import { useTranslation } from "react-i18next";

interface ItemCardProps {
  item: IItem;
  onEdit: (item: IItem) => void;
  onDelete: (item: IItem) => void;
}

export const ItemCard: React.FC<ItemCardProps> = ({
  item,
  onEdit,
  onDelete,
}) => {
  const cardBg = useColorModeValue("white", "gray.700");
  const navigate = useNavigate();
  const { t } = useTranslation("itemListing");

  console.log(item.images);

  return (
    <Card p={4} bg={cardBg} shadow="sm" borderRadius="lg">
      <VStack align="start" spacing={2}>
        {item.images && item.images.length > 0 ? (
          <Image
            src={`${import.meta.env.VITE_SOCKET_URL}/${item.images[0].replace(/^\/+/, '')}`}
            alt={item.name}
            borderRadius="md"
            objectFit="cover"
            width="100%"
            fallbackSrc={`https://placehold.co/300x200/e2e8f0/1a202c?text=No+Image`}
            height="200px"
          />
        ) : (
          <Image
            src={`https://placehold.co/300x200/e2e8f0/1a202c?text=No+Image`}
            alt={item.name}
            borderRadius="md"
            objectFit="cover"
            width="100%"
            height="200px"
          />
        )}
        <Heading size="md" noOfLines={2} height="50px">
          {item.name}
        </Heading>
        <Badge colorScheme={item.type === "product" ? "blue" : "green"}>
          {item.type === "product" ? t("product") : t("service")}
        </Badge>
        <Badge colorScheme={item.listingType === "sale" ? "purple" : "orange"}>
          {item.listingType === "sale" ? t("sale") : t("demand")}
        </Badge>
        <ButtonGroup spacing={2} mt={2}>
          <Button
            size="sm"
            colorScheme="blue"
            onClick={() => navigate(`/items/${item._id}`)}
          >
            {t("seeDetails")}
          </Button>
          <Button size="sm" colorScheme="green" onClick={() => onEdit(item)}>
            {t("buttons.edit")}
          </Button>
          <Button size="sm" colorScheme="red" onClick={() => onDelete(item)}>
            {t("buttons.delete")}
          </Button>
        </ButtonGroup>
      </VStack>
    </Card>
  );
};
