import React, { useState, useEffect } from "react";
import {
  Box,
  Stack,
  Heading,
  Text,
  Card,
  CardHeader,
  CardBody,
  Badge,
  Grid,
  useToast,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Spinner,
  List,
  ListItem,
  ListIcon,
  Textarea,
  Flex,
  Divider,
  VStack,
  HStack,
  Icon,
  SimpleGrid,
} from "@chakra-ui/react";
import {
  cancelSubscription,
  reactivateSubscription,
} from "../../api/packageApi";
import { getPackages, getActiveSubscription } from "../../api";
import { revertCancellationRequest } from "../../api/adminApi";
import { useTranslation } from "react-i18next";
import { CheckCircleIcon } from "@chakra-ui/icons";
import { FiPackage, FiCalendar, FiClock, FiDollarSign, FiFileText, FiCheck, FiX, FiTool } from "react-icons/fi";
import { getUserDesignPackageOrders, type DesignPackageOrder } from "../../api/designPackageApi";
import { useNavigate } from "react-router-dom";

interface Package {
  _id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  type: string;
  price: number;
  currency: string;
  viewRequestLimit: number;
  createRequestLimit: number;
  emailNotification: boolean;
  smsNotification: boolean;
  languageIntroRights: number;
  messagingAllowed: boolean;
  homepageAd: boolean;
  yearEndSectorReport: boolean;
  isActive: boolean;
  features: string[];
  maxMessages: number;
  duration: number;
  order: number;
  isCurrentPackage?: boolean;
  isUpgradeable?: boolean;
  upgradePrice?: number;
}

type SubscriptionStatus =
  | "ACTIVE"
  | "EXPIRED"
  | "UNPAID"
  | "CANCELED"
  | "PENDING"
  | "UPGRADED"
  | "FAILED";

interface CurrentSubscription {
  _id: string;
  packageId: {
    _id: string;
    name: string;
    nameEn: string;
    description: string;
    descriptionEn: string;
    price: number;
    currency: string;
    features: string[];
    type: string;
  };
  endDate: string;
  status: SubscriptionStatus | string;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  remainingDays: number;
}

interface AddonSubscription {
  _id: string;
  packageId: {
    _id: string;
    name: string;
    nameEn: string;
    description: string;
    descriptionEn: string;
    price: number;
    currency: string;
    features: string[];
  };
  endDate: string;
  remainingViewRequests: number;
  remainingCreateRequests: number;
  remainingDays: number;
  status: string;
}

interface PackagesResponse {
  packages: Package[];
  currentSubscription: CurrentSubscription | null;
  addonSubscriptions: AddonSubscription[];
}

const SubscriptionTab: React.FC<{ userData: any }> = () => {
  const { t, i18n } = useTranslation(["userProfile", "cancelRequests"]);
  const toast = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [packagesData, setPackagesData] = useState<PackagesResponse | null>(
    null,
  );
  const [designPackageOrders, setDesignPackageOrders] = useState<DesignPackageOrder[]>([]);
  const [isLoadingDesignOrders, setIsLoadingDesignOrders] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setIsLoadingDesignOrders(true);

        // Fetch packages and subscription data
        const response: any = await getPackages();

        // If currentSubscription is null but we know there should be one, use the active subscription endpoint
        if (!response.currentSubscription) {
          try {
            const activeSubResponse = await getActiveSubscription();
            if (activeSubResponse && activeSubResponse.subscription) {
              // Use the subscription data from the active subscription endpoint
              response.currentSubscription = activeSubResponse.subscription;

              // Extract the package details for display
              if (activeSubResponse.subscription.packageId) {
                const packageId = typeof activeSubResponse.subscription.packageId === 'string'
                  ? activeSubResponse.subscription.packageId
                  : activeSubResponse.subscription.packageId._id;

                // Update package status in the packages array
                response.packages = response.packages.map((pkg: any) => ({
                  ...pkg,
                  isCurrentPackage: pkg._id === packageId,
                  isUpgradeable: pkg.type === 'standard' && pkg.price > (activeSubResponse.subscription.packageId.price || 0)
                }));
              }
            }
          } catch (subError) {
            console.error("Error fetching active subscription:", subError);
          }
        }

        setPackagesData(response);

        // Fetch design package orders
        try {
          const designOrdersResponse = await getUserDesignPackageOrders();
          setDesignPackageOrders(designOrdersResponse.orders || []);
        } catch (designError) {
          console.error("Error fetching design package orders:", designError);
          // Don't show error toast for design packages since it's not critical
        }

      } catch (error) {
        console.error("Error fetching packages:", error);
        toast({
          title: t("subscription.fetchPackagesError.title"),
          description: t("subscription.fetchPackagesError.description"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      } finally {
        setIsLoading(false);
        setIsLoadingDesignOrders(false);
      }
    };

    fetchData();
  }, [t, toast]);

  const handleReactivateSubscription = async () => {
    if (!packagesData?.currentSubscription) return;

    try {
      setIsLoading(true);
      const response = await reactivateSubscription(
        packagesData.currentSubscription._id,
      );

      toast({
        title: t("subscription.reactivateSuccess.title"),
        description: response.message,
        status: "success",
        duration: 5000,
        isClosable: true,
      });

      // Refresh the data
      await refreshData();
    } catch (error: any) {
      toast({
        title: t("subscription.reactivateError.title"),
        description: error.response?.data?.message || error.message,
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleRevertCancellation = async () => {
    if (!packagesData?.currentSubscription) return;

    try {
      setIsLoading(true);
      await revertCancellationRequest(packagesData.currentSubscription._id);

      toast({
        title: t("cancelRequests:revertSuccess.title"),
        description: t("cancelRequests:revertSuccess.description"),
        status: "success",
        duration: 5000,
        isClosable: true,
      });

      // Refresh the data
      await refreshData();
    } catch (error: any) {
      toast({
        title: t("cancelRequests:revertError.title"),
        description: error.response?.data?.message || t("cancelRequests:revertError.description"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" py={8}>
        <Spinner size="xl" />
      </Box>
    );
  }

  const { currentSubscription, addonSubscriptions } = packagesData || {
    currentSubscription: null,
    addonSubscriptions: []
  };

  const getFeatureTranslation = (feature: string) => {
    const featureTranslations: { [key: string]: string } = {
      email_notifications: t("features.emailNotifications"),
      sms_notifications: t("features.smsNotifications"),
      language_intro: t("features.languageIntro"),
      basic_messaging: t("features.basicMessaging"),
      unlimited_messaging: t("features.unlimitedMessaging"),
      homepage_ad: t("features.homepageAd"),
      sector_report: t("features.sectorReport"),
      additional_views: t("features.additionalViews"),
      additional_creates: t("features.additionalCreates"),
    };
    return featureTranslations[feature] || feature;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'yellow';
      case 'PAID': return 'blue';
      case 'IN_PROGRESS': return 'orange';
      case 'COMPLETED': return 'green';
      case 'CANCELLED': return 'red';
      case 'REFUNDED': return 'gray';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return FiClock;
      case 'PAID': return FiDollarSign;
      case 'IN_PROGRESS': return FiTool;
      case 'COMPLETED': return FiCheck;
      case 'CANCELLED': return FiX;
      case 'REFUNDED': return FiX;
      default: return FiFileText;
    }
  };

  const refreshData = async () => {
    try {
      const [packagesResponse, designOrdersResponse] = await Promise.all([
        getPackages(),
        getUserDesignPackageOrders().catch(() => ({ orders: [] }))
      ]);
      setPackagesData(packagesResponse);
      setDesignPackageOrders(designOrdersResponse.orders || []);
    } catch (error) {
      console.error("Error refreshing data:", error);
    }
  };


  return (
    <Stack spacing={{ base: 4, md: 6 }}>
      {currentSubscription ? (
        <>
          {/* Current Subscription */}
          <Card variant="elevated">
            <CardHeader pb={{ base: 2, md: 6 }}>
              <Stack direction={{ base: "column", md: "row" }} justify="space-between" align={{ base: "start", md: "center" }} spacing={{ base: 3, md: 0 }}>
                <Stack spacing={{ base: 1, md: 2 }}>
                  <Heading size={{ base: "md", md: "lg" }}>
                    {i18n.language === "en"
                      ? currentSubscription?.packageId?.nameEn
                      : currentSubscription?.packageId?.name}
                  </Heading>
                  <Text color="gray.600" fontSize={{ base: "sm", md: "md" }}>
                    {i18n.language === "en"
                      ? currentSubscription?.packageId?.descriptionEn
                      : currentSubscription?.packageId?.description}
                  </Text>
                </Stack>
                <Stack align={{ base: "start", md: "end" }} direction={{ base: "row", md: "column" }} spacing={{ base: 3, md: 1 }}>
                  <Badge
                    colorScheme={
                      currentSubscription?.status === "CANCELED"
                        ? "yellow"
                        : "green"
                    }
                    fontSize={{ base: "sm", md: "md" }}
                    px={{ base: 2, md: 3 }}
                    py={1}
                  >
                    {currentSubscription?.status === "CANCELED"
                      ? t("subscription.cancelled")
                      : t("subscription.active")}
                  </Badge>
                  <Text color="gray.600" fontWeight="semibold" fontSize={{ base: "md", md: "lg" }}>
                    ${currentSubscription?.packageId?.price}
                  </Text>
                </Stack>
              </Stack>
            </CardHeader>
            <CardBody pt={{ base: 2, md: 6 }}>
              <Grid
                templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }}
                gap={{ base: 4, md: 6 }}
              >
                <Stack spacing={{ base: 2, md: 3 }}>
                  <Text fontWeight="bold" fontSize={{ base: "sm", md: "md" }}>
                    {t("subscription.remainingDays")}
                  </Text>
                  <Text fontSize={{ base: "sm", md: "md" }}>
                    {currentSubscription?.remainingDays} {t("subscription.days")}
                  </Text>
                  <Text fontWeight="bold" fontSize={{ base: "sm", md: "md" }}>{t("subscription.endDate")}</Text>
                  <Text fontSize={{ base: "sm", md: "md" }}>
                    {currentSubscription?.endDate ? new Date(currentSubscription.endDate).toLocaleDateString() : ''}
                  </Text>
                </Stack>
                <Stack spacing={{ base: 2, md: 3 }}>
                  <Text fontWeight="bold" fontSize={{ base: "sm", md: "md" }}>
                    {t("subscription.remainingRequests")}
                  </Text>
                  <Text fontSize={{ base: "sm", md: "md" }}>
                    {t("subscription.viewRequests")}:{" "}
                    {currentSubscription?.remainingViewRequests}
                  </Text>
                  <Text fontSize={{ base: "sm", md: "md" }}>
                    {t("subscription.createRequests")}:{" "}
                    {currentSubscription?.remainingCreateRequests}
                  </Text>
                </Stack>
              </Grid>
              <Box mt={4}>
                <Text fontWeight="bold" mb={2}>
                  {t("subscription.features")}
                </Text>
                <List spacing={2}>
                  {currentSubscription?.packageId?.features?.map((feature) => (
                    <ListItem key={feature}>
                      <ListIcon as={CheckCircleIcon} color="green.500" />

                      {getFeatureTranslation(feature)}
                    </ListItem>
                  ))}
                </List>
              </Box>
            </CardBody>
          </Card>

          {/* Addon Subscriptions */}
          {addonSubscriptions?.length > 0 && (
            <>
              <Heading size="md" mt={6} mb={4}>
                {t("subscription.activeAddons")}
              </Heading>
              <Stack spacing={4}>
                {addonSubscriptions.map((addon) => (
                  <Card key={addon._id} variant="elevated">
                    <CardHeader>
                      <Stack
                        direction="row"
                        justify="space-between"
                        align="center"
                      >
                        <Stack>
                          <Heading size="md">
                            {i18n.language === "en"
                              ? addon.packageId?.nameEn
                              : addon.packageId?.name}
                          </Heading>
                          <Text color="gray.600">
                            {i18n.language === "en"
                              ? addon.packageId?.descriptionEn
                              : addon.packageId?.description}
                          </Text>
                        </Stack>
                        <Stack align="end">
                          <Badge
                            colorScheme={
                              addon.status === "CANCELED" ? "yellow" : "purple"
                            }
                            fontSize="md"
                            px={3}
                            py={1}
                          >
                            {addon.status === "CANCELED"
                              ? t("subscription.cancelled")
                              : t("subscription.addon")}
                          </Badge>
                          <Text color="gray.600">${addon.packageId?.price}</Text>
                        </Stack>
                      </Stack>
                    </CardHeader>
                    <CardBody>
                      <Grid
                        templateColumns={{ base: "1fr", md: "repeat(2, 1fr)" }}
                        gap={6}
                      >
                        <Stack>
                          <Text fontWeight="bold">
                            {t("subscription.remainingDays")}
                          </Text>
                          <Text>
                            {addon?.remainingDays} {t("subscription.days")}
                          </Text>
                          <Text fontWeight="bold">
                            {t("subscription.endDate")}
                          </Text>
                          <Text>
                            {new Date(addon.endDate).toLocaleDateString()}
                          </Text>
                        </Stack>
                        <Stack>
                          <Text fontWeight="bold">
                            {t("subscription.remainingRequests")}
                          </Text>
                          <Text>
                            {t("subscription.viewRequests")}:{" "}
                            {addon?.remainingViewRequests}
                          </Text>
                          <Text>
                            {t("subscription.createRequests")}:{" "}
                            {addon?.remainingCreateRequests}
                          </Text>
                        </Stack>
                      </Grid>
                      <Box mt={4}>
                        <Text fontWeight="bold" mb={2}>
                          {t("subscription.features")}
                        </Text>
                        <List spacing={2}>
                          {addon?.packageId?.features.map((feature) => (
                            <ListItem key={feature}>
                              <ListIcon
                                as={CheckCircleIcon}
                                color="green.500"
                              />

                              {getFeatureTranslation(feature)}
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                    </CardBody>
                  </Card>
                ))}
              </Stack>
            </>
          )}

          {/* Action Buttons */}
          <Stack direction="row" spacing={4} mt={6}>

            {/* Reactivate Button - Show for canceled subscriptions that haven't expired */}
            {currentSubscription?.status === "CANCELED" &&
              currentSubscription?.endDate &&
              new Date(currentSubscription.endDate) > new Date() && (
                <Button
                  colorScheme="green"
                  onClick={handleReactivateSubscription}
                  isLoading={isLoading}
                >
                  {t("subscription.reactivateButton")}
                </Button>
              )}

            {/* Revert Cancellation Button - Show for pending cancellation subscriptions */}
            {currentSubscription?.status === "PENDING_CANCELLATION" && (
              <Button
                colorScheme="blue"
                size="sm"
                onClick={handleRevertCancellation}
                isLoading={isLoading}
              >
                {t("cancelRequests:revertRequest")}
              </Button>
            )}
          </Stack>

        </>
      ) : (
        <Box p={6} bg="white" textAlign="center" my={8} mx="auto" h="100%">
          <Flex justifyContent="center" mb={6}>
            <FiPackage size="60px" color="#3182CE" />
          </Flex>
          <Heading size="lg" mb={2}>
            {t("subscription.noSubscription")}
          </Heading>
          <Text fontSize="md" color="gray.600" mb={4}>
            {t("subscription.noSubscriptionMessage")}
          </Text>
          <Button
            colorScheme="blue"
            onClick={() => (window.location.href = "/packages")}
          >
            {t("subscription.browsePackages")}
          </Button>
        </Box>
      )}

      {/* Design Package Orders Section */}
      <Divider my={6} />
      <VStack spacing={6} align="stretch">
        <HStack justifyContent="space-between" alignItems="center">
          <Heading size="lg" display="flex" alignItems="center" gap={3}>
            <Icon as={FiTool} color="purple.500" />
            {t("subscription.designPackageOrders")}
          </Heading>
          <Button
            size="sm"
            colorScheme="purple"
            onClick={() => window.location.href = '/design-packages'}
          >
            {t("subscription.browsePackages")}
          </Button>
        </HStack>

        {isLoadingDesignOrders ? (
          <Box display="flex" justifyContent="center" py={8}>
            <Spinner size="lg" />
          </Box>
        ) : designPackageOrders.length > 0 ? (
          <SimpleGrid columns={{ base: 1, lg: 2 }} spacing={6}>
            {designPackageOrders.map((order) => (
              <Card key={order._id} variant="elevated" shadow="md">
                <CardHeader pb={2}>
                  <HStack justifyContent="space-between" alignItems="flex-start">
                    <VStack align="start" spacing={1}>
                      <Heading size="md">
                        {i18n.language === "en"
                          ? order.packageId?.nameEn || order.packageId?.name
                          : order.packageId?.name}
                      </Heading>
                      <Text fontSize="sm" color="gray.600">
                        {t("subscription.order")} #{order.orderNumber}
                      </Text>
                    </VStack>
                    <VStack align="end" spacing={1}>
                      <Badge
                        colorScheme={getStatusColor(order.status)}
                        fontSize="sm"
                        px={3}
                        py={1}
                        display="flex"
                        alignItems="center"
                        gap={1}
                      >
                        <Icon as={getStatusIcon(order.status)} w={3} h={3} />
                        {order.status}
                      </Badge>
                      <Text fontSize="sm" fontWeight="bold" color="gray.700">
                        ${order.price} {order.currency}
                      </Text>
                    </VStack>
                  </HStack>
                </CardHeader>

                <CardBody pt={2}>
                  <VStack spacing={4} align="stretch">
                    {/* Order Details */}
                    <SimpleGrid columns={2} spacing={4}>
                      <VStack align="start" spacing={2}>
                        <HStack>
                          <Icon as={FiCalendar} color="gray.500" w={4} h={4} />
                          <Text fontSize="sm" fontWeight="medium">
                            {t("subscription.designPackageOrders")}
                          </Text>
                        </HStack>
                        <Text fontSize="sm" color="gray.600">
                          {new Date(order.createdAt).toLocaleDateString()}
                        </Text>
                      </VStack>

                      {order.deliveryDate && (
                        <VStack align="start" spacing={2}>
                          <HStack>
                            <Icon as={FiClock} color="gray.500" w={4} h={4} />
                            <Text fontSize="sm" fontWeight="medium">
                              {t("subscription.deliveryDate")}
                            </Text>
                          </HStack>
                          <Text fontSize="sm" color="gray.600">
                            {new Date(order.deliveryDate).toLocaleDateString()}
                          </Text>
                        </VStack>
                      )}
                    </SimpleGrid>

                    {/* Description */}
                    {order.packageId && (
                      <Box>
                        <Text fontSize="sm" color="gray.600" lineHeight={1.5}>
                          {i18n.language === "en"
                            ? order.packageId.descriptionEn || order.packageId.description
                            : order.packageId.description}
                        </Text>
                      </Box>
                    )}

                    {/* Features */}
                    {order.packageId?.features && order.packageId.features.length > 0 && (
                      <Box>
                        <Text fontSize="sm" fontWeight="medium" mb={2}>
                          {t("subscription.features")}:
                        </Text>
                        <List spacing={1}>
                          {(i18n.language === "en"
                            ? order.packageId.featuresEn || order.packageId.features
                            : order.packageId.features
                          ).slice(0, 3).map((feature, index) => (
                            <ListItem key={index} fontSize="sm">
                              <ListIcon as={CheckCircleIcon} color="green.400" w={3} h={3} />
                              {feature}
                            </ListItem>
                          ))}
                        </List>
                      </Box>
                    )}

                    {/* Notes */}
                    {order.notes && (
                      <Box>
                        <Text fontSize="sm" fontWeight="medium" mb={1}>
                          {t("subscription.notes")}:
                        </Text>
                        <Text fontSize="sm" color="gray.600" fontStyle="italic">
                          {order.notes}
                        </Text>
                      </Box>
                    )}

                    {/* Files (if completed) */}
                    {order.status === 'COMPLETED' && order.files && order.files.length > 0 && (
                      <Box>
                        <Text fontSize="sm" fontWeight="medium" mb={2}>
                          {t("subscription.deliveredFiles")}
                        </Text>
                        <VStack spacing={2} align="stretch">
                          {order.files.map((file, index) => (
                            <HStack key={index} justify="space-between">
                              <HStack>
                                <Icon as={FiFileText} color="blue.500" w={4} h={4} />
                                <Text fontSize="sm">{file.name}</Text>
                              </HStack>
                              <Button
                                size="xs"
                                colorScheme="blue"
                                variant="outline"
                                onClick={() => window.open(file.url, '_blank')}
                              >
                                {t("subscription.download")}
                              </Button>
                            </HStack>
                          ))}
                        </VStack>
                      </Box>
                    )}
                  </VStack>
                </CardBody>
              </Card>
            ))}
          </SimpleGrid>
        ) : (
          <Card variant="outline">
            <CardBody>
              <VStack spacing={4} py={8} textAlign="center">
                <Icon as={FiTool} w={12} h={12} color="gray.400" />
                <VStack spacing={2}>
                  <Heading size="md" color="gray.600">
                    {t("subscription.noDesignPackageOrders")}
                  </Heading>
                  <Text color="gray.500" maxW="md">
                    {t("subscription.noDesignPackageOrdersDescription")}
                  </Text>
                </VStack>
                <Button
                  colorScheme="purple"
                  onClick={() => window.location.href = '/design-packages'}
                  leftIcon={<Icon as={FiTool} />}
                >
                  {t("subscription.browsePackages")}
                </Button>
              </VStack>
            </CardBody>
          </Card>
        )}
      </VStack>
    </Stack>
  );
};

export default SubscriptionTab;
