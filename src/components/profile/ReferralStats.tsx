import React from "react";
import {
  Box,
  Heading,
  Text,
  VStack,
  HStack,
  Stat,
  StatLabel,
  StatNumber,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  useColorModeValue,
  useClipboard,
  Button,
  Input,
  InputGroup,
  InputRightElement,
  Tooltip,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import { IUser } from "@/types/user";
import { FaCopy, FaCheck } from "react-icons/fa";

interface ReferralStatsProps {
  user: IUser | null;
}

const ReferralStats: React.FC<ReferralStatsProps> = ({ user }) => {
  const { t } = useTranslation(["profile"]);
  const boxBg = useColorModeValue("white", "gray.800");
  const borderColor = useColorModeValue("gray.200", "gray.700");

  // Extract values safely - BEFORE any conditional returns
  const referralCode = user?.referralCode || "";
  const referrals = user?.referrals || [];
  const referralUrl = `${window.location.origin}/register?ref=${referralCode}`;

  // Always call hooks at the top level
  const { hasCopied, onCopy } = useClipboard(referralUrl);

  // Return early if user is null - AFTER all hooks are called
  if (!user) {
    return null;
  }

  const activePackages = referrals.filter((ref) => ref.hasPackage).length;

  return (
    <Box
      mt={6}
      p={5}
      borderWidth="1px"
      borderRadius="lg"
      backgroundColor={boxBg}
      borderColor={borderColor}
      width="100%"
    >
      <Heading as="h3" size="md" mb={4}>
        {t("profile:referrals.title")}
      </Heading>

      <VStack spacing={6} align="stretch">
        <Box>
          <Text fontWeight="bold" mb={2}>
            {t("profile:referrals.yourCode")}
          </Text>
          <InputGroup size="md">
            <Input pr="4.5rem" value={referralCode} isReadOnly />
          </InputGroup>
        </Box>

        <Box>
          <Text fontWeight="bold" mb={2}>
            {t("profile:referrals.shareUrl")}
          </Text>
          <InputGroup size="md">
            <Input pr="4.5rem" value={referralUrl} isReadOnly />

            <InputRightElement width="4.5rem">
              <Tooltip label={hasCopied ? "Copied!" : "Copy"}>
                <Button h="1.75rem" size="sm" onClick={onCopy}>
                  {hasCopied ? <FaCheck /> : <FaCopy />}
                </Button>
              </Tooltip>
            </InputRightElement>
          </InputGroup>
        </Box>

        <HStack spacing={8}>
          <Stat>
            <StatLabel>{t("profile:referrals.totalReferrals")}</StatLabel>
            <StatNumber>{referrals.length}</StatNumber>
          </Stat>
          <Stat>
            <StatLabel>{t("profile:referrals.activePackages")}</StatLabel>
            <StatNumber>{activePackages}</StatNumber>
          </Stat>
        </HStack>

        <Box>
          <Heading as="h4" size="sm" mb={4}>
            {t("profile:referrals.referralsList")}
          </Heading>

          {referrals.length > 0 ? (
            <Box overflowX="auto">
              <Table variant="simple" size="sm">
                <Thead>
                  <Tr>
                    <Th>{t("profile:referrals.name")}</Th>
                    <Th>{t("profile:referrals.dateJoined")}</Th>
                    <Th>{t("profile:referrals.status")}</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {referrals.map((referral) => (
                    <Tr key={referral._id}>
                      <Td>{`${referral.firstName} ${referral.lastName}`}</Td>
                      <Td>
                        {format(new Date(referral.createdAt), "dd/MM/yyyy")}
                      </Td>
                      <Td>
                        <Badge
                          colorScheme={referral.hasPackage ? "green" : "gray"}
                        >
                          {referral.hasPackage
                            ? t("profile:referrals.activePackage")
                            : t("profile:referrals.noPackage")}
                        </Badge>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            </Box>
          ) : (
            <Box textAlign="center" py={4}>
              <Text color="gray.500">
                {t("profile:referrals.noReferralsYet")}
              </Text>
            </Box>
          )}
        </Box>
      </VStack>
    </Box>
  );
};

export default ReferralStats;
