import React, { useState, useEffect } from "react";
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  useToast,
  Heading,
  Select,
  Textarea,
} from "@chakra-ui/react";
import {
  getUserProfile,
  updateUserProfile,
  getCities,
  getCategories,
  getCountries,
  getCategoriesByParentId,
} from "../../api";
import { ICategory } from "@/types/category";
import { useTranslation } from "react-i18next";
import { ICity, ICountry } from "@/types/user";

interface ProfileTabProps {
  onProfileUpdate?: () => void;
}

// Update profile payload type
interface IUpdateProfilePayload {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  phoneNumber: string;
  address: string;
  city?: string;
  country?: string;
  zipCode: string;
  birthDate: string;
  categoryLevel1Id?: string;
  categoryLevel2Id?: string;
  categoryLevel3Id?: string;
}

const ProfileTab: React.FC<ProfileTabProps> = ({ onProfileUpdate }) => {
  const { t } = useTranslation(["profile", "userProfile"]);
  const toast = useToast();

  const [firstName, setFirstName] = useState<string>("");
  const [lastName, setLastName] = useState<string>("");
  const [email, setEmail] = useState<string>("");
  const [password, setPassword] = useState<string>("");
  const [phoneNumber, setPhoneNumber] = useState<string>("");
  const [address, setAddress] = useState<string>("");
  const [city, setCity] = useState<ICity | null>(null);
  const [country, setCountry] = useState<ICountry | null>(null);
  const [birthDate, setBirthDate] = useState<any>("");
  const [zipCode, setZipCode] = useState<string>("");
  const [cities, setCities] = useState<ICity[]>([]);
  const [countries, setCountries] = useState<ICountry[]>([]);
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [category, setCategory] = useState<ICategory | null>(null);
  const [level2Categories, setLevel2Categories] = useState<ICategory[]>([]);
  const [level2Category, setLevel2Category] = useState<ICategory | null>(null);
  const [level3Categories, setLevel3Categories] = useState<ICategory[]>([]);
  const [level3Category, setLevel3Category] = useState<ICategory | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [profile, categoriesData, countriesData] = await Promise.all([
          getUserProfile(),
          getCategories(),
          getCountries(),
        ]);

        // Set user data
        setFirstName(profile.firstName || "");
        setLastName(profile.lastName || "");
        setEmail(profile.email || "");
        setPhoneNumber(profile.phoneNumber || "");
        setAddress(profile.address || "");
        setZipCode(profile.zipCode || "");
        setBirthDate(
          profile.birthDate
            ? new Date(profile.birthDate).toISOString().split("T")[0]
            : "",
        );

        // Set categories
        setCategories(categoriesData);

        // Set countries
        setCountries(countriesData);

        // Set user's categories if they exist
        if (profile.categoryLevel1Id) {
          const level1 = categoriesData.find(
            (cat) => cat._id === profile.categoryLevel1Id,
          );
          if (level1) {
            setCategory(level1);

            try {
              // Fetch level 2 categories
              const level2Cats = await getCategoriesByParentId(level1.id);
              setLevel2Categories(level2Cats);

              if (profile.categoryLevel2Id) {
                const level2 = level2Cats.find(
                  (cat:any) => cat._id === profile.categoryLevel2Id,
                );
                if (level2) {
                  setLevel2Category(level2);

                  try {
                    // Fetch level 3 categories
                    const level3Cats = await getCategoriesByParentId(level2.id);
                    setLevel3Categories(level3Cats);

                    if (profile.categoryLevel3Id) {
                      const level3 = level3Cats.find(
                        (cat:any) => cat._id === profile.categoryLevel3Id,
                      );
                      if (level3) {
                        setLevel3Category(level3);
                      }
                    }
                  } catch (error) {
                    console.error("Failed to fetch level 3 categories:", error);
                    setLevel3Categories([]);
                  }
                }
              }
            } catch (error) {
              console.error("Failed to fetch level 2 categories:", error);
              setLevel2Categories([]);
            }
          }
        }

        // Set user's country and city if they exist
        if (profile.country) {
          const userCountry = countriesData.find(
            (c: ICountry) => c.name === profile.country,
          );
          if (userCountry) {
            setCountry(userCountry);

            // After setting country, fetch cities
            try {
              const citiesData = await getCities(userCountry.code);
              setCities(citiesData);

              // Then set user's city if it exists
              if (profile.city) {
                const userCity = citiesData.find(
                  (c: ICity) => c.name === profile.city,
                );
                setCity(userCity || null);
              }
            } catch (error) {
              console.error("Failed to fetch cities:", error);
            }
          }
        }
      } catch (error: any) {
        toast({
          title: t("profile:errors.failedToLoadProfile"),
          description: t("profile:errors.failedToLoadProfileDesc"),
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
    };

    fetchData();
  }, [toast, t]);

  useEffect(() => {
    if (country) {
      const fetchCities = async () => {
        try {
          const data = await getCities(country.code);
          setCities(data);
        } catch (error: any) {
          console.error("Failed to fetch cities:", error);
        }
      };
      fetchCities();
    }
  }, [country]);

  const handleUpdate = async () => {
    try {
      const updateData: IUpdateProfilePayload = {
        firstName,
        lastName,
        email,
        ...(password ? { password } : {}),
        phoneNumber,
        address,
        city: city?.name,
        country: country?.name,
        zipCode,
        birthDate: birthDate,
        ...(category?._id ? { categoryLevel1Id: category._id.toString() } : {}),
        ...(level2Category?._id
          ? { categoryLevel2Id: level2Category._id.toString() }
          : {}),
        ...(level3Category?._id
          ? { categoryLevel3Id: level3Category._id.toString() }
          : {}),
      };

      await updateUserProfile(updateData);
      toast({
        title: t("profile:success.profileUpdated"),
        description: t("profile:success.profileUpdatedDesc"),
        status: "success",
        duration: 5000,
        isClosable: true,
      });
      if (onProfileUpdate) {
        onProfileUpdate();
      }
    } catch (error: any) {
      toast({
        title: t("profile:errors.failedToUpdateProfile"),
        description: t("profile:errors.failedToUpdateProfileDesc"),
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  return (
    <VStack spacing={8} align="stretch">
      <Box>
        <Heading size="md" mb={4}>
          {t("profile:profile.personalInfo")}
        </Heading>
        <VStack spacing={4} align="start">
          <FormControl id="firstName">
            <FormLabel>{t("profile:profile.firstName")}</FormLabel>
            <Input
              type="text"
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
            />
          </FormControl>

          <FormControl id="lastName">
            <FormLabel>{t("profile:profile.lastName")}</FormLabel>
            <Input
              type="text"
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
            />
          </FormControl>

          <FormControl id="email">
            <FormLabel>{t("profile:profile.email")}</FormLabel>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </FormControl>

          <FormControl id="birthDate">
            <FormLabel>{t("profile:profile.birthDate")}</FormLabel>
            <Input
              type="date"
              value={birthDate}
              onChange={(e) => setBirthDate(e.target.value)}
            />
          </FormControl>

          <FormControl id="phoneNumber">
            <FormLabel>{t("profile:profile.phoneNumber")}</FormLabel>
            <Input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
            />
          </FormControl>

          <FormControl id="country">
            <FormLabel>{t("profile:profile.country")}</FormLabel>
            <Select
              value={country?.name || ""}
              onChange={(e) => {
                const selectedCountry = countries.find(
                  (c: ICountry) => c.name === e.target.value,
                );
                setCountry(selectedCountry || null);
              }}
            >
              <option value="">{t("profile:profile.selectCountry")}</option>
              {countries.map((c: ICountry) => (
                <option key={c.code} value={c.name}>
                  {c.name}
                </option>
              ))}
            </Select>
          </FormControl>

          <FormControl id="city">
            <FormLabel>{t("profile:profile.city")}</FormLabel>
            <Select
              value={city?.name || ""}
              onChange={(e) => {
                const selectedCity = cities.find(
                  (c: ICity) => c.name === e.target.value,
                );
                setCity(selectedCity || null);
              }}
              isDisabled={!country}
            >
              <option value="">{t("profile:profile.selectCity")}</option>
              {cities.map((c: ICity) => (
                <option key={c.city_id} value={c.name}>
                  {c.name}
                </option>
              ))}
            </Select>
          </FormControl>

          <FormControl id="address">
            <FormLabel>{t("profile:profile.address")}</FormLabel>
            <Textarea
              value={address}
              onChange={(e) => setAddress(e.target.value)}
            />
          </FormControl>

          <FormControl id="zipCode">
            <FormLabel>{t("profile:profile.zipCode")}</FormLabel>
            <Input
              type="text"
              value={zipCode}
              onChange={(e) => setZipCode(e.target.value)}
            />
          </FormControl>

          <FormControl id="password">
            <FormLabel>{t("profile:profile.password")}</FormLabel>
            <Input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder={t("profile:profile.passwordPlaceholder")}
            />
          </FormControl>

          <FormControl id="categoryLevel1">
            <FormLabel>{t("profile:profile.category")}</FormLabel>
            <Select
              value={category ? category._id : ""}
              onChange={async (e) => {
                const selected = categories.find(
                  (cat) => cat._id === e.target.value,
                );
                setCategory(selected || null);
                setLevel2Category(null);
                setLevel3Category(null);
                if (selected) {
                  try {
                    const level2Cats = await getCategoriesByParentId(selected.id);
                    setLevel2Categories(level2Cats);
                  } catch (error) {
                    console.error("Failed to fetch level 2 categories:", error);
                    setLevel2Categories([]);
                    toast({
                      title: t("profile:errors.fetchSubCategoriesFailed"),
                      status: "error",
                      duration: 3000,
                      isClosable: true,
                    });
                  }
                } else {
                  setLevel2Categories([]);
                }
                setLevel3Categories([]);
              }}
              placeholder={t("profile:profile.selectCategory")}
            >
              <option value="">{t("profile:profile.selectCategory")}</option>
              {categories
                ?.filter((cat) => !cat.parent_id)
                .map((cat) => (
                  <option key={cat._id} value={cat._id}>
                    {cat.name}
                  </option>
                ))}
            </Select>
          </FormControl>

          <FormControl id="categoryLevel2">
            <FormLabel>{t("profile:profile.subCategory")}</FormLabel>
            <Select
              value={level2Category ? level2Category._id : ""}
              onChange={async (e) => {
                const selected = level2Categories.find(
                  (cat) => cat._id === e.target.value,
                );
                setLevel2Category(selected || null);
                setLevel3Category(null);
                if (selected) {
                  try {
                    const level3Cats = await getCategoriesByParentId(selected.id);
                    setLevel3Categories(level3Cats);
                  } catch (error) {
                    console.error("Failed to fetch level 3 categories:", error);
                    setLevel3Categories([]);
                    toast({
                      title: t("profile:errors.fetchSubCategoriesFailed"),
                      status: "error",
                      duration: 3000,
                      isClosable: true,
                    });
                  }
                } else {
                  setLevel3Categories([]);
                }
              }}
              placeholder={t("profile:profile.selectSubCategory")}
              isDisabled={!category}
            >
              <option value="">{t("profile:profile.selectSubCategory")}</option>
              {level2Categories.map((cat) => (
                <option key={cat._id} value={cat._id}>
                  {cat.name}
                </option>
              ))}
            </Select>
          </FormControl>

          <FormControl id="categoryLevel3">
            <FormLabel>{t("profile:profile.level3Category")}</FormLabel>
            <Select
              value={level3Category ? level3Category._id : ""}
              onChange={(e) => {
                const selected = level3Categories.find(
                  (cat) => cat._id === e.target.value,
                );
                setLevel3Category(selected || null);
              }}
              placeholder={t("profile:profile.selectLevel3Category")}
              isDisabled={!level2Category}
            >
              <option value="">
                {t("profile:profile.selectLevel3Category")}
              </option>
              {level3Categories.map((cat) => (
                <option key={cat._id} value={cat._id}>
                  {cat.name}
                </option>
              ))}
            </Select>
          </FormControl>

          <Button colorScheme="teal" onClick={handleUpdate}>
            {t("profile:profile.updateProfile")}
          </Button>
        </VStack>
      </Box>
    </VStack>
  );
};

export default ProfileTab;
