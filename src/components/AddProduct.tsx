{/* Previous imports remain unchanged */}
import ImageUpload from './ImageUpload';

const AddProduct = () => {
  // Previous state remains unchanged
  const [images, setImages] = useState<string[]>([]);

  const handleImageUpload = (file: File) => {
    if (images.length >= 4) {
      alert('En fazla 4 fotoğraf yükleyebilirsiniz.');
      return;
    }

    const reader = new FileReader();
    reader.onloadend = () => {
      setImages(prev => [...prev, reader.result as string]);
    };
    reader.readAsDataURL(file);
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <Layout>
      {/* Previous content remains unchanged */}

      {/* Image Upload Section */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Fotoğraflar</h2>
        <p className="text-sm text-gray-600 mb-4">En fazla 4 fotoğraf yükleyebilirsiniz.</p>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="aspect-square relative">
              {images[index] ? (
                <div className="relative h-full">
                  <img
                    src={images[index]}
                    alt={`Product ${index + 1}`}
                    className="w-full h-full object-cover rounded-lg"
                  />
                  <button
                    onClick={() => removeImage(index)}
                    className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <ImageUpload
                  onChange={handleImageUpload}
                  className="h-full"
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Rest of the component remains unchanged */}
    </Layout>
  );
};

export default AddProduct;