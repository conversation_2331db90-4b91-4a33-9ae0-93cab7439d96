import React, { useState, useEffect } from 'react';
import { useToast } from '@chakra-ui/react';
import { useTranslation } from 'react-i18next';
import { ShoppingCart, X, Check, CreditCard } from 'lucide-react';
import { api } from '../api';
import { getStoredCards, addStoredCard } from '../api/cardApi';
import { useNavigate } from 'react-router-dom';

interface DesignPackage {
  _id: string;
  name: string;
  nameEn: string;
  description: string;
  descriptionEn: string;
  price: number;
  currency: string;
  features: string[];
  featuresEn: string[];
  deliveryTime: number;
  revisionCount: number;
}

interface DesignPackagePaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  package: DesignPackage | null;
}

interface SavedCard {
  _id: string;
  cardAlias: string;
  cardType: string;
  lastFourDigits: string;
}

const DesignPackagePaymentModal: React.FC<DesignPackagePaymentModalProps> = ({
  isOpen,
  onClose,
  package: selectedPackage
}) => {
  const { t, i18n } = useTranslation('packages');
  const toast = useToast();
  const navigate = useNavigate();

  const [paymentOption, setPaymentOption] = useState<'new' | 'saved'>('new');
  const [savedCards, setSavedCards] = useState<SavedCard[]>([]);
  const [selectedCardId, setSelectedCardId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingCards, setIsLoadingCards] = useState(false);
  const [saveCard, setSaveCard] = useState(false);
  const [notes, setNotes] = useState('');

  // Form fields
  const [cardInfo, setCardInfo] = useState({
    cardHolderName: '',
    cardNumber: '',
    expireMonth: '',
    expireYear: '',
    cvc: '',
    cardAlias: ''
  });

  const [errors, setErrors] = useState<any>({});

  // Generate year options
  const currentYear = new Date().getFullYear();
  const yearOptions = Array.from({ length: 20 }, (_, i) => currentYear + i);

  useEffect(() => {
    if (isOpen) {
      fetchSavedCards();
    }
  }, [isOpen]);

  const fetchSavedCards = async () => {
    setIsLoadingCards(true);
    try {
      const response = await getStoredCards();
      setSavedCards(response);
      if (response.length === 0) {
        setPaymentOption('new');
      }
    } catch (error) {
      console.error('Error fetching saved cards:', error);
    } finally {
      setIsLoadingCards(false);
    }
  };

  const validateForm = () => {
    const newErrors: any = {};

    if (paymentOption === 'new') {
      if (!cardInfo.cardHolderName) {
        newErrors.cardHolderName = t('payment.form.required');
      }
      if (!cardInfo.cardNumber || cardInfo.cardNumber.replace(/\s/g, '').length !== 16) {
        newErrors.cardNumber = t('payment.form.invalidCardNumber');
      }
      if (!cardInfo.expireMonth) {
        newErrors.expireMonth = t('payment.form.required');
      }
      if (!cardInfo.expireYear) {
        newErrors.expireYear = t('payment.form.required');
      }
      if (!cardInfo.cvc || cardInfo.cvc.length !== 3) {
        newErrors.cvc = t('payment.form.invalidCvc');
      }
      if (saveCard && !cardInfo.cardAlias) {
        newErrors.cardAlias = t('payment.form.required');
      }
    } else {
      if (!selectedCardId) {
        newErrors.selectedCard = t('payment.errors.selectSavedCard.description');
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || '';
    const parts = [];

    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }

    if (parts.length) {
      return parts.join(' ');
    } else {
      return value;
    }
  };

  const handleSubmit = async () => {
    if (!validateForm() || !selectedPackage) return;

    setIsLoading(true);
    try {
      const payload: any = {
        notes
      };

      if (paymentOption === 'new') {
        payload.cardInfo = {
          ...cardInfo,
          cardNumber: cardInfo.cardNumber.replace(/\s/g, '')
        };
        if (saveCard) {
          payload.saveCard = true;
        }
      } else {
        payload.storedCardId = selectedCardId;
      }

      const response = await api.post(`/design-packages/${selectedPackage._id}/order`, payload);

      if (response.data.paymentPageUrl) {
        // 3D Secure redirect
        window.location.href = response.data.paymentPageUrl;
      } else {
        // Payment successful
        toast({
          title: t('payment.messages.success.title'),
          description: t('payment.messages.success.description'),
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        onClose();
        // Redirect to orders or dashboard
        navigate('/profile?tab=orders');
      }
    } catch (error: any) {
      toast({
        title: t('payment.messages.error.title'),
        description: error.response?.data?.message || t('payment.messages.error.description'),
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!selectedPackage) return null;

  const packageName = i18n.language === 'en' ? selectedPackage.nameEn : selectedPackage.name;
  const features = i18n.language === 'en' ? selectedPackage.featuresEn : selectedPackage.features;

  return (
    <div
      className="fixed inset-0 z-50 overflow-hidden bg-black bg-opacity-50"
      onClick={onClose}
      style={{ overflow: 'hidden' }}
    >
      <div className="flex items-center justify-center min-h-screen pt-4 px-3 md:px-4 pb-20 text-center md:block md:p-0">
        <div className="fixed inset-0 transition-opacity" aria-hidden="true">
          <div className="absolute inset-0 bg-gray-900 opacity-75"></div>
        </div>

        <span className="hidden md:inline-block md:align-middle md:h-screen" aria-hidden="true">&#8203;</span>

        <div
          className="inline-block align-bottom bg-white rounded-xl text-left overflow-y-auto shadow-2xl transform transition-all md:my-8 md:align-middle md:max-w-xl md:w-full max-h-[90vh] w-full max-w-sm"
          onClick={(e) => e.stopPropagation()}
        >
          <div className="absolute top-0 right-0 pt-4 pr-4">
            <button
              type="button"
              className="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary"
              onClick={onClose}
            >
              <span className="sr-only">{t("payment.modal.close")}</span>
              <X className="h-6 w-6" />
            </button>
          </div>
          
          <div className="bg-white px-3 md:px-4 pt-5 pb-4 md:p-6">
            <div className="md:flex md:items-start">
              <div className="mt-3 text-center md:mt-0 md:text-left w-full">
                <div className="flex items-center space-x-2 mb-4">
                  <div className="w-10 h-10 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                    <ShoppingCart className="w-5 h-5" />
                  </div>
                  <h3 className="text-lg md:text-xl font-bold text-gray-900">
                    {t("payment.modal.title")}
                  </h3>
                </div>
                
                {/* Package Details */}
                <div className="mb-4 md:mb-6 p-3 md:p-4 rounded-lg bg-primary/5 border border-primary/10">
                  <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-3 space-y-1 md:space-y-0">
                    <span className="font-medium text-primary text-sm md:text-base">
                      {packageName}
                    </span>
                    <span className="text-lg md:text-xl font-bold text-gray-900">
                      ${selectedPackage.price}
                    </span>
                  </div>
                  <div className="space-y-2">
                    {features.slice(0, 3).map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                        <span className="text-sm text-gray-600">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Notes */}
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('designPackages.notes')}
                  </label>
                  <input
                    type="text"
                    placeholder={t('designPackages.notesPlaceholder')}
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                  />
                </div>

                <div className="border-t my-6"></div>

                {/* Payment Options */}
                {isLoadingCards ? (
                  <div className="flex justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* Card Selection */}
                    {savedCards.length > 0 && (
                      <div className="space-y-3">
                        <label className="block text-sm font-medium text-gray-700">
                          {t("payment.form.paymentMethod")}
                        </label>
                        <div className="space-y-2">
                          {/* Saved Cards Option */}
                          <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                            <input
                              type="radio"
                              checked={paymentOption === 'saved'}
                              onChange={() => setPaymentOption('saved')}
                              className="h-4 w-4 text-primary focus:ring-primary"
                            />
                            <span className="ml-3 text-sm font-medium text-gray-700">
                              {t("payment.options.useSavedCard")}
                            </span>
                          </label>
                          
                          {paymentOption === 'saved' && (
                            <div className="ml-7 mt-2">
                              <select
                                value={selectedCardId}
                                onChange={(e) => setSelectedCardId(e.target.value)}
                                className="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary text-sm rounded-md"
                              >
                                <option value="">{t('payment.form.selectCard')}</option>
                                {savedCards.map((card) => (
                                  <option key={card._id} value={card._id}>
                                    {card.cardAlias} - {card.cardType} **** {card.lastFourDigits}
                                  </option>
                                ))}
                              </select>
                              {errors.selectedCard && (
                                <p className="mt-1 text-sm text-red-600">{errors.selectedCard}</p>
                              )}
                            </div>
                          )}
                          
                          {/* New Card Option */}
                          <label className="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                            <input
                              type="radio"
                              checked={paymentOption === 'new'}
                              onChange={() => setPaymentOption('new')}
                              className="h-4 w-4 text-primary focus:ring-primary"
                            />
                            <span className="ml-3 text-sm font-medium text-gray-700">
                              {t("payment.options.useNewCard")}
                            </span>
                          </label>
                        </div>
                      </div>
                    )}
                    
                    {/* New card form */}
                    {paymentOption === 'new' && (
                      <div className="space-y-4">
                        <div className="rounded-lg border border-gray-200 p-5 bg-white shadow-sm">
                          <div className="mb-4">
                            <label htmlFor="cardHolderName" className="block text-sm font-medium text-gray-700">
                              {t("payment.form.cardHolderName")}
                            </label>
                            <div className="mt-1 relative rounded-md shadow-sm">
                              <input
                                type="text"
                                id="cardHolderName"
                                value={cardInfo.cardHolderName}
                                onChange={(e) => setCardInfo({ ...cardInfo, cardHolderName: e.target.value })}
                                placeholder={t("payment.form.cardHolderNamePlaceholder")}
                                className="block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm"
                              />
                            </div>
                            {errors.cardHolderName && (
                              <p className="mt-1 text-sm text-red-600">{errors.cardHolderName}</p>
                            )}
                          </div>

                          <div className="mb-4">
                            <label htmlFor="cardNumber" className="block text-sm font-medium text-gray-700">
                              {t("payment.form.cardNumber")}
                            </label>
                            <div className="mt-1 relative rounded-md shadow-sm">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <CreditCard className="h-5 w-5 text-gray-400" />
                              </div>
                              <input
                                type="text"
                                id="cardNumber"
                                value={cardInfo.cardNumber}
                                onChange={(e) => setCardInfo({ ...cardInfo, cardNumber: formatCardNumber(e.target.value) })}
                                placeholder={t("payment.form.cardNumberPlaceholder")}
                                maxLength={19}
                                className="block w-full pl-10 border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm"
                              />
                            </div>
                            {errors.cardNumber && (
                              <p className="mt-1 text-sm text-red-600">{errors.cardNumber}</p>
                            )}
                          </div>

                          <div className="grid grid-cols-3 gap-4">
                            <div>
                              <label htmlFor="expireMonth" className="block text-sm font-medium text-gray-700">
                                {t("payment.form.expiryMonth")}
                              </label>
                              <select
                                id="expireMonth"
                                value={cardInfo.expireMonth}
                                onChange={(e) => setCardInfo({ ...cardInfo, expireMonth: e.target.value })}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm"
                              >
                                <option value="">MM</option>
                                {Array.from({ length: 12 }, (_, i) => {
                                  const month = (i + 1).toString().padStart(2, "0");
                                  return (
                                    <option key={month} value={month}>
                                      {month}
                                    </option>
                                  );
                                })}
                              </select>
                              {errors.expireMonth && (
                                <p className="mt-1 text-sm text-red-600">{errors.expireMonth}</p>
                              )}
                            </div>

                            <div>
                              <label htmlFor="expireYear" className="block text-sm font-medium text-gray-700">
                                {t("payment.form.expiryYear")}
                              </label>
                              <select
                                id="expireYear"
                                value={cardInfo.expireYear}
                                onChange={(e) => setCardInfo({ ...cardInfo, expireYear: e.target.value })}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm"
                              >
                                <option value="">YYYY</option>
                                {yearOptions.map((year) => (
                                  <option key={year} value={year}>
                                    {year}
                                  </option>
                                ))}
                              </select>
                              {errors.expireYear && (
                                <p className="mt-1 text-sm text-red-600">{errors.expireYear}</p>
                              )}
                            </div>

                            <div>
                              <label htmlFor="cvc" className="block text-sm font-medium text-gray-700">
                                {t("payment.form.cvc")}
                              </label>
                              <input
                                type="text"
                                id="cvc"
                                value={cardInfo.cvc}
                                onChange={(e) => setCardInfo({ ...cardInfo, cvc: e.target.value.replace(/\D/g, "") })}
                                placeholder={t("payment.form.cvcPlaceholder")}
                                maxLength={3}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm"
                              />
                              {errors.cvc && (
                                <p className="mt-1 text-sm text-red-600">{errors.cvc}</p>
                              )}
                            </div>
                          </div>

                          {/* Save card checkbox */}
                          <div className="mt-4">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={saveCard}
                                onChange={(e) => setSaveCard(e.target.checked)}
                                className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                              />
                              <span className="ml-2 text-sm text-gray-700">
                                {t("payment.form.saveCard")}
                              </span>
                            </label>
                          </div>

                          {/* Card alias input (shown when save card is checked) */}
                          {saveCard && (
                            <div className="mt-3">
                              <label htmlFor="cardAlias" className="block text-sm font-medium text-gray-700">
                                {t("payment.form.cardAlias")}
                              </label>
                              <input
                                type="text"
                                id="cardAlias"
                                value={cardInfo.cardAlias}
                                onChange={(e) => setCardInfo({ ...cardInfo, cardAlias: e.target.value })}
                                placeholder={t("payment.form.cardAliasPlaceholder")}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary text-sm"
                              />
                              {errors.cardAlias && (
                                <p className="mt-1 text-sm text-red-600">{errors.cardAlias}</p>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200"></div>
          <div className="px-3 md:px-6 py-4 bg-gray-50 rounded-b-xl">
            <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-3">
              <button
                type="button"
                className="py-3 px-6 bg-white rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors shadow-sm"
                onClick={onClose}
              >
                {t("payment.actions.close")}
              </button>
              <button
                type="button"
                className="py-3 px-6 bg-primary rounded-lg text-white font-medium hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors shadow-md flex-1 flex justify-center items-center"
                onClick={handleSubmit}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin -ml-1 mr-2 h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
                    {t("payment.processing")}
                  </>
                ) : (
                  <>
                    <CreditCard className="h-5 w-5 mr-2" />
                    {t('payment.payAmount', { amount: selectedPackage.price }) || `Pay $${selectedPackage.price}`}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DesignPackagePaymentModal;