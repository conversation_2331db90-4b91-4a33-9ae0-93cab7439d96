import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Laptop,
  Recycle,
  GraduationCap,
  Wind,
  Factory,
  Building2,
  Play,
  Heart,
  Plus,
  Globe,
  ChevronRight,
  Loader2
} from 'lucide-react';
import { getCategories, getCategoriesByParentId } from '../api/index';
import { ICategory } from '../types/category';

interface Subcategory {
  _id: string;
  name: string;
  items: Array<{
    _id: string;
    name: string;
  }>;
}

interface CategoryWithIcon {
  _id: string;
  icon: any;
  name: string;
  subcategories?: Subcategory[];
}

const Categories: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('categories');
  const [selectedCategory, setSelectedCategory] = useState<CategoryWithIcon | null>(null);
  const [categories, setCategories] = useState<CategoryWithIcon[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [subcategories, setSubcategories] = useState<ICategory[]>([]);
  const [items, setItems] = useState<Record<string, ICategory[]>>({});

  const categoryIcons = {
    "Information Technology": Laptop,
    "Environment and Waste": Recycle,
    "Education": GraduationCap,
    "Energy": Wind,
    "Manufacturing": Factory,
    "Construction": Building2,
    "Media": Play,
    "Health": Plus,
    "Social": Heart,
    "Transportation": Globe
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        const data = await getCategories();

        // Filter top-level categories (no parent_id)
        const topLevelCategories = data.filter(category =>
          !category.parent_id || category.parent_id === "" || category.parent_id === null
        );

        // Map categories to include icons
        const categoriesWithIcons = topLevelCategories.map(category => ({
          ...category,
          icon: categoryIcons[category.name] || Laptop,
        }));

        setCategories(categoriesWithIcons);
      } catch (error) {
        console.error('Failed to fetch categories:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const handleCategoryClick = async (category: CategoryWithIcon) => {
    if (selectedCategory && selectedCategory._id === category._id) {
      setSelectedCategory(null);
      return;
    }

    setSelectedCategory(category);

    try {
      // Fetch subcategories for the selected category
      const subCategoriesData = await getCategoriesByParentId(category.id);
      setSubcategories(subCategoriesData);

      // Fetch items for each subcategory
      const itemsMap: Record<string, ICategory[]> = {};

      for (const subCategory of subCategoriesData) {
        const subItems = await getCategoriesByParentId(subCategory.id);
        itemsMap[subCategory._id] = subItems;
      }

      setItems(itemsMap);
    } catch (error) {
      console.error('Failed to fetch subcategories or items:', error);
    }
  };

  return (
    <div className="bg-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-8">{t('title')}</h2>

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="w-8 h-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
              {categories.map((category) => (
                <button
                  key={category._id}
                  onClick={() => handleCategoryClick(category)}
                  className={`group relative flex items-center justify-between p-4 rounded-xl transition-all duration-200 ${
                    selectedCategory?._id === category._id
                      ? 'bg-primary text-white shadow-lg scale-[1.02]'
                      : 'bg-gray-50 text-gray-900 hover:bg-gray-100 hover:shadow-md hover:scale-[1.02]'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <category.icon className={`w-6 h-6 transition-colors ${
                      selectedCategory?._id === category._id ? 'text-white' : 'text-primary group-hover:text-primary'
                    }`} />
                    <span className="font-medium">{category.name}</span>
                  </div>
                  <ChevronRight className={`w-5 h-5 transition-transform ${
                    selectedCategory?._id === category._id ? 'rotate-90' : ''
                  }`} />
                </button>
              ))}
            </div>

            {/* Subcategories */}
            {selectedCategory && subcategories.length > 0 && (
              <div className="mt-8 animate-fadeIn">
                <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center space-x-2">
                  <selectedCategory.icon className="w-5 h-5 text-primary" />
                  <span>{t('subcategoriesOf', { category: selectedCategory.name })}</span>
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {subcategories.map((subcategory) => (
                    <div
                      key={subcategory._id}
                      className="p-6 rounded-xl bg-gray-50 border-2 border-transparent hover:border-primary/30 hover:shadow-md transition-all duration-200"
                    >
                      <h4 className="font-semibold text-gray-900 mb-4">{subcategory.name}</h4>
                      <ul className="space-y-2">
                        {items[subcategory._id] && items[subcategory._id].map((item) => (
                          <li
                            key={item._id}
                            onClick={() => navigate(`/items?category=${item._id}`)}
                            className="flex items-center space-x-2 text-gray-600 hover:text-primary cursor-pointer transition-colors"
                          >
                            <ChevronRight className="w-4 h-4" />
                            <span>{item.name}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default Categories;