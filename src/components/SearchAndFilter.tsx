import React from "react";
import {
  Box,
  Input,
  Select,
  HStack,
  useColorModeValue,
} from "@chakra-ui/react";
import { FiSearch } from "react-icons/fi";

const SearchAndFilter: React.FC = () => {
  const bgColor = useColorModeValue("gray.50", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");

  return (
    <Box bg={bgColor} p={4} borderRadius="md" shadow="sm">
      <HStack spacing={4}>
        <Box position="relative" flex={1}>
          <Input
            placeholder="Search..."
            pl={10}
            borderColor={borderColor}
            _focus={{ borderColor: "primary.500", boxShadow: "outline" }}
          />

          <Box
            position="absolute"
            left={3}
            top="50%"
            transform="translateY(-50%)"
          >
            <FiSearch color="gray.300" />
          </Box>
        </Box>
        <Select
          placeholder="Filter by category"
          borderColor={borderColor}
          _focus={{ borderColor: "primary.500", boxShadow: "outline" }}
        >
          <option value="category1">Category 1</option>
          <option value="category2">Category 2</option>
          <option value="category3">Category 3</option>
        </Select>
      </HStack>
    </Box>
  );
};

export default SearchAndFilter;
