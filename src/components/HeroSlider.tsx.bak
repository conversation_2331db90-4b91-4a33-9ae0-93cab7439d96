import React from "react";
import { Box, Image, useBreakpointValue, Link } from "@chakra-ui/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

interface SliderImage {
  web: string;
  mobile: string;
  link?: string;
}

interface HeroSliderProps {
  images: SliderImage[];
}

const HeroSlider: React.FC<HeroSliderProps> = ({ images }) => {
  const isMobile = useBreakpointValue({ base: true, md: false });
  const sliderHeight = useBreakpointValue({
    base: "300px", // Mobile
    sm: "400px", // Small tablets
    md: "500px", // Tablets
    lg: "600px", // Desktop
    xl: "700px", // Large screens
  });

  return (
    <Box width="100%" overflow="hidden">
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        navigation={!isMobile}
        pagination={{ clickable: true }}
        autoplay={{ delay: 5000 }}
        loop={true}
        style={{ width: "100%", height: sliderHeight }}
      >
        {images.map((image, index) => (
          <SwiperSlide key={index}>
            {image.link ? (
              <Link
                href={image.link}
                isExternal
                display="flex"
                height="100%"
                width="100%"
                alignItems="center"
                justifyContent="center"
                _hover={{ opacity: 0.9 }}
                transition="opacity 0.2s"
              >
                <Image
                  src={
                    import.meta.env.VITE_SOCKET_URL +
                    (isMobile ? image.mobile : image.web)
                  }
                  alt={`Slide ${index + 1}`}
                  width="100%"
                  height="100%"
                  objectFit="cover"
                  loading={index === 0 ? "eager" : "lazy"}
                />
              </Link>
            ) : (
              <Box
                height="100%"
                width="100%"
                display="flex"
                alignItems="center"
                justifyContent="center"
              >
                <Image
                  src={
                    import.meta.env.VITE_SOCKET_URL +
                    (isMobile ? image.mobile : image.web)
                  }
                  alt={`Slide ${index + 1}`}
                  width="100%"
                  height="100%"
                  objectFit="cover"
                  loading={index === 0 ? "eager" : "lazy"}
                />
              </Box>
            )}
          </SwiperSlide>
        ))}
      </Swiper>
    </Box>
  );
};

export default HeroSlider;
