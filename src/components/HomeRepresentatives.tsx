import { useState, useEffect, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import { IRepresentative } from "@/types/representative";
import { getActiveRepresentatives } from "../api/representativeApi";
import {
  Box,
  Button,
  Input,
  Select,
  Avatar,
  Heading,
  Grid,
  Spinner,
  Text,
} from "@chakra-ui/react";

interface Representative {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  country: string;
  city: string;
  countryName?: string;
  cityName?: string;
  profilePicture?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  company?: string;
  title?: string;
  languages?: string[];
  expertise?: string[];
  experience?: string;
  verified?: boolean;
  region?: string;
}

const HomeRepresentatives = () => {
  const { t } = useTranslation();
  const [sortBy, setSortBy] = useState("name");
  const [filterText, setFilterText] = useState("");
  const [representatives, setRepresentatives] = useState<Representative[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchRepresentatives = async () => {
      try {
        setIsLoading(true);
        const response: any = await getActiveRepresentatives();
        if (response.success && response.data) {
          // Show only first 8 representatives for homepage
          setRepresentatives(response.data.slice(0, 8));
        } else {
          console.error('Failed to fetch representatives:', response.error);
        }
      } catch (error) {
        console.error('Error fetching representatives:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchRepresentatives();
  }, []);

  const getFullName = (rep: Representative) => {
    return `${rep.firstName} ${rep.lastName}`;
  };

  const sortedAndFilteredRepresentatives = representatives
    .filter((rep: Representative) =>
      getFullName(rep)
        .toLowerCase()
        .includes(filterText.toLowerCase()),
    )
    .sort((a: Representative, b: Representative) => {
      return getFullName(a).localeCompare(getFullName(b));
    });

  if (isLoading) {
    return (
      <Box maxW="container.xl" mx="auto" py={12} textAlign="center">
        <Spinner size="xl" />
        <Text mt={4}>{t("common:loading") || "Loading..."}</Text>
      </Box>
    );
  }

  return (
    <Box maxW="container.xl" mx="auto" py={12}>
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={8}
      >
        <Heading as="h2" size="2xl">
          {t("representatives.title") || "Our Representatives"}
        </Heading>
        <Box display="flex" gap={4}>
          <Input
            type="text"
            placeholder={t("representatives.filters.searchPlaceholder") || "Search representatives..."}
            value={filterText}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              setFilterText(e.target.value)
            }
            w="16rem"
          />

          <Select value={sortBy} onChange={(e) => setSortBy(e.target.value)}>
            <option value="name">{t("representatives.sortByName") || "Sort by Name"}</option>
          </Select>
        </Box>
      </Box>

      {sortedAndFilteredRepresentatives.length === 0 ? (
        <Box textAlign="center" py={8}>
          <Text color="gray.500">{t("representatives.noRepresentatives") || "No representatives found"}</Text>
        </Box>
      ) : (
        <Grid
          templateColumns={{
            base: "1fr",
            md: "repeat(2, 1fr)",
            lg: "repeat(3, 1fr)",
            xl: "repeat(4, 1fr)",
          }}
          gap={6}
        >
          {sortedAndFilteredRepresentatives.map(
            (representative: Representative) => (
              <Box
                key={representative._id}
                borderWidth="1px"
                borderRadius="md"
                boxShadow="md"
                p={4}
                _hover={{ boxShadow: "lg" }}
              >
                <Box display="flex" flexDirection="column" alignItems="center">
                  <Avatar
                    size="xl"
                    src={
                      representative.profilePicture?.startsWith("data:") || representative.profilePicture?.startsWith("http")
                        ? representative.profilePicture
                        : representative.profilePicture 
                          ? `${import.meta.env.VITE_SOCKET_URL}/uploads/${representative.profilePicture.replace(/^\/+/, '')}`
                          : undefined
                    }
                    name={getFullName(representative)}
                  />

                  <Heading as="h3" size="md" mt={4} textAlign="center">
                    {getFullName(representative)}
                  </Heading>
                  {representative.title && (
                    <Text color="teal.500" fontSize="sm" textAlign="center">
                      {representative.title}
                    </Text>
                  )}
                  {representative.company && (
                    <Text color="gray.600" fontSize="sm" textAlign="center">
                      {representative.company}
                    </Text>
                  )}
                </Box>
                <Box mt={4} textAlign="center">
                  <Button variant="outline" size="sm">
                    {t("representatives.emailButton") || "Contact"}
                  </Button>
                </Box>
              </Box>
            ),
          )}
        </Grid>
      )}
      
      {/* Show link to view all representatives */}
      <Box textAlign="center" mt={8}>
        <Button as="a" href="/representatives" colorScheme="teal" variant="outline">
          {t("representatives.viewAll") || "View All Representatives"}
        </Button>
      </Box>
    </Box>
  );
};

export default HomeRepresentatives;
