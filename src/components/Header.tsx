import React from "react";
import { useLocation } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import UserHeader from "./UserHeader";
import AdminHeader from "./AdminHeader";
import OfflineHeader from "./OfflineHeader";

const Header: React.FC = () => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith("/admin");

  const { isAuthenticated, user, isLoading, isAdmin } = useAuth();

  // List of paths that have their own headers, to avoid duplicates
  const pathsWithOwnHeaders = [
    "/login",
    "/register",
    "/forgot-password",
    "/reset-password",
    "/admin/login"
  ];

  // Don't render any header on paths with their own custom headers
  if (pathsWithOwnHeaders.includes(location.pathname)) {
    return null;
  }

  // If user is authenticated
  if (!isLoading && isAuthenticated) {
    // Show AdminHeader for admin routes and when user is admin
    if (isAdminRoute && isAdmin) {
      return <AdminHeader />;
    } else if (!isAdminRoute) {
      return <UserHeader profileInfo={user as any} />;
    }
  } else if (!isLoading) {
    // If not authenticated and not on a path with its own header, show OfflineHeader
    return <OfflineHeader />;
  }

  // Default case - loading or undefined state
  return null;
};

export default Header;
