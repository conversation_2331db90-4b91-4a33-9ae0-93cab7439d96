import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  Menu, 
  X, 
  ChevronDown,
  MessageSquare,
  Settings,
  LogOut,
  Building2,
  ShoppingBag,
  Headphones,
  Plus,
  Search,
  Bell,
  Globe2,
  Shield,
  TrendingUp,
  Users
} from 'lucide-react';
import { isLoggedIn, getUser, logout } from '../data/testUser';

const Header = () => {
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isProductsOpen, setIsProductsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const loggedIn = isLoggedIn();
  const user = getUser();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const whyUsLinks = [
    { name: 'Global Erişim', path: '/reasons/global-access', icon: Globe2 },
    { name: 'Güvenli Platform', path: '/reasons/secure-platform', icon: Shield },
    { name: 'Hızlı Büyüme', path: '/reasons/fast-growth', icon: TrendingUp },
    { name: 'İş Birlikleri', path: '/reasons/partnerships', icon: Users }
  ];

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      {/* Top Header */}
      <div className="bg-primary">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-10">
            <p className="text-sm text-white">
              Global ticarette güvenilir çözüm ortağınız
            </p>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <button 
              onClick={() => navigate('/')}
              className="flex items-center space-x-2"
            >
              <img 
                src="https://www.e-exportcity.com/logo.png"
                alt="e-exportcity"
                className="h-8"
              />
            </button>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {loggedIn ? (
                <>
                  {/* Search */}
                  <form onSubmit={handleSearch} className="relative w-96">
                    <input
                      type="text"
                      placeholder="Ürün, hizmet veya firma ara..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  </form>

                  {/* Products and Services Dropdown */}
                  <div className="relative">
                    <button 
                      onClick={() => setIsProductsOpen(!isProductsOpen)}
                      className="flex items-center space-x-2 px-4 py-2.5 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors"
                    >
                      <span>Ürünler ve Hizmetler</span>
                      <ChevronDown className={`h-4 w-4 transition-transform ${isProductsOpen ? 'rotate-180' : ''}`} />
                    </button>

                    {isProductsOpen && (
                      <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-100 py-2 z-50">
                        <button
                          onClick={() => {
                            navigate('/products/add');
                            setIsProductsOpen(false);
                          }}
                          className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <Plus className="w-5 h-5 text-gray-400" />
                          <span>Ürün/Hizmet Ekle</span>
                        </button>
                        <div className="border-t my-2"></div>
                        <button
                          onClick={() => {
                            navigate('/products?type=product');
                            setIsProductsOpen(false);
                          }}
                          className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <ShoppingBag className="w-5 h-5 text-gray-400" />
                          <span>Ürün Satışı</span>
                        </button>
                        <button
                          onClick={() => {
                            navigate('/products?type=service');
                            setIsProductsOpen(false);
                          }}
                          className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <Headphones className="w-5 h-5 text-gray-400" />
                          <span>Hizmet Talebi</span>
                        </button>
                      </div>
                    )}
                  </div>

                  {/* Navigation Links */}
                  <button 
                    onClick={() => navigate('/companies')}
                    className="text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    Firmalar
                  </button>
                  <button 
                    onClick={() => navigate('/representatives')}
                    className="text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    Temsilciler
                  </button>
                </>
              ) : (
                <>
                  <button 
                    onClick={() => navigate('/about')}
                    className="text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    Hakkımızda
                  </button>

                  {/* Why Us Dropdown */}
                  <div className="relative group">
                    <button 
                      className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors"
                    >
                      <span>Neden E-exportcity</span>
                      <ChevronDown className="h-4 w-4" />
                    </button>

                    <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-100 py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                      {whyUsLinks.map((link, index) => (
                        <button
                          key={index}
                          onClick={() => navigate(link.path)}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-gray-600 hover:text-primary hover:bg-gray-50 transition-colors"
                        >
                          <link.icon className="w-5 h-5" />
                          <span>{link.name}</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  <button 
                    onClick={() => navigate('/partnership')}
                    className="text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    İş Ortaklığı
                  </button>

                  <button 
                    onClick={() => navigate('/faq')}
                    className="text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    SSS
                  </button>
                </>
              )}
            </div>

            {/* User Menu */}
            <div className="hidden md:flex items-center space-x-4">
              {loggedIn ? (
                <>
                  {/* Messages Button */}
                  <button 
                    onClick={() => navigate('/messages')}
                    className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    <MessageSquare className="h-6 w-6" />
                    <span className="absolute -top-1 -right-1 w-5 h-5 flex items-center justify-center bg-red-500 text-white text-xs rounded-full">
                      3
                    </span>
                  </button>

                  {/* Notifications Button */}
                  <button 
                    onClick={() => navigate('/notifications')}
                    className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
                  >
                    <Bell className="h-6 w-6" />
                    <span className="absolute -top-1 -right-1 w-5 h-5 flex items-center justify-center bg-primary text-white text-xs rounded-full">
                      5
                    </span>
                  </button>

                  {/* Profile Button */}
                  <button
                    onClick={() => setIsProfileOpen(!isProfileOpen)}
                    className="flex items-center space-x-2"
                  >
                    <img
                      src={user?.avatar}
                      alt="Profile"
                      className="w-8 h-8 rounded-lg object-cover"
                    />
                    <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isProfileOpen ? 'rotate-180' : ''}`} />
                  </button>
                </>
              ) : (
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => navigate('/login')}
                    className="px-4 py-2 text-primary hover:text-[#0A9996] transition-colors"
                  >
                    Giriş Yap
                  </button>
                  <button
                    onClick={() => navigate('/register')}
                    className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors"
                  >
                    Üye Ol
                  </button>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-gray-400 hover:text-gray-600"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Profile Dropdown */}
      {isProfileOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-100 py-2 z-50">
          <div className="px-4 py-3 border-b">
            <p className="font-medium text-gray-900">{user?.firstName} {user?.lastName}</p>
            <p className="text-sm text-gray-600">{user?.email}</p>
          </div>

          <div className="py-2">
            <button 
              onClick={() => {
                navigate('/companies/techglobal-solutions');
                setIsProfileOpen(false);
              }}
              className="w-full flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Building2 className="h-5 w-5 text-gray-400" />
              <span>Firma Profili</span>
            </button>
          </div>

          <div className="border-t py-2">
            <button 
              onClick={() => {
                navigate('/settings');
                setIsProfileOpen(false);
              }}
              className="w-full flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <Settings className="h-5 w-5 text-gray-400" />
              <span>Ayarlar</span>
            </button>

            <button 
              onClick={() => {
                handleLogout();
                setIsProfileOpen(false);
              }}
              className="w-full flex items-center space-x-3 px-4 py-2 text-red-600 hover:bg-gray-50 transition-colors"
            >
              <LogOut className="h-5 w-5" />
              <span>Çıkış Yap</span>
            </button>
          </div>
        </div>
      )}

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-b">
          <div className="px-4 py-3">
            {loggedIn && (
              <form onSubmit={handleSearch} className="relative">
                <input
                  type="text"
                  placeholder="Ürün, hizmet veya firma ara..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </form>
            )}
          </div>

          <div className="px-4 py-2 space-y-1">
            {loggedIn ? (
              <>
                <button 
                  onClick={() => {
                    navigate('/products/add');
                    setIsMenuOpen(false);
                  }}
                  className="w-full flex items-center space-x-2 px-4 py-2 text-primary hover:bg-gray-50 rounded-lg transition-colors"
                >
                  <Plus className="h-5 w-5" />
                  <span>Ürün/Hizmet Ekle</span>
                </button>

                <button 
                  onClick={() => {
                    navigate('/companies');
                    setIsMenuOpen(false);
                  }}
                  className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  Firmalar
                </button>
                <button 
                  onClick={() => {
                    navigate('/representatives');
                    setIsMenuOpen(false);
                  }}
                  className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  Temsilciler
                </button>
                <button 
                  onClick={() => {
                    navigate('/products');
                    setIsMenuOpen(false);
                  }}
                  className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  Ürünler ve Hizmetler
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => {
                    navigate('/about');
                    setIsMenuOpen(false);
                  }}
                  className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  Hakkımızda
                </button>

                {whyUsLinks.map((link, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      navigate(link.path);
                      setIsMenuOpen(false);
                    }}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <link.icon className="h-5 w-5" />
                    <span>{link.name}</span>
                  </button>
                ))}

                <button
                  onClick={() => {
                    navigate('/partnership');
                    setIsMenuOpen(false);
                  }}
                  className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  İş Ortaklığı
                </button>

                <button
                  onClick={() => {
                    navigate('/faq');
                    setIsMenuOpen(false);
                  }}
                  className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                >
                  SSS
                </button>

                <div className="flex flex-col space-y-2 mt-4">
                  <button
                    onClick={() => {
                      navigate('/login');
                      setIsMenuOpen(false);
                    }}
                    className="w-full px-4 py-2 text-primary hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    Giriş Yap
                  </button>
                  <button
                    onClick={() => {
                      navigate('/register');
                      setIsMenuOpen(false);
                    }}
                    className="w-full px-4 py-2 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors"
                  >
                    Üye Ol
                  </button>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default Header;