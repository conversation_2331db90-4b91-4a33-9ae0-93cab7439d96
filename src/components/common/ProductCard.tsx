import React from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Eye, Pencil, Trash, Store, MapPin, ExternalLink, CheckCircle } from "lucide-react";
import { IItem } from "../../types/item";

interface ProductCardProps {
  item: any;
  showActions?: boolean;
  onEdit?: (item: IItem) => void;
  onDelete?: (item: IItem) => void;
  onView?: (itemId: string) => void;
  showStore?: boolean;
  isManagement?: boolean;
}




const ProductCard: React.FC<ProductCardProps> = ({
  item,
  showActions = false,
  onEdit,
  onDelete,
  onView,
  showStore = true,
}) => {
  const { t } = useTranslation("productCard");

  const imageSrc = item.images && item.images.length > 0
    ? `${import.meta.env.VITE_SOCKET_URL}${item.images[0].startsWith('/') ? item.images[0] : `/${item.images[0]}`}`
    : undefined;

  const handleAction = (e: React.MouseEvent, action: (item: IItem) => void) => {
    e.preventDefault();
    e.stopPropagation();
    action(item);
  };

  const handleView = (e: React.MouseEvent) => {
    if (!onView) return;
    e.preventDefault();
    e.stopPropagation();
    onView(item._id);
  };

  return (
    <div
      onClick={onView ? handleView : undefined}
      className="h-full flex flex-col overflow-hidden rounded-xl bg-white shadow-sm transition-all duration-300 cursor-pointer hover:shadow-md"
      style={{ position: "relative" }} // Add relative positioning for absolute Link
    >
      <div className="relative">
        {/* Product Image */}
        <div className="aspect-[4/3] relative overflow-hidden rounded-t-xl">
          {imageSrc ? (
            <img
              src={imageSrc}
              alt={t("image.alt", { title: item.name })}
              className="w-full h-full object-cover"
              onError={(e) => {
                // Prevent infinite loop by removing the onerror handler
                e.currentTarget.onerror = null;
                // Replace with inline fallback (instead of another image request)
                e.currentTarget.style.display = 'none';
                e.currentTarget.parentElement!.classList.add('bg-gray-100');
                e.currentTarget.parentElement!.innerHTML += `<div class="absolute inset-0 flex items-center justify-center"><svg class="h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg></div>`;
              }}
            />
          ) : (
            <div className="flex aspect-[4/3] w-full items-center justify-center bg-gray-100">
              <Store className="h-10 w-10 text-gray-400" />
            </div>
          )}

          {/* Overlay gradient for better text readability */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>

          {/* Combined type and listing type badge at bottom-left */}
          <div className="absolute bottom-4 left-4 z-10">
            <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${item.listingType?.toLowerCase() === 'demand'
              ? 'bg-primary/90 text-white'
              : 'bg-[#10B981]/90 text-white'
              }`}>
              {item.type === "product" ? t("itemTypes.product") : t("itemTypes.service")} {t(`listingTypes.${item.listingType?.toLowerCase() || 'sale'}`)}
            </span>
          </div>

          {/* Views counter at top-right */}
          <div className="absolute top-4 right-4 z-10">
            <div className="flex items-center space-x-1 bg-white/90 rounded-full px-2 py-1">
              <Eye className="h-4 w-4 text-gray-600" />
              <span className="text-sm font-medium text-gray-600">
                {typeof item.viewCount === 'number' ? item.viewCount : 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6">
        {/* Title and Store Info */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-1 line-clamp-2">
            {item.name}
          </h3>

          {/* Store Info */}
          {showStore && item.storeId && (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-primary">{item.storeId.name}</span>
                {item.storeId.isVerified && (
                  <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                )}
              </div>
            </div>
          )}
        </div>

        {/* Location */}
        {showStore && (item.storeId?.city || item.storeId?.country) && (
          <div className="flex items-center space-x-2 mb-4">
            <MapPin className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600">
              {[item.storeId.city, item.storeId.country].filter(Boolean).join(", ")}
            </span>
          </div>
        )}

        {/* Action Buttons */}
        {showActions && (
          <div className="flex justify-center space-x-2">
            <button
              onClick={handleView}
              className="inline-flex items-center rounded-md bg-blue-50 px-2.5 py-1.5 text-sm font-medium text-blue-700 hover:bg-blue-100 transition-colors"
              title={t("button.view")}
            >
              <Eye className="mr-1 h-4 w-4" />
              <span>{t("button.view")}</span>
            </button>

            {onEdit && (
              <button
                onClick={(e) => handleAction(e, onEdit)}
                className="inline-flex items-center rounded-md bg-green-50 px-2.5 py-1.5 text-sm font-medium text-green-700 hover:bg-green-100 transition-colors"
                title={t("button.edit")}
              >
                <Pencil className="mr-1 h-4 w-4" />
                <span>{t("button.edit")}</span>
              </button>
            )}

            {onDelete && (
              <button
                onClick={(e) => handleAction(e, onDelete)}
                className="inline-flex items-center rounded-md bg-red-50 px-2.5 py-1.5 text-sm font-medium text-red-700 hover:bg-red-100 transition-colors"
                title={t("button.delete")}
              >
                <Trash className="mr-1 h-4 w-4" />
                <span>{t("button.delete")}</span>
              </button>
            )}
          </div>
        )}

        {/* View button for cards without action buttons */}
        {!showActions && (
          <button
            onClick={onView ? handleView : undefined}
            className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
          >
            <span>{t("button.viewDetails")}</span>
            <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </button>
        )}

        {/* Clickable overlay when the card is a link */}
        {!onView && (
          <Link
            to={`/items/${item._id}`}
            className="absolute inset-0 z-10"
            aria-label={t("button.viewDetails")}
          ></Link>
        )}
      </div>
    </div>
  );
};

export default ProductCard;