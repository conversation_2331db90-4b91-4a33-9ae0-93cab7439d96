import React, { useState, useEffect } from "react";
import { Box, Textarea } from "@chakra-ui/react";
import DOMPurify from "dompurify";

interface RichTextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const RichTextEditor: React.FC<RichTextEditorProps> = ({
  value,
  onChange,
  placeholder,
}) => {
  const [internalValue, setInternalValue] = useState(value);

  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    // Sanitize HTML content before passing it up
    const sanitizedValue = DOMPurify.sanitize(newValue);
    onChange(sanitizedValue);
  };

  return (
    <Box className="rich-text-editor">
      <Textarea
        value={internalValue}
        onChange={handleChange}
        placeholder={placeholder}
        minHeight="200px"
        p={2}
        resize="vertical"
        whiteSpace="pre-wrap"
      />
    </Box>
  );
};

export default RichTextEditor;
