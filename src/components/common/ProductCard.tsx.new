import React from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { motion } from "framer-motion";
import { Eye, Pencil, Trash, Building, Store, MapPin } from "lucide-react";
import { IItem } from "../../types/item";

interface ProductCardProps {
  item: IItem;
  showActions?: boolean;
  onEdit?: (item: IItem) => void;
  onDelete?: (item: IItem) => void;
  onView?: (itemId: string) => void;
  showStore?: boolean;
  isManagement?: boolean;
}

// Motion variants for hover effects
const cardVariants = {
  hover: {
    y: -5,
    boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
    transition: { duration: 0.3 }
  }
};

const badgeColors = {
  product: {
    bg: "bg-blue-100",
    text: "text-blue-800"
  },
  service: {
    bg: "bg-green-100",
    text: "text-green-800"
  },
  sale: {
    bg: "bg-orange-100",
    text: "text-orange-800"
  },
  demand: {
    bg: "bg-purple-100",
    text: "text-purple-800"
  },
  active: {
    bg: "bg-green-100",
    text: "text-green-800"
  },
  inactive: {
    bg: "bg-yellow-100",
    text: "text-yellow-800"
  }
};

const ProductCard: React.FC<ProductCardProps> = ({
  item,
  showActions = false,
  onEdit,
  onDelete,
  onView,
  showStore = true,
}) => {
  const { t } = useTranslation("productCard");

  const imageSrc = item.images && item.images.length > 0
    ? `${import.meta.env.VITE_SOCKET_URL}/uploads/${item.images[0].replace(/^\/+/, '')}`
    : undefined;

  const handleAction = (e: React.MouseEvent, action: (item: IItem) => void) => {
    e.preventDefault();
    e.stopPropagation();
    action(item);
  };

  const handleView = (e: React.MouseEvent) => {
    if (!onView) return;
    e.preventDefault();
    e.stopPropagation();
    onView(item._id);
  };

  return (
    <motion.div
      className="h-full flex flex-col overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm transition-all duration-300"
      whileHover="hover"
      variants={cardVariants}
      onClick={onView ? handleView : undefined}
    >
      <div className="relative">
        {/* Product Image */}
        {imageSrc ? (
          <img
            src={imageSrc}
            alt={t("image.alt", { title: item.name })}
            className="h-52 w-full object-cover"
          />
        ) : (
          <div className="flex h-52 w-full items-center justify-center bg-gray-100">
            <Store className="h-10 w-10 text-gray-400" />
          </div>
        )}
        
        {/* Overlay gradient for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
        
        {/* Item type badge at top-left */}
        <div className="absolute top-2 left-2 z-10">
          <span className={`inline-flex rounded-full px-2.5 py-1 text-xs font-medium ${
            badgeColors[item.type as keyof typeof badgeColors].bg
          } ${badgeColors[item.type as keyof typeof badgeColors].text}`}>
            {item.type === "product" ? t("itemTypes.product") : t("itemTypes.service")}
          </span>
        </div>
        
        {/* Listing type badge at top-right */}
        {item.listingType && (
          <div className="absolute top-2 right-2 z-10">
            <span className={`inline-flex rounded-full px-2.5 py-1 text-xs font-medium ${
              badgeColors[item.listingType.toLowerCase() as keyof typeof badgeColors].bg
            } ${badgeColors[item.listingType.toLowerCase() as keyof typeof badgeColors].text}`}>
              {t(`listingTypes.${item.listingType.toLowerCase()}`)}
            </span>
          </div>
        )}
      </div>
      
      <div className="flex flex-1 flex-col p-4">
        {/* Title */}
        <h3 className="mb-2 line-clamp-2 text-lg font-semibold text-gray-900">
          {item.name}
        </h3>
        
        {/* Category */}
        {item.category && (
          <span className="mb-2 inline-flex rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800">
            {item.category.name || item.category.nameEn}
          </span>
        )}
        
        {/* Description */}
        <p className="mb-3 line-clamp-2 text-sm text-gray-600">
          {item.description}
        </p>
        
        {/* Store Info */}
        {showStore && item.storeId && (
          <div className="mt-auto">
            <div className="flex items-center space-x-1 text-sm">
              <Building className="h-3.5 w-3.5 text-gray-500" />
              <span className="font-medium text-gray-700">
                {item.storeId.name}
              </span>
            </div>
            {item.storeId.city || item.storeId.country ? (
              <div className="flex items-center space-x-1 text-xs text-gray-500 mt-1">
                <MapPin className="h-3 w-3" />
                <span>
                  {[item.storeId.city, item.storeId.country].filter(Boolean).join(", ")}
                </span>
              </div>
            ) : null}
          </div>
        )}
        
        {/* Item Status */}
        {"status" in item && (
          <div className="mt-2">
            <span className={`inline-flex rounded-full px-2 py-0.5 text-xs font-medium ${
              badgeColors[(item.status === "ACTIVE" ? "active" : "inactive") as keyof typeof badgeColors].bg
            } ${badgeColors[(item.status === "ACTIVE" ? "active" : "inactive") as keyof typeof badgeColors].text}`}>
              {t(`itemStatus.${item.status.toLowerCase()}`)}
            </span>
          </div>
        )}
      </div>
      
      {/* Action Buttons */}
      {showActions && (
        <div className="border-t border-gray-200 p-2">
          <div className="flex justify-center space-x-2">
            <button
              onClick={handleView}
              className="inline-flex items-center rounded-md bg-blue-50 px-2.5 py-1.5 text-sm font-medium text-blue-700 hover:bg-blue-100 transition-colors"
              title={t("button.view")}
            >
              <Eye className="mr-1 h-4 w-4" />
              <span>{t("button.view")}</span>
            </button>
            
            {onEdit && (
              <button
                onClick={(e) => handleAction(e, onEdit)}
                className="inline-flex items-center rounded-md bg-green-50 px-2.5 py-1.5 text-sm font-medium text-green-700 hover:bg-green-100 transition-colors"
                title={t("button.edit")}
              >
                <Pencil className="mr-1 h-4 w-4" />
                <span>{t("button.edit")}</span>
              </button>
            )}
            
            {onDelete && (
              <button
                onClick={(e) => handleAction(e, onDelete)}
                className="inline-flex items-center rounded-md bg-red-50 px-2.5 py-1.5 text-sm font-medium text-red-700 hover:bg-red-100 transition-colors"
                title={t("button.delete")}
              >
                <Trash className="mr-1 h-4 w-4" />
                <span>{t("button.delete")}</span>
              </button>
            )}
          </div>
        </div>
      )}
      
      {/* View button for cards without action buttons */}
      {!showActions && onView && (
        <div className="p-4 pt-0">
          <button
            onClick={handleView}
            className="w-full rounded-lg bg-primary py-2 text-sm font-medium text-white hover:bg-primary-dark transition-colors"
          >
            {t("button.viewDetails")}
          </button>
        </div>
      )}
      
      {/* Clickable overlay when the card is a link */}
      {!onView && (
        <Link 
          to={`/items/${item._id}`}
          className="absolute inset-0 z-10"
          aria-label={t("button.viewDetails")}
        ></Link>
      )}
    </motion.div>
  );
};

export default ProductCard;