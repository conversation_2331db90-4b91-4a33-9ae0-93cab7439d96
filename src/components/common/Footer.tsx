import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { Linkedin, Instagram, Youtube, GitBranch as BrandTiktok, Mail, Phone, MapPin, Globe, Shield, TrendingUp, Users, ExternalLink } from "lucide-react";
import TermsModal from "./TermsModal";

const Footer: React.FC = () => {
  const { t } = useTranslation("common");
  const navigate = useNavigate();
  const [isTermsModalOpen, setIsTermsModalOpen] = useState(false);

  const quickLinks = [
    { name: t("footer.links.about"), path: "/about" },
    { name: t("footer.links.contact"), path: "/contact" },
    { name: t("footer.links.faq"), path: "/faq" },
    { name: t("footer.links.partnership"), path: "/partnership" },
  ];

  const whyUs = [
    { name: t("reasons.globalAccess.hero.title"), path: "/reasons/global-access", icon: Globe },
    { name: t("reasons.securePlatform.hero.title"), path: "/reasons/secure-platform", icon: Shield },
    { name: t("reasons.fastGrowth.hero.title"), path: "/reasons/fast-growth", icon: TrendingUp },
    { name: t("reasons.partnerships.hero.title"), path: "/reasons/partnerships", icon: Users }
  ];

  const packages = [
    { name: t("footer.links.packages"), path: "/packages" },
    { name: t("footer.links.designPackages"), path: "/design-packages" },
    { name: t("footer.links.partnership"), path: "/partnership" }
  ];

  return (
    <footer className="bg-[#1A1A1A] text-white">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-primary">{t("footer.sections.contact")}</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-primary flex-shrink-0 mt-1" />
                <p className="text-gray-400 hover:text-primary transition-colors">
                  {t("footer.contact.address")}
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-primary" />
                <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-primary transition-colors">
                  {t("footer.contact.email")}
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-primary" />
                <a href="tel:+905400668000" className="text-gray-400 hover:text-primary transition-colors">
                  {t("footer.contact.phone")}
                </a>
              </div>
            </div>

            {/* Social Media */}
            <div className="mt-6">
              <div className="flex items-center space-x-4">
                <a
                  href="https://linkedin.com/company/e-exportcity"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <Linkedin className="h-5 w-5" />
                </a>
                <a
                  href="https://instagram.com/e-exportcity"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <Instagram className="h-5 w-5" />
                </a>
                <a
                  href="https://tiktok.com/@e-exportcity"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <BrandTiktok className="h-5 w-5" />
                </a>
                <a
                  href="https://youtube.com/@e-exportcity"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <Youtube className="h-5 w-5" />
                </a>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-primary">{t("footer.quickLinks")}</h3>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <button
                    onClick={() => navigate(link.path)}
                    className="text-gray-400 hover:text-primary transition-colors"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Why Us */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-primary">{t("header.navigation.whyUs")}</h3>
            <ul className="space-y-3">
              {whyUs.map((item, index) => (
                <li key={index}>
                  <button
                    onClick={() => navigate(item.path)}
                    className="flex items-center space-x-2 text-gray-400 hover:text-primary transition-colors group"
                  >
                    <item.icon className="h-5 w-5" />
                    <span>{item.name}</span>
                    <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Packages */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-primary">{t("navigation.packages")}</h3>
            <ul className="space-y-3">
              {packages.map((pkg, index) => (
                <li key={index}>
                  <button
                    onClick={() => navigate(pkg.path)}
                    className="text-gray-400 hover:text-primary transition-colors"
                  >
                    {pkg.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-center mb-6">
            <img src="/iyzico.svg" alt="Iyzico Payment Solutions" className="h-8" />
          </div>
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <div className="flex items-center text-sm text-gray-400">
              <span>© 2024 E-exportcity. {t("footer.copyright")} <a href="https://yazilimcibul.com/" target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">Yazılımcı Bul</a> tarafından geliştirildi.</span>
            </div>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <button
                onClick={() => setIsTermsModalOpen(true)}
                className="text-sm text-gray-400 hover:text-primary transition-colors"
              >
                {t("footer.terms")}
              </button>
              <button
                onClick={() => navigate("/privacy-policy")}
                className="text-sm text-gray-400 hover:text-primary transition-colors"
              >
                {t("footer.privacyPolicy")}
              </button>
            </div>
          </div>
        </div>
      </div>

      <TermsModal
        isOpen={isTermsModalOpen}
        onClose={() => setIsTermsModalOpen(false)}
      />
    </footer>
  );
};

export default Footer;
