import React from "react";
import {
  Box,
  Card,
  CardBody,
  Heading,
  Text,
  Image,
  Badge,
  ButtonGroup,
  HStack,
  VStack,
  useColorModeValue,
  IconButton,
  Tooltip,
  Tag,
  CardFooter,
} from "@chakra-ui/react";
import { Link as RouterLink } from "react-router-dom";
import { FaEye, FaPen, FaTrash, FaStore } from "react-icons/fa";
import { useTranslation } from "react-i18next";
import { IItem } from "../../types/item";
import { motion } from "framer-motion";

const MotionCard = motion(Card);

interface ProductCardProps {
  item: IItem;
  showActions?: boolean;
  onEdit?: (item: IItem) => void;
  onDelete?: (item: IItem) => void;
  onView?: (item: IItem) => void;
  showStore?: boolean;
  isManagement?: boolean;
}

const ProductCard: React.FC<ProductCardProps> = ({
  item,
  showActions = false,
  onEdit,
  onDelete,
  onView,
  showStore = true,
}) => {
  const { t } = useTranslation("productCard");
  const cardBg = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.600", "gray.200");
  const badgeTypeColor = item.type === "product" ? "blue" : "green";
  const badgeListingColor = item.listingType === "sale" ? "orange" : "purple";

  const imageSrc = item.images && item.images.length > 0
    ? `${import.meta.env.VITE_SOCKET_URL}/${item.images[0].replace(/^\/+/, '')}`
    : undefined;

  const handleAction = (e: React.MouseEvent, action: (item: IItem) => void) => {
    e.preventDefault();
    e.stopPropagation();
    action(item);
  };

  return (
    <MotionCard
      as={RouterLink}
      to={`/items/${item._id}`}
      maxW="100%"
      overflow="hidden"
      variant="outline"
      borderColor={borderColor}
      bg={cardBg}
      boxShadow="sm"
      borderRadius="lg"
      transition="all 0.3s"
      whileHover={{
        y: -5,
        boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
      }}
      height="100%"
      display="flex"
      flexDirection="column"
      _hover={{ textDecoration: "none" }}
    >
      <Box position="relative">
        <Image
          src={imageSrc}
          alt={t("image.alt", { title: item.name })}
          borderTopRadius="lg"
          objectFit="cover"
          width="100%"
          height="200px"
          fallback={
            <Box
              height="200px"
              bg="gray.100"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              <FaStore size="2rem" color="gray" />
            </Box>
          }
        />
        {/* Item type badge at the top-left */}
        <Box position="absolute" top="2" left="2" zIndex={1}>
          <Badge colorScheme={badgeTypeColor} borderRadius="full" px="2" py="1">
            {item.type === "product" ? t("itemTypes.product") : t("itemTypes.service")}
          </Badge>
        </Box>

        {/* Listing type badge at the top-right */}
        {item.listingType && (
          <Box position="absolute" top="2" right="2" zIndex={1}>
            <Badge colorScheme={badgeListingColor} borderRadius="full" px="2" py="1">
              {t(`listingTypes.${item.listingType.toLowerCase()}`)}
            </Badge>
          </Box>
        )}
      </Box>

      <CardBody flex="1" display="flex" flexDirection="column">
        <VStack align="start" spacing="3" flex="1">
          <Heading size="md" noOfLines={2}>
            {item.name}
          </Heading>

          {/* Category */}
          {item.category && (
            <Badge colorScheme="teal" variant="subtle" fontSize="xs">
              {item.category.name || item.category.nameEn}
            </Badge>
          )}

          <Text color={textColor} noOfLines={2} fontSize="sm">
            {item.description}
          </Text>
          {showStore && item.store && (
            <VStack align="start" spacing="1" fontSize="sm" w="100%">
              <HStack spacing="1" align="center">
                <FaStore size="0.8rem" />
                <Text fontWeight="medium" color={textColor} noOfLines={1}>
                  {item.store.name}
                  {item.store.location && (item.store.location.city || item.store.location.country) && (
                    <Text as="span" fontWeight="normal">
                      {" - "}
                      {[
                        item.store.location.city,
                        item.store.location.country
                      ].filter(Boolean).join("/")}
                    </Text>
                  )}
                </Text>
              </HStack>
            </VStack>
          )}

          {"status" in item && (
            <Tag colorScheme={item.status === "ACTIVE" ? "green" : "yellow"} size="sm">
              {t(`itemStatus.${item.status.toLowerCase()}`)}
            </Tag>
          )}
        </VStack>
      </CardBody>

      {showActions && (
        <CardFooter
          borderTop="1px"
          borderColor={borderColor}
          justifyContent="center"
          py="3"
        >
          <ButtonGroup spacing="2" size="sm">
            <Tooltip label={t("button.view")}>
              <IconButton
                aria-label={t("button.view")}
                icon={<FaEye />}
                colorScheme="blue"
                onClick={(e) => onView ? handleAction(e, onView) : null}
              />
            </Tooltip>
            {onEdit && (
              <Tooltip label={t("button.edit")}>
                <IconButton
                  aria-label={t("button.edit")}
                  icon={<FaPen />}
                  colorScheme="green"
                  onClick={(e) => handleAction(e, onEdit)}
                />
              </Tooltip>
            )}
            {onDelete && (
              <Tooltip label={t("button.delete")}>
                <IconButton
                  aria-label={t("button.delete")}
                  icon={<FaTrash />}
                  colorScheme="red"
                  onClick={(e) => handleAction(e, onDelete)}
                />
              </Tooltip>
            )}
          </ButtonGroup>
        </CardFooter>
      )}
    </MotionCard>
  );
};

export default ProductCard;
