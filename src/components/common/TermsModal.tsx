import React from 'react';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';

interface TermsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept?: () => void;
}

const TermsModal: React.FC<TermsModalProps> = ({
  isOpen,
  onClose,
  onAccept,
}) => {
  const { t } = useTranslation('common');

  if (!isOpen) return null;

  const termsContent = `
GİZLİLİK POLİTİKASI

Exportcity Yazılım Hizmetleri A.Ş. olarak, kullanıcılarımızın gizliliğine saygı duymaktayız ve kişisel verilerinizin korunmasına büyük önem vermekteyiz. Bu Gizlilik Politikası, www.exportcity.com alan adlı internet sitemiz ve mobil uygulamamız üzerinden yürütülen hizmetlerde, kullanıcılarımızdan toplanan bilgilerin nasıl kullanıldığını ve korunduğunu açıklamaktadır.

1. Toplanan Veriler

Hizmetlerimizden faydalanmak için kayıt olduğunuzda veya sistemi kullandığınızda şu bilgiler toplanabilir:

• Ad, soyad, firma adı
• Telefon numarası, e-posta adresi
• IP adresi ve lokasyon verisi
• Cihaz bilgileri ve çerez verileri (cookie)
• Talep/teklif ve işlem geçmişi
• Ödeme bilgileri (kart verileri doğrudan ödeme sağlayıcı İyzico tarafından alınır)

2. Kişisel Verilerin Kullanım Amacı

Toplanan kişisel veriler aşağıdaki amaçlarla kullanılmaktadır:

• Kullanıcı kaydı oluşturmak ve üyeliği yönetmek
• Talepleri ve teklifleri yayınlamak ve eşleştirmek
• Sipariş ve ödeme süreçlerini yürütmek
• Teknik destek sağlamak
• Yasal yükümlülükleri yerine getirmek
• Kullanıcı deneyimini iyileştirmek ve güvenliği sağlamak

3. Verilerin Paylaşımı

Exportcity, kullanıcı verilerini aşağıda belirtilen istisnalar dışında hiçbir şekilde üçüncü kişilerle paylaşmaz:

• Yasal yükümlülükler kapsamında resmi kurum talepleri
• Ödeme işlemleri için İyzico gibi altyapı sağlayıcıları
• Sunucu hizmetleri aldığımız firmalar
• Kullanıcının açık rızası bulunan durumlar

4. Çerezler (Cookies)

Web sitemiz ve uygulamamızda kullanıcı deneyimini geliştirmek ve analiz yapmak amacıyla çerezler kullanılmaktadır. Tarayıcı ayarlarınızdan çerez kullanımını reddedebilirsiniz; ancak bu, bazı özelliklerin çalışmasını engelleyebilir.

5. Veri Güvenliği

Verilerinizin güvenliği için:

• SSL (güvenli bağlantı sertifikası)
• Güvenli sunucular
• Yetkisiz erişime karşı şifreleme sistemleri kullanılmaktadır.

Ödeme bilgileri ise sadece İyzico tarafından alınmakta olup, Exportcity sunucularında saklanmamaktadır.

6. Haklarınız

KVKK kapsamında:

• Verilerinizi öğrenme
• Düzeltme ve silme talebi
• İşlemenin durdurulmasını isteme haklarına sahipsiniz.

<NAME_EMAIL> adresine iletebilirsiniz.

7. Değişiklikler

Exportcity, işbu Gizlilik Politikası üzerinde her zaman değişiklik yapma hakkına sahiptir. Güncellenmiş politika www.exportcity.com üzerinden yayımlanarak yürürlüğe girer.
  `;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold">{t('footer.terms')}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            aria-label="Close"
          >
            <X size={24} />
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto p-6">
          <pre className="whitespace-pre-wrap text-sm leading-relaxed text-gray-700">
            {termsContent}
          </pre>
        </div>
        
        <div className="flex justify-end gap-4 p-6 border-t">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            {t('close')}
          </button>
          {onAccept && (
            <button
              onClick={onAccept}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              {t('accept')}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default TermsModal;