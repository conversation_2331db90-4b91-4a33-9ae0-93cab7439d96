import React from "react";
import {
  Box,
  Image,
  Text,
  Stack,
  Heading,
  Badge,
  useColorModeValue,
  HStack,
  Icon,
  Flex,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { FaEye, FaMapMarkerAlt, FaPhone, FaEnvelope, FaBuilding, FaHandshake } from "react-icons/fa";
import { IStore } from "../../types/store";
import ImagePlaceholder from "../ImagePlaceholder";
import { useTranslation } from "react-i18next";

const MotionBox = motion(Box);

interface StoreCardProps {
  store: IStore;
  isAuthenticated: boolean;
  showFullDescription?: boolean;
  showLocationDetails?: boolean;
  isFeatured?: boolean;
}

const StoreCard: React.FC<StoreCardProps> = ({
  store,
  isAuthenticated,
  showFullDescription = false,
  showLocationDetails = false,
  isFeatured = false,
}) => {
  const { t } = useTranslation("storeCard");
  const bgColor = useColorModeValue("white", "gray.700");
  const borderColor = useColorModeValue("gray.200", "gray.600");
  const textColor = useColorModeValue("gray.600", "gray.300");

  const logoSrc = store.logo ? `${import.meta.env.VITE_SOCKET_URL}/${store.logo}` : undefined;
  const coverSrc = store.coverImage ? `${import.meta.env.VITE_SOCKET_URL}/${store.coverImage}` : undefined;
  // Get view count from store data
  return (
    <MotionBox
      as={Link}
      to={`/stores/${store._id}`}
      borderWidth="1px"
      borderRadius="lg"
      overflow="hidden"
      bg={bgColor}
      borderColor={borderColor}
      transition="0.3s"
      _hover={{
        transform: "translateY(-8px)",
        boxShadow: "xl",
        borderColor: "blue.500"
      }}
      position="relative"
      height="100%"
      display="flex"
      flexDirection="column"
      whileHover={{
        y: -8,
        boxShadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
    >
      {/* Featured badge */}
      {isFeatured && (
        <Badge
          position="absolute"
          top={2}
          left={2}
          zIndex={2}
          colorScheme="purple"
          fontSize="xs"
          textTransform="uppercase"
          px={2}
          py={1}
          borderRadius="full"
        >
          {t("featured")}
        </Badge>
      )}

      {/* View count badge */}
      <Badge
        position="absolute"
        top={2}
        right={2}
        zIndex={2}
        bg="rgba(0, 0, 0, 0.7)"
        color="white"
        px={2}
        py={1}
        borderRadius="md"
        fontSize="xs"
        display="flex"
        alignItems="center"
        gap={1}
        title={t("productViews", "Total product views")}
      >
        <FaEye /> {typeof store.viewCount === 'number' ? store.viewCount : 0} {t("views", "views")}
      </Badge>

      {/* Cover Image */}
      <Box
        position="relative"
        height="180px"
        width="100%"
      >
        <Image
          src={coverSrc || logoSrc}
          alt={store.name}
          height="100%"
          width="100%"
          objectFit="cover"
          fallback={
            <ImagePlaceholder
              width="100%"
              height="100%"
              text={t("noCover", "No Cover Image")}
            />
          }
        />

        {/* Logo (Small image on the right) */}
        {logoSrc && (
          <Box
            position="absolute"
            bottom={4}
            right={4}
            width="50px"
            height="50px"
            borderRadius="md"
            overflow="hidden"
            borderWidth="2px"
            borderColor="white"
            boxShadow="md"
          >
            <Image
              src={logoSrc}
              alt={`${store.name} logo`}
              width="100%"
              height="100%"
              objectFit="cover"
              fallback={
                <ImagePlaceholder
                  width="100%"
                  height="100%"
                  text=""
                />
              }
            />
          </Box>
        )}
      </Box>

      {/* Content */}
      <Box p={4} flex="1" display="flex" flexDirection="column">
        <Stack spacing={3} flex="1">
          <Heading as="h3" size="md" noOfLines={1}>
            {store.name}
          </Heading>

          {/* Display store type */}
          <HStack>
            <Icon as={store.type === 'broker' ? FaHandshake : FaBuilding} color={store.type === 'broker' ? 'purple.500' : 'blue.500'} />
            <Badge colorScheme={store.type === 'broker' ? 'purple' : 'blue'} fontSize="xs" px={2} py={0.5} borderRadius="md">
              {t(`types.${store.type || 'company'}`)}
            </Badge>
          </HStack>
          
          {/* Display location information */}
          <HStack fontSize="xs" color={textColor}>
            <Icon as={FaMapMarkerAlt} />
            <Text>
              {store.location?.city && store.location?.country 
                ? `${store.location.city}, ${store.location.country}` 
                : store.location?.city 
                ? store.location.city 
                : store.location?.country 
                ? store.location.country 
                : store.address || t('noLocation')}
            </Text>
          </HStack>

          {(isAuthenticated || showFullDescription) && (
            <Text color={textColor} noOfLines={showFullDescription ? undefined : 2} fontSize="sm">
              {store.description}
            </Text>
          )}

          {showLocationDetails && isAuthenticated && (
            <Stack spacing={2} mt={2}>
              {store.address && (
                <HStack fontSize="xs" color={textColor}>
                  <Icon as={FaMapMarkerAlt} />
                  <Text>{store.address}</Text>
                </HStack>
              )}
              {store.phone && (
                <HStack fontSize="xs" color={textColor}>
                  <Icon as={FaPhone} />
                  <Text>{store.phone}</Text>
                </HStack>
              )}
              {store.email && (
                <HStack fontSize="xs" color={textColor}>
                  <Icon as={FaEnvelope} />
                  <Text>{store.email}</Text>
                </HStack>
              )}
            </Stack>
          )}
        </Stack>

        {/* Store status badges */}
        <Flex mt={3} wrap="wrap" gap={2}>
          {store.isActive && (
            <Badge colorScheme="green" fontSize="xs" px={2} py={1} borderRadius="full">
              {t("active")}
            </Badge>
          )}
          {store.isApproved && (
            <Badge colorScheme="blue" fontSize="xs" px={2} py={1} borderRadius="full">
              {t("approved")}
            </Badge>
          )}
        </Flex>
      </Box>
    </MotionBox>
  );
};

export default StoreCard;
