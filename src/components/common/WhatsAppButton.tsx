import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Toolt<PERSON> } from "@chakra-ui/react";
import { FaHeadset } from "react-icons/fa";
import { useTranslation } from "react-i18next";

interface WhatsAppButtonProps {
  // No props needed now that we're redirecting to internal page
}

const WhatsAppButton: React.FC<WhatsAppButtonProps> = () => {
  const { t } = useTranslation("common");

  const supportUrl = "/profile/tickets";

  return (
    <Tooltip label={t("common:whatsapp.tooltip")} placement="left">
      <Link
        href={supportUrl}
        isExternal
        position="fixed"
        bottom="4"
        right="4"
        zIndex={999}
      >
        <IconButton
          aria-label="Live Support"
          icon={<FaHeadset />}
          colorScheme="blue"
          rounded="full"
          size="lg"
          shadow="lg"
          _hover={{ transform: "scale(1.1)" }}
          transition="all 0.2s"
        />
      </Link>
    </Tooltip>
  );
};

export default WhatsAppButton;
