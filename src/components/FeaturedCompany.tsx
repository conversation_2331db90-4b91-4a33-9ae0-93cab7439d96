import React, { useRef } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { MapPin, ExternalLink, CheckCircle } from "lucide-react";
import { IStore } from "../types/store";

interface FeaturedCompanyProps {
  selectedStore: any;
  stores: IStore[];
  onStoreSelect: (store: IStore) => void;
}

const FeaturedCompany: React.FC<FeaturedCompanyProps> = ({
  selectedStore,
  stores,
  onStoreSelect,
}) => {
  const { t } = useTranslation("featuredCompany");
  const storeRef = useRef<HTMLDivElement>(null);

  if (!selectedStore) return null;

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-10">
          {t("title")}
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Featured Company Details - Takes up 2/3 on large screens */}
          <div className="lg:col-span-2">
            <div
              className="bg-white rounded-xl overflow-hidden shadow-lg"
              ref={storeRef}
            >
              <div className="h-80 w-full overflow-hidden bg-gray-100">
                {selectedStore.coverImage ? (
                  <img
                    src={`${import.meta.env.VITE_SOCKET_URL}/${selectedStore.coverImage}`}
                    alt={t("image.coverAlt", { name: selectedStore.name })}
                    className="w-full h-full object-cover"
                    onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                      // Prevent infinite loop by removing the onerror handler
                      e.currentTarget.onerror = null;
                      // Hide the image and show a fallback icon
                      e.currentTarget.style.display = 'none';
                      const parent = e.currentTarget.parentElement;
                      if (parent) {
                        const fallback = document.createElement('div');
                        fallback.className = 'flex h-full w-full items-center justify-center';
                        fallback.innerHTML = `<svg class="h-16 w-16 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>`;
                        parent.appendChild(fallback);
                      }
                    }}
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center">
                    <svg className="h-16 w-16 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                      <polyline points="9 22 9 12 15 12 15 22"></polyline>
                    </svg>
                  </div>
                )}
              </div>

              <div className="relative p-6">
                <div className="absolute -top-10 left-6 w-20 h-20 rounded-full border-4 border-white overflow-hidden bg-white shadow-md">
                  {selectedStore.logo ? (
                    <img
                      src={`${import.meta.env.VITE_SOCKET_URL}/${selectedStore.logo}`}
                      alt={t("image.logoAlt", { name: selectedStore.name })}
                      className="w-full h-full object-cover"
                      onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                        // Prevent infinite loop by removing the onerror handler
                        e.currentTarget.onerror = null;
                        // Hide the image and show a fallback icon
                        e.currentTarget.style.display = 'none';
                        const parent = e.currentTarget.parentElement;
                        if (parent) {
                          const fallback = document.createElement('div');
                          fallback.className = 'flex h-full w-full items-center justify-center';
                          fallback.innerHTML = `<svg class="h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>`;
                          parent.appendChild(fallback);
                        }
                      }}
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center">
                      <svg className="h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </div>
                  )}
                </div>

                <div className="pt-8">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-2xl font-bold text-gray-900">
                      {selectedStore.name}
                    </h3>

                    {selectedStore.verified && (
                      <span className="inline-flex items-center rounded-full bg-blue-50 px-2.5 py-1 text-xs font-medium text-blue-700">
                        <CheckCircle className="mr-1 h-3.5 w-3.5" />
                        {t("verified")}
                      </span>
                    )}
                  </div>

                  <div className="flex items-center text-gray-500 mb-4">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span className="text-sm">
                      {selectedStore.city}, {selectedStore.country}
                    </span>
                  </div>

                  <p className="text-gray-600 mb-6 line-clamp-3">
                    {selectedStore.description || t("noDescription")}
                  </p>

                  <div className="flex flex-wrap gap-2 mb-6">
                    {selectedStore.categories && selectedStore.categories.map((category: any) => (
                      <span
                        key={category._id || category}
                        className="inline-flex rounded-full bg-gray-100 px-3 py-1 text-sm font-medium text-gray-800"
                      >
                        {category.name || category}
                      </span>
                    ))}
                  </div>

                  <Link
                    to={`/stores/${selectedStore._id}`}
                    className="inline-flex items-center gap-2 text-primary hover:text-primary-dark transition-colors font-medium"
                  >
                    <span>{t("viewCompany")}</span>
                    <ExternalLink className="h-4 w-4" />
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Company List - Takes up 1/3 on large screens */}
          <div className="lg:h-[calc(100vh-200px)] lg:sticky lg:top-24 bg-white rounded-xl shadow-md p-6 overflow-hidden">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              {t("otherCompanies")}
            </h3>

            <div className="overflow-y-auto max-h-[calc(100%-3rem)]">
              <div className="space-y-3">
                {stores.map((store: any) => (
                  <div
                    key={store._id}
                    className={`p-3 rounded-lg cursor-pointer transition-all duration-200 ${selectedStore._id === store._id
                        ? 'bg-blue-50 border-l-4 border-blue-500'
                        : 'bg-gray-50 hover:bg-gray-100'
                      }`}
                    onClick={() => onStoreSelect(store)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0 w-12 h-12 rounded-full overflow-hidden bg-white">
                        {store.logo ? (
                          <img
                            src={`${import.meta.env.VITE_SOCKET_URL}/${store.logo}`}
                            alt={t("image.logoAlt", { name: store.name })}
                            className="w-full h-full object-cover"
                            onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                              // Prevent infinite loop by removing the onerror handler
                              e.currentTarget.onerror = null;
                              // Hide the image and show a fallback icon
                              e.currentTarget.style.display = 'none';
                              const parent = e.currentTarget.parentElement;
                              if (parent) {
                                const fallback = document.createElement('div');
                                fallback.className = 'flex h-full w-full items-center justify-center';
                                fallback.innerHTML = `<svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>`;
                                parent.appendChild(fallback);
                              }
                            }}
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center">
                            <svg className="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                              <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                          </div>
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {store.name}
                          </p>
                          {store.verified && (
                            <CheckCircle className="h-4 w-4 text-blue-500" />
                          )}
                        </div>
                        <p className="text-xs text-gray-500 truncate">
                          {store.city}, {store.country}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturedCompany;