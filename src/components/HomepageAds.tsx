import React from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { ExternalLink } from "lucide-react";
import { api } from "@/api";
import { getStoreId } from "@/utils/helpers";
import Carousel from "./Carousel";

interface HomepageAdsProps {
  ads: any[];
  onImageClick: (ad: any) => void;
}

const HomepageAds: React.FC<HomepageAdsProps> = ({ ads, onImageClick }) => {
  const { t } = useTranslation("home");
  const navigate = useNavigate();

  if (!ads || ads.length === 0) {
    return null;
  }

  return (
    <div className="w-full py-16">
      <div className="full-width-container">
        <div className="w-full max-w-[1920px] mx-auto bg-white">
          <div className="flex items-center justify-between mb-12 px-4 md:px-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900">{t("homepageAds.title")}</h2>
              <p className="mt-3 text-lg text-gray-600">{t("homepageAds.subtitle")}</p>
            </div>
          </div>

          <div className="px-4 md:px-12">
            <Carousel
              items={ads.map((ad) => (
                <div
                  key={ad._id}
                  className="rounded-xl overflow-hidden shadow-lg transition-all duration-300 bg-white border border-gray-100 hover:translate-y-[-8px] hover:shadow-2xl cursor-pointer"
                  onClick={async () => {
                    try {
                      const response = await api.post(`/home-ads/click/${ad._id}`);
                      const { itemId } = response.data;

                      if (itemId) {
                        navigate(`/items/${itemId}`);
                      } else if (ad.storeId) {
                        const storeId = getStoreId(ad.storeId);
                        navigate(`/stores/${storeId}`);
                      }
                    } catch (error) {
                      console.error("Failed to record click:", error);
                    }
                  }}
                >
                  <div className="relative">
                    <img
                      src={
                        import.meta.env.VITE_SOCKET_URL +
                        (ad.image.startsWith('/') ? '' : '/') +
                        '/uploads/' +
                        (ad.image.startsWith('/') ? '' : '/') +
                        ad.image.replace(/^\/+/, '')
                      }
                      alt={ad.title}
                      className="h-[200px] md:h-[220px] w-full object-cover cursor-zoom-in transition-transform duration-500 hover:scale-105"
                      onClick={(e) => {
                        e.stopPropagation();
                        onImageClick(ad);
                      }}
                    />

                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent pointer-events-none"></div>

                    <div className="absolute top-4 right-4 px-3 py-1 rounded-full bg-gradient-to-r from-blue-600 to-teal-600 text-white font-medium uppercase tracking-wide text-xs shadow-md">
                      {t("homepageAds.featured")}
                    </div>
                  </div>

                  <div className="p-4 md:p-5 bg-white">
                    <h3 className="text-base md:text-lg font-semibold text-gray-900 line-clamp-2">
                      {ad.title}
                    </h3>

                    <div className="flex justify-between items-center w-full pt-2">
                      <span className="text-sm text-gray-600">
                        {t("homepageAds.clickToView")}
                      </span>
                      <button className="text-sm px-3 py-1 rounded-full border border-blue-500 text-blue-500 inline-flex items-center space-x-1 hover:bg-gradient-to-r hover:from-blue-600 hover:to-teal-600 hover:text-white hover:border-transparent transition-colors">
                        <span>{t("homepageAds.view")}</span>
                        <ExternalLink className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
              slidesPerView={{ base: 1, sm: 2, md: 3, lg: 3, xl: 4 }}
              spaceBetween={24}
              height={{ base: "350px", md: "400px" }}
              autoplayDelay={4000}
              autoplay={true}
              loop={true}
            />
          </div>

          <div className="mt-8 text-center md:hidden">
            <button
              onClick={() => navigate('/featured')}
              className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
            >
              <span>{t("homepageAds.viewAll")}</span>
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomepageAds;