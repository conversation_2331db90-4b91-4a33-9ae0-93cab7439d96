import React, { ReactNode } from "react";
import { Box, useBreakpointValue, useColorModeValue } from "@chakra-ui/react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "./Carousel.css";

interface CarouselProps {
  items: ReactNode[];
  autoplay?: boolean;
  showNavigation?: boolean;
  showPagination?: boolean;
  slidesPerView?: number | { [key: string]: number };
  spaceBetween?: number;
  loop?: boolean;
  autoplayDelay?: number;
  height?: string | { [key: string]: string };
}

const Carousel: React.FC<CarouselProps> = ({
  items,
  autoplay = true,
  showNavigation = true,
  showPagination = true,
  slidesPerView = 1,
  spaceBetween = 30,
  loop = true,
  autoplayDelay = 5000,
  height = "400px",
}) => {
  const isMobile = useBreakpointValue({ base: true, md: false });
  const responsiveHeight =
    typeof height === "string" ? height : useBreakpointValue(height);
  const responsiveSlidesPerView =
    typeof slidesPerView === "number"
      ? slidesPerView
      : useBreakpointValue(slidesPerView);
  const arrowBg = useColorModeValue("whiteAlpha.700", "blackAlpha.700");
  const arrowHoverBg = useColorModeValue("whiteAlpha.900", "blackAlpha.900");

  return (
    <Box
      width="100%"
      overflow="hidden"
      position="relative"
      sx={{
        "--swiper-navigation-size": "24px",
        "--swiper-navigation-color": useColorModeValue("#1A202C", "#FFFFFF"),
        "--swiper-pagination-color": useColorModeValue("#1A202C", "#FFFFFF"),
        ".swiper-button-prev, .swiper-button-next": {
          width: "40px",
          height: "40px",
          backgroundColor: arrowBg,
          borderRadius: "full",
          transition: "all 0.3s ease",
          "&:hover": {
            backgroundColor: arrowHoverBg,
          },
          "&::after": {
            fontSize: "16px",
            fontWeight: "bold",
          },
        },
        ".swiper-button-prev": {
          left: "10px",
        },
        ".swiper-button-next": {
          right: "10px",
        },
        ".swiper-pagination-bullet": {
          width: "8px",
          height: "8px",
          opacity: 0.5,
          "&-active": {
            opacity: 1,
            transform: "scale(1.2)",
          },
        },
      }}
    >
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        navigation={showNavigation && !isMobile}
        pagination={showPagination ? { clickable: true } : false}
        autoplay={autoplay ? { delay: autoplayDelay } : false}
        loop={loop}
        spaceBetween={spaceBetween}
        slidesPerView={responsiveSlidesPerView}
        style={{ width: "100%", height: responsiveHeight }}
      >
        {items.map((item, index) => (
          <SwiperSlide key={index}>
            <Box
              height="100%"
              width="100%"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {item}
            </Box>
          </SwiperSlide>
        ))}
      </Swiper>
    </Box>
  );
};

export default Carousel;
