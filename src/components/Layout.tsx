import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Header from './Header';
import Footer from './Footer';
import LiveChat from './LiveChat';
import {
  Home,
  User,
  LogIn,
  UserPlus,
  Package,
  Plus,
  MessageSquare,
  Bell,
  Settings
} from 'lucide-react';
import { isLoggedIn } from '../data/testUser';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const loggedIn = isLoggedIn();

  const mobileNavItems = loggedIn ? [
    { icon: Home, label: '<PERSON> Sayfa', path: '/' },
    { icon: Package, label: '<PERSON>rün<PERSON>', path: '/products' },
    { icon: Plus, label: 'Ekle', path: '/products/add' },
    { icon: MessageSquare, label: '<PERSON>jlar', path: '/messages', badge: 3 },
    { icon: Settings, label: '<PERSON><PERSON><PERSON>', path: '/settings' }
  ] : [
    { icon: Home, label: '<PERSON> <PERSON><PERSON>', path: '/' },
    { icon: User, label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', path: '/about' },
    { icon: LogIn, label: 'Giriş Yap', path: '/login' },
    { icon: UserPlus, label: 'Üye Ol', path: '/register' }
  ];

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Header />

      {/* Main Content */}
      <main className="flex-grow pb-[72px] md:pb-0">
        {children}
      </main>

      {/* Live Chat */}
      <LiveChat />

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Layout;