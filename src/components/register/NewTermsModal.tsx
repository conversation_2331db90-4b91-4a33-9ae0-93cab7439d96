import React from "react";
import { Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { terms } from "../../locales/terms";
import Modal from "../ui/Modal";

interface TermsModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
}

const NewTermsModal: React.FC<TermsModalProps> = ({ isOpen, onClose, onAccept }) => {
  const { t, i18n } = useTranslation(["terms", "common"]);
  const currentLanguage = i18n.language.split("-")[0];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={t("terms:title")}
    >
      <div className="space-y-4">
        <Text whiteSpace="pre-wrap" className="text-sm text-gray-700 max-h-[60vh] overflow-y-auto p-2">
          {terms[currentLanguage as keyof typeof terms]}
        </Text>
        
        <div className="flex justify-end space-x-3 mt-4">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
          >
            {t("common:cancel")}
          </button>
          <button
            onClick={onAccept}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover transition-colors"
          >
            {t("terms:accept")}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default NewTermsModal;
