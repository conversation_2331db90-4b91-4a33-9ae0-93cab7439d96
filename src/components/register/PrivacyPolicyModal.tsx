import React from 'react';
import { useTranslation } from 'react-i18next';
import { X } from 'lucide-react';

interface PrivacyPolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAccept: () => void;
}

const PrivacyPolicyModal: React.FC<PrivacyPolicyModalProps> = ({
  isOpen,
  onClose,
  onAccept,
}) => {
  const { t } = useTranslation('privacyPolicy');

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-2xl font-bold">{t('title')}</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            aria-label="Close"
          >
            <X size={24} />
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto p-6">
          <p className="mb-6">{t('intro')}</p>

          <section className="mb-6">
            <h3 className="text-xl font-semibold mb-3">{t('sections.collectedData.title')}</h3>
            <p className="mb-3">{t('sections.collectedData.description')}</p>
            <ul className="list-disc pl-6 space-y-1">
              {(t('sections.collectedData.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </section>

          <section className="mb-6">
            <h3 className="text-xl font-semibold mb-3">{t('sections.dataUsage.title')}</h3>
            <p className="mb-3">{t('sections.dataUsage.description')}</p>
            <ul className="list-disc pl-6 space-y-1">
              {(t('sections.dataUsage.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </section>

          <section className="mb-6">
            <h3 className="text-xl font-semibold mb-3">{t('sections.dataSharing.title')}</h3>
            <p className="mb-3">{t('sections.dataSharing.description')}</p>
            <ul className="list-disc pl-6 space-y-1">
              {(t('sections.dataSharing.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </section>

          <section className="mb-6">
            <h3 className="text-xl font-semibold mb-3">{t('sections.cookies.title')}</h3>
            <p>{t('sections.cookies.description')}</p>
          </section>

          <section className="mb-6">
            <h3 className="text-xl font-semibold mb-3">{t('sections.dataSecurity.title')}</h3>
            <p className="mb-3">{t('sections.dataSecurity.description')}</p>
            <ul className="list-disc pl-6 space-y-1">
              {(t('sections.dataSecurity.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
            <p className="mt-3">{t('sections.dataSecurity.payment')}</p>
          </section>

          <section className="mb-6">
            <h3 className="text-xl font-semibold mb-3">{t('sections.yourRights.title')}</h3>
            <p className="mb-3">{t('sections.yourRights.description')}</p>
            <ul className="list-disc pl-6 space-y-1">
              {(t('sections.yourRights.items', { returnObjects: true }) as string[]).map((item: string, index: number) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
            <p className="mt-3">{t('sections.yourRights.contact')}</p>
          </section>

          <section className="mb-6">
            <h3 className="text-xl font-semibold mb-3">{t('sections.changes.title')}</h3>
            <p>{t('sections.changes.description')}</p>
          </section>
        </div>
        
        <div className="flex justify-end gap-4 p-6 border-t">
          <button
            onClick={onClose}
            className="px-6 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            {t('close', { ns: 'common' })}
          </button>
          <button
            onClick={onAccept}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            {t('accept', { ns: 'common' })}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicyModal;