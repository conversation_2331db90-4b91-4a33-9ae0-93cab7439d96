import React from "react";
import { Box, Text } from "@chakra-ui/react";
import { useTranslation } from "react-i18next";

interface MaskedImageProps {
  width?: string | number;
  height?: string | number;
  circular?: boolean;
}

const MaskedImage: React.FC<MaskedImageProps> = ({
  width = "100%",
  height = "200px",
  circular = false,
}) => {
  const { t } = useTranslation();

  return (
    <Box
      width={width}
      height={height}
      display="flex"
      alignItems="center"
      justifyContent="center"
      bg="gray.200"
      borderRadius={circular ? "full" : "md"}
    >
      <Text color="gray.600" fontSize="sm" textAlign="center">
        {t("hiddenImage", { defaultValue: "Hidden Image" })}
      </Text>
    </Box>
  );
};

export default MaskedImage;
