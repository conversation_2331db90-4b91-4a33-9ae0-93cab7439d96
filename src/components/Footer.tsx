import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Linkedin, Instagram, Youtube, GitBranch as BrandTiktok, Mail, Phone, MapPin, Globe, Shield, TrendingUp, Users, ExternalLink } from 'lucide-react';

const Footer = () => {
  const navigate = useNavigate();

  const quickLinks = [
    { name: 'Hakkımızda', path: '/about' },
    { name: 'Firmalar', path: '/companies' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', path: '/representatives' },
    { name: '<PERSON><PERSON><PERSON><PERSON> <PERSON> Hizmetler', path: '/products' },
    { name: 'SSS', path: '/faq' }
  ];

  const whyUs = [
    { name: 'Global Erişim', path: '/reasons/global-access', icon: Globe },
    { name: 'Güvenli Platform', path: '/reasons/secure-platform', icon: Shield },
    { name: '<PERSON>ızlı Büyüme', path: '/reasons/fast-growth', icon: TrendingUp },
    { name: '<PERSON><PERSON>ler<PERSON>', path: '/reasons/partnerships', icon: Users }
  ];

  const packages = [
    { name: 'Üyelik Paketleri', path: '/packages' },
    { name: 'Tasarım Paketleri', path: '/design-packages' },
    { name: 'İş Ortaklığı', path: '/partnership' }
  ];

  return (
    <footer className="bg-[#1A1A1A] text-white">
      {/* Main Footer */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-primary">İletişim</h3>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-primary flex-shrink-0 mt-1" />
                <p className="text-gray-400 hover:text-primary transition-colors">
                  İstanbul, Türkiye
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-primary" />
                <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-primary transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-primary" />
                <a href="tel:+905406668000" className="text-gray-400 hover:text-primary transition-colors">
                  0540 666 8000
                </a>
              </div>
            </div>

            {/* Social Media */}
            <div className="mt-6">
              <div className="flex items-center space-x-4">
                <a 
                  href="https://linkedin.com/company/e-exportcity" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <Linkedin className="h-5 w-5" />
                </a>
                <a 
                  href="https://instagram.com/e-exportcity" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <Instagram className="h-5 w-5" />
                </a>
                <a 
                  href="https://tiktok.com/@e-exportcity" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <BrandTiktok className="h-5 w-5" />
                </a>
                <a 
                  href="https://youtube.com/@e-exportcity" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-primary transition-colors"
                >
                  <Youtube className="h-5 w-5" />
                </a>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-primary">Hızlı Erişim</h3>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <button
                    onClick={() => navigate(link.path)}
                    className="text-gray-400 hover:text-primary transition-colors"
                  >
                    {link.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Why Us */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-primary">Neden E-exportcity?</h3>
            <ul className="space-y-3">
              {whyUs.map((item, index) => (
                <li key={index}>
                  <button
                    onClick={() => navigate(item.path)}
                    className="flex items-center space-x-2 text-gray-400 hover:text-primary transition-colors group"
                  >
                    <item.icon className="h-5 w-5" />
                    <span>{item.name}</span>
                    <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
                  </button>
                </li>
              ))}
            </ul>
          </div>

          {/* Packages */}
          <div>
            <h3 className="text-lg font-semibold mb-6 text-primary">Paketler</h3>
            <ul className="space-y-3">
              {packages.map((pkg, index) => (
                <li key={index}>
                  <button
                    onClick={() => navigate(pkg.path)}
                    className="text-gray-400 hover:text-primary transition-colors"
                  >
                    {pkg.name}
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between">
            <div className="text-sm text-gray-400 hover:text-primary transition-colors">
              © 2024 E-exportcity. Tüm hakları saklıdır.
            </div>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <button 
                onClick={() => navigate('/privacy')}
                className="text-sm text-gray-400 hover:text-primary transition-colors"
              >
                Gizlilik Politikası
              </button>
              <button 
                onClick={() => navigate('/terms')}
                className="text-sm text-gray-400 hover:text-primary transition-colors"
              >
                Kullanım Koşulları
              </button>
              <button 
                onClick={() => navigate('/cookies')}
                className="text-sm text-gray-400 hover:text-primary transition-colors"
              >
                Çerez Politikası
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;