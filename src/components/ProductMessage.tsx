import React, { useState, useEffect } from "react";
import {
  Button,
  Textarea,
  Box,
  useToast,
  Text,
  VStack,
} from "@chakra-ui/react";
import { useSocket } from "../context/SocketContext";
import { MessageCircle } from "lucide-react";
import { IMessage, IMessageUser } from "../types/message";
import { useAuthCheck } from "@/hooks/useAuthCheck";

interface ProductMessageProps {
  productId: string;
  sellerId: IMessageUser;
  productName: string;
  onMessageSent?: () => void;
}

interface MessageData {
  content: string;
  sender: string;
  timestamp: Date;
  read?: boolean;
}

interface ReadReceiptData {
  messageId: string;
  readBy: string;
  timestamp: Date;
}

const ProductMessage: React.FC<ProductMessageProps> = ({
  productId,
  sellerId,
  productName,
  onMessageSent,
}) => {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { socket } = useSocket();
  const toast = useToast();
  const { user: tokenData } = useAuthCheck();

  useEffect(() => {
    if (!socket || !productId || !sellerId._id) return;

    // Wait for socket to be connected
    if (!socket.connected) {
      socket.connect();
    }

    const roomId = `${tokenData?._id}-${sellerId._id}-${productId}`;
    socket.emit("joinProductRoom", { productId, roomId });

    socket.on("message:received", (messageData: MessageData) => {
      if (messageData.sender !== tokenData?._id) {
        setMessages((prev: any) => [...prev, messageData]);
      }
    });

    socket.on("message:error", (error: any) => {
      toast({
        title: "Error",
        description:
          error?.response?.data?.message ||
          error?.message ||
          "Failed to send message",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    });

    socket.on("messageReadReceipt", (data: ReadReceiptData) => {
      setMessages((prev: any) =>
        prev.map((msg: any) =>
          msg._id === data.messageId ? { ...msg, read: true } : msg,
        ),
      );
    });

    return () => {
      socket.off("message:received");
      socket.off("message:error");
      socket.off("messageReadReceipt");
      socket.emit("leaveProductRoom", { productId, roomId });
    };
  }, [socket, productId, sellerId._id, tokenData?._id]);

  const handleSendMessage = async () => {
    if (!message.trim() || !socket || !socket.connected) {
      toast({
        title: "Connection Error",
        description: "Please wait while we connect to the server",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    if (!tokenData?._id || !sellerId._id) {
      toast({
        title: "Authentication Error",
        description: "Please log in to send messages",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsLoading(true);

    try {
      const roomId = `${tokenData._id}-${sellerId._id}-${productId}`;
      socket.emit("message:send", {
        content: message,
        senderId: tokenData._id,
        recipientId: sellerId._id,
        productId,
        roomId,
        source: "socket",
      });

      setMessages((prev: any) => [
        ...prev,
        {
          content: message,
          senderId: tokenData._id,
          recipientId: sellerId._id,
          productId,
          roomId,
          createdAt: new Date(),
          read: false,
        },
      ]);

      setMessage("");
      if (onMessageSent) {
        onMessageSent();
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description:
          error?.response?.data?.message ||
          error?.message ||
          "Failed to send message",
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <VStack spacing={4} align="stretch">
      <Box>
        <Text fontWeight="bold">
          To: {sellerId.firstName} {sellerId.lastName}
        </Text>
        <Text fontSize="sm" color="gray.500">
          Product: {productName}
        </Text>
      </Box>

      <Box
        flex={1}
        overflowY="auto"
        maxH="300px"
        p={4}
        bg="gray.50"
        borderRadius="md"
      >
        {messages.map((msg: any, index) => (
          <Box
            key={index}
            mb={2}
            p={2}
            bg={msg.senderId === tokenData?._id ? "blue.100" : "white"}
            borderRadius="md"
            alignSelf={
              msg.senderId === tokenData?._id ? "flex-end" : "flex-start"
            }
          >
            <Text>{msg.content}</Text>
            <Text fontSize="xs" color="gray.500">
              {new Date(msg.createdAt).toLocaleTimeString()}
              {msg.read && " ✓✓"}
            </Text>
          </Box>
        ))}
      </Box>

      <Box>
        <Textarea
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Type your message..."
          size="sm"
          resize="none"
          rows={3}
        />

        <Button
          mt={2}
          colorScheme="teal"
          leftIcon={<MessageCircle />}
          onClick={handleSendMessage}
          isLoading={isLoading}
          loadingText="Sending..."
          isDisabled={!message.trim()}
        >
          Send Message
        </Button>
      </Box>
    </VStack>
  );
};

export default ProductMessage;
