import React from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Globe2, Shield, TrendingUp, Users, ArrowRight } from 'lucide-react';
import FeatureShowcase from './FeatureShowcase';

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};

const IntroSections = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: Globe2,
      title: "Global Erişim",
      description: "150+ ülkede aktif iş ağına erişim imkanı",
      stats: ["150+ Ülke", "10K+ Aktif Üye", "25K+ İşlem"],
      link: "/reasons/global-access",
      color: "from-[#1CB3AF] to-[#0A2540]", // System green to navy blue gradient
      overlayColor: "from-black/30 to-transparent"
    },
    {
      icon: Shield,
      title: "Güvenli Platform",
      description: "Doğrulanmış firmalar ile güvenli ticaret imkanı",
      stats: ["SSL Güvenlik", "2FA Koruma", "%99.9 Uptime"],
      link: "/reasons/secure-platform",
      color: "from-[#1CB3AF] to-[#0A2540]", // System green to navy blue gradient
      overlayColor: "from-black/30 to-transparent"
    },
    {
      icon: TrendingUp,
      title: "Hızlı Büyüme",
      description: "İhracat potansiyelinizi maksimize edin",
      stats: ["%150 Büyüme", "50+ Yeni Pazar", "1K+ Başarı"],
      link: "/reasons/fast-growth",
      color: "from-[#1CB3AF] to-[#0A2540]", // System green to navy blue gradient
      overlayColor: "from-black/30 to-transparent"
    },
    {
      icon: Users,
      title: "İş Birlikleri",
      description: "Güvenilir iş ortaklarıyla çalışma imkanı",
      stats: ["5K+ Partner", "75+ Ülke", "%96 Memnuniyet"],
      link: "/reasons/partnerships",
      color: "from-[#1CB3AF] to-[#0A2540]", // System green to navy blue gradient
      overlayColor: "from-black/30 to-transparent"
    }
  ];

  return (
    <>
      {/* Why E-exportcity Section */}
      <div className="space-y-32 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div 
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Neden E-exportcity?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Global ticarette güvenilir çözüm ortağınız olarak yanınızdayız
            </p>
          </motion.div>

          <motion.div 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            {features.map((feature, index) => (
              <motion.button
                key={index}
                variants={itemVariants}
                onClick={() => navigate(feature.link)}
                className="relative overflow-hidden rounded-2xl hover:shadow-xl transition-all duration-300 group h-[400px]"
                whileHover={{ scale: 1.02 }}
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.color}`} />
                
                {/* Overlay gradient for better text contrast */}
                <div className={`absolute inset-0 bg-gradient-to-t ${feature.overlayColor}`} />

                <div className="relative z-10 h-full flex flex-col p-8">
                  <div>
                    <feature.icon className="h-12 w-12 text-white mb-6" />
                    <h3 className="text-2xl font-bold text-white mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-white/90 mb-6 text-lg">
                      {feature.description}
                    </p>
                  </div>

                  <div className="mt-auto">
                    <div className="grid grid-cols-1 gap-3 mb-6">
                      {feature.stats.map((stat, idx) => (
                        <div key={idx} className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 text-white font-medium">
                          {stat}
                        </div>
                      ))}
                    </div>
                    <div className="flex items-center text-white group-hover:translate-x-2 transition-transform">
                      <span className="text-lg font-medium">Detaylı Bilgi</span>
                      <ArrowRight className="ml-2 h-6 w-6" />
                    </div>
                  </div>
                </div>
              </motion.button>
            ))}
          </motion.div>
        </div>
      </div>

      {/* Feature Showcase */}
      <FeatureShowcase />
    </>
  );
};

export default IntroSections;