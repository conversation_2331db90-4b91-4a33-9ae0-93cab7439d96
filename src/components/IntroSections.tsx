import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Globe2,
  Shield,
  TrendingUp,
  Users,
  ArrowRight
} from 'lucide-react';
import FeatureShowcase from './FeatureShowcase';

const IntroSections: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('introSections');

  // Define consistent feature identifiers with their respective icons
  const featureConfig: Record<string, { icon: any, id: string }> = {
    0: { icon: Globe2, id: 'global-access' },
    1: { icon: Shield, id: 'secure-platform' },
    2: { icon: TrendingUp, id: 'fast-growth' },
    3: { icon: Users, id: 'partnerships' }
  };

  const features = (t('features', { returnObjects: true }) as any).map((feature: any, idx: number) => {
    // Find the matching feature config or use a default
    const config = featureConfig[idx];

    return {
      ...feature,
      icon: config.icon,
      id: config.id,
      color: "from-[#1CB3AF] to-[#0A2540]",
      overlayColor: "from-black/30 to-transparent"
    };
  });

  return (
    <>
      {/* Why E-exportcity Section */}
      <div className="space-y-32 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              {t('whyUs.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('whyUs.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature: any, index: any) => (
              <button
                key={index}
                onClick={() => {
                  console.log('Navigating to:', `/reasons/${feature.id}`); // Debug log
                  navigate(`/reasons/${feature.id}`);
                }}
                className="relative overflow-hidden rounded-2xl hover:shadow-xl transition-all duration-300 group h-[390px]"
              >
                {/* Background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.color}`} />

                {/* Overlay gradient for better text contrast */}
                <div className={`absolute inset-0 bg-gradient-to-t ${feature.overlayColor}`} />

                <div className="relative z-10 h-full flex flex-col p-8">
                  <div>
                    <feature.icon className="h-12 w-12 text-white mb-6" />
                    <h3 className="text-2xl font-bold text-white mb-3">
                      {feature.title}
                    </h3>
                    <p className="text-white/90 text-[17px] min-h-[70px]">
                      {feature.description}
                    </p>
                  </div>

                  <div className="mt-auto">
                    <div className="grid grid-cols-1 gap-3 mb-6">
                      {feature.stats.map((stat: string, idx: number) => (
                        <div key={idx} className="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 text-white font-medium">
                          {stat}
                        </div>
                      ))}
                    </div>
                    <div className="flex items-center text-white group-hover:translate-x-2 transition-transform">
                      <span className="text-lg font-medium">{t('detailedInfo')}</span>
                      <ArrowRight className="ml-2 h-6 w-6" />
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Feature Showcase */}
      <FeatureShowcase />
    </>
  );
};

export default IntroSections;