import React, { useEffect, useState } from "react";
import {
  Box,
  Image,
  Text,
  Heading,
  Badge,
  useColorModeValue,
  IconButton,
  Flex,
  Spinner,
  useBreakpointValue,
  HStack,
  Button,
  keyframes,
  useColorMode,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  useToast,
  Icon,
} from "@chakra-ui/react";
import { ChevronLeftIcon, ChevronRightIcon, ExternalLinkIcon, InfoIcon } from "@chakra-ui/icons";
import { useTranslation } from "react-i18next";
import { api, getUserPackages } from "@/api";
import { useNavigate } from "react-router-dom";
import { Store } from "lucide-react";
import { getStoreId } from "@/utils/helpers";

interface HomeAd {
  _id: string;
  itemId?: {
    _id: string;
    name: string;
    description: string;
    images: string[];
    type: string;
  };
  title: string;
  image: string;
  // storeId is populated by the backend as an object with _id, name, and owner properties
  storeId: {
    _id: string;
    name: string;
    owner: {
      firstName: string;
      lastName: string;
    };
  };
  startDate: string;
  endDate: string;
}

interface HomeAdsProps {
  ads?: HomeAd[];
  onImageClick: (imageUrl: string) => void;
}

const HomeAds: React.FC<HomeAdsProps> = ({ ads: propAds, onImageClick }) => {
  const { t } = useTranslation("home");
  const [ads, setAds] = useState<HomeAd[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [canViewFullAd, setCanViewFullAd] = useState(false);
  const { isOpen: isUpgradeModalOpen, onOpen: onUpgradeModalOpen, onClose: onUpgradeModalClose } = useDisclosure();
  const navigate = useNavigate();
  const toast = useToast();

  // More professional color scheme
  const bgColor = useColorModeValue("gray.50", "gray.900");

  useEffect(() => {
    if (propAds) {
      setAds(propAds);
      setIsLoading(false);
      return;
    }

    const fetchAds = async () => {
      try {
        const response = await api.get("/home-ads/active");
        setAds(response.data);
        setIsLoading(false);
      } catch (err) {
        setError(t("errors.loadingFailed"));
        setIsLoading(false);
      }
    };

    fetchAds();

    // Check if user has appropriate packages for viewing full ads
    const checkPackagePermissions = async () => {
      try {
        const packagesResponse: any = await getUserPackages();
        const userPackages = packagesResponse.packages || [];

        // First check addon packages, then standard packages
        const addonPackagesWithViewAccess = userPackages.filter(
          (pkg: any) => pkg.type === 'addon' && pkg.adsViewEnabled === true && pkg.status === 'active'
        );

        const standardPackagesWithViewAccess = userPackages.filter(
          (pkg: any) => pkg.type === 'standard' && pkg.adsViewEnabled === true && pkg.status === 'active'
        );

        // Check if any package (prioritize addon, then standard) allows viewing
        setCanViewFullAd(
          addonPackagesWithViewAccess.length > 0 || standardPackagesWithViewAccess.length > 0
        );

      } catch (packageError) {
        console.error('Error fetching user packages:', packageError);
        // Default to false if packages can't be checked
        setCanViewFullAd(false);
      }
    };

    checkPackagePermissions();
  }, [t, propAds]);

  useEffect(() => {
    if (ads.length > 1) {
      const timer = setInterval(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % ads.length);
      }, 5000);

      return () => clearInterval(timer);
    }
  }, [ads.length]);

  const handlePrevious = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + ads.length) % ads.length);
  };

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % ads.length);
  };

  const handleImageClick = (e: React.MouseEvent<HTMLImageElement>) => {
    e.preventDefault();

    const currentAd = ads[currentIndex];
    if (!currentAd) return;

    // Track the ad click
    handleAdClick(currentAd._id);
  };

  const handleViewButtonClick = (e: React.MouseEvent, ad: HomeAd) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('View button clicked for ad:', ad.title);

    // Handle the ad click (tracking + redirection if applicable)
    handleAdClick(ad._id);
  };

  const handleAdClick = (adId: string) => {
    // Track ad click with the correct route format
    api.post(`/home-ads/click/${adId}`)
      .then(response => {
        const { itemId } = response.data;
        const currentAd = ads.find(ad => ad._id === adId);
        if (!currentAd) {
          console.error('Ad not found in local state', adId);
          return;
        }

        console.log('Ad click tracked successfully', {
          adId,
          hasItemId: !!itemId,
          hasStoreId: !!currentAd.storeId,
          responseItemId: itemId,
          adItemId: currentAd.itemId?._id
        });

        // First priority: Use the itemId from the API response if available
        if (itemId) {
          console.log('Navigating to product page:', itemId);
          navigate(`/items/${itemId}`);
          return;
        }

        // Second priority: Use the itemId from the ad object if available
        if (currentAd.itemId && currentAd.itemId._id) {
          console.log('Navigating to product page from ad data:', currentAd.itemId._id);
          navigate(`/items/${currentAd.itemId._id}`);
          return;
        }

        // Third priority: If no product is associated, redirect to store page if storeId exists
        if (currentAd.storeId) {
          const storeId = getStoreId(currentAd.storeId);
          console.log('Navigating to store page with storeId:', storeId);
          navigate(`/stores/${storeId}`);
          return;
        }

        // Last resort: If no product or store, show the image or upgrade modal
        if (canViewFullAd) {
          // If no store association but user can view, show the image
          const imageUrl = import.meta.env.VITE_SOCKET_URL + '/uploads' + currentAd.image;
          console.log('Opening image viewer:', imageUrl);
          onImageClick(imageUrl);
        } else {
          // Show upgrade modal if no permission
          console.log('Opening upgrade modal - user cannot view full ad');
          onUpgradeModalOpen();
        }
      })
      .catch(error => {
        console.error("Error tracking ad click:", error);
        toast({
          title: t("errors.clickTrackingFailed"),
          status: "error",
          duration: 3000,
          isClosable: true,
        });

        // Fallback navigation even if tracking fails
        const currentAd = ads.find(ad => ad._id === adId);
        if (!currentAd) return;

        if (currentAd.itemId && currentAd.itemId._id) {
          navigate(`/items/${currentAd.itemId._id}`);
        } else if (currentAd.storeId) {
          const storeId = getStoreId(currentAd.storeId);
          navigate(`/stores/${storeId}`);
        }
      });
  };

  const slideUp = keyframes`
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  `;

  // Responsive dimensions
  const containerHeight = useBreakpointValue({
    base: "350px",
    sm: "400px",
    md: "450px",
    lg: "500px"
  });

  const buttonSize = useBreakpointValue({ base: "md", md: "lg" });
  const { colorMode } = useColorMode();

  // More professional gradient overlay with brand colors
  const gradientOverlay = colorMode === "dark"
    ? `linear-gradient(to top, rgba(26, 32, 44, 0.95),
      rgba(26, 32, 44, 0.7) 50%,
      rgba(45, 55, 72, 0.3))`
    : `linear-gradient(to top,
      rgba(44, 82, 130, 0.9),
      rgba(44, 82, 130, 0.6) 50%,
      rgba(44, 82, 130, 0.2))`;

  // Additional gradient for buttons and accents
  const accentGradient = colorMode === "dark"
    ? "linear-gradient(45deg, #3182CE, #319795)"
    : "linear-gradient(45deg, #2C5282, #285E61)";

  if (isLoading) {
    return (
      <Box
        borderRadius="xl"
        overflow="hidden"
        bg={bgColor}
        boxShadow="lg"
        height={containerHeight}
        display="flex"
        alignItems="center"
        justifyContent="center"
        position="relative"
      >
        <Spinner size="xl" color="blue.500" thickness="4px" speed="0.65s" />
      </Box>
    );
  }

  if (error || ads.length === 0) {
    return null;
  }

  const currentAd = ads[currentIndex];

  if (!currentAd) {
    return null;
  }

  return (
    <>
      {/* Upgrade Modal */}
      <Modal isOpen={isUpgradeModalOpen} onClose={onUpgradeModalClose} isCentered>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <Flex align="center">
              <InfoIcon color="blue.500" mr={2} />
              {t("upgradeRequired")}
            </Flex>
          </ModalHeader>
          <ModalBody>
            {t("upgradeForAdsView")}
          </ModalBody>
          <ModalFooter>
            <Button colorScheme="blue" mr={3} onClick={() => {
              onUpgradeModalClose();
              navigate("/packages");
            }}>
              {t("viewPackages")}
            </Button>
            <Button variant="ghost" onClick={onUpgradeModalClose}>
              {t("close")}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      <Box
        borderRadius="xl"
        overflow="hidden"
        bg={bgColor}
        boxShadow="lg"
        height={containerHeight}
        position="relative"
        onClick={(e) => handleImageClick(e as any)}
        cursor="pointer"
        transition="transform 0.3s ease, box-shadow 0.3s ease"
        _hover={{
          transform: "translateY(-5px)",
          boxShadow: "xl",
        }}
      >
        <Box position="relative" height="100%">
          {/* Background Image */}
          <Image
            src={
              currentAd.itemId && currentAd.itemId.images && currentAd.itemId.images.length > 0
                ? import.meta.env.VITE_SOCKET_URL + '/uploads' + currentAd.itemId.images[0]
                : import.meta.env.VITE_SOCKET_URL + '/uploads' + currentAd.image
            }
            alt={currentAd.title || currentAd.itemId?.name || "Advertisement"}
            objectFit="cover"
            width="100%"
            height="100%"
          />

          {/* Gradient Overlay */}
          <Box
            position="absolute"
            bottom="0"
            left="0"
            right="0"
            height="100%"
            bgGradient={gradientOverlay}
          />

          {/* Content */}
          <Box
            position="absolute"
            bottom="0"
            left="0"
            right="0"
            p={6}
            color="white"
            animation={`${slideUp} 0.6s ease-out`}
          >
            <Flex
              direction="column"
              maxW={{ base: "100%", md: "70%" }}
              height="100%"
              justifyContent="flex-end"
            >
              <Box mb={3}>
                <HStack spacing={2} mb={2}>
                  <Badge
                    colorScheme="blue"
                    fontSize="xs"
                    textTransform="uppercase"
                    px={2}
                    py={1}
                    borderRadius="md"
                    bgGradient={accentGradient}
                  >
                    {t("homepageAds.featured")}
                  </Badge>
                  <Badge
                    colorScheme="teal"
                    fontSize="xs"
                    textTransform="uppercase"
                    px={2}
                    py={1}
                    borderRadius="md"
                  >
                    {currentAd.itemId?.type === "product"
                      ? t("homepageAds.product")
                      : t("homepageAds.service")}
                  </Badge>
                </HStack>
                <Heading
                  as="h3"
                  size="lg"
                  fontWeight="bold"
                  mb={2}
                  textShadow="1px 1px 3px rgba(0,0,0,0.3)"
                >
                  {currentAd.title || currentAd.itemId?.name || t("homepageAds.featuredAd")}
                </Heading>
                <Text
                  fontSize="md"
                  noOfLines={3}
                  mb={4}
                  opacity={0.9}
                  textShadow="1px 1px 2px rgba(0,0,0,0.2)"
                >
                  {currentAd.itemId?.description || t("homepageAds.clickToViewAdDetails")}
                </Text>
                <HStack spacing={4}>
                  <Button
                    rightIcon={<ExternalLinkIcon />}
                    colorScheme="blue"
                    size={buttonSize}
                    onClick={(e) => handleViewButtonClick(e, currentAd)}
                    bgGradient={accentGradient}
                    _hover={{
                      bgGradient: accentGradient,
                      opacity: 0.9,
                      transform: "translateY(-2px)",
                    }}
                    boxShadow="md"
                  >
                    {currentAd.itemId
                      ? t("homepageAds.viewProduct")
                      : t("homepageAds.viewDetails")}
                  </Button>
                  {currentAd.storeId && (
                    <Button
                      rightIcon={<Icon as={Store} />}
                      colorScheme="teal"
                      size={buttonSize}
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                        // Navigate to store page with proper storeId handling
                        const storeId = getStoreId(currentAd.storeId);
                        console.log('Store button clicked, navigating to:', storeId, 'with data:', currentAd.storeId);
                        navigate(`/stores/${storeId}`);
                      }}
                      bgGradient={accentGradient}
                      _hover={{
                        bgGradient: accentGradient,
                        opacity: 0.9,
                        transform: "translateY(-2px)",
                      }}
                      boxShadow="md"
                    >
                      {t("homepageAds.visitStore")}
                    </Button>
                  )}
                </HStack>
              </Box>
            </Flex>
          </Box>

          {/* Navigation Arrows */}
          {ads.length > 1 && (
            <>
              <IconButton
                aria-label="Previous ad"
                icon={<ChevronLeftIcon boxSize={8} />}
                position="absolute"
                left={4}
                top="50%"
                transform="translateY(-50%)"
                size={buttonSize}
                colorScheme="whiteAlpha"
                borderRadius="full"
                onClick={(e) => {
                  e.stopPropagation();
                  handlePrevious();
                }}
                opacity={0.7}
                _hover={{ opacity: 1 }}
              />
              <IconButton
                aria-label="Next ad"
                icon={<ChevronRightIcon boxSize={8} />}
                position="absolute"
                right={4}
                top="50%"
                transform="translateY(-50%)"
                size={buttonSize}
                colorScheme="whiteAlpha"
                borderRadius="full"
                onClick={(e) => {
                  e.stopPropagation();
                  handleNext();
                }}
                opacity={0.7}
                _hover={{ opacity: 1 }}
              />
            </>
          )}

          {/* Ad Indicator Dots */}
          {ads.length > 1 && (
            <Flex
              position="absolute"
              bottom={2}
              left="50%"
              transform="translateX(-50%)"
              zIndex={3}
            >
              {ads.map((_, i) => (
                <Box
                  key={i}
                  w={2}
                  h={2}
                  mx={1}
                  borderRadius="full"
                  bg={i === currentIndex ? "white" : "whiteAlpha.600"}
                  transition="background 0.3s ease"
                  onClick={(e) => {
                    e.stopPropagation();
                    setCurrentIndex(i);
                  }}
                  cursor="pointer"
                  _hover={{
                    bg: "white",
                  }}
                />
              ))}
            </Flex>
          )}
        </Box>
      </Box>
    </>
  );
};

export default HomeAds;
