import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useSocket } from "@/context/SocketContext";
import { useAuth } from "@/context/AuthContext";
import LanguageSelector from "./LanguageSelector";
import { IUser } from "@/types/user";
import {
  Menu,
  X,
  ChevronDown,
  MessageSquare,
  Settings,
  LogOut,
  Building2,
  ShoppingBag,
  Search,
  /* Bell removed as no longer needed */
  User as UserIcon,
  HelpCircle,
  Plus,
  Globe2,
  Shield,
  TrendingUp,
  Users
} from "lucide-react";

interface UserHeaderProps {
  profileInfo: IUser;
}

const UserHeader: React.FC<UserHeaderProps> = ({ profileInfo }: any) => {
  const { unreadCount, clearNotifications } = useSocket();
  const navigate = useNavigate();
  const { t } = useTranslation(["header", "common"]);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isProductsOpen, setIsProductsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { logout } = useAuth();

  const whyUsLinks = [
    { name: t('common:reasons.globalAccess.hero.title'), path: '/reasons/global-access', icon: Globe2 },
    { name: t('common:reasons.securePlatform.hero.title'), path: '/reasons/secure-platform', icon: Shield },
    { name: t('common:reasons.fastGrowth.hero.title'), path: '/reasons/fast-growth', icon: TrendingUp },
    { name: t('common:reasons.partnerships.hero.title'), path: '/reasons/partnerships', icon: Users }
  ];

  const handleMessageIconClick = () => {
    clearNotifications();
    navigate("/messages");
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/items?search=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <>
      {/* Spacer div to push content below fixed header */}
      <div className="h-[106px]"></div>

      <div className="fixed top-0 left-0 right-0 z-50">
        {/* Top Header */}
        <div className="bg-primary">
          <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
            <div className="flex items-center justify-between h-10">
              <p className="text-sm text-white hidden md:block">
                {t("header.slogan") || "Global ticarette güvenilir çözüm ortağınız"}
              </p>
              <div className="flex items-center space-x-4 text-white w-full md:w-auto justify-between md:justify-end">
                <button
                  onClick={() => navigate('/design-packages')}
                  className="text-sm hover:text-gray-200 transition-colors"
                >
                  {t("header.navigation.designPackages")}
                </button>
                <LanguageSelector display="native" textColor="white" />
              </div>
            </div>
          </div>
        </div>

        {/* Main Header */}
        <div className="bg-white border-b shadow-sm">
          <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              {/* Logo */}
              <button
                onClick={() => navigate('/')}
                className="flex items-center space-x-2"
              >
                <img
                  src="/logo.png"
                  alt="e-exportcity"
                  className="h-8"
                  onError={(e) => {
                    // Prevent infinite loop by removing the onerror handler
                    e.currentTarget.onerror = null;
                    // Use a fallback image or hide
                    e.currentTarget.style.display = 'none';
                    const parent = e.currentTarget.parentElement;
                    if (parent) {
                      const fallback = document.createElement('div');
                      fallback.className = 'h-8 w-32 bg-gray-200 rounded flex items-center justify-center';
                      fallback.innerHTML = '<span class="text-gray-500 font-semibold">e-exportcity</span>';
                      parent.appendChild(fallback);
                    }
                  }}
                />
              </button>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-8">
                {/* Search */}
                <form onSubmit={handleSearch} className="relative w-96">
                  <input
                    type="text"
                    placeholder={t("header.search.placeholder")}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  />
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                </form>

                {/* Products and Services Dropdown */}
                <div className="relative">
                  <button
                    onClick={() => setIsProductsOpen(!isProductsOpen)}
                    className="flex items-center space-x-2 px-4 py-2.5 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors"
                  >
                    <span>{t("header.navigation.items")}</span>
                    <ChevronDown className={`h-4 w-4 transition-transform ${isProductsOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {isProductsOpen && (
                    <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-100 py-2 z-50">
                      <button
                        onClick={() => {
                          navigate('/items/add');
                          setIsProductsOpen(false);
                        }}
                        className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <Plus className="w-5 h-5 text-gray-400" />
                        <span>{t("header.navigation.add_item")}</span>
                      </button>
                      <div className="border-t my-2"></div>
                      <button
                        onClick={() => {
                          navigate('/items?type=product');
                          setIsProductsOpen(false);
                        }}
                        className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <ShoppingBag className="w-5 h-5 text-gray-400" />
                        <span>{t("header.navigation.products")}</span>
                      </button>
                      <button
                        onClick={() => {
                          navigate('/items?type=service');
                          setIsProductsOpen(false);
                        }}
                        className="w-full flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                      >
                        <HelpCircle className="w-5 h-5 text-gray-400" />
                        <span>{t("header.navigation.services")}</span>
                      </button>
                    </div>
                  )}
                </div>

                {/* Navigation Links */}
                <button
                  onClick={() => navigate('/stores')}
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  {t("header.navigation.stores")}
                </button>

                {/* Why Us dropdown removed for logged in users */}
                <button
                  onClick={() => navigate('/packages')}
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  {t("header.navigation.packages")}
                </button>
                <button
                  onClick={() => navigate('/representatives')}
                  className="text-gray-600 hover:text-gray-900 transition-colors"
                >
                  {t("header.representatives")}
                </button>
              </div>

              {/* Mobile: Design Packages and Language Selector */}
              <div className="flex md:hidden items-center space-x-2">
                <button
                  onClick={() => navigate('/design-packages')}
                  className="text-sm text-primary hover:text-[#0A9996] transition-colors font-medium"
                >
                  {t("header.navigation.designPackages")}
                </button>
                <LanguageSelector display="native" textColor="white" />
              </div>

              {/* User Menu */}
              <div className="hidden md:flex items-center space-x-4">
                {/* Messages Button */}
                <button
                  onClick={handleMessageIconClick}
                  className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors"
                >
                  <MessageSquare className="h-6 w-6" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 w-5 h-5 flex items-center justify-center bg-red-500 text-white text-xs rounded-full">
                      {unreadCount}
                    </span>
                  )}
                </button>

                {/* Notifications Button removed as requested */}

                {/* Profile Button */}
                <div className="relative">
                  <button
                    onClick={() => setIsProfileOpen(!isProfileOpen)}
                    className="flex items-center space-x-2"
                  >
                    {profileInfo?.avatar ? (
                      <img
                        src={profileInfo.avatar}
                        alt="Profile"
                        className="w-8 h-8 rounded-lg object-cover"
                        onError={(e) => {
                          // Prevent infinite loop by removing the onerror handler
                          e.currentTarget.onerror = null;
                          // Hide the image and show a fallback icon
                          e.currentTarget.style.display = 'none';
                          const parent = e.currentTarget.parentElement;
                          if (parent) {
                            const fallback = document.createElement('div');
                            fallback.className = 'flex h-8 w-8 items-center justify-center bg-gray-200 rounded-lg';
                            fallback.innerHTML = `<svg class="h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>`;
                            parent.appendChild(fallback);
                          }
                        }}
                      />
                    ) : (
                      <div className="flex h-8 w-8 items-center justify-center bg-gray-200 rounded-lg">
                        <UserIcon className="h-5 w-5 text-gray-500" />
                      </div>
                    )}
                    <ChevronDown className={`h-4 w-4 text-gray-400 transition-transform ${isProfileOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* Profile Dropdown */}
                  {isProfileOpen && (
                    <div className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-100 py-2 z-50">
                      <div className="px-4 py-3 border-b">
                        <p className="font-medium text-gray-900">{profileInfo?.firstName} {profileInfo?.lastName}</p>
                        <p className="text-sm text-gray-600">{profileInfo?.email}</p>
                      </div>

                      <div className="py-2">
                        <button
                          onClick={() => {
                            navigate('/profile');
                            setIsProfileOpen(false);
                          }}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <UserIcon className="h-5 w-5 text-gray-400" />
                          <span>{t("header.user.profile")}</span>
                        </button>
                        <button
                          onClick={() => {
                            navigate('/profile/store');
                            setIsProfileOpen(false);
                          }}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <Building2 className="h-5 w-5 text-gray-400" />
                          <span>{t("header.navigation.company_profile")}</span>
                        </button>
                      </div>

                      <div className="border-t py-2">
                        <button
                          onClick={() => {
                            navigate('/profile');
                            setIsProfileOpen(false);
                          }}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <Settings className="h-5 w-5 text-gray-400" />
                          <span>{t("header.settings")}</span>
                        </button>

                        <button
                          onClick={() => {
                            logout();
                            setIsProfileOpen(false);
                          }}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors"
                        >
                          <LogOut className="h-5 w-5 text-gray-400" />
                          <span>{t("header.user.logout")}</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden p-2 text-gray-400 hover:text-gray-600"
              >
                {isMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden bg-white border-b shadow-md">
            <div className="px-4 py-3">
              <form onSubmit={handleSearch} className="relative">
                <input
                  type="text"
                  placeholder={t("header.search.placeholder")}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              </form>
            </div>

            <div className="px-4 py-2 space-y-1">
              <button
                onClick={() => {
                  navigate('/items/add');
                  setIsMenuOpen(false);
                }}
                className="w-full flex items-center space-x-2 px-4 py-2 text-primary hover:bg-gray-50 rounded-lg transition-colors"
              >
                <Plus className="h-5 w-5" />
                <span>{t("header.navigation.add_item")}</span>
              </button>

              <button
                onClick={() => {
                  navigate('/stores');
                  setIsMenuOpen(false);
                }}
                className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
              >
                {t("header.navigation.stores")}
              </button>

              <div className="py-2 border-t my-2">
                <p className="px-4 py-2 text-sm font-medium text-gray-500">{t("header.navigation.whyUs")}</p>
                {whyUsLinks.map((link, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      navigate(link.path);
                      setIsMenuOpen(false);
                    }}
                    className="w-full flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
                  >
                    <link.icon className="h-5 w-5" />
                    <span>{link.name}</span>
                  </button>
                ))}
              </div>

              <button
                onClick={() => {
                  navigate('/packages');
                  setIsMenuOpen(false);
                }}
                className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
              >
                {t("header.navigation.packages")}
              </button>

              <button
                onClick={() => {
                  navigate('/representatives');
                  setIsMenuOpen(false);
                }}
                className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
              >
                {t("header.representatives")}
              </button>

              <button
                onClick={() => {
                  navigate('/design-packages');
                  setIsMenuOpen(false);
                }}
                className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
              >
                {t("header.navigation.designPackages")}
              </button>

              <button
                onClick={() => {
                  handleMessageIconClick();
                  setIsMenuOpen(false);
                }}
                className="w-full flex items-center justify-between px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
              >
                <span>{t("header.notifications.messages")}</span>
                {unreadCount > 0 && (
                  <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                    {unreadCount}
                  </span>
                )}
              </button>

              <button
                onClick={() => {
                  navigate('/profile');
                  setIsMenuOpen(false);
                }}
                className="w-full text-left px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg transition-colors"
              >
                {t("header.user.profile")}
              </button>

              <button
                onClick={() => {
                  logout();
                  setIsMenuOpen(false);
                }}
                className="w-full text-left px-4 py-2 text-red-600 hover:text-red-700 hover:bg-gray-50 rounded-lg transition-colors"
              >
                {t("header.user.logout")}
              </button>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default UserHeader;
