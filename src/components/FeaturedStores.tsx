import React from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation, Pagination, Autoplay } from "swiper/modules";
import { ExternalLink } from "lucide-react";
import { IStore } from "../types/store";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";

interface FeaturedStoresProps {
  stores: IStore[];
  isAuthenticated: boolean;
  onStoreClick: (storeId: string) => void;
}

const FeaturedStores: React.FC<FeaturedStoresProps> = ({
  stores,
  isAuthenticated,
  onStoreClick,
}) => {
  const { t } = useTranslation("featuredStores");
  const navigate = useNavigate();

  if (!stores || stores.length === 0) {
    return null;
  }

  const breakpoints = {
    320: { slidesPerView: 2, spaceBetween: 10 },
    640: { slidesPerView: 3, spaceBetween: 15 },
    768: { slidesPerView: 4, spaceBetween: 20 },
    1024: { slidesPerView: 5, spaceBetween: 20 },
    1280: { slidesPerView: 6, spaceBetween: 20 },
  };

  return (
    <div className="w-full py-16">
      <div className="full-width-container">
        <div className="w-full max-w-[1920px] mx-auto bg-white">
          <div className="flex items-center justify-between mb-12 px-4 md:px-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900">{t("title")}</h2>
              <p className="mt-3 text-lg text-gray-600">{t("subtitle")}</p>
            </div>
            <button
              onClick={() => navigate('/stores')}
              className="hidden md:inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
            >
              <span>{t("viewAll")}</span>
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>

          <div className="px-4 md:px-12">
            <div className="relative">
              <Swiper
                modules={[Navigation, Pagination, Autoplay]}
                spaceBetween={20}
                breakpoints={breakpoints}
                navigation
                pagination={{ clickable: true }}
                autoplay={{ delay: 5000 }}
                loop={true}
                className="py-4 pb-12"
              >
                {stores.map((store: any) => (
                  <SwiperSlide key={store._id} className="px-1">
                    <div
                      className="bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden cursor-pointer"
                      onClick={() => {
                        if (isAuthenticated) {
                          navigate(`/stores/${store._id}`);
                        } else {
                          onStoreClick(store._id);
                        }
                      }}
                    >
                      <div className="h-40 overflow-hidden bg-gray-100">
                        {store.coverImage ? (
                          <img
                            src={`${import.meta.env.VITE_SOCKET_URL}/${store.coverImage}`}
                            alt={store.name}
                            className="w-full h-full object-cover transform hover:scale-105 transition-transform duration-500"
                            onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                              // Prevent infinite loop by removing the onerror handler
                              e.currentTarget.onerror = null;
                              // Hide the image and show a fallback icon
                              e.currentTarget.style.display = 'none';
                              const parent = e.currentTarget.parentElement;
                              if (parent) {
                                const fallback = document.createElement('div');
                                fallback.className = 'flex h-full w-full items-center justify-center';
                                fallback.innerHTML = `<svg class="h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>`;
                                parent.appendChild(fallback);
                              }
                            }}
                          />
                        ) : (
                          <div className="flex h-full w-full items-center justify-center">
                            <svg className="h-10 w-10 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                              <polyline points="9 22 9 12 15 12 15 22"></polyline>
                            </svg>
                          </div>
                        )}
                      </div>

                      <div className="relative px-4 pt-10 pb-4">
                        <div className="absolute -top-8 left-4 w-16 h-16 rounded-full border-4 border-white overflow-hidden bg-white shadow-sm">
                          {store.logo ? (
                            <img
                              src={`${import.meta.env.VITE_SOCKET_URL}/${store.logo}`}
                              alt={`${store.name} logo`}
                              className="w-full h-full object-cover"
                              onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                                // Prevent infinite loop by removing the onerror handler
                                e.currentTarget.onerror = null;
                                // Hide the image and show a fallback icon
                                e.currentTarget.style.display = 'none';
                                const parent = e.currentTarget.parentElement;
                                if (parent) {
                                  const fallback = document.createElement('div');
                                  fallback.className = 'flex h-full w-full items-center justify-center';
                                  fallback.innerHTML = `<svg class="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path><circle cx="12" cy="7" r="4"></circle></svg>`;
                                  parent.appendChild(fallback);
                                }
                              }}
                            />
                          ) : (
                            <div className="flex h-full w-full items-center justify-center">
                              <svg className="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                              </svg>
                            </div>
                          )}
                        </div>

                        <h3 className="text-lg font-semibold text-gray-900 mb-1 truncate">
                          {store.name}
                        </h3>

                        <div className="flex items-center text-sm text-gray-500 mb-2">
                          <span className="truncate">
                            {store.city}, {store.country}
                          </span>
                        </div>

                        {store.verified && (
                          <div className="absolute top-2 right-2 bg-blue-50 px-2 py-1 rounded-full">
                            <span className="text-xs font-medium text-blue-600">
                              {t("verified")}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </div>
          </div>

          <div className="mt-8 text-center md:hidden">
            <button
              onClick={() => navigate('/stores')}
              className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
            >
              <span>{t("viewAll")}</span>
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturedStores;