import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination, Navigation } from 'swiper/modules';
import { ArrowRight, Package } from 'lucide-react';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

const Slider = () => {
  const navigate = useNavigate();

  const slides = [
    {
      image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&q=80",
      title: "Global Ticarette Güvenilir Çözüm Ortağınız",
      description: "150+ ülkede binlerce firma ile tanışın, işinizi büyütün",
      link: "/about"
    },
    {
      image: "https://images.unsplash.com/photo-1550751827-4bd374c3f58b?auto=format&fit=crop&q=80",
      title: "Güvenli ve Şeffaf Platform",
      description: "Doğrulanmış firmalar ve güvenli ticaret altyapısı",
      link: "/reasons/secure-platform"
    },
    {
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80",
      title: "Hızlı Büyüme Fırsatı",
      description: "İhracat potansiyelinizi maksimize edin",
      link: "/reasons/fast-growth"
    },
    {
      image: "https://images.unsplash.com/photo-1600880292203-757bb62b4baf?auto=format&fit=crop&q=80",
      title: "Stratejik İş Birlikleri",
      description: "Güvenilir iş ortaklarıyla çalışma imkanı",
      link: "/reasons/partnerships"
    }
  ];

  return (
    <div className="relative h-[400px] md:h-[500px] w-full mt-[64px] md:mt-0">
      <Swiper
        modules={[Autoplay, Pagination, Navigation]}
        spaceBetween={0}
        slidesPerView={1}
        autoplay={{
          delay: 5000,
          disableOnInteraction: false,
        }}
        pagination={{
          clickable: true,
          bulletClass: 'swiper-pagination-bullet !bg-white/50 !opacity-100',
          bulletActiveClass: 'swiper-pagination-bullet-active !bg-white',
        }}
        navigation={{
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev',
        }}
        className="h-full"
      >
        {slides.map((slide, index) => (
          <SwiperSlide key={index}>
            <div className="relative h-full w-full">
              <img
                src={slide.image}
                alt={slide.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-black/50" />
              <div className="absolute inset-0 flex items-center">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  <div className="max-w-2xl">
                    <h2 className="text-2xl md:text-4xl lg:text-5xl font-bold text-white mb-4 md:mb-6">
                      {slide.title}
                    </h2>
                    <p className="text-base md:text-lg lg:text-xl text-white/90 mb-6 md:mb-8">
                      {slide.description}
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <button
                        onClick={() => navigate(slide.link)}
                        className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-primary text-white rounded-lg font-medium hover:bg-[#0A9996] transition-colors group"
                      >
                        <span>Detaylı Bilgi</span>
                        <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                      </button>
                      <button
                        onClick={() => navigate('/register')}
                        className="inline-flex items-center px-6 md:px-8 py-3 md:py-4 bg-white text-primary rounded-lg font-medium hover:bg-gray-100 transition-colors group"
                      >
                        <Package className="h-5 w-5 mr-2" />
                        <span>Ücretsiz Üye Ol</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation Buttons */}
      <div className="swiper-button-prev !text-white !opacity-50 hover:!opacity-100 transition-opacity" />
      <div className="swiper-button-next !text-white !opacity-50 hover:!opacity-100 transition-opacity" />
    </div>
  );
};

export default Slider;