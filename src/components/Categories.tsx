import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Laptop, Recycle, GraduationCap, Wind, Factory, Building2, Play, Heart, Plus, Globe, ChevronRight } from 'lucide-react';

interface Category {
  icon: any;
  name: string;
  subcategories?: {
    name: string;
    items: string[];
  }[];
}

const categories: Category[] = [
  {
    icon: Laptop,
    name: "Bilgi Teknolojileri",
    subcategories: [
      {
        name: "<PERSON><PERSON><PERSON>lı<PERSON>",
        items: ["Web Yazılımları", "Mobil Uygulamalar", "Masaüstü Yazılımlar", "<PERSON>yun Yazılımları"]
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON>",
        items: ["Bilgisayar Donanımları", "Ağ Ekipmanları", "Sunucu Sistemleri", "Veri Depolama"]
      }
    ]
  },
  {
    icon: Recycle,
    name: "<PERSON><PERSON><PERSON> ve Atık",
    subcategories: [
      {
        name: "<PERSON><PERSON>k Yönetimi",
        items: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON> İşleme", "<PERSON><PERSON>k Toplama", "<PERSON>hl<PERSON>li Atık"]
      },
      {
        name: "<PERSON><PERSON><PERSON> Korum<PERSON>",
        items: ["Hava Kalitesi", "Su Arıtma", "Toprak İyileştirme", "Yenilenebilir Enerji"]
      }
    ]
  },
  {
    icon: GraduationCap,
    name: "Eğitim",
    subcategories: [
      {
        name: "Akademik",
        items: ["Üniversiteler", "Okullar", "Dil Okulları", "Mesleki Eğitim"]
      },
      {
        name: "Kurumsal",
        items: ["İş Eğitimi", "Liderlik", "Satış", "Teknik Eğitim"]
      }
    ]
  },
  {
    icon: Wind,
    name: "Enerji",
    subcategories: [
      {
        name: "Yenilenebilir",
        items: ["Güneş", "Rüzgar", "Hidroelektrik", "Biyokütle"]
      },
      {
        name: "Geleneksel",
        items: ["Petrol", "Doğalgaz", "Kömür", "Nükleer"]
      }
    ]
  },
  {
    icon: Factory,
    name: "İmalat",
    subcategories: [
      {
        name: "Endüstriyel",
        items: ["Makine", "Otomotiv", "Elektronik", "Metal"]
      },
      {
        name: "Tüketici",
        items: ["Tekstil", "Gıda", "Mobilya", "Kozmetik"]
      }
    ]
  },
  {
    icon: Building2,
    name: "İnşaat",
    subcategories: [
      {
        name: "Yapı",
        items: ["Konut", "Ticari", "Endüstriyel", "Altyapı"]
      },
      {
        name: "Malzeme",
        items: ["Çimento", "Demir", "Ahşap", "Yalıtım"]
      }
    ]
  },
  {
    icon: Play,
    name: "Medya",
    subcategories: [
      {
        name: "Dijital",
        items: ["Web", "Sosyal Medya", "Mobil", "Video"]
      },
      {
        name: "Geleneksel",
        items: ["TV", "Radyo", "Gazete", "Dergi"]
      }
    ]
  },
  {
    icon: Plus,
    name: "Sağlık",
    subcategories: [
      {
        name: "Tıbbi",
        items: ["Hastaneler", "Klinikler", "Laboratuvarlar", "İlaç"]
      },
      {
        name: "Ekipman",
        items: ["Cihazlar", "Sarf Malzeme", "Protez", "Ortopedi"]
      }
    ]
  },
  {
    icon: Heart,
    name: "Sosyal",
    subcategories: [
      {
        name: "Hizmetler",
        items: ["Danışmanlık", "Rehabilitasyon", "Bakım", "Eğitim"]
      },
      {
        name: "STK",
        items: ["Yardım", "Çevre", "Eğitim", "Sağlık"]
      }
    ]
  },
  {
    icon: Globe,
    name: "Ulaşım",
    subcategories: [
      {
        name: "Kara",
        items: ["Karayolu", "Demiryolu", "Metro", "Tramvay"]
      },
      {
        name: "Hava/Deniz",
        items: ["Havayolu", "Denizyolu", "Liman", "Lojistik"]
      }
    ]
  }
];

const Categories = () => {
  const navigate = useNavigate();
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);

  return (
    <div className="bg-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-8">Sektörler</h2>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {categories.map((category, index) => (
            <button
              key={index}
              onClick={() => {
                setSelectedCategory(selectedCategory === category ? null : category);
                setSelectedSubcategory(null);
              }}
              className={`group relative flex items-center justify-between p-4 rounded-xl transition-all duration-200 ${
                selectedCategory === category
                  ? 'bg-primary text-white shadow-lg scale-[1.02]'
                  : 'bg-gray-50 text-gray-900 hover:bg-gray-100 hover:shadow-md hover:scale-[1.02]'
              }`}
            >
              <div className="flex items-center space-x-3">
                <category.icon className={`w-6 h-6 transition-colors ${
                  selectedCategory === category ? 'text-white' : 'text-primary group-hover:text-primary'
                }`} />
                <span className="font-medium">{category.name}</span>
              </div>
              {category.subcategories && (
                <ChevronRight className={`w-5 h-5 transition-transform ${
                  selectedCategory === category ? 'rotate-90' : ''
                }`} />
              )}
            </button>
          ))}
        </div>

        {/* Subcategories */}
        {selectedCategory?.subcategories && (
          <div className="mt-8 animate-fadeIn">
            <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center space-x-2">
              <selectedCategory.icon className="w-5 h-5 text-primary" />
              <span>{selectedCategory.name} Alt Kategorileri</span>
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {selectedCategory.subcategories.map((subcategory, index) => (
                <div
                  key={index}
                  className={`p-6 rounded-xl transition-all duration-200 ${
                    selectedSubcategory === subcategory.name
                      ? 'bg-primary/5 border-2 border-primary shadow-lg'
                      : 'bg-gray-50 border-2 border-transparent hover:border-primary/30 hover:shadow-md'
                  }`}
                >
                  <h4 className="font-semibold text-gray-900 mb-4">{subcategory.name}</h4>
                  <ul className="space-y-2">
                    {subcategory.items.map((item, itemIndex) => (
                      <li 
                        key={itemIndex}
                        onClick={() => navigate(`/category/${item.toLowerCase().replace(/\s+/g, '-')}`)}
                        className="flex items-center space-x-2 text-gray-600 hover:text-primary cursor-pointer transition-colors"
                      >
                        <ChevronRight className="w-4 h-4" />
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Categories;