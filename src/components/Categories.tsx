import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Laptop,
  Recycle,
  GraduationCap,
  Wind,
  Factory,
  Building2,
  Play,
  Heart,
  Plus,
  Globe,
  ChevronRight,
  Truck
} from 'lucide-react';
import { categoryApi } from '../api/categoryApi';
import { ICategory } from '../types/category';

interface Subcategory {
  _id: string;
  id: string;
  name: string;
  nameEn: string;
  hasSubCategories: boolean;
  subcategories?: Subcategory[];
}

interface Category {
  id: string;
  icon: any;
  name: string;
  nameEn: string;
  type: string;
  subcategories?: Subcategory[];
}

const Categories: React.FC = () => {
  const navigate = useNavigate();
  const { t, i18n } = useTranslation('categories');
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(false);
  const [subcategories, setSubcategories] = useState<ICategory[]>([]);

  // Static top-level categories with icons
  const topLevelCategories: Category[] = [
    // {
    //   id: "1",
    //   icon: Wheat,
    //   name: "Tarım",
    //   nameEn: "Agriculture",
    //   type: "Product"
    // },
    {
      id: "2",
      icon: Factory,
      name: "İmalat",
      nameEn: "Manufacturing",
      type: "Product"
    },
    {
      id: "3",
      icon: Building2,
      name: "İnşaat",
      nameEn: "Construction",
      type: "Product"
    },
    {
      id: "4",
      icon: Wind,
      name: "Enerji",
      nameEn: "Energy",
      type: "Product"
    },
    {
      id: "5",
      icon: Plus,
      name: "Sağlık",
      nameEn: "Health",
      type: "Product"
    },
    {
      id: "6",
      icon: Laptop,
      name: "Bilgi Teknolojileri",
      nameEn: "Information Technology",
      type: "Service"
    },
    {
      id: "7",
      icon: Truck,
      name: "Ulaşım",
      nameEn: "Transportation",
      type: "Service"
    },
    {
      id: "8",
      icon: GraduationCap,
      name: "Eğitim",
      nameEn: "Education",
      type: "Service"
    },
    {
      id: "9",
      icon: Play,
      name: "Medya",
      nameEn: "Media",
      type: "Service"
    },
    {
      id: "10",
      icon: Recycle,
      name: "Çevre ve Atık",
      nameEn: "Environment",
      type: "Service"
    },
    {
      id: "11",
      icon: Heart,
      name: "Sosyal",
      nameEn: "Social",
      type: "Service"
    }
  ];

  // Fetch subcategories when a category is selected
  useEffect(() => {
    if (selectedCategory) {
      fetchSubcategories(selectedCategory.id);
    }
  }, [selectedCategory]);

  const fetchSubcategories = async (categoryId: string) => {
    setLoading(true);
    try {
      const data = await categoryApi.getSubcategories(categoryId);
      console.log('API response data:', data);
      setSubcategories(data);
    } catch (error) {
      console.error('Error fetching subcategories:', error);
    } finally {
      setLoading(false);
    }
  };

  // Build hierarchical structure from flat data
  const buildHierarchy = (data: ICategory[], parentId: string): Subcategory[] => {
    return data
      .filter(item => item.parent_id === parentId)
      .map(item => {
        // Convert nested subcategories if they exist in the API response
        let nestedSubcategories: Subcategory[] = [];
        if (item.subcategories && item.subcategories.length > 0) {
          nestedSubcategories = item.subcategories.map(subItem => ({
            _id: subItem._id,
            id: subItem.id,
            name: subItem.name,
            nameEn: subItem.nameEn,
            hasSubCategories: subItem.hasSubCategories || false,
            subcategories: subItem.subcategories as Subcategory[] || []
          }));
        } else {
          // Otherwise build hierarchy from flat data
          nestedSubcategories = buildHierarchy(data, item.id);
        }

        return {
          _id: item._id,
          id: item.id,
          name: item.name,
          nameEn: item.nameEn,
          hasSubCategories: item.hasSubCategories || false,
          subcategories: nestedSubcategories
        };
      });
  };

  const getDisplayName = (item: { name: string; nameEn: string }) => {
    return i18n.language === 'en' ? item.nameEn : item.name;
  };

  const hierarchicalSubcategories = selectedCategory && subcategories.length > 0
    ? buildHierarchy(subcategories, selectedCategory.id)
    : [];

  // Debug logging
  console.log('Selected Category ID:', selectedCategory?.id);
  console.log('Subcategories:', subcategories);
  console.log('Hierarchical Subcategories:', hierarchicalSubcategories);

  return (
    <div className="bg-white py-12">
      <div className="max-w-7xl mx-auto px-4 md:px-6 lg:px-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-8">{t('title')}</h2>

        {/* Grid layout with exactly 5 items per row */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
          {topLevelCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => {
                setSelectedCategory(selectedCategory?.id === category.id ? null : category);
              }}
              className={`group relative flex items-center justify-between p-3 rounded-xl transition-all duration-200 ${selectedCategory?.id === category.id
                ? 'bg-primary text-white shadow-lg scale-[1.02]'
                : 'bg-gray-50 text-gray-900 hover:bg-gray-100 hover:shadow-md hover:scale-[1.02]'
                }`}
            >
              <div className="flex items-center space-x-3">
                <category.icon className={`w-6 h-6 transition-colors ${selectedCategory?.id === category.id ? 'text-white' : 'text-primary group-hover:text-primary'
                  }`} />
                <span className="font-medium text-sm">{getDisplayName(category)}</span>
              </div>
              <ChevronRight className={`w-5 h-5 transition-transform ${selectedCategory?.id === category.id ? 'rotate-90' : ''
                }`} />
            </button>
          ))}
        </div>

        {/* Subcategories */}
        {selectedCategory && hierarchicalSubcategories.length > 0 && (
          <div className="mt-8 animate-fadeIn">
            <h3 className="text-lg font-medium text-gray-900 mb-6 flex items-center space-x-2">
              <selectedCategory.icon className="w-5 h-5 text-primary" />
              <span>{getDisplayName(selectedCategory)} {t('subcategories')}</span>
            </h3>

            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {hierarchicalSubcategories.map((subcategory) => (
                  <div
                    key={subcategory.id}
                    className="p-6 rounded-xl bg-gray-50 border-2 border-transparent hover:border-primary/30 hover:shadow-md transition-all duration-200"
                  >
                    <h4 className="font-semibold text-gray-900 mb-4">{getDisplayName(subcategory)}</h4>
                    {subcategory.subcategories && subcategory.subcategories.length > 0 && (
                      <ul className="space-y-2">
                        {subcategory.subcategories.map((item) => (
                          <li
                            key={item.id}
                            onClick={() => navigate(`/items?category=${item._id}`)}
                            className="flex items-center space-x-2 text-gray-600 hover:text-primary cursor-pointer transition-colors"
                          >
                            <ChevronRight className="w-4 h-4" />
                            <span>{getDisplayName(item)}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default Categories;