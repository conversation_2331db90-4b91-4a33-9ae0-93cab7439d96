import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Globe2,
  Shield,
  TrendingUp,
  Users,
  ArrowRight,
  Package
} from 'lucide-react';

const AboutSection: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('aboutSection');

  const features = [
    {
      icon: Globe2,
      title: t('features.globalAccess.title'),
      description: t('features.globalAccess.description')
    },
    {
      icon: Shield,
      title: t('features.securePlatform.title'),
      description: t('features.securePlatform.description')
    },
    {
      icon: TrendingUp,
      title: t('features.fastGrowth.title'),
      description: t('features.fastGrowth.description')
    },
    {
      icon: Users,
      title: t('features.partnerships.title'),
      description: t('features.partnerships.description')
    }
  ];

  return (
    <div className="bg-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            {t('subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80"
                alt="About E-exportcity"
                className="rounded-2xl shadow-xl"
              />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-tr from-primary/20 to-transparent"></div>
            </div>
          </div>

          <div className="space-y-6">
            <p className="text-gray-600">
              {t('description')}
            </p>

            <div className="space-y-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="bg-primary/10 rounded-lg p-2">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <button
                onClick={() => navigate('/about')}
                className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors group"
              >
                <span>{t('cta.detailedInfo')}</span>
                <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </button>
              <button
                onClick={() => navigate('/packages')}
                className="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors group"
              >
                <Package className="h-5 w-5 mr-2" />
                <span>{t('cta.buyPackage')}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutSection;