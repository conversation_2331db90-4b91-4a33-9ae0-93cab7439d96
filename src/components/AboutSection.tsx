import React from 'react';
import { motion } from 'framer-motion';
import { 
  Globe2, 
  Shield, 
  TrendingUp, 
  Users,
  CheckCircle,
  ArrowRight,
  Package
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

const AboutSection = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: Globe2,
      title: "Global Erişim",
      description: "150+ ülkede aktif iş ağına erişim imkanı ile global ticarette sınırları kaldırın"
    },
    {
      icon: Shield,
      title: "Güvenli Platform",
      description: "Doğrulanmış firmalar ve güvenli ticaret altyapısı ile güvenle ticaret yapın"
    },
    {
      icon: TrendingUp,
      title: "Hı<PERSON>lı Büyüme",
      description: "İhracat potansiyelinizi maksimize edin, işinizi hızla büyütün"
    },
    {
      icon: Users,
      title: "<PERSON>ş Birlikleri",
      description: "Güvenilir iş ortaklar<PERSON><PERSON> imkanı ve stratejik ortaklıklar kurun"
    }
  ];

  return (
    <div className="bg-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div 
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Hakkımızda
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            E-exportcity, Türkiye'nin önde gelen dijital ihracat platformu olarak, işletmelerin global pazarlara açılmasını kolaylaştırıyor.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="relative">
              <img
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80"
                alt="About E-exportcity"
                className="rounded-2xl shadow-xl"
              />
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-tr from-primary/20 to-transparent"></div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="space-y-6"
          >
            <p className="text-gray-600">
              Gelişmiş teknoloji altyapımız, uzman ihracat danışmanlarımız ve kapsamlı hizmet ağımızla, ihracatçılarımızın uluslararası ticaretteki rekabet gücünü artırıyoruz.
            </p>

            <div className="space-y-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="bg-primary/10 rounded-lg p-2">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <button
                onClick={() => navigate('/about')}
                className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors group"
              >
                <span>Detaylı Bilgi</span>
                <ArrowRight className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </button>
              <button
                onClick={() => navigate('/packages')}
                className="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-[#0A9996] transition-colors group"
              >
                <Package className="h-5 w-5 mr-2" />
                <span>Paket Satın Al</span>
              </button>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default AboutSection;