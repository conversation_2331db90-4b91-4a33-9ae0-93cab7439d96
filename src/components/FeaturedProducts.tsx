import React from 'react';
import { useNavigate } from 'react-router-dom';
import { BadgeCheck, MapPin, ExternalLink, Eye } from 'lucide-react';
import { isLoggedIn } from '../data/testUser';

const FeaturedProducts = () => {
  const navigate = useNavigate();
  const loggedIn = isLoggedIn();

  const products = [
    {
      id: 'industrial-automation',
      name: "Endüstriyel Otomasyon Yazılımı",
      company: "TechGlobal Solutions",
      type: "Hizmet Talebi",
      location: "İstanbul, Türkiye",
      image: "https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?auto=format&fit=crop&q=80",
      views: "1.2K",
      verified: true
    },
    {
      id: 'organic-agriculture',
      name: "Organik Tarım Ürünleri",
      company: "EcoTrade International",
      type: "Ürün Satışı",
      location: "İzmir, Türkiye",
      image: "https://images.unsplash.com/photo-1470107355970-2ace9f20ab8d?auto=format&fit=crop&q=80",
      views: "890",
      verified: true
    },
    {
      id: 'medical-equipment',
      name: "Medikal Ekipman İhracatı",
      company: "MediCare Exports",
      type: "Ürün Satışı",
      location: "Ankara, Türkiye",
      image: "https://images.unsplash.com/photo-1583947215259-38e31be8751f?auto=format&fit=crop&q=80",
      views: "750",
      verified: false
    },
    {
      id: 'construction-materials',
      name: "İnşaat Malzemeleri",
      company: "BuildPro Construction",
      type: "Ürün Satışı",
      location: "Bursa, Türkiye",
      image: "https://images.unsplash.com/photo-1581094288338-2314dddb7ece?auto=format&fit=crop&q=80",
      views: "1.1K",
      verified: true
    }
  ];

  const handleProductClick = (productId: string) => {
    if (loggedIn) {
      navigate(`/products/${productId}`);
    } else {
      navigate('/register');
    }
  };

  return (
    <div className="bg-gradient-to-b from-gray-50 to-white py-16">
      <div className="max-w-[1920px] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-12 px-4 md:px-12">
          <div>
            <h2 className="text-3xl font-bold text-gray-900">Öne Çıkan Ürün ve Hizmetler</h2>
            <p className="mt-3 text-lg text-gray-600">Firmaların yüklediği en popüler ürün ve hizmetler</p>
          </div>
          <button 
            onClick={() => navigate('/products')}
            className="hidden md:inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
          >
            <span>Tüm Ürünleri Gör</span>
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-4 md:px-12">
          {products.map((product, index) => (
            <div key={index} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
              <div className="aspect-[4/3] relative overflow-hidden">
                <img 
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>
                
                <div className="absolute bottom-4 left-4">
                  <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                    product.type === 'Hizmet Talebi' 
                      ? 'bg-primary/90 text-white' 
                      : 'bg-[#10B981]/90 text-white'
                  }`}>
                    {product.type}
                  </span>
                </div>

                <div className="absolute top-4 right-4">
                  <div className="flex items-center space-x-1 bg-white/90 rounded-full px-2 py-1">
                    <Eye className="h-4 w-4 text-gray-600" />
                    <span className="text-sm font-medium text-gray-600">{product.views}</span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">{product.name}</h3>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-primary">{product.company}</span>
                      {product.verified && (
                        <BadgeCheck className="h-4 w-4 text-primary flex-shrink-0" />
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 mb-4">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">{product.location}</span>
                </div>

                <button 
                  onClick={() => handleProductClick(product.id)}
                  className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
                >
                  <span>Detayları Gör</span>
                  <ExternalLink className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-8 text-center md:hidden">
          <button 
            onClick={() => navigate('/products')}
            className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
          >
            <span>Tüm Ürünleri Gör</span>
            <ExternalLink className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default FeaturedProducts;