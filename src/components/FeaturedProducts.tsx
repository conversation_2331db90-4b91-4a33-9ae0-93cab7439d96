import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { BadgeCheck, MapPin, ExternalLink, Eye, ArrowRight } from 'lucide-react';
import { useAuthCheck } from '@/hooks/useAuthCheck';
import { IItem } from '@/types/item';

interface FeaturedProductsProps {
  items: IItem[];
  onViewItem: (itemId: string) => void;
}

const FeaturedProducts: React.FC<FeaturedProductsProps> = ({ items, onViewItem }) => {
  const navigate = useNavigate();
  const { t } = useTranslation(['home', 'common']);
  useAuthCheck();

  // Always render the section, even if there are no items
  // This ensures the section is always visible
  const displayItems = items && items.length > 0 ? items : [];

  return (
    <div className="w-full bg-gradient-to-b from-gray-50 to-white py-16">
      <div className="full-width-container">
        <div className="w-full max-w-[1920px] mx-auto bg-white">
          <div className="flex items-center justify-between mb-12 px-4 md:px-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900">{t('featuredProducts.title')}</h2>
              <p className="mt-3 text-lg text-gray-600">{t('featuredProducts.subtitle')}</p>
            </div>
            <button
              onClick={() => navigate('/items')}
              className="hidden md:inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
            >
              <span>{t('featuredProducts.viewAll')}</span>
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 px-4 md:px-12">
            {displayItems.slice(0, 4).map((item: any) => (
              <div key={item._id} className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
                <div className="aspect-[4/3] relative overflow-hidden">
                  <img
                    src={`${import.meta.env.VITE_SOCKET_URL}/${item.images?.[0]}`}
                    alt={item.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent"></div>

                  <div className="absolute bottom-4 left-4">
                    <span className={`px-3 py-1.5 rounded-full text-sm font-medium ${item.type === 'service'
                      ? 'bg-primary/90 text-white'
                      : 'bg-[#10B981]/90 text-white'
                      }`}>
                      {item.type === 'service' ? t('common:common.service') : t('common:common.product')}
                    </span>
                  </div>

                  <div className="absolute top-4 right-4">
                    <div className="flex items-center space-x-1 bg-white/90 rounded-full px-2 py-1">
                      <Eye className="h-4 w-4 text-gray-600" />
                      <span className="text-sm font-medium text-gray-600">{item?.viewCount || 0}</span>
                    </div>
                  </div>
                </div>

                <div className="p-6">
                  <div className="mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 truncate">{item.name}</h3>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-primary">{item.store?.name || t('common:unknownStore')}</span>
                        {item.store?.isApproved && (
                          <BadgeCheck className="h-4 w-4 text-primary flex-shrink-0" />
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mb-4">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600">
                      {item.store?.location ?
                        `${item.store.location.city || ''}, ${item.store.location.country || ''}` :
                        t('common:locationUnavailable')}
                    </span>
                  </div>

                  <button
                    onClick={() => onViewItem(item._id)}
                    className="w-full py-2.5 bg-primary text-white rounded-lg text-sm font-medium transition-all duration-200 flex items-center justify-center space-x-2 hover:bg-[#0A9996] group"
                  >
                    <span>{t('featuredProducts.viewDetails')}</span>
                    <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-8 text-center md:hidden">
            <button
              onClick={() => navigate('/items')}
              className="inline-flex items-center space-x-2 text-primary hover:text-[#0A9996] transition-colors font-medium"
            >
              <span>{t('featuredProducts.viewAll')}</span>
              <ExternalLink className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FeaturedProducts;