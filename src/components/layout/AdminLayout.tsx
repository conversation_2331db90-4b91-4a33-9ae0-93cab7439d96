import React from "react";
import { Box, Flex } from "@chakra-ui/react";
import Header from "../Header";
import AdminSidebar from "../admin/AdminSidebar";

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  return (
    <Flex minH="100vh" direction="column">
      <Header />
      <Box flex="1">
        <AdminSidebar>{children}</AdminSidebar>
      </Box>
    </Flex>
  );
};

export default AdminLayout;
