import React from "react";
import Footer from "../common/Footer";
import Header from "../Header";
import { useLocation } from "react-router-dom";
import { Headphones } from "lucide-react";

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const location = useLocation();

  const hideFooterPaths = ["/messages", "/message-center"];
  const shouldShowFooter = !hideFooterPaths.includes(location.pathname);
  const isAdminRoute = location.pathname.startsWith("/admin");

  return (
    <div className="min-h-screen flex flex-col bg-gray-50 overflow-x-hidden">
      <Header />

      {/* Main Content */}
      <main className="flex-grow w-full">
        {children}
      </main>

      {/* Chat But<PERSON> (Only show outside of admin routes) */}
      {!isAdminRoute && (
        <div className="fixed bottom-6 right-6 z-40">
          <button
            onClick={() => window.location.href = '/profile/tickets'}
            className="bg-primary hover:bg-primary-hover text-white p-4 rounded-full shadow-lg transition-colors"
          >
            <Headphones className="h-6 w-6" />
          </button>
        </div>
      )}


      {/* Footer */}
      {shouldShowFooter && <Footer />}
    </div>
  );
};

export default Layout;
