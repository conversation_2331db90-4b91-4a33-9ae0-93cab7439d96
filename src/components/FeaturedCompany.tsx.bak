import React, { useRef } from "react";
import {
  Box,
  Grid,
  GridItem,
  Image,
  Text,
  VStack,
  Heading,
  Link as ChakraLink,
  useBreakpointValue,
} from "@chakra-ui/react";
import { Link } from "react-router-dom";
import { IStore } from "../types/store";
import { useTranslation } from "react-i18next";
import ImagePlaceholder from "./ImagePlaceholder";

interface FeaturedCompanyProps {
  selectedStore: IStore;
  stores: IStore[];
  onStoreSelect: (store: IStore) => void;
}

const FeaturedCompany: React.FC<FeaturedCompanyProps> = ({
  selectedStore,
  stores,
  onStoreSelect,
}) => {
  const { t } = useTranslation("featuredCompany");
  const coverImageHeight = useBreakpointValue({ base: "300px", md: "500px" });
  const storeRef = useRef<HTMLDivElement>(null);

  return (
    <Box py={8}>
      <Heading size="lg" mb={6} textAlign="center">
        {t("title")}
      </Heading>
      <Grid
        templateColumns={{ base: "1fr", md: "2fr 1fr" }}
        gap={8}
        alignItems="start"
      >
        {/* Featured Company Image and Details */}
        <GridItem>
          <Box
            borderRadius="lg"
            overflow="hidden"
            boxShadow="xl"
            ref={storeRef}
          >
            <Image
              src={`${import.meta.env.VITE_SOCKET_URL}/${selectedStore.coverImage}`}
              alt={t("image.coverAlt", { name: selectedStore.name })}
              width="100%"
              height={coverImageHeight}
              objectFit="cover"
              fallback={
                <ImagePlaceholder
                  width="100%"
                  height={coverImageHeight || "500px"}
                  text={t("image.noCover")}
                />
              }
            />

            <Box p={6} bg="white">
              <Heading size="md" mb={2}>
                {selectedStore.name}
              </Heading>
              <Text color="gray.600" noOfLines={3}>
                {selectedStore.description}
              </Text>
              <ChakraLink
                as={Link}
                to={`/stores/${selectedStore._id}`}
                color="blue.500"
                fontWeight="bold"
                display="inline-block"
                mt={4}
              >
                {t("viewCompany")} →
              </ChakraLink>
            </Box>
          </Box>
        </GridItem>

        {/* Company List */}
        <GridItem position="sticky" top="0">
          <VStack
            spacing={4}
            align="stretch"
            maxHeight={storeRef?.current?.clientHeight}
            overflowY="auto"
          >
            <Heading size="md" mb={2}>
              {t("otherCompanies")}
            </Heading>
            {stores.map((store) => (
              <Box
                key={store._id}
                p={4}
                borderWidth="1px"
                borderRadius="md"
                cursor="pointer"
                transition="all 0.2s"
                bg={selectedStore._id === store._id ? "blue.50" : "white"}
                _hover={{ bg: "gray.50" }}
                onClick={() => onStoreSelect(store)}
              >
                <Grid templateColumns="80px 1fr" gap={4}>
                  <Image
                    src={`${import.meta.env.VITE_SOCKET_URL}/${store.logo}`}
                    alt={t("image.logoAlt", { name: store.name })}
                    width="80px"
                    height="80px"
                    objectFit="cover"
                    borderRadius="md"
                    fallback={
                      <ImagePlaceholder
                        width="80px"
                        height="80px"
                        text={t("image.noLogo")}
                      />
                    }
                  />

                  <Box>
                    <Text fontWeight="bold" mb={1}>
                      {store.name}
                    </Text>
                    <Text fontSize="sm" color="gray.600" noOfLines={2}>
                      {store.description}
                    </Text>
                  </Box>
                </Grid>
              </Box>
            ))}
          </VStack>
        </GridItem>
      </Grid>
    </Box>
  );
};

export default FeaturedCompany;
