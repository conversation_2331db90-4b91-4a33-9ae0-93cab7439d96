import React, { useEffect, useState } from "react";
import {
  Box,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Spinner,
  Text,
  useToast,
  useColorModeValue,
} from "@chakra-ui/react";
import { useTranslation } from "react-i18next";
import { ITicket } from "@/types/ticket";
import { ticketApi } from "@/api/ticketApi";

const TicketList: React.FC = () => {
  const [tickets, setTickets] = useState<ITicket[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { t } = useTranslation(["tickets", "common"]);
  const toast = useToast();
  const bgColor = useColorModeValue("white", "gray.700");

  useEffect(() => {
    fetchTickets();
  }, []);

  const fetchTickets = async () => {
    try {
      const response: any = await ticketApi.getUserTickets();
      if (response.success) {
        setTickets(response.data);
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: t("tickets:messages.fetch_error"),
        description: error.message,
        status: "error",
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return "yellow";
      case "in_progress":
        return "blue";
      case "resolved":
        return "green";
      case "closed":
        return "red";
      default:
        return "gray";
    }
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" py={8}>
        <Spinner size="xl" />
      </Box>
    );
  }

  if (tickets.length === 0) {
    return (
      <Box textAlign="center" py={8}>
        <Text color="gray.500">{t("tickets:noTickets")}</Text>
      </Box>
    );
  }

  return (
    <Box bg={bgColor} borderRadius="lg" shadow="md" overflow="hidden">
      <Table variant="simple">
        <Thead>
          <Tr>
            <Th>{t("tickets:table.id")}</Th>
            <Th>{t("tickets:table.title")}</Th>
            <Th>{t("tickets:table.type")}</Th>
            <Th>{t("tickets:table.category")}</Th>
            <Th>{t("tickets:table.amount")}</Th>
            <Th>{t("tickets:table.status")}</Th>
            <Th>{t("tickets:table.createdAt")}</Th>
          </Tr>
        </Thead>
        <Tbody>
          {tickets.map((ticket: any) => (
            <Tr
              key={`ticket-${ticket.id}`}
              cursor="pointer"
              _hover={{ bg: "gray.50" }}
            >
              <Td>#{ticket.id}</Td>
              <Td>{ticket.title}</Td>
              <Td>{t(`tickets:types.${ticket.type}`)}</Td>
              <Td>{ticket.category}</Td>
              <Td>${ticket.amount}</Td>
              <Td>
                <Badge colorScheme={getStatusColor(ticket.status)}>
                  {t(`tickets:status.${ticket.status.toLowerCase()}`)}
                </Badge>
              </Td>
              <Td>{new Date(ticket.createdAt).toLocaleDateString()}</Td>
            </Tr>
          ))}
        </Tbody>
      </Table>
    </Box>
  );
};

export default TicketList;
