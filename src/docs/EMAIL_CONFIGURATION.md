# Email Configuration Guide for eExportCity Platform

This document provides information on how to configure and test the email service for the eExportCity platform.

## Configuration Overview

The platform uses a multi-provider email strategy with fallbacks to ensure reliable email delivery. The priorities are:

1. **Primary Mail Configuration** - Your primary mail server (configurable via environment variables)
2. **Alternative Mail Configuration** - Alternative settings for the same or different mail server
3. **SendGrid** - A reliable third-party provider used as a fallback (optional)
4. **Ethereal** - A fake SMTP service for testing in development environments (automatic in dev/test)

## Environment Variables

### Basic Configuration
Configure your email settings in the `.env` file:

```
# Primary SMTP Configuration
PRIMARY_MAIL_HOST=mail.yourdomain.com
PRIMARY_MAIL_PORT=465
PRIMARY_MAIL_SECURE=true
PRIMARY_MAIL_USER=<EMAIL>
PRIMARY_MAIL_PASS=your-password

# Alternative SMTP Configuration (optional)
ALT_MAIL_HOST=alternate-mail.yourdomain.com
ALT_MAIL_PORT=587
ALT_MAIL_SECURE=false
ALT_MAIL_USER=<EMAIL>
ALT_MAIL_PASS=alt-password

# SendGrid Fallback (optional but recommended)
SENDGRID_API_KEY=your-sendgrid-api-key

# Email From Address
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Your Company Name

# Debug Mode (set to 'true' to enable detailed SMTP logs)
DEBUG_MAIL=false
```

### Legacy Configuration Support

The system also supports the original configuration format for backward compatibility:

```
# Legacy SMTP Configuration (will be used if PRIMARY_* variables aren't set)
SMTP_HOST=mail.yourdomain.com
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=your-password
```

## Testing Your Email Configuration

Several test scripts are available to verify your email configuration:

### 1. Test with Custom Mail Settings

The most flexible option that allows testing with any mail server:

```bash
cd api
npx ts-node src/scripts/test-custom-mail.ts
```

You can specify custom mail settings via environment variables or command line arguments:

```bash
# Using environment variables
TEST_MAIL_HOST=smtp.gmail.com TEST_MAIL_PORT=587 TEST_MAIL_USER=<EMAIL> TEST_MAIL_PASS=your-password npx ts-node src/scripts/test-custom-mail.ts

# Using command line arguments
npx ts-node src/scripts/test-custom-mail.ts --host=smtp.gmail.com --port=587 --user=<EMAIL> --pass=your-password --secure=false
```

### 2. Test Your Primary Mail Server

```bash
cd api
npx ts-node src/scripts/test-hosting-mail.ts
```

This script attempts to send an email using only your hosting.com.tr mail server configuration. It tries both your primary port configuration and alternative port settings.

### 3. Test the Full Email Service with Fallbacks

```bash
cd api
npx ts-node src/scripts/test-mail.ts
```

This script tests the complete email service, which will attempt all configured providers in sequence until one succeeds.

### 4. Test with Ethereal (for Development)

```bash
cd api
npx ts-node src/scripts/test-ethereal.ts
```

This script creates a temporary Ethereal email account and sends a test email. The email will be captured by Ethereal and can be viewed using the preview URL provided in the console output.

## Troubleshooting

### SMTP Connection Issues

1. **Connection Refused or Timeouts**
   - Verify the mail server hostname and port are correct
   - Check if there are firewall restrictions blocking outbound SMTP connections
   - Try alternative ports (common SMTP ports: 25, 465, 587, 2525)

2. **SSL/TLS Issues**
   - For port 465, use `secure: true`
   - For port 587, use `secure: false` and `requireTLS: true`
   - For port 25, use `secure: false`

3. **Authentication Failures**
   - Ensure your username and password are correct
   - Some providers require app-specific passwords
   - Check if your mail server has IP-based restrictions

4. **Low-Level Diagnosis**
   - Use our socket diagnosis tool for direct SMTP testing:
     ```bash
     npx ts-node src/scripts/diagnose-smtp-socket.ts
     ```

### Common Mail Server Configurations

#### Gmail
```
PRIMARY_MAIL_HOST=smtp.gmail.com
PRIMARY_MAIL_PORT=587
PRIMARY_MAIL_SECURE=false
PRIMARY_MAIL_USER=<EMAIL>
PRIMARY_MAIL_PASS=your-app-password
```
Note: For Gmail, you need to create an "App Password" in your Google Account security settings.

#### Outlook/Office 365
```
PRIMARY_MAIL_HOST=smtp.office365.com
PRIMARY_MAIL_PORT=587
PRIMARY_MAIL_SECURE=false
PRIMARY_MAIL_USER=<EMAIL>
PRIMARY_MAIL_PASS=your-password
```

#### hosting.com.tr
Common configuration for hosting.com.tr mail servers (may vary):
```
PRIMARY_MAIL_HOST=mail.yourdomain.com
PRIMARY_MAIL_PORT=465
PRIMARY_MAIL_SECURE=true
PRIMARY_MAIL_USER=<EMAIL>
PRIMARY_MAIL_PASS=your-password
```

## Production Recommendations

For production environments, we recommend:

1. Ensuring your mail server is properly configured
2. Setting up DKIM, SPF, and DMARC records for your domain to improve deliverability
3. Maintaining a SendGrid account as a reliable fallback
4. Monitoring email delivery rates and bounces
5. Setting up email templates for consistent communication
6. Using environment-specific settings (separate dev/test/prod configurations)

## Advanced Configuration

If you need to modify the email transport configurations beyond what environment variables allow, edit the `api/src/services/emailService.ts` file:

- Adjust timeouts for connection, greeting, and socket if needed
- Modify TLS settings based on your mail server requirements
- Add additional fallback providers if necessary

For more information on Nodemailer configuration options, see the [Nodemailer documentation](https://nodemailer.com/smtp/).