import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import Home from './pages/Home';
import Login from './pages/Login';
import Register from './pages/Register';
import Packages from './pages/Packages';
import AdditionalPackages from './pages/AdditionalPackages';
import DesignPackages from './pages/DesignPackages';
import Partnership from './pages/Partnership';
import FAQ from './pages/FAQ';
import Search from './pages/Search';
import About from './pages/About';
import Companies from './pages/Companies';
import CompanyProfile from './pages/CompanyProfile';
import Representatives from './pages/Representatives';
import Products from './pages/Products';
import AddProduct from './pages/AddProduct';
import ProductDetail from './pages/ProductDetail';
import Settings from './pages/Settings';
import Messages from './pages/Messages';
import Notifications from './pages/Notifications';
import GlobalAccess from './pages/reasons/GlobalAccess';
import SecurePlatform from './pages/reasons/SecurePlatform';
import FastGrowth from './pages/reasons/FastGrowth';
import Partnerships from './pages/reasons/Partnerships';

const App = () => {
  return (
    <Routes>
      <Route path="/" element={<Home />} />
      <Route path="/login" element={<Login />} />
      <Route path="/register" element={<Register />} />
      <Route path="/packages" element={<Packages />} />
      <Route path="/packages/:id" element={<AdditionalPackages />} />
      <Route path="/design-packages" element={<DesignPackages />} />
      <Route path="/partnership" element={<Partnership />} />
      <Route path="/faq" element={<FAQ />} />
      <Route path="/search" element={<Search />} />
      <Route path="/about" element={<About />} />
      <Route path="/companies" element={<Companies />} />
      <Route path="/companies/:id" element={<CompanyProfile />} />
      <Route path="/representatives" element={<Representatives />} />
      <Route path="/products" element={<Products />} />
      <Route path="/products/add" element={<AddProduct />} />
      <Route path="/products/:id" element={<ProductDetail />} />
      <Route path="/settings" element={<Settings />} />
      <Route path="/messages" element={<Messages />} />
      <Route path="/notifications" element={<Notifications />} />
      <Route path="/reasons/global-access" element={<GlobalAccess />} />
      <Route path="/reasons/secure-platform" element={<SecurePlatform />} />
      <Route path="/reasons/fast-growth" element={<FastGrowth />} />
      <Route path="/reasons/partnerships" element={<Partnerships />} />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default App;