// App.tsx
import React, { useEffect, useState } from "react";
import "./index.css"; // Import global styles including Tailwind directives
import { Routes, Route, Navigate } from "react-router-dom";
import {
  Box,
  Container,
  ChakraProvider,
} from "@chakra-ui/react";
import { FullPageLoading } from "./components/ui/LoadingSpinner";
import LoadingSpinner from "./components/ui/LoadingSpinner";
import Home from "./pages/Home";
import Login from "./pages/Login";
import Register from "./pages/Register";
import PackageSelection from "./pages/PackageSelection";
import MessageCenter from "./pages/MessageCenter";
import AdminLogin from "./pages/admin/AdminLogin";
import UserProfile from "./pages/UserProfile";
import ItemListing from "./pages/ItemListing";
import ItemView from "./pages/ItemView";
import PackageListing from "./pages/PackageListing";
import ProductAdd from "./pages/ProductAdd";
import i18nPromise from "./i18n";
import EditPackage from "./pages/EditPackage";

// Lazy load admin components
const ManageDesignPackages = React.lazy(() => import("./pages/admin/ManageDesignPackages"));
const EditDesignPackage = React.lazy(() => import("./pages/admin/EditDesignPackage"));
const AdminDashboard = React.lazy(() => import("./pages/admin/AdminDashboard"));
const ManagePackages = React.lazy(() => import("./pages/admin/ManagePackages"));
const ManageProductsServices = React.lazy(() => import("./pages/admin/ManageProductsServices"));
const ManageUsers = React.lazy(() => import("./pages/admin/ManageUsers"));
const ManageRepresentatives = React.lazy(() => import("./pages/admin/ManageRepresentatives"));
const ManageTickets = React.lazy(() => import("./pages/admin/ManageTickets"));
const ManageHomeAds = React.lazy(() => import("./pages/admin/ManageHomeAds"));
const ManageStores = React.lazy(() => import("./pages/admin/ManageStores"));
const ManageLiveChat = React.lazy(() => import("./pages/admin/ManageLiveChat"));
// Note: EditPackage is used for both admin and user, so not lazy loaded with admin group here
// If EditPackage for admin is different, it should be a separate component and lazy loaded.

import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import DebugStore from "./pages/DebugStore";
import IyzicoPaymentCallback from "./pages/IyzicoPaymentCallback";
import Stores from "./pages/Stores";
import StoreDetail from "./pages/StoreDetail";
import UserItems from "./pages/UserItems";
import { useAuth } from "./context/AuthContext";
import Layout from "./components/layout/Layout";
import AdminLayout from "./components/layout/AdminLayout";
import About from "./pages/About";
import Partnership from "./pages/Partnership";
import FAQ from "./pages/FAQ";
import GlobalAccess from "./pages/reasons/GlobalAccess";
import SecurePlatform from "./pages/reasons/SecurePlatform";
import FastGrowth from "./pages/reasons/FastGrowth";
import Partnerships from "./pages/reasons/Partnerships";
import Representatives from "./pages/Representatives";
import DesignPackages from "./pages/DesignPackages";
import Contact from "./pages/Contact";
import PrivacyPolicy from "./pages/PrivacyPolicy";
import LiveChat from "./components/LiveChat";
import { i18n } from "i18next";

// Simple loading component for Suspense fallback
const SuspenseLoader = () => (
  <div className="flex items-center justify-center h-64">
    <LoadingSpinner size="lg" />
  </div>
);

// Define route protection components outside of App component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <FullPageLoading />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

const ProtectedAdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isAdmin, isLoading } = useAuth();

  if (isLoading) {
    return <FullPageLoading />;
  }

  if (!isAuthenticated || !isAdmin) {
    return <Navigate to="/admin/login" replace />;
  }

  return <>{children}</>;
};

const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return <FullPageLoading />;
  }

  if (isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  const [i18n, setI18n] = useState<i18n | null>(null);
  const { isLoading } = useAuth();

  useEffect(() => {
    i18nPromise.then((i18nInstance) => {
      setI18n(i18nInstance);
    });

    // Add developer info to console
    console.log("%c Developer: Umut Korkmaz", "font-size: 16px; color: #2196F3;");
    console.log("%c Contact: <EMAIL>", "font-size: 16px; color: #2196F3;");
    console.log("%c LinkedIn: https://linkedin.com/in/umut-korkmaz", "font-size: 16px; color: #2196F3;");
  }, []);

  if (!i18n || isLoading) {
    return <FullPageLoading />;
  }

  // Function to check if the current path is an admin path
  const isAdminPath = () => {
    return window.location.pathname.startsWith('/admin');
  };

  return (
    <ChakraProvider>
      <Box>
        <Box>
          {/* Add LiveChat component only for non-admin paths */}
          {!isAdminPath() && <LiveChat />}

          <Routes>
            {/* Public Routes - Redirect to home if authenticated */}
            <Route
              path="/login"
              element={
                <PublicRoute>
                  <Login />
                </PublicRoute>
              }
            />

            <Route
              path="/register"
              element={
                <PublicRoute>
                  <Register />
                </PublicRoute>
              }
            />

            <Route
              path="/forgot-password"
              element={
                <PublicRoute>
                  <ForgotPassword />
                </PublicRoute>
              }
            />

            <Route
              path="/reset-password"
              element={
                <PublicRoute>
                  <ResetPassword />
                </PublicRoute>
              }
            />

            {/* Special Routes - Always accessible */}
            <Route
              path="/payment-callback"
              element={<IyzicoPaymentCallback />}
            />

            <Route path="/admin/login" element={<AdminLogin />} />

            {/* Public static pages - No authentication required */}
            <Route
              path="/about"
              element={
                <Layout>
                  <About />
                </Layout>
              }
            />

            <Route
              path="/partnership"
              element={
                <Layout>
                  <Partnership />
                </Layout>
              }
            />

            <Route
              path="/faq"
              element={
                <Layout>
                  <FAQ />
                </Layout>
              }
            />

            <Route
              path="/reasons/global-access"
              element={
                <Layout>
                  <GlobalAccess />
                </Layout>
              }
            />

            <Route
              path="/reasons/secure-platform"
              element={
                <Layout>
                  <SecurePlatform />
                </Layout>
              }
            />

            <Route
              path="/reasons/fast-growth"
              element={
                <Layout>
                  <FastGrowth />
                </Layout>
              }
            />

            <Route
              path="/reasons/partnerships"
              element={
                <Layout>
                  <Partnerships />
                </Layout>
              }
            />

            <Route
              path="/representatives"
              element={
                <Layout>
                  <Representatives />
                </Layout>
              }
            />

            <Route
              path="/packages"
              element={
                <Layout>
                  <Container maxW="container.xl" py={8}>
                    <PackageSelection />
                  </Container>
                </Layout>
              }
            />

            <Route
              path="/design-packages"
              element={
                <Layout>
                  <Container maxW="container.xl" py={8}>
                    <DesignPackages />
                  </Container>
                </Layout>
              }
            />

            <Route
              path="/contact"
              element={
                <Layout>
                  <Contact />
                </Layout>
              }
            />

            <Route
              path="/privacy-policy"
              element={
                <Layout>
                  <PrivacyPolicy />
                </Layout>
              }
            />

            {/* Protected Routes - Redirect to login if not authenticated */}
            <Route
              path="/"
              element={
                <Layout>
                  <Container maxW="container.xl" pb={8}>
                    <Home />
                  </Container>
                </Layout>
              }
            />

            {/* Moved package selection to public pages */}

            <Route
              path="/messages"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Container maxW="container.xl" py={8}>
                      <MessageCenter />
                    </Container>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/profile/*"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Container maxW="container.xl" py={8}>
                      <UserProfile />
                    </Container>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/my-items"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Container maxW="container.xl" py={8}>
                      <UserItems />
                    </Container>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/items"
              element={
                <Layout>
                  <Container maxW="container.xl" py={8}>
                    <ItemListing />
                  </Container>
                </Layout>
              }
            />

            <Route
              path="/items/add"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Container maxW="container.xl" py={8}>
                      <ProductAdd />
                    </Container>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/items/:id"
              element={
                <Layout>
                  <Container maxW="container.xl" py={8}>
                    <ItemView />
                  </Container>
                </Layout>
              }
            />

            <Route
              path="/packages/:id"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Container maxW="container.xl" py={8}>
                      <PackageListing />
                    </Container>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/edit-package/:id"
              element={
                <ProtectedRoute>
                  <Layout>
                    <Container maxW="container.xl" py={8}>
                      <EditPackage />
                    </Container>
                  </Layout>
                </ProtectedRoute>
              }
            />

            <Route
              path="/stores"
              element={
                <Layout>
                  <Stores />
                </Layout>
              }
            />

            <Route
              path="/stores/:id"
              element={
                <Layout>
                  <Container maxW="container.xl" py={8}>
                    <StoreDetail />
                  </Container>
                </Layout>
              }
            />

            {/* Admin Routes */}
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<FullPageLoading />}>
                      <AdminDashboard />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/packages"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManagePackages />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/packages/:id"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <EditPackage />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/design-packages"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManageDesignPackages />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/design-packages/:id"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <EditDesignPackage />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/products"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManageProductsServices />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/users"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManageUsers />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/representatives"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManageRepresentatives />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/tickets"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManageTickets />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/homepage-ads"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManageHomeAds />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/stores"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManageStores />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            <Route
              path="/admin/live-chat"
              element={
                <ProtectedAdminRoute>
                  <AdminLayout>
                    <React.Suspense fallback={<SuspenseLoader />}>
                      <ManageLiveChat />
                    </React.Suspense>
                  </AdminLayout>
                </ProtectedAdminRoute>
              }
            />

            {/* Fallback route */}
            <Route path="/debug/store" element={<DebugStore />} />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Box>
      </Box>
    </ChakraProvider>
  );
};

export default App;
