const fs = require('fs');
const path = require('path');

// Languages to update
const languages = ['ar', 'de', 'en', 'es', 'fr', 'it', 'ru', 'tr', 'zh'];

// New translations to add to each language file
const newTranslations = {
  en: {
    fetchDataFailed: {
      title: "Data Loading Error",
      description: "Failed to load initial data. Please refresh and try again."
    },
    fetchCategoriesFailed: {
      title: "Data Loading Error",
      description: "Failed to load categories. Please refresh and try again."
    }
  },
  tr: {
    fetchDataFailed: {
      title: "Veri Yükleme Hatası",
      description: "Başlangıç verileri yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin."
    },
    fetchCategoriesFailed: {
      title: "Veri Yükleme Hatası",
      description: "Kategoriler yüklenemedi. Lütfen sayfayı yenileyip tekrar deneyin."
    }
  },
  ar: {
    fetchDataFailed: {
      title: "خطأ في تحميل البيانات",
      description: "فشل في تحميل البيانات الأولية. يرجى تحديث الصفحة والمحاولة مرة أخرى."
    },
    fetchCategoriesFailed: {
      title: "خطأ في تحميل البيانات",
      description: "فشل في تحميل الفئات. يرجى تحديث الصفحة والمحاولة مرة أخرى."
    }
  },
  de: {
    fetchDataFailed: {
      title: "Fehler beim Laden der Daten",
      description: "Fehler beim Laden der Anfangsdaten. Bitte aktualisieren Sie die Seite und versuchen Sie es erneut."
    },
    fetchCategoriesFailed: {
      title: "Fehler beim Laden der Daten",
      description: "Fehler beim Laden der Kategorien. Bitte aktualisieren Sie die Seite und versuchen Sie es erneut."
    }
  },
  es: {
    fetchDataFailed: {
      title: "Error al Cargar Datos",
      description: "Error al cargar datos iniciales. Por favor actualice la página e intente nuevamente."
    },
    fetchCategoriesFailed: {
      title: "Error al Cargar Datos",
      description: "Error al cargar categorías. Por favor actualice la página e intente nuevamente."
    }
  },
  fr: {
    fetchDataFailed: {
      title: "Erreur de Chargement des Données",
      description: "Échec du chargement des données initiales. Veuillez actualiser la page et réessayer."
    },
    fetchCategoriesFailed: {
      title: "Erreur de Chargement des Données",
      description: "Échec du chargement des catégories. Veuillez actualiser la page et réessayer."
    }
  },
  it: {
    fetchDataFailed: {
      title: "Errore di Caricamento Dati",
      description: "Impossibile caricare i dati iniziali. Aggiorna la pagina e riprova."
    },
    fetchCategoriesFailed: {
      title: "Errore di Caricamento Dati",
      description: "Impossibile caricare le categorie. Aggiorna la pagina e riprova."
    }
  },
  ru: {
    fetchDataFailed: {
      title: "Ошибка Загрузки Данных",
      description: "Не удалось загрузить начальные данные. Пожалуйста, обновите страницу и попробуйте снова."
    },
    fetchCategoriesFailed: {
      title: "Ошибка Загрузки Данных",
      description: "Не удалось загрузить категории. Пожалуйста, обновите страницу и попробуйте снова."
    }
  },
  zh: {
    fetchDataFailed: {
      title: "数据加载错误",
      description: "无法加载初始数据。请刷新页面并重试。"
    },
    fetchCategoriesFailed: {
      title: "数据加载错误",
      description: "无法加载类别。请刷新页面并重试。"
    }
  }
};

// Update each language file
languages.forEach(lang => {
  const filePath = path.join(__dirname, 'src/locales', lang, 'register.json');
  
  try {
    // Read the current file
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);
    
    // Add the new translations
    if (!data.toasts.fetchDataFailed) {
      data.toasts.fetchDataFailed = newTranslations[lang]?.fetchDataFailed || newTranslations.en.fetchDataFailed;
    }
    
    if (!data.toasts.fetchCategoriesFailed) {
      data.toasts.fetchCategoriesFailed = newTranslations[lang]?.fetchCategoriesFailed || newTranslations.en.fetchCategoriesFailed;
    }
    
    // Write the updated file
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`Updated ${lang}/register.json`);
  } catch (error) {
    console.error(`Error updating ${lang}/register.json:`, error.message);
  }
});

console.log('All register locale files updated successfully!');
