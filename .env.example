# MongoDB Configuration
MONGO_URI=mongodb://localhost:27017/your_database

# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_REFRESH_SECRET=your_jwt_refresh_secret

# Email Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password
EMAIL_FROM=<EMAIL>

EMAIL_SERVER_HOST=mail.eexportcity.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASS=Info13579!

# Iyzico Payment Configuration
IYZICO_API_KEY=your_iyzico_api_key
IYZICO_SECRET_KEY=your_iyzico_secret_key
IYZICO_BASE_URL=https://sandbox-api.iyzipay.com

# Frontend URL
APP_FE_URL=http://localhost:3000

# NetGSM Configuration
NETGSM_USERCODE=your_netgsm_usercode
NETGSM_PASSWORD=your_netgsm_password
NETGSM_MSGHEADER=your_company_name

# Card Encryption Configuration
CARD_ENCRYPTION_KEY=your_32_character_encryption_key_here_must_be_32
# Generate a secure 32-character key: openssl rand -hex 16
