# E-Exportcity Platform

## Project Description
This e-exportcity platform is designed to connect companies and brokers, enabling them to list products and services, make requests, and engage in business transactions. The platform also allows for the registration of individual brokers and companies, with different membership levels providing various privileges and access. The platform includes robust admin functionality for managing platform activities, user interactions, and content moderation.

## Key Features
- User Management
  - Companies: Register, create profiles, list products/services, and interact with requests.
  - Brokers: Register, create profiles, and act as intermediaries in business transactions.
- Product/Service Listings
  - Ability for users to list products or services they offer or need.
  - Categories and sub-categories to organize listings.
  - Search and filter functionality to find relevant products/services.
- Membership Packages
  - Bronze, Silver, Gold: Different membership tiers with privileges such as the number of requests that can be viewed/created, promotion time, and access to premium features.
  - Packages can only be updated by the admin.
- Messaging System
  - Direct communication between users (companies and brokers) through an internal messaging system.
- Review and Rating System
  - Users can review and rate transactions to ensure quality and trustworthiness.
- Admin Dashboard
  - Manage platform content, including user-generated listings, reviews, and messages.
  - Oversee user memberships, monitor activities, and update membership packages.
  - Approve or reject product/service listings and advertisements.
  - Add or update sectors and sub-categories.
- Multi-language Support
  - Support for Turkish and English, with the ability to add more languages.
- Advertisement Options
  - Users can purchase homepage advertisements for increased visibility.
- Additional Features
  - SMS and Email Notifications: Automated notifications for account activity, new messages, etc.
  - Homepage Advertisement: Users can purchase this for 00.
  - Additional Request Views/Creations: Purchase 10 extra views or creation rights for 5.

## Technologies and Libraries
- **Frontend**:
  - React (with TypeScript)
  - Vite (as the build tool)
  - Redux Toolkit (for state management)
  - React Router (for routing)
  - Axios (for API requests)
  - Chakra UI (for UI components)
  - Tailwind CSS (for styling)
  - react-i18next (for internationalization)

## Getting Started
1. **Install Dependencies**: Run `pnpm install` to install all necessary packages.
2. **Run the Development Server**: Run `pnpm dev` to start the development server.
3. **Build the Project**: Run `pnpm build` to build the project for production.

