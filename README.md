# e-exportcity-backend

Backend for the e-exportcity platform.

## Setup

1. Clone the repository
2. Install dependencies:
```bash
pnpm install
```

3. Create a `.env` file based on `.env.production` or `.env.example`.

## Environment Configuration

Key environment variables:
- `APP_URL`: The URL of the API (e.g. https://api.e-exportcity.com)
- `APP_FE_URL`: The URL of the frontend (e.g. https://www.e-exportcity.com)
- `FRONTEND_URL`: The URL of the frontend (same as APP_FE_URL)
- `MONGO_URI`: MongoDB connection string
- `JWT_SECRET_USER`: Secret for user JWT tokens
- `JWT_SECRET_ADMIN`: Secret for admin JWT tokens
- `PORT`: Port to run the server on (default: 5050)
- `EMAIL_*`: Email configuration settings
- `IYZICO_*`: Payment gateway settings
- `REDIS_ENABLED`: Set to 'true' to enable Redis, 'false' to disable

## Development

Run the development server:
```bash
pnpm run dev
```

## Testing

Run tests:
```bash
pnpm test
```

## Production

Build for production:
```bash
pnpm build
```

Run in production:
```bash
pnpm start
```

## Database Seeding

Seed the database with test data using:
```bash
pnpm seed
pnpm seed:categories
pnpm seed:items
pnpm seed:admin
pnpm seed:user
pnpm seed:subscriptions
pnpm seed:stores
```

## Dependencies

- Express: Web framework
- Mongoose: MongoDB ODM
- TypeScript: Static typing
- JWT: Authentication
- Nodemailer: Email service
- Socket.io: Real-time communication
- Iyzipay: Payment processing
- Redis: Caching (optional)